#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/22
@Auth ： 逗逗的小老鼠
@File ：mock_mq_message.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：Mock MQ message generator
"""

import json
import time
from datetime import datetime
from lib.get_log import logger, exception

@exception(logger)
def create_mock_mq_message(topic_name, tag_name, message_body):
    """
    创建模拟的MQ消息体
    
    Args:
        topic_name: MQ主题名称
        tag_name: MQ标签名称
        message_body: 消息内容，JSON格式字符串或字典
    
    Returns:
        dict: 完整的MQ消息体
    """
    # 确保message_body是字典类型
    if isinstance(message_body, str):
        try:
            message_body = json.loads(message_body)
        except json.JSONDecodeError:
            logger.error("Invalid JSON string provided for message_body")
            raise ValueError("Invalid JSON string provided for message_body")
    
    # 创建MQ消息体
    mq_message = {
        'topic_name': topic_name,
        'tag_name': tag_name,
        'message_id': f"mock_{int(time.time() * 1000)}",
        'keys': f"mock_key_{int(time.time() * 1000)}",
        'born_timestamp': int(time.time() * 1000),
        'store_timestamp': int(time.time() * 1000),
        'born_host': "mock_host:9876",
        'message_body': message_body
    }
    
    return mq_message

@exception(logger)
def mock_offline_order_message():
    """
    模拟离线订单消息
    
    Returns:
        dict: 模拟的MQ消息体
    """
    # 离线订单数据
    order_data = {
        "baseOrderInfo": {
            "orderNo": {"orderNo": "1603614888399450135"},
            "parentOrderNo": None,
            "thirdPlatformCodeValue": "HAIDIAN",
            "thirdOrderNo": {"thirdOrderNo": "1025050600000009"},
            "parentThirdOrderNo": None,
            "dayNum": "",
            "orderStateValue": "DONE",
            "created": "2025-05-06 16:49:05",
            "payTime": "2025-05-06 16:49:05",
            "completeTime": "2025-05-06 16:49:05",
            "billTime": "2025-05-06 16:49:05",
            "dataVersion": 1,
            "actualPayAmount": 6.400000,
            "actualCollectAmount": 6.400000,
            "orderCouponList": [],
            "serialNo": None,
            "isOnPromotion": False
        },
        "basePrescriptionInfo": {
            "prescriptionTypeValue": None,
            "prescriptionNo": None
        },
        "payInfoList": [
            {
                "payType": {"payType": "1"},
                "payName": "现金",
                "payAmount": 6.400000
            }
        ],
        "orderDetailList": [
            {
                "baseOrderDetailInfo": {
                    "orderNo": {"orderNo": "1603614888399450135"},
                    "rowNo": "1",
                    "platformSkuId": {"platformSkuId": "101428"},
                    "erpCode": {"erpCode": "101428"},
                    "erpName": "双唑泰栓_天洋_7枚*1板",
                    "commodityCount": 1.000000,
                    "statusValue": "NORMAL",
                    "giftTypeValue": "NOT_GIFT",
                    "originalPrice": 7.000000,
                    "price": 6.400000,
                    "commodityCostPrice": 5.207875,
                    "totalAmount": 6.400000,
                    "totalOriginalAmount": 7.000000000000,
                    "discountShare": 0.600000,
                    "discountAmount": 0.600000,
                    "billPrice": 6.400000,
                    "billAmount": 6.400000,
                    "isOnPromotion": False,
                    "fiveClass": "A010802002"
                },
                "pickInfoList": [
                    {
                        "erpCode": {"erpCode": "101428"},
                        "makeNo": {"makeNo": "A1002"},
                        "count": 1.000000
                    }
                ]
            }
        ],
        "baseUserInfo": {
            "userId": {"userId": "1826288749977672201"},
            "userName": "杨红平",
            "userCardNo": {"userCardNo": "200000606847"},
            "userMobile": {"userMobile": "17780664240"}
        },
        "baseOrganizationInfo": {
            "storeCode": "H712",
            "storeName": "[H712]一心堂成都二环路西三段药店",
            "companyCode": "1006",
            "companyName": "四川一心堂医药连锁有限公司(测试)",
            "storeDirectJoinTypeValue": "DIRECT_SALES"
        },
        "baseCashierDeskInfo": {
            "posCashierDeskNo": {"posCashierDeskNo": "101"},
            "cashier": "3785",
            "cashierName": "李姗",
            "picker": None,
            "pickerName": None,
            "shiftId": {"shiftId": "3"},
            "shiftDate": "2025-05-06 16:49:05"
        },
        "medInsSettle": None,
        "orderPromotionList": [],
        "orderCouponList": []
    }
    
    # 创建MQ消息
    return create_mock_mq_message(
        topic_name='TP_ORDER_OFFLINE_ORDER-DATA',
        tag_name='TAG_CREATED',
        message_body=order_data
    )

if __name__ == '__main__':
    # 生成模拟消息
    mock_message = mock_offline_order_message()
    
    # 打印消息
    print(json.dumps(mock_message, indent=2, ensure_ascii=False))
    
    # 也可以保存到文件
    with open('mock_offline_order_message.json', 'w', encoding='utf-8') as f:
        json.dump(mock_message, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Mock message generated and saved to mock_offline_order_message.json")
