2025-05-14 14:09:19,718 - handle_rocketmq_consumer.py - ERROR - [line:120] - 启动消费者...
2025-05-14 14:09:19,721 - handle_rocketmq_consumer.py - ERROR - [line:58] - 消费者初始化成功，消费组为：Test_Data_Check_Group
2025-05-14 14:09:19,723 - handle_rocketmq_consumer.py - ERROR - [line:60] - 消费者初始化成功，地址为：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 14:09:19,725 - handle_rocketmq_consumer.py - ERROR - [line:62] - 消费者初始化成功，线程数为：10
2025-05-14 14:09:19,728 - handle_rocketmq_consumer.py - ERROR - [line:68] - 消费者主题订阅成功：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:09:20,463 - handle_rocketmq_consumer.py - ERROR - [line:130] - 消费者已连接
2025-05-14 14:09:22,485 - handle_rocketmq_consumer.py - ERROR - [line:120] - 启动消费者...
2025-05-14 14:09:22,487 - handle_rocketmq_consumer.py - ERROR - [line:58] - 消费者初始化成功，消费组为：Test_Data_Check_Group
2025-05-14 14:09:22,488 - handle_rocketmq_consumer.py - ERROR - [line:60] - 消费者初始化成功，地址为：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 14:09:22,488 - handle_rocketmq_consumer.py - ERROR - [line:62] - 消费者初始化成功，线程数为：10
2025-05-14 14:09:22,490 - handle_rocketmq_consumer.py - ERROR - [line:68] - 消费者主题订阅成功：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:09:22,522 - handle_rocketmq_consumer.py - ERROR - [line:130] - 消费者已连接
2025-05-14 14:09:25,470 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:28,043 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:30,986 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:33,049 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:35,991 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:38,050 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:40,994 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:43,056 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:45,998 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:48,062 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:51,005 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:53,068 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:56,008 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:09:58,074 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:01,534 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:03,599 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:06,540 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:08,602 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:11,542 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:13,618 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:16,546 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:18,624 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:21,552 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:23,630 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:26,555 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:28,638 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:31,562 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:34,169 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:37,094 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:39,175 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:42,098 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:44,179 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:47,104 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:49,181 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:52,110 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:54,182 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:57,116 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:10:59,188 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:02,119 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:04,195 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:07,648 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:09,725 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:12,655 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:14,731 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:17,659 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:19,736 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:22,664 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:24,742 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:27,667 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:29,746 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:32,672 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:34,752 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:37,676 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:40,308 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:43,231 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:45,315 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:48,237 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:50,318 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:53,243 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:55,325 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:11:58,246 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:00,331 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:03,248 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:05,335 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:08,254 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:10,341 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:13,877 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:15,962 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:18,892 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:20,968 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:23,895 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:25,970 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:28,902 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:30,977 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:33,908 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:35,991 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:38,912 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:40,998 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:43,917 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:46,623 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:49,544 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:51,628 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:54,549 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:56,633 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:12:59,553 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:01,640 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:04,559 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:06,644 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:09,566 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:11,650 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:14,571 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:16,656 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:20,176 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:22,265 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:25,182 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:27,271 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:30,188 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:32,275 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:35,195 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:37,278 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:40,201 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:42,280 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:45,206 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:47,286 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:50,211 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:52,910 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:55,834 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:13:57,915 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:00,839 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:02,921 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:05,845 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:07,927 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:10,854 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:12,932 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:15,861 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:17,934 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:20,865 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:22,940 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:26,395 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:28,470 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:31,401 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:33,477 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:36,407 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:38,481 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:41,413 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:43,485 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:46,419 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:48,491 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:51,425 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:53,497 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:56,426 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:14:59,068 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:01,998 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:04,074 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:07,004 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:09,080 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:12,010 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:14,084 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:17,017 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:19,090 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:22,024 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:24,093 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:27,035 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:29,100 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:32,263 - handle_rocketmq_consumer.py - ERROR - [line:78] - 消息任务已经开始执行
2025-05-14 14:15:32,265 - handle_rocketmq_consumer.py - ERROR - [line:83] - 收到消息 [Topic=TP_ORDER_OFFLINE_ORDER-DATA],Tag=b'TAG_CREATED', ID=7F0000010007728938A94601F0A00920，queue_offset=2976，commit_log_offset=70075270165，prepared_transaction_offset=0
2025-05-14 14:15:32,585 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:34,648 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:37,590 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:39,654 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:42,596 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:44,657 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:47,602 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:49,660 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:52,607 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:54,666 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:57,612 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:15:59,707 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:02,617 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:05,280 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:08,189 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:10,284 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:13,194 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:15,290 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:18,200 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:20,296 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:23,205 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:25,301 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:28,209 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:30,307 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:33,214 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:35,312 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:38,771 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:40,870 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:43,776 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:45,876 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:48,783 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:50,881 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:53,786 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:55,886 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:16:58,792 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:00,892 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:03,796 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:05,899 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:08,800 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:11,464 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:14,362 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:16,470 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:19,365 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:21,475 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:24,368 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:26,480 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:29,374 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:31,485 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:34,377 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:36,490 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:39,383 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:41,496 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:44,943 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:47,059 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:49,949 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:52,064 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:54,956 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:57,066 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:17:59,959 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:18:02,073 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:18:04,965 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:18:07,078 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:18:09,971 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:20:05,497 - handle_rocketmq_consumer.py - ERROR - [line:120] - 启动消费者...
2025-05-14 14:20:05,499 - handle_rocketmq_consumer.py - ERROR - [line:58] - 消费者初始化成功，消费组为：Test_Data_Check_Group
2025-05-14 14:20:05,500 - handle_rocketmq_consumer.py - ERROR - [line:60] - 消费者初始化成功，地址为：10.100.65.145:8100;10.100.64.44:8100
2025-05-14 14:20:05,501 - handle_rocketmq_consumer.py - ERROR - [line:62] - 消费者初始化成功，线程数为：10
2025-05-14 14:20:05,502 - handle_rocketmq_consumer.py - ERROR - [line:68] - 消费者主题订阅成功：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:20:07,521 - handle_rocketmq_consumer.py - ERROR - [line:120] - 启动消费者...
2025-05-14 14:20:07,523 - handle_rocketmq_consumer.py - ERROR - [line:58] - 消费者初始化成功，消费组为：Test_Data_Check_Group
2025-05-14 14:20:07,524 - handle_rocketmq_consumer.py - ERROR - [line:60] - 消费者初始化成功，地址为：10.100.65.145:8100;10.100.64.44:8100
2025-05-14 14:20:07,525 - handle_rocketmq_consumer.py - ERROR - [line:62] - 消费者初始化成功，线程数为：10
2025-05-14 14:20:07,527 - handle_rocketmq_consumer.py - ERROR - [line:68] - 消费者主题订阅成功：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:20:35,515 - handle_rocketmq_consumer.py - ERROR - [line:130] - 消费者已连接
2025-05-14 14:20:37,541 - handle_rocketmq_consumer.py - ERROR - [line:130] - 消费者已连接
2025-05-14 14:20:40,522 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:20:42,547 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:20:45,528 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:20:47,552 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:20:50,534 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:20:52,559 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:20:55,540 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:20:57,562 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:00,546 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:03,148 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:06,131 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:08,150 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:11,139 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:13,157 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:16,147 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:18,162 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:21,153 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:23,167 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:26,183 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:28,174 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:31,186 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:33,175 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:36,805 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:38,794 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:41,807 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:43,796 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:46,813 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:48,798 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:51,839 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:53,804 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:56,848 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:21:58,811 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:01,854 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:03,818 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:06,856 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:09,481 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:12,524 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:14,487 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:17,528 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:19,492 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:22,532 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:24,498 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:27,538 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:29,502 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:32,540 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:34,506 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:37,543 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:39,509 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:43,262 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:45,228 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:48,269 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:50,234 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:53,275 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:55,240 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:22:58,280 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:00,246 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:03,286 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:05,286 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:08,292 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:10,292 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:13,298 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:15,986 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:18,992 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:20,989 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:23,998 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:25,996 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:29,004 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:31,001 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:34,006 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:36,008 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:39,012 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:41,014 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:44,019 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:23:46,021 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:27:12,255 - handle_rocketmq_consumer.py - ERROR - [line:120] - 启动消费者...
2025-05-14 14:27:12,257 - handle_rocketmq_consumer.py - ERROR - [line:58] - 消费者初始化成功，消费组为：Test_Data_Check_Group
2025-05-14 14:27:12,258 - handle_rocketmq_consumer.py - ERROR - [line:60] - 消费者初始化成功，地址为：10.100.65.145:8101;10.100.64.44:8101
2025-05-14 14:27:12,259 - handle_rocketmq_consumer.py - ERROR - [line:62] - 消费者初始化成功，线程数为：10
2025-05-14 14:27:12,260 - handle_rocketmq_consumer.py - ERROR - [line:68] - 消费者主题订阅成功：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:27:14,277 - handle_rocketmq_consumer.py - ERROR - [line:120] - 启动消费者...
2025-05-14 14:27:14,279 - handle_rocketmq_consumer.py - ERROR - [line:58] - 消费者初始化成功，消费组为：Test_Data_Check_Group
2025-05-14 14:27:14,279 - handle_rocketmq_consumer.py - ERROR - [line:60] - 消费者初始化成功，地址为：10.100.65.145:8101;10.100.64.44:8101
2025-05-14 14:27:14,280 - handle_rocketmq_consumer.py - ERROR - [line:62] - 消费者初始化成功，线程数为：10
2025-05-14 14:27:14,282 - handle_rocketmq_consumer.py - ERROR - [line:68] - 消费者主题订阅成功：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:27:24,835 - handle_rocketmq_consumer.py - ERROR - [line:130] - 消费者已连接
2025-05-14 14:27:29,842 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:27:34,846 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:27:39,852 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:08,972 - handle_rocketmq_consumer.py - ERROR - [line:120] - 启动消费者...
2025-05-14 14:29:08,973 - handle_rocketmq_consumer.py - ERROR - [line:58] - 消费者初始化成功，消费组为：Test_Data_Check_Group
2025-05-14 14:29:08,974 - handle_rocketmq_consumer.py - ERROR - [line:60] - 消费者初始化成功，地址为：10.4.3.241:9876;10.4.3.241:9876
2025-05-14 14:29:08,975 - handle_rocketmq_consumer.py - ERROR - [line:62] - 消费者初始化成功，线程数为：10
2025-05-14 14:29:08,977 - handle_rocketmq_consumer.py - ERROR - [line:68] - 消费者主题订阅成功：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:29:08,992 - handle_rocketmq_consumer.py - ERROR - [line:130] - 消费者已连接
2025-05-14 14:29:11,069 - handle_rocketmq_consumer.py - ERROR - [line:120] - 启动消费者...
2025-05-14 14:29:11,070 - handle_rocketmq_consumer.py - ERROR - [line:58] - 消费者初始化成功，消费组为：Test_Data_Check_Group
2025-05-14 14:29:11,071 - handle_rocketmq_consumer.py - ERROR - [line:60] - 消费者初始化成功，地址为：10.4.3.241:9876;10.4.3.241:9876
2025-05-14 14:29:11,072 - handle_rocketmq_consumer.py - ERROR - [line:62] - 消费者初始化成功，线程数为：10
2025-05-14 14:29:11,073 - handle_rocketmq_consumer.py - ERROR - [line:68] - 消费者主题订阅成功：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:29:11,086 - handle_rocketmq_consumer.py - ERROR - [line:130] - 消费者已连接
2025-05-14 14:29:13,997 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:16,088 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:19,004 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:21,090 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:24,010 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:26,096 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:29,011 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:31,102 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:34,681 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:36,775 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:39,685 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:41,778 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:44,688 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:46,784 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:49,693 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:51,788 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:54,695 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:56,794 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:29:59,701 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:01,800 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:04,702 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:06,801 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:10,362 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:12,461 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:15,368 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:17,467 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:20,370 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:22,472 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:25,376 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:27,478 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:30,381 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:32,483 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:35,383 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:37,489 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:41,038 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:43,143 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:46,041 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:48,148 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:51,046 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:53,154 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:56,048 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:30:58,161 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:31:01,049 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:31:03,167 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:31:06,051 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:31:08,170 - handle_rocketmq_consumer.py - ERROR - [line:135] - 消费者保持活跃
2025-05-14 14:37:40,550 - handle_rocketmq_consumer.py - ERROR - [line:139] - 启动消费者...
2025-05-14 14:37:40,552 - handle_rocketmq_consumer.py - ERROR - [line:59] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 14:37:40,553 - handle_rocketmq_consumer.py - ERROR - [line:67] - 设置NameServer地址：10.4.3.241:9876;10.4.3.241:9876
2025-05-14 14:37:40,554 - handle_rocketmq_consumer.py - ERROR - [line:71] - 设置消费线程数：10
2025-05-14 14:37:40,555 - handle_rocketmq_consumer.py - ERROR - [line:80] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:37:40,555 - handle_rocketmq_consumer.py - ERROR - [line:82] - 消费者初始化完成，等待连接验证
2025-05-14 14:37:40,569 - handle_rocketmq_consumer.py - ERROR - [line:154] - 消费者已成功连接到RocketMQ服务器
2025-05-14 14:37:42,685 - handle_rocketmq_consumer.py - ERROR - [line:139] - 启动消费者...
2025-05-14 14:37:42,686 - handle_rocketmq_consumer.py - ERROR - [line:59] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 14:37:42,687 - handle_rocketmq_consumer.py - ERROR - [line:67] - 设置NameServer地址：10.4.3.241:9876;10.4.3.241:9876
2025-05-14 14:37:42,688 - handle_rocketmq_consumer.py - ERROR - [line:71] - 设置消费线程数：10
2025-05-14 14:37:42,689 - handle_rocketmq_consumer.py - ERROR - [line:80] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:37:42,690 - handle_rocketmq_consumer.py - ERROR - [line:82] - 消费者初始化完成，等待连接验证
2025-05-14 14:37:42,703 - handle_rocketmq_consumer.py - ERROR - [line:154] - 消费者已成功连接到RocketMQ服务器
2025-05-14 14:37:45,571 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:37:47,709 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:37:50,577 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:37:53,455 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:37:56,322 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:37:58,461 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:38:01,328 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:38:03,467 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:38:06,334 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:38:08,473 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:38:11,340 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:38:13,479 - handle_rocketmq_consumer.py - ERROR - [line:162] - 消费者保持活跃
2025-05-14 14:47:52,240 - handle_rocketmq_consumer.py - ERROR - [line:73] - NameServer地址设置成功：**********:9876;**********:9876
2025-05-14 14:47:52,260 - handle_rocketmq_consumer.py - ERROR - [line:89] - 与RocketMQ服务器连接测试成功
2025-05-14 14:47:52,267 - handle_rocketmq_consumer.py - ERROR - [line:189] - 启动消费者...
2025-05-14 14:47:52,269 - handle_rocketmq_consumer.py - ERROR - [line:195] - 开始初始化消费者...
2025-05-14 14:47:52,270 - handle_rocketmq_consumer.py - ERROR - [line:109] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 14:47:52,271 - handle_rocketmq_consumer.py - ERROR - [line:117] - 设置NameServer地址：**********:9876;**********:9876
2025-05-14 14:47:52,273 - handle_rocketmq_consumer.py - ERROR - [line:121] - 设置消费线程数：10
2025-05-14 14:47:52,274 - handle_rocketmq_consumer.py - ERROR - [line:130] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:47:52,275 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者初始化完成，等待连接验证
2025-05-14 14:47:52,276 - handle_rocketmq_consumer.py - ERROR - [line:202] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 14:47:52,297 - handle_rocketmq_consumer.py - ERROR - [line:205] - 消费者已成功连接到RocketMQ服务器
2025-05-14 14:47:54,818 - handle_rocketmq_consumer.py - ERROR - [line:73] - NameServer地址设置成功：**********:9876;**********:9876
2025-05-14 14:47:54,832 - handle_rocketmq_consumer.py - ERROR - [line:89] - 与RocketMQ服务器连接测试成功
2025-05-14 14:47:54,835 - handle_rocketmq_consumer.py - ERROR - [line:189] - 启动消费者...
2025-05-14 14:47:54,836 - handle_rocketmq_consumer.py - ERROR - [line:195] - 开始初始化消费者...
2025-05-14 14:47:54,837 - handle_rocketmq_consumer.py - ERROR - [line:109] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 14:47:54,839 - handle_rocketmq_consumer.py - ERROR - [line:117] - 设置NameServer地址：**********:9876;**********:9876
2025-05-14 14:47:54,840 - handle_rocketmq_consumer.py - ERROR - [line:121] - 设置消费线程数：10
2025-05-14 14:47:54,841 - handle_rocketmq_consumer.py - ERROR - [line:130] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 14:47:54,843 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者初始化完成，等待连接验证
2025-05-14 14:47:54,844 - handle_rocketmq_consumer.py - ERROR - [line:202] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 14:47:54,857 - handle_rocketmq_consumer.py - ERROR - [line:205] - 消费者已成功连接到RocketMQ服务器
2025-05-14 14:47:57,299 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:47:59,858 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:02,305 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:04,864 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:07,309 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:09,869 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:12,313 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:14,870 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:17,317 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:19,876 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:22,321 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:25,679 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:28,078 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:30,687 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:33,083 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:35,693 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:38,088 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:40,700 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:43,094 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:45,705 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:48,098 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:50,711 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:53,102 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:55,719 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:48:58,867 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:01,478 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:03,874 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:06,481 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:08,879 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:11,483 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:13,884 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:16,490 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:18,889 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:21,492 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:23,894 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:26,497 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:28,904 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:32,245 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:49:34,653 - handle_rocketmq_consumer.py - ERROR - [line:213] - 消费者保持活跃
2025-05-14 14:58:02,183 - handle_rocketmq_consumer.py - ERROR - [line:73] - NameServer地址设置成功：**********:9876;**********:9876
2025-05-14 14:58:02,185 - handle_rocketmq_consumer.py - ERROR - [line:88] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 14:58:02,201 - handle_rocketmq_consumer.py - ERROR - [line:98] - 验证连接状态，尝试 1/3
2025-05-14 14:58:02,202 - handle_rocketmq_consumer.py - ERROR - [line:126] - RocketMQ连接测试失败: 'PushConsumer' object has no attribute 'is_running'
2025-05-14 14:58:04,380 - handle_rocketmq_consumer.py - ERROR - [line:73] - NameServer地址设置成功：**********:9876;**********:9876
2025-05-14 14:58:04,381 - handle_rocketmq_consumer.py - ERROR - [line:88] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 14:58:04,393 - handle_rocketmq_consumer.py - ERROR - [line:98] - 验证连接状态，尝试 1/3
2025-05-14 14:58:04,396 - handle_rocketmq_consumer.py - ERROR - [line:126] - RocketMQ连接测试失败: 'PushConsumer' object has no attribute 'is_running'
2025-05-14 14:59:57,955 - handle_rocketmq_consumer.py - ERROR - [line:73] - NameServer地址设置成功：**********:9876;**********:9876
2025-05-14 14:59:57,957 - handle_rocketmq_consumer.py - ERROR - [line:88] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 14:59:57,973 - handle_rocketmq_consumer.py - ERROR - [line:98] - 验证连接状态，尝试 1/3
2025-05-14 14:59:59,975 - handle_rocketmq_consumer.py - ERROR - [line:112] - 连接验证失败: 'PushConsumer' object has no attribute 'get_subscription_for_topic'
2025-05-14 14:59:59,976 - handle_rocketmq_consumer.py - ERROR - [line:98] - 验证连接状态，尝试 2/3
2025-05-14 15:00:01,977 - handle_rocketmq_consumer.py - ERROR - [line:112] - 连接验证失败: 'PushConsumer' object has no attribute 'get_subscription_for_topic'
2025-05-14 15:00:01,979 - handle_rocketmq_consumer.py - ERROR - [line:98] - 验证连接状态，尝试 3/3
2025-05-14 15:00:04,765 - handle_rocketmq_consumer.py - ERROR - [line:112] - 连接验证失败: 'PushConsumer' object has no attribute 'get_subscription_for_topic'
2025-05-14 15:00:04,766 - handle_rocketmq_consumer.py - ERROR - [line:124] - RocketMQ连接测试失败: 连接验证失败: 'PushConsumer' object has no attribute 'get_subscription_for_topic'
2025-05-14 15:09:15,564 - handle_rocketmq_consumer.py - ERROR - [line:90] - NameServer地址设置成功：**********:9876;**********:9876
2025-05-14 15:09:15,566 - handle_rocketmq_consumer.py - ERROR - [line:105] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:09:15,591 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 1/3
2025-05-14 15:09:17,594 - handle_rocketmq_consumer.py - ERROR - [line:127] - 与RocketMQ服务器连接测试可能成功，但无法完全验证
2025-05-14 15:09:17,597 - handle_rocketmq_consumer.py - ERROR - [line:270] - 启动消费者...
2025-05-14 15:09:17,598 - handle_rocketmq_consumer.py - ERROR - [line:276] - 开始初始化消费者...
2025-05-14 15:09:17,600 - handle_rocketmq_consumer.py - ERROR - [line:161] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 15:09:17,601 - handle_rocketmq_consumer.py - ERROR - [line:169] - 设置NameServer地址：**********:9876;**********:9876
2025-05-14 15:09:17,601 - handle_rocketmq_consumer.py - ERROR - [line:173] - 设置消费线程数：10
2025-05-14 15:09:17,602 - handle_rocketmq_consumer.py - ERROR - [line:182] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 15:09:17,603 - handle_rocketmq_consumer.py - ERROR - [line:184] - 消费者初始化完成，等待连接验证
2025-05-14 15:09:17,604 - handle_rocketmq_consumer.py - ERROR - [line:283] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:09:17,615 - handle_rocketmq_consumer.py - ERROR - [line:293] - 验证连接状态，尝试 1/3
2025-05-14 15:09:19,617 - handle_rocketmq_consumer.py - ERROR - [line:306] - 消费者已启动，并可能成功连接到RocketMQ服务器
2025-05-14 15:09:19,732 - handle_rocketmq_consumer.py - ERROR - [line:90] - NameServer地址设置成功：**********:9876;**********:9876
2025-05-14 15:09:19,734 - handle_rocketmq_consumer.py - ERROR - [line:105] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:09:19,748 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 1/3
2025-05-14 15:09:21,750 - handle_rocketmq_consumer.py - ERROR - [line:127] - 与RocketMQ服务器连接测试可能成功，但无法完全验证
2025-05-14 15:09:21,753 - handle_rocketmq_consumer.py - ERROR - [line:270] - 启动消费者...
2025-05-14 15:09:21,754 - handle_rocketmq_consumer.py - ERROR - [line:276] - 开始初始化消费者...
2025-05-14 15:09:21,755 - handle_rocketmq_consumer.py - ERROR - [line:161] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 15:09:21,756 - handle_rocketmq_consumer.py - ERROR - [line:169] - 设置NameServer地址：**********:9876;**********:9876
2025-05-14 15:09:21,757 - handle_rocketmq_consumer.py - ERROR - [line:173] - 设置消费线程数：10
2025-05-14 15:09:21,758 - handle_rocketmq_consumer.py - ERROR - [line:182] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 15:09:21,759 - handle_rocketmq_consumer.py - ERROR - [line:184] - 消费者初始化完成，等待连接验证
2025-05-14 15:09:21,759 - handle_rocketmq_consumer.py - ERROR - [line:283] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:09:21,774 - handle_rocketmq_consumer.py - ERROR - [line:293] - 验证连接状态，尝试 1/3
2025-05-14 15:09:23,777 - handle_rocketmq_consumer.py - ERROR - [line:306] - 消费者已启动，并可能成功连接到RocketMQ服务器
2025-05-14 15:09:24,619 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:29,620 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:30,461 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:34,622 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:35,464 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:39,627 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:40,468 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:44,633 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:45,474 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:49,639 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:50,476 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:50,478 - handle_rocketmq_consumer.py - ERROR - [line:356] - 等待连接验证，已等待5秒
2025-05-14 15:09:54,641 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:54,642 - handle_rocketmq_consumer.py - ERROR - [line:356] - 等待连接验证，已等待5秒
2025-05-14 15:09:55,481 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:09:59,646 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:00,487 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:05,486 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:06,283 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:10,492 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:11,288 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:15,498 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:16,294 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:20,504 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:21,301 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:21,302 - handle_rocketmq_consumer.py - ERROR - [line:356] - 等待连接验证，已等待5秒
2025-05-14 15:10:25,509 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:25,510 - handle_rocketmq_consumer.py - ERROR - [line:356] - 等待连接验证，已等待5秒
2025-05-14 15:10:26,307 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:30,516 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:31,313 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:36,325 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:37,123 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:41,329 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:42,130 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:46,330 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:47,136 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:51,336 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:52,142 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:52,143 - handle_rocketmq_consumer.py - ERROR - [line:356] - 等待连接验证，已等待5秒
2025-05-14 15:10:56,342 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:10:56,345 - handle_rocketmq_consumer.py - ERROR - [line:356] - 等待连接验证，已等待5秒
2025-05-14 15:10:57,148 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:11:01,348 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:11:02,154 - handle_rocketmq_consumer.py - ERROR - [line:323] - 消费者保持活跃
2025-05-14 15:25:50,345 - handle_rocketmq_consumer.py - ERROR - [line:90] - NameServer地址设置成功：**********:9876;**********:9876
2025-05-14 15:25:50,347 - handle_rocketmq_consumer.py - ERROR - [line:105] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:25:50,361 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 1/3
2025-05-14 15:25:52,363 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:25:52,364 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:25:52,365 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:25:52,367 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 2/3
2025-05-14 15:25:54,370 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:25:54,371 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:25:54,372 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:25:54,373 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 3/3
2025-05-14 15:25:56,375 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:25:56,376 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:25:56,377 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:25:56,378 - handle_rocketmq_consumer.py - ERROR - [line:155] - RocketMQ连接测试失败: 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:25:58,542 - handle_rocketmq_consumer.py - ERROR - [line:90] - NameServer地址设置成功：**********:9876;**********:9876
2025-05-14 15:25:58,543 - handle_rocketmq_consumer.py - ERROR - [line:105] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:25:58,556 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 1/3
2025-05-14 15:26:00,559 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:26:00,561 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:26:00,562 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:26:00,563 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 2/3
2025-05-14 15:26:02,567 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:26:02,568 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:26:02,569 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:26:02,569 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 3/3
2025-05-14 15:26:04,571 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:26:04,573 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:26:04,575 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:26:04,577 - handle_rocketmq_consumer.py - ERROR - [line:155] - RocketMQ连接测试失败: 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:28:30,420 - handle_rocketmq_consumer.py - ERROR - [line:90] - NameServer地址设置成功：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 15:28:30,421 - handle_rocketmq_consumer.py - ERROR - [line:105] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:28:30,589 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 1/3
2025-05-14 15:28:32,592 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:28:32,594 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:28:32,595 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:28:32,596 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 2/3
2025-05-14 15:28:34,599 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:28:34,601 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:28:34,601 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:28:34,602 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 3/3
2025-05-14 15:28:36,605 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:28:36,607 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:28:36,608 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:28:36,608 - handle_rocketmq_consumer.py - ERROR - [line:155] - RocketMQ连接测试失败: 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:28:38,964 - handle_rocketmq_consumer.py - ERROR - [line:90] - NameServer地址设置成功：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 15:28:38,966 - handle_rocketmq_consumer.py - ERROR - [line:105] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:28:39,102 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 1/3
2025-05-14 15:28:41,105 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:28:41,106 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:28:41,106 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:28:41,109 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 2/3
2025-05-14 15:28:43,111 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:28:43,113 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:28:43,114 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:28:43,114 - handle_rocketmq_consumer.py - ERROR - [line:115] - 验证连接状态，尝试 3/3
2025-05-14 15:28:45,117 - handle_rocketmq_consumer.py - ERROR - [line:132] - 消费者实例没有offset_table属性，尝试其他验证方法
2025-05-14 15:28:45,118 - handle_rocketmq_consumer.py - ERROR - [line:140] - 消费者实例没有有效的运行状态属性，连接可能失败
2025-05-14 15:28:45,119 - handle_rocketmq_consumer.py - ERROR - [line:143] - 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:28:45,119 - handle_rocketmq_consumer.py - ERROR - [line:155] - RocketMQ连接测试失败: 连接验证失败: 消费者实例没有有效的运行状态属性
2025-05-14 15:31:31,385 - handle_rocketmq_consumer.py - ERROR - [line:328] - 启动消费者...
2025-05-14 15:31:39,318 - handle_rocketmq_consumer.py - ERROR - [line:334] - 开始初始化消费者...
2025-05-14 15:31:48,468 - handle_rocketmq_consumer.py - ERROR - [line:175] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 15:31:48,469 - handle_rocketmq_consumer.py - ERROR - [line:183] - 设置NameServer地址：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 15:31:48,471 - handle_rocketmq_consumer.py - ERROR - [line:187] - 设置消费线程数：10
2025-05-14 15:31:48,473 - handle_rocketmq_consumer.py - ERROR - [line:196] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 15:31:48,474 - handle_rocketmq_consumer.py - ERROR - [line:198] - 消费者初始化完成，等待连接验证
2025-05-14 15:31:51,270 - handle_rocketmq_consumer.py - ERROR - [line:341] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:32:23,081 - handle_rocketmq_consumer.py - ERROR - [line:351] - 验证连接状态，尝试 1/3
2025-05-14 15:32:34,136 - handle_rocketmq_consumer.py - ERROR - [line:437] - 停止消费者...
2025-05-14 15:32:34,162 - handle_rocketmq_consumer.py - ERROR - [line:443] - 消费者已停止
2025-05-14 15:38:56,907 - handle_rocketmq_consumer.py - ERROR - [line:321] - 启动消费者...
2025-05-14 15:39:04,512 - handle_rocketmq_consumer.py - ERROR - [line:327] - 开始初始化消费者...
2025-05-14 15:39:06,003 - handle_rocketmq_consumer.py - ERROR - [line:176] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 15:39:06,004 - handle_rocketmq_consumer.py - ERROR - [line:184] - 设置NameServer地址：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 15:39:06,005 - handle_rocketmq_consumer.py - ERROR - [line:188] - 设置消费线程数：10
2025-05-14 15:39:06,007 - handle_rocketmq_consumer.py - ERROR - [line:197] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 15:39:06,008 - handle_rocketmq_consumer.py - ERROR - [line:199] - 消费者初始化完成，等待连接验证
2025-05-14 15:39:14,468 - handle_rocketmq_consumer.py - ERROR - [line:334] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:40:12,569 - handle_rocketmq_consumer.py - ERROR - [line:342] - 消费者启动耗时: 1.02 秒
2025-05-14 15:40:17,373 - handle_rocketmq_consumer.py - ERROR - [line:350] - 等待消费者连接建立...
2025-05-14 15:40:23,930 - handle_rocketmq_consumer.py - ERROR - [line:354] - 消费者已启动，但无法完全验证连接状态
2025-05-14 15:40:46,792 - handle_rocketmq_consumer.py - ERROR - [line:355] - 如果地址错误，可能会在日志中看到连接失败的错误信息
2025-05-14 15:40:47,441 - handle_rocketmq_consumer.py - ERROR - [line:356] - 当前配置的NameServer地址: 10.4.3.242:9876;10.4.3.243:9876
2025-05-14 15:40:56,049 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:41:04,606 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:41:19,362 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:41:27,826 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:42:13,490 - handle_rocketmq_consumer.py - ERROR - [line:413] - 停止消费者...
2025-05-14 15:42:13,515 - handle_rocketmq_consumer.py - ERROR - [line:419] - 消费者已停止
2025-05-14 15:42:40,937 - handle_rocketmq_consumer.py - ERROR - [line:321] - 启动消费者...
2025-05-14 15:42:40,938 - handle_rocketmq_consumer.py - ERROR - [line:327] - 开始初始化消费者...
2025-05-14 15:42:40,940 - handle_rocketmq_consumer.py - ERROR - [line:176] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 15:42:40,941 - handle_rocketmq_consumer.py - ERROR - [line:184] - 设置NameServer地址：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 15:42:40,942 - handle_rocketmq_consumer.py - ERROR - [line:188] - 设置消费线程数：10
2025-05-14 15:42:40,944 - handle_rocketmq_consumer.py - ERROR - [line:197] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 15:42:40,945 - handle_rocketmq_consumer.py - ERROR - [line:199] - 消费者初始化完成，等待连接验证
2025-05-14 15:42:40,946 - handle_rocketmq_consumer.py - ERROR - [line:334] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:42:41,110 - handle_rocketmq_consumer.py - ERROR - [line:342] - 消费者启动耗时: 0.00 秒
2025-05-14 15:42:41,112 - handle_rocketmq_consumer.py - ERROR - [line:346] - 消费者启动过快，可能是因为NameServer地址错误
2025-05-14 15:42:41,114 - handle_rocketmq_consumer.py - ERROR - [line:358] - 消费者连接失败: 消费者启动过快，可能是因为NameServer地址错误
2025-05-14 15:42:41,115 - handle_rocketmq_consumer.py - ERROR - [line:402] - 连接异常: 消费者启动过快，可能是因为NameServer地址错误
2025-05-14 15:42:41,116 - handle_rocketmq_consumer.py - ERROR - [line:276] - 尝试重连 (1/10)
2025-05-14 15:42:41,138 - handle_rocketmq_consumer.py - ERROR - [line:176] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 15:42:41,139 - handle_rocketmq_consumer.py - ERROR - [line:184] - 设置NameServer地址：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 15:42:41,140 - handle_rocketmq_consumer.py - ERROR - [line:188] - 设置消费线程数：10
2025-05-14 15:42:41,141 - handle_rocketmq_consumer.py - ERROR - [line:197] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 15:42:41,143 - handle_rocketmq_consumer.py - ERROR - [line:199] - 消费者初始化完成，等待连接验证
2025-05-14 15:42:41,275 - handle_rocketmq_consumer.py - ERROR - [line:287] - 消费者重连启动耗时: 0.00 秒
2025-05-14 15:42:41,276 - handle_rocketmq_consumer.py - ERROR - [line:291] - 消费者重连启动过快，可能是因为NameServer地址错误
2025-05-14 15:42:41,278 - handle_rocketmq_consumer.py - ERROR - [line:404] - 达到最大重试次数，停止重连
2025-05-14 15:42:41,279 - handle_rocketmq_consumer.py - ERROR - [line:413] - 停止消费者...
2025-05-14 15:42:41,301 - handle_rocketmq_consumer.py - ERROR - [line:419] - 消费者已停止
2025-05-14 15:44:48,110 - handle_rocketmq_consumer.py - ERROR - [line:321] - 启动消费者...
2025-05-14 15:44:48,112 - handle_rocketmq_consumer.py - ERROR - [line:327] - 开始初始化消费者...
2025-05-14 15:44:48,114 - handle_rocketmq_consumer.py - ERROR - [line:176] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 15:44:48,115 - handle_rocketmq_consumer.py - ERROR - [line:184] - 设置NameServer地址：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 15:44:48,117 - handle_rocketmq_consumer.py - ERROR - [line:188] - 设置消费线程数：10
2025-05-14 15:44:48,118 - handle_rocketmq_consumer.py - ERROR - [line:197] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 15:44:48,119 - handle_rocketmq_consumer.py - ERROR - [line:199] - 消费者初始化完成，等待连接验证
2025-05-14 15:44:48,120 - handle_rocketmq_consumer.py - ERROR - [line:334] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:44:48,151 - handle_rocketmq_consumer.py - ERROR - [line:342] - 消费者启动耗时: 0.00 秒
2025-05-14 15:44:48,152 - handle_rocketmq_consumer.py - ERROR - [line:350] - 等待消费者连接建立...
2025-05-14 15:44:51,156 - handle_rocketmq_consumer.py - ERROR - [line:354] - 消费者已启动，但无法完全验证连接状态
2025-05-14 15:44:51,157 - handle_rocketmq_consumer.py - ERROR - [line:355] - 如果地址错误，可能会在日志中看到连接失败的错误信息
2025-05-14 15:44:51,158 - handle_rocketmq_consumer.py - ERROR - [line:356] - 当前配置的NameServer地址: 10.4.3.242:9876;10.4.3.243:9876
2025-05-14 15:44:56,164 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:02,013 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:07,019 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:12,026 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:17,031 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:22,038 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:22,040 - handle_rocketmq_consumer.py - ERROR - [line:399] - 等待连接验证，已等待5秒
2025-05-14 15:45:27,046 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:32,051 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:37,958 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:42,962 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:47,968 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:52,973 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:45:52,974 - handle_rocketmq_consumer.py - ERROR - [line:399] - 等待连接验证，已等待5秒
2025-05-14 15:45:57,980 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:02,985 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:08,885 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:13,886 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:18,893 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:23,896 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:23,898 - handle_rocketmq_consumer.py - ERROR - [line:399] - 等待连接验证，已等待5秒
2025-05-14 15:46:28,904 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:33,910 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:38,916 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:44,835 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:46,264 - handle_rocketmq_consumer.py - ERROR - [line:214] - 消息任务已经开始执行
2025-05-14 15:46:46,275 - handle_rocketmq_consumer.py - ERROR - [line:219] - 收到消息 [Topic=TP_ORDER_OFFLINE_ORDER-DATA],Tag=b'TAG_CREATED', ID=7F0000010007728938A9465577C40929，queue_offset=2986，commit_log_offset=750623645211，prepared_transaction_offset=0
2025-05-14 15:46:49,840 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:54,862 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:46:54,864 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:46:59,868 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:47:04,874 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:47:09,881 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:47:15,784 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:47:20,790 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:47:25,796 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:47:25,797 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:47:30,803 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:47:35,809 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:47:40,153 - handle_rocketmq_consumer.py - ERROR - [line:413] - 停止消费者...
2025-05-14 15:47:40,211 - handle_rocketmq_consumer.py - ERROR - [line:419] - 消费者已停止
2025-05-14 15:47:47,457 - handle_rocketmq_consumer.py - ERROR - [line:321] - 启动消费者...
2025-05-14 15:47:47,458 - handle_rocketmq_consumer.py - ERROR - [line:327] - 开始初始化消费者...
2025-05-14 15:47:47,459 - handle_rocketmq_consumer.py - ERROR - [line:176] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 15:47:47,461 - handle_rocketmq_consumer.py - ERROR - [line:184] - 设置NameServer地址：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 15:47:47,462 - handle_rocketmq_consumer.py - ERROR - [line:188] - 设置消费线程数：10
2025-05-14 15:47:47,463 - handle_rocketmq_consumer.py - ERROR - [line:197] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 15:47:47,464 - handle_rocketmq_consumer.py - ERROR - [line:199] - 消费者初始化完成，等待连接验证
2025-05-14 15:47:47,465 - handle_rocketmq_consumer.py - ERROR - [line:334] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 15:47:47,498 - handle_rocketmq_consumer.py - ERROR - [line:342] - 消费者启动耗时: 0.00 秒
2025-05-14 15:47:47,499 - handle_rocketmq_consumer.py - ERROR - [line:350] - 等待消费者连接建立...
2025-05-14 15:47:51,409 - handle_rocketmq_consumer.py - ERROR - [line:354] - 消费者已启动，但无法完全验证连接状态
2025-05-14 15:47:51,410 - handle_rocketmq_consumer.py - ERROR - [line:355] - 如果地址错误，可能会在日志中看到连接失败的错误信息
2025-05-14 15:47:51,412 - handle_rocketmq_consumer.py - ERROR - [line:356] - 当前配置的NameServer地址: 10.4.3.242:9876;10.4.3.243:9876
2025-05-14 15:47:56,417 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:01,423 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:06,430 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:11,436 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:16,444 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:22,361 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:22,362 - handle_rocketmq_consumer.py - ERROR - [line:399] - 等待连接验证，已等待5秒
2025-05-14 15:48:27,368 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:32,375 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:36,713 - handle_rocketmq_consumer.py - ERROR - [line:214] - 消息任务已经开始执行
2025-05-14 15:48:36,715 - handle_rocketmq_consumer.py - ERROR - [line:219] - 收到消息 [Topic=TP_ORDER_OFFLINE_ORDER-DATA],Tag=b'TAG_CREATED', ID=7F0000010007728938A94657285C092A，queue_offset=2979，commit_log_offset=750626463573，prepared_transaction_offset=0
2025-05-14 15:48:37,380 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:42,385 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:47,392 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:52,397 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:48:52,399 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:48:58,308 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:03,311 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:08,314 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:13,320 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:18,326 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:23,330 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:23,332 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:49:29,248 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:34,254 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:39,259 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:44,262 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:49,268 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:54,275 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:49:54,276 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:49:59,279 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:05,176 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:10,180 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:15,186 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:20,193 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:25,194 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:25,195 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:50:30,197 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:36,126 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:41,133 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:46,140 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:51,146 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:56,151 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:50:56,153 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:51:01,159 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:06,165 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:12,089 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:17,092 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:22,095 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:27,102 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:27,103 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:51:32,108 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:37,114 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:43,089 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:48,092 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:53,098 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:58,104 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:51:58,105 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:52:03,111 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:52:08,115 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:52:13,117 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:52:19,098 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:52:24,104 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:52:29,110 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:52:29,112 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:52:34,117 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:52:39,124 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:52:44,128 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:52:50,058 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:52:55,064 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:00,070 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:00,072 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:53:05,078 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:10,083 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:15,089 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:20,096 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:26,178 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:31,185 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:31,186 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:53:36,193 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:41,197 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:46,203 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:51,209 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:53:57,109 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:02,117 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:02,118 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:54:07,123 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:12,125 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:17,130 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:22,136 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:27,142 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:33,073 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:33,075 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:54:38,081 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:43,087 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:48,093 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:53,100 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:54:58,107 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:04,063 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:04,065 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:55:09,071 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:14,077 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:19,084 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:24,089 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:29,092 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:34,098 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:34,099 - handle_rocketmq_consumer.py - ERROR - [line:377] - 连接状态正常，已收到消息
2025-05-14 15:55:40,047 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:45,053 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:50,060 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:55,067 - handle_rocketmq_consumer.py - ERROR - [line:366] - 消费者保持活跃
2025-05-14 15:55:57,108 - handle_rocketmq_consumer.py - ERROR - [line:413] - 停止消费者...
2025-05-14 15:55:57,137 - handle_rocketmq_consumer.py - ERROR - [line:419] - 消费者已停止
2025-05-14 16:00:30,575 - handle_rocketmq_consumer.py - ERROR - [line:323] - 启动消费者...
2025-05-14 16:00:30,576 - handle_rocketmq_consumer.py - ERROR - [line:329] - 开始初始化消费者...
2025-05-14 16:00:30,578 - handle_rocketmq_consumer.py - ERROR - [line:176] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 16:00:30,579 - handle_rocketmq_consumer.py - ERROR - [line:184] - 设置NameServer地址：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 16:00:30,579 - handle_rocketmq_consumer.py - ERROR - [line:188] - 设置消费线程数：10
2025-05-14 16:00:30,580 - handle_rocketmq_consumer.py - ERROR - [line:244] - 消费者实例的属性: ['__class__', '__del__', '__delattr__', '__dict__', '__dir__', '__doc__', '__enter__', '__eq__', '__exit__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_callback_refs', '_handle', '_orderly', '_register_callback', '_unregister_callback', 'set_group', 'set_instance_name', 'set_message_batch_max_size', 'set_message_model', 'set_namesrv_addr', 'set_namesrv_domain', 'set_session_credentials', 'set_thread_count', 'shutdown', 'start', 'subscribe']
2025-05-14 16:00:30,581 - handle_rocketmq_consumer.py - ERROR - [line:247] - 消费者实例类型: <class 'rocketmq.client.PushConsumer'>
2025-05-14 16:00:30,582 - handle_rocketmq_consumer.py - ERROR - [line:251] - 消费者实例内存地址: 140663637964496
2025-05-14 16:00:30,583 - handle_rocketmq_consumer.py - ERROR - [line:255] - 配置的NameServer地址: 10.4.3.242:9876;10.4.3.243:9876
2025-05-14 16:00:30,583 - handle_rocketmq_consumer.py - ERROR - [line:259] - 没有直接的方法来验证连接状态，假设连接可能成功
2025-05-14 16:00:30,584 - handle_rocketmq_consumer.py - ERROR - [line:199] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 16:00:30,585 - handle_rocketmq_consumer.py - ERROR - [line:201] - 消费者初始化完成，等待连接验证
2025-05-14 16:00:30,586 - handle_rocketmq_consumer.py - ERROR - [line:336] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 16:00:30,746 - handle_rocketmq_consumer.py - ERROR - [line:344] - 消费者启动耗时: 0.00 秒
2025-05-14 16:00:30,748 - handle_rocketmq_consumer.py - ERROR - [line:352] - 等待消费者连接建立...
2025-05-14 16:00:33,752 - handle_rocketmq_consumer.py - ERROR - [line:356] - 消费者已启动，但无法完全验证连接状态
2025-05-14 16:00:33,753 - handle_rocketmq_consumer.py - ERROR - [line:357] - 如果地址错误，可能会在日志中看到连接失败的错误信息
2025-05-14 16:00:33,755 - handle_rocketmq_consumer.py - ERROR - [line:358] - 当前配置的NameServer地址: 10.4.3.242:9876;10.4.3.243:9876
2025-05-14 16:00:39,726 - handle_rocketmq_consumer.py - ERROR - [line:368] - 消费者保持活跃
2025-05-14 16:00:44,732 - handle_rocketmq_consumer.py - ERROR - [line:368] - 消费者保持活跃
2025-05-14 16:00:49,738 - handle_rocketmq_consumer.py - ERROR - [line:368] - 消费者保持活跃
2025-05-14 16:00:54,740 - handle_rocketmq_consumer.py - ERROR - [line:368] - 消费者保持活跃
2025-05-14 16:00:59,747 - handle_rocketmq_consumer.py - ERROR - [line:368] - 消费者保持活跃
2025-05-14 16:01:04,753 - handle_rocketmq_consumer.py - ERROR - [line:368] - 消费者保持活跃
2025-05-14 16:01:04,755 - handle_rocketmq_consumer.py - ERROR - [line:401] - 等待连接验证，已等待5秒
2025-05-14 16:01:10,754 - handle_rocketmq_consumer.py - ERROR - [line:368] - 消费者保持活跃
2025-05-14 16:01:15,765 - handle_rocketmq_consumer.py - ERROR - [line:368] - 消费者保持活跃
2025-05-14 16:01:20,775 - handle_rocketmq_consumer.py - ERROR - [line:368] - 消费者保持活跃
2025-05-14 16:01:21,351 - handle_rocketmq_consumer.py - ERROR - [line:415] - 停止消费者...
2025-05-14 16:01:21,390 - handle_rocketmq_consumer.py - ERROR - [line:421] - 消费者已停止
2025-05-14 16:01:26,070 - handle_rocketmq_consumer.py - ERROR - [line:323] - 启动消费者...
2025-05-14 16:01:26,071 - handle_rocketmq_consumer.py - ERROR - [line:329] - 开始初始化消费者...
2025-05-14 16:01:26,073 - handle_rocketmq_consumer.py - ERROR - [line:176] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 16:01:26,074 - handle_rocketmq_consumer.py - ERROR - [line:184] - 设置NameServer地址：10.4.3.242:9879;10.4.3.243:9879
2025-05-14 16:01:26,075 - handle_rocketmq_consumer.py - ERROR - [line:188] - 设置消费线程数：10
2025-05-14 16:01:26,076 - handle_rocketmq_consumer.py - ERROR - [line:244] - 消费者实例的属性: ['__class__', '__del__', '__delattr__', '__dict__', '__dir__', '__doc__', '__enter__', '__eq__', '__exit__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_callback_refs', '_handle', '_orderly', '_register_callback', '_unregister_callback', 'set_group', 'set_instance_name', 'set_message_batch_max_size', 'set_message_model', 'set_namesrv_addr', 'set_namesrv_domain', 'set_session_credentials', 'set_thread_count', 'shutdown', 'start', 'subscribe']
2025-05-14 16:01:26,077 - handle_rocketmq_consumer.py - ERROR - [line:247] - 消费者实例类型: <class 'rocketmq.client.PushConsumer'>
2025-05-14 16:01:26,081 - handle_rocketmq_consumer.py - ERROR - [line:251] - 消费者实例内存地址: 140609658614688
2025-05-14 16:01:26,082 - handle_rocketmq_consumer.py - ERROR - [line:255] - 配置的NameServer地址: 10.4.3.242:9879;10.4.3.243:9879
2025-05-14 16:01:26,083 - handle_rocketmq_consumer.py - ERROR - [line:259] - 没有直接的方法来验证连接状态，假设连接可能成功
2025-05-14 16:01:26,084 - handle_rocketmq_consumer.py - ERROR - [line:199] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 16:01:26,085 - handle_rocketmq_consumer.py - ERROR - [line:201] - 消费者初始化完成，等待连接验证
2025-05-14 16:01:26,086 - handle_rocketmq_consumer.py - ERROR - [line:336] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 16:01:26,103 - handle_rocketmq_consumer.py - ERROR - [line:344] - 消费者启动耗时: 0.00 秒
2025-05-14 16:01:26,104 - handle_rocketmq_consumer.py - ERROR - [line:352] - 等待消费者连接建立...
2025-05-14 16:01:29,108 - handle_rocketmq_consumer.py - ERROR - [line:356] - 消费者已启动，但无法完全验证连接状态
2025-05-14 16:01:29,109 - handle_rocketmq_consumer.py - ERROR - [line:357] - 如果地址错误，可能会在日志中看到连接失败的错误信息
2025-05-14 16:01:29,110 - handle_rocketmq_consumer.py - ERROR - [line:358] - 当前配置的NameServer地址: 10.4.3.242:9879;10.4.3.243:9879
2025-05-14 16:01:33,960 - handle_rocketmq_consumer.py - ERROR - [line:415] - 停止消费者...
2025-05-14 16:01:33,977 - handle_rocketmq_consumer.py - ERROR - [line:421] - 消费者已停止
2025-05-14 16:05:05,262 - handle_rocketmq_consumer.py - ERROR - [line:324] - 启动消费者...
2025-05-14 16:05:05,264 - handle_rocketmq_consumer.py - ERROR - [line:330] - 开始初始化消费者...
2025-05-14 16:05:05,266 - handle_rocketmq_consumer.py - ERROR - [line:176] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 16:05:05,268 - handle_rocketmq_consumer.py - ERROR - [line:184] - 设置NameServer地址：10.4.3.242:9879;10.4.3.243:9879
2025-05-14 16:05:05,269 - handle_rocketmq_consumer.py - ERROR - [line:188] - 设置消费线程数：10
2025-05-14 16:05:05,271 - handle_rocketmq_consumer.py - ERROR - [line:245] - 消费者实例的属性: ['__class__', '__del__', '__delattr__', '__dict__', '__dir__', '__doc__', '__enter__', '__eq__', '__exit__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_callback_refs', '_handle', '_orderly', '_register_callback', '_unregister_callback', 'set_group', 'set_instance_name', 'set_message_batch_max_size', 'set_message_model', 'set_namesrv_addr', 'set_namesrv_domain', 'set_session_credentials', 'set_thread_count', 'shutdown', 'start', 'subscribe']
2025-05-14 16:05:05,273 - handle_rocketmq_consumer.py - ERROR - [line:248] - 消费者实例类型: <class 'rocketmq.client.PushConsumer'>
2025-05-14 16:05:05,275 - handle_rocketmq_consumer.py - ERROR - [line:252] - 消费者实例内存地址: 139937152316464
2025-05-14 16:05:05,276 - handle_rocketmq_consumer.py - ERROR - [line:256] - 配置的NameServer地址: 10.4.3.242:9879;10.4.3.243:9879
2025-05-14 16:05:05,277 - handle_rocketmq_consumer.py - ERROR - [line:260] - 没有直接的方法来验证连接状态，假设连接可能成功
2025-05-14 16:05:05,279 - handle_rocketmq_consumer.py - ERROR - [line:199] - None
2025-05-14 16:05:05,280 - handle_rocketmq_consumer.py - ERROR - [line:200] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 16:05:05,281 - handle_rocketmq_consumer.py - ERROR - [line:202] - 消费者初始化完成，等待连接验证
2025-05-14 16:05:05,282 - handle_rocketmq_consumer.py - ERROR - [line:337] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 16:05:05,305 - handle_rocketmq_consumer.py - ERROR - [line:345] - 消费者启动耗时: 0.00 秒
2025-05-14 16:05:05,307 - handle_rocketmq_consumer.py - ERROR - [line:353] - 等待消费者连接建立...
2025-05-14 16:05:08,311 - handle_rocketmq_consumer.py - ERROR - [line:357] - 消费者已启动，但无法完全验证连接状态
2025-05-14 16:05:08,312 - handle_rocketmq_consumer.py - ERROR - [line:358] - 如果地址错误，可能会在日志中看到连接失败的错误信息
2025-05-14 16:05:08,313 - handle_rocketmq_consumer.py - ERROR - [line:359] - 当前配置的NameServer地址: 10.4.3.242:9879;10.4.3.243:9879
2025-05-14 16:05:13,317 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:05:18,322 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:05:19,214 - handle_rocketmq_consumer.py - ERROR - [line:416] - 停止消费者...
2025-05-14 16:05:19,218 - handle_rocketmq_consumer.py - ERROR - [line:422] - 消费者已停止
2025-05-14 16:05:51,377 - handle_rocketmq_consumer.py - ERROR - [line:324] - 启动消费者...
2025-05-14 16:05:51,378 - handle_rocketmq_consumer.py - ERROR - [line:330] - 开始初始化消费者...
2025-05-14 16:05:51,379 - handle_rocketmq_consumer.py - ERROR - [line:176] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 16:05:51,380 - handle_rocketmq_consumer.py - ERROR - [line:184] - 设置NameServer地址：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 16:05:51,381 - handle_rocketmq_consumer.py - ERROR - [line:188] - 设置消费线程数：10
2025-05-14 16:05:51,382 - handle_rocketmq_consumer.py - ERROR - [line:245] - 消费者实例的属性: ['__class__', '__del__', '__delattr__', '__dict__', '__dir__', '__doc__', '__enter__', '__eq__', '__exit__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_callback_refs', '_handle', '_orderly', '_register_callback', '_unregister_callback', 'set_group', 'set_instance_name', 'set_message_batch_max_size', 'set_message_model', 'set_namesrv_addr', 'set_namesrv_domain', 'set_session_credentials', 'set_thread_count', 'shutdown', 'start', 'subscribe']
2025-05-14 16:05:51,383 - handle_rocketmq_consumer.py - ERROR - [line:248] - 消费者实例类型: <class 'rocketmq.client.PushConsumer'>
2025-05-14 16:05:51,384 - handle_rocketmq_consumer.py - ERROR - [line:252] - 消费者实例内存地址: 140138471568432
2025-05-14 16:05:51,385 - handle_rocketmq_consumer.py - ERROR - [line:256] - 配置的NameServer地址: 10.4.3.242:9876;10.4.3.243:9876
2025-05-14 16:05:51,386 - handle_rocketmq_consumer.py - ERROR - [line:260] - 没有直接的方法来验证连接状态，假设连接可能成功
2025-05-14 16:05:51,387 - handle_rocketmq_consumer.py - ERROR - [line:199] - None
2025-05-14 16:05:51,388 - handle_rocketmq_consumer.py - ERROR - [line:200] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 16:05:51,389 - handle_rocketmq_consumer.py - ERROR - [line:202] - 消费者初始化完成，等待连接验证
2025-05-14 16:05:51,389 - handle_rocketmq_consumer.py - ERROR - [line:337] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 16:05:51,532 - handle_rocketmq_consumer.py - ERROR - [line:345] - 消费者启动耗时: 0.00 秒
2025-05-14 16:05:51,534 - handle_rocketmq_consumer.py - ERROR - [line:353] - 等待消费者连接建立...
2025-05-14 16:05:54,538 - handle_rocketmq_consumer.py - ERROR - [line:357] - 消费者已启动，但无法完全验证连接状态
2025-05-14 16:05:54,539 - handle_rocketmq_consumer.py - ERROR - [line:358] - 如果地址错误，可能会在日志中看到连接失败的错误信息
2025-05-14 16:05:54,539 - handle_rocketmq_consumer.py - ERROR - [line:359] - 当前配置的NameServer地址: 10.4.3.242:9876;10.4.3.243:9876
2025-05-14 16:05:59,542 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:06:01,930 - handle_rocketmq_consumer.py - ERROR - [line:416] - 停止消费者...
2025-05-14 16:06:01,954 - handle_rocketmq_consumer.py - ERROR - [line:422] - 消费者已停止
2025-05-14 16:07:23,722 - handle_rocketmq_consumer.py - ERROR - [line:324] - 启动消费者...
2025-05-14 16:07:23,723 - handle_rocketmq_consumer.py - ERROR - [line:330] - 开始初始化消费者...
2025-05-14 16:07:23,724 - handle_rocketmq_consumer.py - ERROR - [line:176] - 消费者实例创建，消费组为：Test_Data_Check_Group
2025-05-14 16:07:23,725 - handle_rocketmq_consumer.py - ERROR - [line:184] - 设置NameServer地址：10.4.3.242:9876;10.4.3.243:9876
2025-05-14 16:07:23,725 - handle_rocketmq_consumer.py - ERROR - [line:188] - 设置消费线程数：10
2025-05-14 16:07:23,726 - handle_rocketmq_consumer.py - ERROR - [line:245] - 消费者实例的属性: ['__class__', '__del__', '__delattr__', '__dict__', '__dir__', '__doc__', '__enter__', '__eq__', '__exit__', '__format__', '__ge__', '__getattribute__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__str__', '__subclasshook__', '__weakref__', '_callback_refs', '_handle', '_orderly', '_register_callback', '_unregister_callback', 'set_group', 'set_instance_name', 'set_message_batch_max_size', 'set_message_model', 'set_namesrv_addr', 'set_namesrv_domain', 'set_session_credentials', 'set_thread_count', 'shutdown', 'start', 'subscribe']
2025-05-14 16:07:23,727 - handle_rocketmq_consumer.py - ERROR - [line:248] - 消费者实例类型: <class 'rocketmq.client.PushConsumer'>
2025-05-14 16:07:23,728 - handle_rocketmq_consumer.py - ERROR - [line:252] - 消费者实例内存地址: 140147560609584
2025-05-14 16:07:23,729 - handle_rocketmq_consumer.py - ERROR - [line:256] - 配置的NameServer地址: 10.4.3.242:9876;10.4.3.243:9876
2025-05-14 16:07:23,730 - handle_rocketmq_consumer.py - ERROR - [line:260] - 没有直接的方法来验证连接状态，假设连接可能成功
2025-05-14 16:07:23,731 - handle_rocketmq_consumer.py - ERROR - [line:199] - None
2025-05-14 16:07:23,732 - handle_rocketmq_consumer.py - ERROR - [line:200] - 订阅主题：TP_ORDER_OFFLINE_ORDER-DATA
2025-05-14 16:07:23,733 - handle_rocketmq_consumer.py - ERROR - [line:202] - 消费者初始化完成，等待连接验证
2025-05-14 16:07:23,734 - handle_rocketmq_consumer.py - ERROR - [line:337] - 尝试启动消费者并连接到RocketMQ服务器...
2025-05-14 16:07:23,899 - handle_rocketmq_consumer.py - ERROR - [line:345] - 消费者启动耗时: 0.00 秒
2025-05-14 16:07:23,901 - handle_rocketmq_consumer.py - ERROR - [line:353] - 等待消费者连接建立...
2025-05-14 16:07:26,903 - handle_rocketmq_consumer.py - ERROR - [line:357] - 消费者已启动，但无法完全验证连接状态
2025-05-14 16:07:26,904 - handle_rocketmq_consumer.py - ERROR - [line:358] - 如果地址错误，可能会在日志中看到连接失败的错误信息
2025-05-14 16:07:26,905 - handle_rocketmq_consumer.py - ERROR - [line:359] - 当前配置的NameServer地址: 10.4.3.242:9876;10.4.3.243:9876
2025-05-14 16:07:31,909 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:07:36,915 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:07:41,921 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:07:46,928 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:07:52,848 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:07:57,853 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:07:57,854 - handle_rocketmq_consumer.py - ERROR - [line:402] - 等待连接验证，已等待5秒
2025-05-14 16:08:02,860 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:08:07,866 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:08:12,869 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:08:17,874 - handle_rocketmq_consumer.py - ERROR - [line:369] - 消费者保持活跃
2025-05-14 16:08:19,538 - handle_rocketmq_consumer.py - ERROR - [line:422] - 停止消费者...
2025-05-14 16:08:19,563 - handle_rocketmq_consumer.py - ERROR - [line:428] - 消费者已停止
2025-05-14 20:39:30,363 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:39:30,365 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:39:30,367 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:39:30,369 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:39:30,371 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:39:30,373 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:39:30,375 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:39:30,377 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:39:30,379 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:39:30,381 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:39:30,383 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:39:30,385 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:39:30,387 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:39:30,389 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:39:30,391 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:39:30,392 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:39:30,394 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:39:30,396 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:39:30,398 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:39:30,399 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:39:30,401 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:39:30,402 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:39:30,405 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:39:30,406 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:39:30,409 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:39:30,411 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:39:30,413 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:39:30,414 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:39:30,417 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:39:30,418 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:39:30,421 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:39:30,423 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:39:30,425 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:39:30,427 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:39:30,429 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:39:30,431 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:39:30,434 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:39:30,437 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:39:30,439 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:39:30,440 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:39:30,442 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:39:30,444 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:39:30,446 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:39:30,448 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:39:30,450 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:39:30,452 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:39:30,454 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:39:30,456 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:39:30,457 - monitored_data_verify.py - ERROR - [line:45] - 【TP_ORDER_OFFLINE_ORDER-DATA_TAG_CREATED】数据组中payinfo_detail_join字段对应的数据为空，请检查核实
2025-05-14 20:40:03,171 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:03,173 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:03,177 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:03,179 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:03,181 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:03,183 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:03,185 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:03,187 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:03,189 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:03,191 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:03,193 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:03,194 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:03,197 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:03,199 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:03,201 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:03,203 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:03,206 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:03,207 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:03,209 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:03,211 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:03,213 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:03,214 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:03,216 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:03,218 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:03,221 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:03,222 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:03,224 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:03,226 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:03,228 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:03,230 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:03,232 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:03,234 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:03,236 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:03,237 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:03,239 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:03,241 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:03,243 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:03,245 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:03,247 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:03,249 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:03,251 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:03,252 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:03,254 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:03,256 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:03,258 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:03,260 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:03,262 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:03,264 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:03,266 - monitored_data_verify.py - ERROR - [line:45] - 【TP_ORDER_OFFLINE_ORDER-DATA_TAG_CREATED】数据组中payinfo_detail_join字段对应的数据为空，请检查核实
2025-05-14 20:40:19,142 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:19,145 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:19,147 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:19,148 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:19,150 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:19,152 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:19,154 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:19,156 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:19,158 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:19,159 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:19,161 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*originalPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:19,162 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:19,166 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:19,167 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:19,169 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:19,170 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:19,171 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:19,172 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:19,174 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:19,175 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:19,177 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:19,179 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:19,180 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount*billPrice,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:19,181 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:billAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:19,183 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:19,184 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:19,186 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:19,187 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:19,189 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:19,190 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:19,192 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:19,193 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:19,195 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:19,196 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:19,198 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalAmount+discountShare,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:19,199 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:totalOriginalAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:19,202 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:19,204 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1}]}
2025-05-14 20:40:19,206 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:19,208 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '2', 'platformSkuId': {'platformSkuId': '187999'}, 'erpCode': {'erpCode': '187999'}, 'erpName': '金振口服液_康缘_10ML*10支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 66, 'price': 66, 'commodityCostPrice': 23.362494, 'totalAmount': 66, 'totalOriginalAmount': 66, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 66, 'billAmount': 66, 'isOnPromotion': '', 'fiveClass': 'A041205002'}, 'pickInfoList': [{'erpCode': {'erpCode': '187999'}, 'makeNo': {'makeNo': '240173'}, 'count': 1}]}
2025-05-14 20:40:19,210 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:19,213 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '3', 'platformSkuId': {'platformSkuId': '156659'}, 'erpCode': {'erpCode': '156659'}, 'erpName': '抗病毒口服液_999_10ML*12支', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 29, 'price': 29, 'commodityCostPrice': 9.557498, 'totalAmount': 29, 'totalOriginalAmount': 29, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 29, 'billAmount': 29, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '156659'}, 'makeNo': {'makeNo': '202501001'}, 'count': 1}]}
2025-05-14 20:40:19,214 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:19,217 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '4', 'platformSkuId': {'platformSkuId': '100826'}, 'erpCode': {'erpCode': '100826'}, 'erpName': '毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.6, 'price': 39.6, 'commodityCostPrice': 14.017351, 'totalAmount': 39.6, 'totalOriginalAmount': 39.6, 'discountShare': 0, 'discountAmount': 0, 'billPrice': 39.6, 'billAmount': 39.6, 'isOnPromotion': '', 'fiveClass': 'A040915001'}, 'pickInfoList': [{'erpCode': {'erpCode': '100826'}, 'makeNo': {'makeNo': '241002'}, 'count': 1}]}
2025-05-14 20:40:19,218 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:19,220 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '5', 'platformSkuId': {'platformSkuId': '129951'}, 'erpCode': {'erpCode': '129951'}, 'erpName': '蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 24, 'price': 18.7711, 'commodityCostPrice': 3.986881, 'totalAmount': 18.7711, 'totalOriginalAmount': 24, 'discountShare': 5.2289, 'discountAmount': 5.2289, 'billPrice': 18.7711, 'billAmount': 18.7711, 'isOnPromotion': '', 'fiveClass': 'A041306001'}, 'pickInfoList': [{'erpCode': {'erpCode': '129951'}, 'makeNo': {'makeNo': '20241208'}, 'count': 1}]}
2025-05-14 20:40:19,223 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:discountAmount,context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:19,225 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '6', 'platformSkuId': {'platformSkuId': '802148'}, 'erpCode': {'erpCode': '802148'}, 'erpName': '生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM', 'commodityCount': 1, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 0.3, 'price': 0, 'commodityCostPrice': 0.097335, 'totalAmount': 0, 'totalOriginalAmount': 0.3, 'discountShare': 0.3, 'discountAmount': 0.3, 'billPrice': 0, 'billAmount': 0, 'isOnPromotion': '', 'fiveClass': 'G010507002'}, 'pickInfoList': [{'erpCode': {'erpCode': '802148'}, 'makeNo': {'makeNo': '20250208'}, 'count': 1}]}
2025-05-14 20:40:19,228 - monitored_data_verify.py - ERROR - [line:45] - 【TP_ORDER_OFFLINE_ORDER-DATA_TAG_CREATED】数据组中payinfo_detail_join字段对应的数据为空，请检查核实
2025-05-14 20:42:21,613 - monitored_data_rule_validation.py - ERROR - [line:135] - Error evaluating formula: 'NoneType' object is not subscriptable,calculation_formula:commodityCount * (originalPrice -price ),context:{'baseOrderDetailInfo': {'orderNo': {'orderNo': '2985346444288092505'}, 'rowNo': '1', 'platformSkuId': {'platformSkuId': '134023'}, 'erpCode': {'erpCode': '134023'}, 'erpName': '复方金银花颗粒_诺金_10G*24袋', 'commodityCount': 1.0, 'statusValue': 'NORMAL', 'giftTypeValue': 'NOT_GIFT', 'originalPrice': 39.8, 'price': 31.1288, 'commodityCostPrice': 10.673242, 'totalAmount': 31.1288, 'totalOriginalAmount': 39.8, 'discountShare': 8.6712, 'discountAmount': 8.6712, 'billPrice': 31.1288, 'billAmount': 31.1288, 'isOnPromotion': '', 'fiveClass': 'A041303002'}, 'pickInfoList': [{'erpCode': {'erpCode': '134023'}, 'makeNo': {'makeNo': '427A022'}, 'count': 1.0}]}
2025-05-15 09:24:57,074 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 09:31:32,291 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 09:34:32,455 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 09:37:32,628 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 09:40:32,722 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 09:43:32,786 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 09:46:32,959 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 09:49:33,109 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 09:52:33,261 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 09:55:33,384 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 09:58:33,509 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 10:01:33,638 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 10:04:33,740 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 10:07:33,921 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 10:10:34,033 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 10:13:34,169 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 10:16:34,334 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 10:19:34,588 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 10:22:34,704 - handle_rocketmq_consumer.py - ERROR - [line:392] - 等待连接验证，已等待5秒
2025-05-15 20:51:30,250 - test_case_result.py - ERROR - [line:267] - 响应码不匹配: 预期=403, 实际=50000
2025-05-15 20:51:30,286 - test_case_result.py - ERROR - [line:267] - 响应码不匹配: 预期=200, 实际=50000
2025-05-15 20:51:30,286 - test_case_result.py - ERROR - [line:267] - 响应码不匹配: 预期=200, 实际=50000
2025-05-15 20:51:30,298 - test_case_result.py - ERROR - [line:267] - 响应码不匹配: 预期=200, 实际=50000
2025-05-15 20:51:30,312 - test_case_result.py - ERROR - [line:267] - 响应码不匹配: 预期=200, 实际=50000
2025-05-16 18:35:41,917 - get_log.py - ERROR - [line:151] - ❌方法执行异常，执行详情如下：
    执行时间：2025-05-16 18:35:41
    方法名称：StorePay
    方法描述：门店支付配置比对
    方法入参：
    无入参
    执行耗时：0.000秒
    执行结果：异常
    异常信息：
        StorePay.__init__() missing 1 required positional argument: 'store_code'
    追踪信息：
        Traceback (most recent call last):
  File "E:\project\pythonProject\yxt_oms\lib\get_log.py", line 115, in wrapper
    result = func(*args,**kwargs)
TypeError: StorePay.__init__() missing 1 required positional argument: 'store_code'
2025-05-19 10:20:47,109 - get_log.py - ERROR - [line:151] - ❌方法执行异常，执行详情如下：
    执行时间：2025-05-19 10:20:47
    方法名称：StoreCompanyMatch
    方法描述：店铺和公司匹配
    方法入参：
    无入参
    执行耗时：0.000秒
    执行结果：异常
    异常信息：
        missing a required argument: 'database_name'
    追踪信息：
        Traceback (most recent call last):
  File "E:\project\pythonProject\yxt_oms\lib\get_log.py", line 115, in wrapper
    result = func(*args,**kwargs)
  File "E:\project\pythonProject\yxt_oms\structure\iteration_test\pay_platform\store_company_match.py", line 21, in __init__
    self.db = db_mysql_connect()
  File "E:\project\pythonProject\yxt_oms\venv\lib\site-packages\decorator.py", line 231, in fun
    args, kw = fix(args, kw, sig)
  File "E:\project\pythonProject\yxt_oms\venv\lib\site-packages\decorator.py", line 203, in fix
    ba = sig.bind(*args, **kwargs)
  File "D:\Python\Python310\lib\inspect.py", line 3177, in bind
    return self._bind(args, kwargs)
  File "D:\Python\Python310\lib\inspect.py", line 3092, in _bind
    raise TypeError(msg) from None
TypeError: missing a required argument: 'database_name'
2025-05-19 10:40:09,047 - store_company_match.py - ERROR - [line:76] - list index out of range
2025-05-19 22:38:00,368 - deal_func_performance.py - ERROR - [line:34] - db_mysql_execute 执行花费时间为 7.3408 秒，入参为【 ('10.100.5.151', 'readonly_user', 'lxoBah85XQq78gsQfo0g', 3306, 'h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ", ()) {}】
2025-05-19 22:38:00,369 - deal_func_performance.py - ERROR - [line:34] - db_mysql_connect 执行花费时间为 7.3417 秒，入参为【 ('h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ") {'environment_flag': 'prod'}】
2025-05-19 22:48:41,354 - deal_func_performance.py - ERROR - [line:34] - db_mysql_execute 执行花费时间为 6.4218 秒，入参为【 ('10.100.5.151', 'readonly_user', 'lxoBah85XQq78gsQfo0g', 3306, 'h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ", ()) {}】
2025-05-19 22:48:41,355 - deal_func_performance.py - ERROR - [line:34] - db_mysql_connect 执行花费时间为 6.4259 秒，入参为【 ('h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ") {'environment_flag': 'prod'}】
2025-05-19 22:51:16,019 - deal_func_performance.py - ERROR - [line:34] - db_mysql_execute 执行花费时间为 8.3530 秒，入参为【 ('10.100.5.151', 'readonly_user', 'lxoBah85XQq78gsQfo0g', 3306, 'h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ", ()) {}】
2025-05-19 22:51:16,020 - deal_func_performance.py - ERROR - [line:34] - db_mysql_connect 执行花费时间为 8.3540 秒，入参为【 ('h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ") {'environment_flag': 'prod'}】
2025-05-19 22:53:26,184 - deal_func_performance.py - ERROR - [line:34] - db_mysql_execute 执行花费时间为 5.8322 秒，入参为【 ('10.100.5.151', 'readonly_user', 'lxoBah85XQq78gsQfo0g', 3306, 'h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ", ()) {}】
2025-05-19 22:53:26,185 - deal_func_performance.py - ERROR - [line:34] - db_mysql_connect 执行花费时间为 5.8342 秒，入参为【 ('h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ") {'environment_flag': 'prod'}】
2025-05-19 22:54:34,348 - deal_func_performance.py - ERROR - [line:34] - db_mysql_execute 执行花费时间为 7.0995 秒，入参为【 ('10.100.5.151', 'readonly_user', 'lxoBah85XQq78gsQfo0g', 3306, 'h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ", ()) {}】
2025-05-19 22:54:34,349 - deal_func_performance.py - ERROR - [line:34] - db_mysql_connect 执行花费时间为 7.1005 秒，入参为【 ('h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ") {'environment_flag': 'prod'}】
2025-05-19 22:56:14,501 - deal_func_performance.py - ERROR - [line:34] - db_mysql_execute 执行花费时间为 5.5224 秒，入参为【 ('10.100.5.151', 'readonly_user', 'lxoBah85XQq78gsQfo0g', 3306, 'h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ", ()) {}】
2025-05-19 22:56:14,502 - deal_func_performance.py - ERROR - [line:34] - db_mysql_connect 执行花费时间为 5.5234 秒，入参为【 ('h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ") {'environment_flag': 'prod'}】
2025-05-19 22:58:54,388 - deal_func_performance.py - ERROR - [line:34] - db_mysql_execute 执行花费时间为 6.4989 秒，入参为【 ('10.100.5.151', 'readonly_user', 'lxoBah85XQq78gsQfo0g', 3306, 'h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ", ()) {}】
2025-05-19 22:58:54,388 - deal_func_performance.py - ERROR - [line:34] - db_mysql_connect 执行花费时间为 6.4999 秒，入参为【 ('h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ") {'environment_flag': 'prod'}】
2025-05-19 23:06:41,585 - deal_func_performance.py - ERROR - [line:34] - db_mysql_execute 执行花费时间为 5.4852 秒，入参为【 ('10.100.5.151', 'readonly_user', 'lxoBah85XQq78gsQfo0g', 3306, 'h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ", ()) {}】
2025-05-19 23:06:41,586 - deal_func_performance.py - ERROR - [line:34] - db_mysql_connect 执行花费时间为 5.4872 秒，入参为【 ('h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ") {'environment_flag': 'prod'}】
2025-05-19 23:14:33,167 - deal_func_performance.py - ERROR - [line:34] - db_mysql_execute 执行花费时间为 7.3214 秒，入参为【 ('10.100.5.151', 'readonly_user', 'lxoBah85XQq78gsQfo0g', 3306, 'h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ", ()) {}】
2025-05-19 23:14:33,168 - deal_func_performance.py - ERROR - [line:34] - db_mysql_connect 执行花费时间为 7.3234 秒，入参为【 ('h3_pay_core', "\n                SELECT\n                    tpicrc.pay_channel_code,\n                    tree.org_parent AS pay_company_code,\n                    tpicrc.id AS t_pay_info_config_rela_channel_id,\n                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,\n                    tpic.pay_business_name AS merchant_name,\n                    tpic.pay_business_code AS merchant_code,\n                    tpcc.channel_business_extends AS pay_config_detail,\n                    tpicrc.info_status AS info_status,\n                    tpcc.config_type AS config_type\n                FROM\n                    t_organization_tree tree\n                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code\n                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id\n                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code\n                WHERE\n\n                    tpic.is_delete = 2\n                    AND tpicrc.pay_channel_code='WEIXIN'\n                ") {'environment_flag': 'prod'}】
2025-05-24 10:51:33,780 - get_log.py - ERROR - [line:151] - ❌方法执行异常，执行详情如下：
    执行时间：2025-05-24 10:51:33
    方法名称：TestCaseResult
    方法描述：测试用例结果
    方法入参：
    无入参
    执行耗时：0.000秒
    执行结果：异常
    异常信息：
        TestCaseResult.__init__() missing 1 required positional argument: 'case_data'
    追踪信息：
        Traceback (most recent call last):
  File "E:\project\pythonProject\yxt_oms\lib\get_log.py", line 115, in wrapper
    result = func(*args,**kwargs)
TypeError: TestCaseResult.__init__() missing 1 required positional argument: 'case_data'
2025-05-24 10:52:51,017 - test_case_result.py - ERROR - [line:128] - 生成预期采购单数据时发生错误: 'str' object has no attribute 'get'
2025-05-24 10:58:39,665 - test_case_data.py - ERROR - [line:71] - 生成测试用例中的值失败：'list' object has no attribute 'get'
2025-05-24 11:00:39,728 - get_api_data.py - ERROR - [line:249] - 获取门店列表失败:unhashable type: 'dict'
2025-05-24 11:00:39,729 - test_case_data.py - ERROR - [line:175] - 生成测试用例中的店铺数据失败：unhashable type: 'dict'
2025-05-24 11:01:42,039 - get_api_data.py - ERROR - [line:249] - 获取门店列表失败:unhashable type: 'dict'
2025-05-24 11:01:42,040 - test_case_data.py - ERROR - [line:175] - 生成测试用例中的店铺数据失败：unhashable type: 'dict'
2025-05-24 11:02:36,240 - get_api_data.py - ERROR - [line:249] - 获取门店列表失败:unhashable type: 'dict'
2025-05-24 11:05:14,086 - get_api_data.py - ERROR - [line:249] - 获取门店列表失败:unhashable type: 'dict'
2025-05-24 11:40:12,952 - get_api_data.py - ERROR - [line:260] - 获取门店列表失败:'str' object has no attribute 'get'
2025-05-24 12:44:38,571 - test_case_data.py - ERROR - [line:369] - 生成测试用例中的商品数据失败：'NoneType' object is not iterable
2025-05-24 13:05:27,070 - test_case_data.py - ERROR - [line:369] - 生成测试用例中的商品数据失败：'<' not supported between instances of 'NoneType' and 'int'
2025-05-24 13:05:27,071 - test_case_data.py - ERROR - [line:71] - 生成测试用例中的值失败：'<' not supported between instances of 'NoneType' and 'int'
2025-05-24 13:05:27,074 - test_case_result.py - ERROR - [line:128] - 生成预期采购单数据时发生错误: 'NoneType' object is not iterable
2025-05-24 13:19:11,535 - test_case_data.py - ERROR - [line:369] - 生成测试用例中的商品数据失败：'<' not supported between instances of 'NoneType' and 'int'
2025-05-24 13:25:00,061 - test_case_data.py - ERROR - [line:370] - 生成测试用例中的商品数据失败：'<' not supported between instances of 'NoneType' and 'int'
2025-05-24 13:25:00,062 - test_case_data.py - ERROR - [line:72] - 生成测试用例中的值失败：'<' not supported between instances of 'NoneType' and 'int'
2025-05-24 13:25:00,064 - test_case_result.py - ERROR - [line:128] - 生成预期采购单数据时发生错误: 'NoneType' object is not iterable
2025-05-24 13:37:42,103 - test_case_data.py - ERROR - [line:370] - 生成测试用例中的商品数据失败：'<' not supported between instances of 'NoneType' and 'int',数据：JM0002
2025-05-24 13:37:42,104 - test_case_data.py - ERROR - [line:72] - 生成测试用例中的值失败：'<' not supported between instances of 'NoneType' and 'int'
2025-05-24 13:37:42,104 - test_case_result.py - ERROR - [line:128] - 生成预期采购单数据时发生错误: 'NoneType' object is not iterable
2025-05-24 15:02:50,287 - test_case_result.py - ERROR - [line:266] - 响应码不匹配: 预期=200, 实际=10000
2025-05-24 16:29:50,837 - test_case_result.py - ERROR - [line:482] - 验证采购单响应时发生错误: 'list' object has no attribute 'get'
2025-05-24 16:35:47,169 - test_case_result.py - ERROR - [line:436] - 采购单【PO20250524JM0001000006】标签不匹配: 预期=nomal, 实际=MULTIPLE_STORE
2025-05-24 16:36:39,496 - test_case_result.py - ERROR - [line:461] - 采购单【PO20250524JM0001000006】商品【142750】名称不一致: 预期=, 实际=复方嗜酸乳杆菌片_益君康_0.5g*10片*2板
2025-05-24 16:36:42,402 - test_case_result.py - ERROR - [line:465] - 采购单【PO20250524JM0001000006】商品【142750】生成厂商不一致: 预期=, 实际=None
2025-05-24 16:36:50,969 - test_case_result.py - ERROR - [line:469] - 采购单【PO20250524JM0001000006】商品【142750】规格不一致: 预期=, 实际=0.5g*10片*2板
2025-05-24 16:36:58,189 - test_case_result.py - ERROR - [line:461] - 采购单【PO20250524JM0001000006】商品【192292】名称不一致: 预期=, 实际=妇炎洁医用护理垫_290mm*70mm*6片
2025-05-24 16:37:00,021 - test_case_result.py - ERROR - [line:465] - 采购单【PO20250524JM0001000006】商品【192292】生成厂商不一致: 预期=, 实际=None
2025-05-24 16:37:01,821 - test_case_result.py - ERROR - [line:469] - 采购单【PO20250524JM0001000006】商品【192292】规格不一致: 预期=, 实际=290MM*70MM*6片
2025-05-24 16:45:03,755 - test_case_result.py - ERROR - [line:393] - 采购单【PO20250524JM0005000005】标签不匹配: 预期=nomal, 实际=MULTIPLE_STORE
2025-05-24 16:45:03,755 - test_case_result.py - ERROR - [line:418] - 采购单【PO20250524JM0005000005】商品【142750】名称不一致: 预期=, 实际=复方嗜酸乳杆菌片_益君康_0.5g*10片*2板
2025-05-24 16:45:03,755 - test_case_result.py - ERROR - [line:422] - 采购单【PO20250524JM0005000005】商品【142750】生成厂商不一致: 预期=, 实际=None
2025-05-24 16:45:03,755 - test_case_result.py - ERROR - [line:426] - 采购单【PO20250524JM0005000005】商品【142750】规格不一致: 预期=, 实际=0.5g*10片*2板
2025-05-24 16:45:03,755 - test_case_result.py - ERROR - [line:429] - 采购单【PO20250524JM0005000005】商品【142750】状态不一致: 预期=normal, 实际=0
2025-05-24 16:45:03,755 - test_case_result.py - ERROR - [line:418] - 采购单【PO20250524JM0005000005】商品【192292】名称不一致: 预期=, 实际=妇炎洁医用护理垫_290mm*70mm*6片
2025-05-24 16:45:03,755 - test_case_result.py - ERROR - [line:422] - 采购单【PO20250524JM0005000005】商品【192292】生成厂商不一致: 预期=, 实际=None
2025-05-24 16:45:03,755 - test_case_result.py - ERROR - [line:426] - 采购单【PO20250524JM0005000005】商品【192292】规格不一致: 预期=, 实际=290MM*70MM*6片
2025-05-24 16:45:03,756 - test_case_result.py - ERROR - [line:429] - 采购单【PO20250524JM0005000005】商品【192292】状态不一致: 预期=normal, 实际=0
2025-05-24 17:09:26,492 - test_case_result.py - ERROR - [line:393] - 采购单【PO20250524JM0001000007】标签不匹配: 预期=nomal, 实际=MULTIPLE_STORE
2025-05-24 17:09:26,493 - test_case_result.py - ERROR - [line:422] - 采购单【PO20250524JM0001000007】商品【192292】生成厂商不一致: 预期=美丽岛(福建)生活用品有限公司, 实际=None
2025-05-24 17:09:26,495 - test_case_result.py - ERROR - [line:429] - 采购单【PO20250524JM0001000007】商品【192292】状态不一致: 预期=normal, 实际=0
2025-05-24 17:13:13,113 - test_case_result.py - ERROR - [line:393] - 采购单【PO20250524JM0001000008】标签不匹配: 预期=nomal, 实际=MULTIPLE_STORE
2025-05-24 17:13:13,114 - test_case_result.py - ERROR - [line:422] - 采购单【PO20250524JM0001000008】商品【192292】生成厂商不一致: 预期=美丽岛(福建)生活用品有限公司, 实际=None
2025-05-24 17:13:13,114 - test_case_result.py - ERROR - [line:422] - 采购单【PO20250524JM0001000008】商品【142750】生成厂商不一致: 预期=通化金马药业集团股份有限公司, 实际=None
2025-05-24 17:14:01,489 - test_case_result.py - ERROR - [line:320] - 响应码不匹配: 预期=10000, 实际=50000
2025-05-24 17:16:20,746 - test_case_result.py - ERROR - [line:321] - 响应码不匹配: 预期=10000, 实际=50000
2025-05-24 17:17:00,386 - test_case_result.py - ERROR - [line:321] - 响应码不匹配: 预期=10000, 实际=50000
2025-05-24 17:17:18,562 - test_case_result.py - ERROR - [line:321] - 响应码不匹配: 预期=10000, 实际=50000
2025-05-26 10:17:56,085 - test_case_result.py - ERROR - [line:325] - 响应码不匹配: 预期=403, 实际=50000
2025-05-26 10:19:51,204 - test_case_result.py - ERROR - [line:329] - 响应消息不匹配: 预期=权限不足, 实际=开小差了，请稍后再试～
2025-05-26 10:22:45,715 - test_case_result.py - ERROR - [line:330] - 响应消息不匹配: 预期=权限不足, 实际=开小差了，请稍后再试～
2025-05-26 12:11:36,903 - test_case_result.py - ERROR - [line:326] - 响应码不匹配: 预期=10000, 实际=50000
2025-05-26 13:44:20,701 - test_case_result.py - ERROR - [line:399] - 采购单【PO20250526JM0002000003】标签不匹配: 预期=cold, 实际=MULTIPLE_STORE
2025-05-26 13:44:20,703 - test_case_result.py - ERROR - [line:428] - 采购单【PO20250526JM0002000003】商品【133570】生成厂商不一致: 预期=惠州大亚制药股份有限公司, 实际=None
2025-05-26 13:44:20,703 - test_case_result.py - ERROR - [line:428] - 采购单【PO20250526JM0002000003】商品【254513】生成厂商不一致: 预期=通化东宝药业股份有限公司(原:通化东宝制药), 实际=None
2025-05-26 13:44:20,703 - test_case_result.py - ERROR - [line:428] - 采购单【PO20250526JM0002000003】商品【176156】生成厂商不一致: 预期=海南葫芦娃药业集团股份有限公司, 实际=None
2025-05-26 13:44:20,704 - test_case_result.py - ERROR - [line:428] - 采购单【PO20250526JM0002000003】商品【100354】生成厂商不一致: 预期=赛诺菲(北京)制药有限公司, 实际=None
2025-05-26 13:44:20,704 - test_case_result.py - ERROR - [line:428] - 采购单【PO20250526JM0002000003】商品【145039】生成厂商不一致: 预期=ELI LILLY AND COMPANY美国, 实际=None
2025-05-26 19:25:52,252 - test_case_result.py - ERROR - [line:399] - 采购单【PO20250526JM0002000004】标签不匹配: 预期=cold, 实际=MULTIPLE_STORE
2025-05-27 10:31:05,433 - test_case_result.py - ERROR - [line:399] - 采购单【PO20250527JG0080000001】标签不匹配: 预期=nomal, 实际=MULTIPLE_STORE
2025-05-27 10:31:05,800 - test_case_result.py - ERROR - [line:399] - 采购单【PO20250527JG0001000001】标签不匹配: 预期=nomal, 实际=MULTIPLE_STORE
2025-05-27 10:33:04,491 - test_case_result.py - ERROR - [line:399] - 采购单【PO20250527JG0056000001】标签不匹配: 预期=nomal, 实际=MULTIPLE_STORE
2025-05-27 10:33:04,656 - test_case_result.py - ERROR - [line:399] - 采购单【PO20250527JG0084000001】标签不匹配: 预期=nomal, 实际=MULTIPLE_STORE
2025-05-27 10:33:04,829 - test_case_result.py - ERROR - [line:399] - 采购单【PO20250527JG0086000001】标签不匹配: 预期=nomal, 实际=MULTIPLE_STORE
2025-05-27 16:51:33,347 - test_case_result.py - ERROR - [line:498] - 采购单：PO20250527JM0033000001，与生成订单机构编码不一致: 采购单=JM0033, 订单=JM0033
2025-05-27 16:51:33,348 - test_case_result.py - ERROR - [line:501] - 采购单：PO20250527JM0033000001，与生成订单创建人不一致: 采购单=4086467781451892826, 订单=None
2025-05-27 16:51:33,348 - test_case_result.py - ERROR - [line:515] - 采购单【PO20250527JM0033000001】商品【118330】数量不一致: 采购单=100.0, 订单=100
2025-05-27 16:51:33,348 - test_case_result.py - ERROR - [line:515] - 采购单【PO20250527JM0033000001】商品【127458】数量不一致: 采购单=50.0, 订单=50
2025-05-27 16:51:33,348 - test_case_result.py - ERROR - [line:515] - 采购单【PO20250527JM0033000001】商品【104463】数量不一致: 采购单=50.0, 订单=50
2025-05-27 16:51:33,348 - test_case_result.py - ERROR - [line:515] - 采购单【PO20250527JM0033000001】商品【100781】数量不一致: 采购单=50.0, 订单=50
2025-05-27 17:01:14,361 - test_case_result.py - ERROR - [line:453] - 采购单：PO20250527JM0052000001，与生成订单机构编码不一致: 采购单=JM0052, 订单=JM0052
2025-05-27 17:01:14,362 - test_case_result.py - ERROR - [line:456] - 采购单：PO20250527JM0052000001，与生成订单创建人不一致: 采购单=4086467781451892826, 订单=None
2025-05-27 17:01:14,362 - test_case_result.py - ERROR - [line:254] - 测试数据执行结果时发生错误: an integer is required
2025-05-27 17:02:34,655 - test_case_result.py - ERROR - [line:400] - 采购单【PO20250527JM0003000008】商品【118330】状态不一致: 预期=normal, 实际=-1, 实际exMsg=库存不足,仓库库存0
2025-05-27 17:03:19,686 - test_case_result.py - ERROR - [line:453] - 采购单：PO20250527JM0009000001，与生成订单机构编码不一致: 采购单=JM0009, 订单=JM0009
2025-05-27 17:03:19,688 - test_case_result.py - ERROR - [line:456] - 采购单：PO20250527JM0009000001，与生成订单创建人不一致: 采购单=4086467781451892826, 订单=None
2025-05-27 17:03:19,689 - test_case_result.py - ERROR - [line:254] - 测试数据执行结果时发生错误: an integer is required
2025-05-27 17:05:19,004 - test_case_result.py - ERROR - [line:400] - 采购单【PO20250527JM0003000010】商品【118330】状态不一致: 预期=normal, 实际=-1, 实际exMsg=库存不足,仓库库存0
2025-05-27 17:05:19,004 - test_case_result.py - ERROR - [line:400] - 采购单【PO20250527JM0003000010】商品【142750】状态不一致: 预期=normal, 实际=-1, 实际exMsg=库存不足,仓库库存893
2025-05-27 19:36:52,130 - test_case_result.py - ERROR - [line:400] - 采购单【PO20250527JM0040000001】商品【142750】状态不一致: 预期=normal, 实际=-1, 实际exMsg=库存不足,仓库库存100
2025-05-27 19:36:52,353 - test_case_result.py - ERROR - [line:400] - 采购单【PO20250527JM0052000002】商品【100781】状态不一致: 预期=normal, 实际=-1, 实际exMsg=库存不足,仓库库存0
2025-05-27 19:36:52,626 - test_case_result.py - ERROR - [line:400] - 采购单【PO20250527JM0002000002】商品【169574】状态不一致: 预期=normal, 实际=-1, 实际exMsg=库存不足,仓库库存0
2025-05-27 19:36:52,626 - test_case_result.py - ERROR - [line:400] - 采购单【PO20250527JM0002000002】商品【108747】状态不一致: 预期=normal, 实际=-1, 实际exMsg=库存不足,仓库库存50
2025-06-03 16:28:15,725 - api_data.py - ERROR - [line:60] - 获取报批配置列表异常:'str' object has no attribute 'get'
2025-06-03 16:32:21,504 - api_data.py - ERROR - [line:125] - 获取审批配置列表异常:'str' object has no attribute 'get'
