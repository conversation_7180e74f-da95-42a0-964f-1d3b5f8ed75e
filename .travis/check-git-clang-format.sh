# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

#!/bin/bash

 if [ "$TRAVIS_PULL_REQUEST" = "true" ]; then
    base_commit="$TRAVIS_BRANCH"
else
    base_commit="HEAD^"
fi

 output="$(sudo python .travis/git-clang-format --binary clang-format-3.8 --commit $base_commit --diff)"

 if [ "$output" = "no modified files to format" ] || [ "$output" = "clang-format did not modify any files" ]; then
    echo "clang-format passed."
    exit 0
else
    echo "clang-format failed."
    echo "$output"
    exit 1
fi
