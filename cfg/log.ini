[loggers]
keys=root,main
[logger_root]
level=ERROR
handlers=consoleHandler,fileHandler
[logger_main]
level=ERROR
qualname=main
handlers=fileHandler
[handlers]
keys=consoleHandler,fileHandler
[handler_consoleHandler]
class=StreamHandler
level=ERROR
formatter=fmt
args=(sys.stdout,)

[handler_fileHandler]
class=logging.handlers.RotatingFileHandler
level=ERROR
formatter=fmt
args=('/log/log.log','a',10485760, 5,'utf-8')
;args=('/mnt/e/project/pythonProject/yxt_oms/log/log.log','a',10485760, 5,'utf-8')
;args=('/opt/python_project/YXT_OMS/log/log.log','a')


[formatters]
keys=fmt

[formatter_fmt]
format=%(asctime)s - %(filename)s - %(levelname)s - [line:%(lineno)d] - %(message)s
