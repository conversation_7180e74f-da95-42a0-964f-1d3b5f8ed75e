# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/3 22:03
@Auth ： 逗逗的小老鼠
@File ：apps.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from apps import create_app
from lib.deal_scheduler import bgsched
from structure.handle.handle_scheduler import schedul_task_config_add
from structure.handle.handle_rocketmq_consumer import RocketMQConsumer
import threading
import signal
import sys
import os

# 选择环境
app = create_app("test")

# 初始化消费者
consumer = RocketMQConsumer(environment_flag="test")
# 创建消费者线程
consumer_thread = threading.Thread(target=consumer.start, daemon=True)

def start_shceduler():
    try:
        # 添加定时任务到任务列表
        task_info = {}
        add_job = schedul_task_config_add(task_info)
        bgsched.start()
    except Exception as e:
        print(f"启动调度器失败: {e}")
        sys.exit(1)

def start_consumer():
    """启动消费者线程"""
    try:
        # 验证MQ配置
        if not consumer.validate_config():
            print("RocketMQ 配置验证失败，消费者未启动")
            return False

        consumer_thread.start()
        print("RocketMQ 消费者线程已启动")
        return True
    except Exception as e:
        print(f"RocketMQ 消费者启动失败: {e}")
        return False

def shutdown_consumer(signum=None, frame=None):
    """关闭消费者线程"""
    print("\n正在关闭 RocketMQ 消费者...")
    consumer.stop()
    consumer_thread.join(timeout=5)
    print("消费者已关闭")
    sys.exit(0)

# 注册终止信号（如 Ctrl+C）
signal.signal(signal.SIGINT, shutdown_consumer)
signal.signal(signal.SIGTERM, shutdown_consumer)



if __name__ == '__main__':
    # # 仅在主线程启动消费者（避免 Flask 调试模式重复启动）
    # if not app.debug or os.environ.get('WERKZEUG_RUN_MAIN') == 'true':
    consumer_started = start_consumer()
    if not consumer_started:
        print("\n警告: RocketMQ消费者启动失败，应用将在没有消费者的情况下运行\n")

    start_shceduler()
    # 从配置中读取参数
    host = app.config.get("HOST", "0.0.0.0")
    port = app.config.get("PORT", 5000)
    debug = app.config.get("DEBUG", False)

    # 打印调试信息
    print(f"启动配置: HOST={host}, PORT={port}, DEBUG={debug}")
    app.run(host="0.0.0.0", port=port, debug=app.config["DEBUG"])