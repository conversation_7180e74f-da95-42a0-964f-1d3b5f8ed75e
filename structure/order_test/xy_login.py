# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/5 15:21
@Auth ： 逗逗的小老鼠
@File ：xy_login.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.deal_ini import readini,updateini
import requests
import time,datetime
import json
"""
    心云平台登录
    :param 
    :return 
"""
@exception(logger)
def xy_user_login():
    "登录心云平台"
    try:
        login_url=readini("xy_login_param.ini","test","login_url")
        username=readini("xy_login_param.ini","test","username")
        password=readini("xy_login_param.ini","test","password")
        clientId=readini("xy_login_param.ini","test","clientId")
        login_payload={"account":username,"pwd":password,"clientId":clientId,"imgVerificationCode":"","loginSourceType":1,"verificationCode":""}
        login_headers={"Content-Type":"application/json"}
        res=requests.post(url=login_url,data=json.dumps(login_payload),headers=login_headers,verify=False)
        if res.status_code==200:
            res_json=res.json()
            res_code=res_json['code']
            if res_code=='10000':
                token=res_json['data']['token']
                timestamp=res_json['timestamp']
                token_result=updateini("xy_login_param.ini","test","token",token)
                timestamp_result = updateini("xy_login_param.ini", "test", "timestamp", str(timestamp))
                result={"token":token,"timestamp":timestamp,"token_result":token_result,"timestamp_result":timestamp_result}
                return result
            else:
                result={"code":res_code,"msg":res_json['msg']}
                raise result
        else:
            result={"code":res.status_code,"msg":"用户登录接口访问异常"}
            raise result
    except Exception as e:
        raise e

"""
    心云平台接口token值获取
    :param 
    :return 
"""
@exception(logger)
def xy_user_token():
    try:
        for i in range(3):
            # 获取配置文件中的token的生成时间
            timestamp = readini("xy_login_param.ini", "test", "timestamp")
            nowtamp=int(time.time()*1000)
            # # 将毫秒时间戳转换成datetime对象
            current_dt = datetime.datetime.fromtimestamp(int(timestamp) / 1000).replace(microsecond=0)
            target_dt = datetime.datetime.fromtimestamp(nowtamp / 1000).replace(microsecond=0)
            # 计算分钟差
            hour_diff = abs((target_dt - current_dt).total_seconds()) // 60
            if hour_diff>240:
                xy_user_login()
                token_flag=0
            else:
                token_flag=1
                break
        if token_flag==1:
            token=readini("xy_login_param.ini", "test", "token")
            return token
        else:
            err_msg="登录失败，无有效token"
            raise err_msg
    except Exception as e:
        raise e


if __name__=="__main__":
    result=xy_user_token()
    print(result)