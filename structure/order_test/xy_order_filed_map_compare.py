# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/14 11:22
@Auth ： 逗逗的小老鼠
@File ：xy_order_filed_map_compare.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.db_conf_test import db_yx_test_dsclound
from lib.db_conf import db_insight
from lib.deal_format_convert import filed_format_convert


"""
    查询订单各平台的字段映射关系
    :param 
    :return filed_map_data：字段映射清单
"""
@exception(logger)
def xy_order_filed_map_list(pos_mode):
    "查询订单各平台的字段映射关系"
    try:
        filed_map_sql=f"""
            SELECT
                * 
            FROM
                order_detail_field_map 
            WHERE
                is_delete = 0 
                AND id NOT IN (
                SELECT
                    id 
                FROM
                    order_detail_field_map 
                WHERE
                    is_delete = 0 
                AND is_diff_pos = 1 
                AND pos_mode != {pos_mode})        
        """
        filed_map_result=db_insight(filed_map_sql)
        filed_map_data=filed_map_result['data']
        return filed_map_data
    except Exception as e:
        raise e


"""
    查询订单字段值映射关系
    :param 
    :return filed_map_data：字段映射清单
"""
@exception(logger)
def xy_order_filed_value_map_list(map_filed,platform_value,platform_code,**kwargs):
    "查询订单字段值映射关系"
    try:
        extend_sql=""
        for key,value in kwargs.items():
            if key=='extend_flag':
                extend_sql=f" AND {key}='{value}' "
        filed_map_sql=f"""SELECT xy_map_value FROM order_detail_filed_value_map WHERE is_delete=0 AND map_filed='{map_filed}' AND platform_value='{platform_value}' AND platfrom_code='{platform_code}' {extend_sql}"""
        filed_map_result=db_insight(filed_map_sql)
        filed_map_data=filed_map_result['data']
        return filed_map_data
    except Exception as e:
        raise e



"""
    通过字段格式进行数据比对
    :param comparison_format:比对的数据指定转换格式
    :param xy_value:心云接口字段值
    :param db_value:心云数据库字段值
    :param platform_value:平台数据字段值
    :return result：订单详情
"""
@exception(logger)
def xy_order_filed_compare(comparison_format,xy_value,db_value,platform_value):
    "通过字段格式进行数据比对"
    try:
        if xy_value!=None:
            xy_value=filed_format_convert(xy_value,comparison_format)
        if db_value!=None:
            db_value=filed_format_convert(db_value,comparison_format)
        if platform_value!=None:
            platform_value=filed_format_convert(platform_value,comparison_format)
        if platform_value==None:
            if xy_value==db_value:
                return True
            else:
                return False
        else:
            if xy_value==db_value==platform_value:
                return True
            else:
                return False
    except Exception as e:
        raise e