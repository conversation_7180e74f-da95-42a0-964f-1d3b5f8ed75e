# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/22 10:30
@Auth ： 逗逗的小老鼠
@File ：xy_order_stock_compare.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception, logger
from structure.order_test.xy_order_db import xy_order_no_query
from structure.order_test.xy_order_stock_db import xy_order_stock_order_record, xy_order_stock_commodity_record,xy_organization_info,xy_commodity_stock
from lib.deal_format_convert import filed_format_convert, value_mathematics
from structure.order_test.xy_order_stock_api import xy_commodity_stock_api,mt_commodity_stock_api

"""
    订单库存占用记录比对
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def order_stock_check(third_order_no):
    "订单库存占用数据库记录比对"
    try:
        result = []
        # 订单侧结果
        order_stock_result = []
        # 商品测试结果
        commodity_stock_result = []
        # 比对数据统计
        order_stock_compare_pass = 0
        order_stock_compare_fail = 0
        commodity_stock_compare_pass = 0
        commodity_stock_compare_fail = 0
        # 根据三方订单号查询系统订单号
        order_no_result = xy_order_no_query(third_order_no)
        if order_no_result['code'] == 10000:
            order_no_data = order_no_result['data']
            order_no = order_no_data['order_no']
            online_store_code = order_no_data['online_store_code']
            organization_code = order_no_data['organization_code']
            order_state = order_no_data['order_state']
            erp_state = order_no_data['erp_state']
            third_platform_code=order_no_data['third_platform_code']
            # 根据组织机构码查询store_id
            store_result = xy_organization_info(organization_code)
            if store_result['code'] == 10000:
                store_id = store_result['data']['store_id']
                # 订单侧根据订单号查询商品详情及库存占用记录
                order_stock_db = xy_order_stock_order_record(order_no)
                order_stock_db_code = order_stock_db['code']
                if order_stock_db_code == 10000:
                    order_stock_db_data = order_stock_db['data']
                    # 订单详情
                    order_detail_data = order_stock_db_data['order_detail_data']
                    # 订单占用记录
                    order_stock_record_data = order_stock_db_data['order_stock_record_data']
                    order_erp_code_list=[]
                    # 遍历商品详情
                    for order_detail_item in order_detail_data:
                        # 详情ID
                        detail_id = order_detail_item['id']
                        # erp编码
                        detail_erp_code = order_detail_item['erp_code']
                        if detail_erp_code in order_erp_code_list:
                            pass
                        else:
                            order_erp_code_list.append(detail_erp_code)
                        # 商品总数
                        detail_commodity_count = filed_format_convert(order_detail_item['commodity_count'],'int')
                        # 商品状态
                        detail_status = order_detail_item['status']
                        # 商品退款数量
                        detail_refund_count = filed_format_convert(order_detail_item['refund_count'],'int')
                        # 商品占用记录数量
                        detail_occupy_record_count = 0
                        # 商品占用总数量
                        detail_occupy_record_sum = 0
                        # 商品释放记录数量
                        detail_free_record_count = 0
                        # 商品释放总数量
                        detail_free_record_sum = 0
                        for order_stock_item in order_stock_record_data:
                            stock_erp_code = order_stock_item['erp_code']
                            stock_online_store_code = order_stock_item['online_store_code']
                            stock_order_detail_id = order_stock_item['order_detail_id']
                            stock_qty = filed_format_convert(order_stock_item['stock_qty'],'int')
                            stock_organization_code = order_stock_item['organization_code']
                            stock_type = order_stock_item['type']
                            stock_serial_number = order_stock_item['serial_number']
                            if detail_id == stock_order_detail_id:
                                # 锁定成功
                                if stock_type == 1:
                                    detail_occupy_record_count = detail_occupy_record_count + 1
                                    detail_occupy_record_sum = filed_format_convert(
                                        value_mathematics(detail_occupy_record_sum, stock_qty, '+'), 'int')
                                # 锁定中
                                elif stock_type == 3:
                                    detail_occupy_record_count = detail_occupy_record_count + 1
                                    order_stock_compare_fail = order_stock_compare_fail + 1
                                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                                    "detail_erp_code": detail_erp_code,
                                                    "compare_msg": "订单商品库存占用状态不应为占用中", "right_value": 1,
                                                    "err_value": stock_type}
                                    order_stock_result.append(order_result)
                                # 锁定失败/异常
                                elif stock_type == 6 or stock_type == 7:
                                    detail_occupy_record_count = detail_occupy_record_count + 1
                                elif stock_type == 2:
                                    detail_free_record_count = detail_free_record_count + 1
                                    detail_free_record_sum = filed_format_convert(
                                        value_mathematics(detail_free_record_sum, stock_qty, '+'), 'int')
                                elif stock_type == 4:
                                    detail_free_record_count = detail_free_record_count + 1
                                    order_stock_compare_fail = order_stock_compare_fail + 1
                                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                                    "detail_erp_code": detail_erp_code,
                                                    "compare_msg": "订单商品库存占用状态不应为释放中", "right_value": 2,
                                                    "err_value": stock_type}
                                    order_stock_result.append(order_result)
                                else:
                                    detail_free_record_count = detail_free_record_count + 1
                                # 库存占用记录中组织机构编码是否正确
                                if stock_organization_code == organization_code:
                                    order_stock_compare_pass = order_stock_compare_pass + 1
                                else:
                                    order_stock_compare_fail = order_stock_compare_fail + 1
                                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                                    "detail_erp_code": detail_erp_code,
                                                    "compare_msg": "订单占用记录中机构编码错误", "right_value": organization_code,
                                                    "err_value": stock_organization_code}
                                    order_stock_result.append(order_result)
                                # 库存占用记录中线上门店编码是否正确
                                if stock_online_store_code == online_store_code:
                                    order_stock_compare_pass = order_stock_compare_pass + 1
                                else:
                                    order_stock_compare_fail = order_stock_compare_fail + 1
                                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                                    "detail_erp_code": detail_erp_code,
                                                    "compare_msg": "订单占用记录中机构编码错误",
                                                    "right_value": online_store_code,
                                                    "err_value": stock_online_store_code}
                                    order_stock_result.append(order_result)
                                # 库存占用记录中erp编码是否正确
                                if stock_erp_code == detail_erp_code:
                                    order_stock_compare_pass = order_stock_compare_pass + 1
                                else:
                                    order_stock_compare_fail = order_stock_compare_fail + 1
                                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                                    "detail_erp_code": detail_erp_code,
                                                    "compare_msg": "订单占用记录中erp编码错误",
                                                    "right_value": detail_erp_code,
                                                    "err_value": stock_erp_code}
                                    order_stock_result.append(order_result)
                        order_stock_record_result=order_stock_record_check(store_id,order_no,erp_state,detail_id,detail_erp_code,detail_commodity_count,detail_refund_count,detail_status,detail_occupy_record_count,detail_occupy_record_sum,detail_free_record_count,detail_free_record_sum)
                        order_stock_compare_pass=order_stock_compare_pass+order_stock_record_result['order_stock_compare_pass']
                        order_stock_compare_fail=order_stock_compare_fail+order_stock_record_result['order_stock_compare_fail']
                        order_stock_result.extend(order_stock_record_result['data'])
                    result_data = {"code": 60001, "msg": "订单侧库存占用记录检查情况","compare_pass":order_stock_compare_pass,"compare_fail":order_stock_compare_fail, "data": order_stock_result}
                    result.append(result_data)
                    # 商品侧根据订单号查询商品库存占用情况
                    commodity_stock_db=xy_order_stock_commodity_record(order_no)
                    commodity_stock_db_code=commodity_stock_db['code']
                    if commodity_stock_db_code==10000:
                        # 商品侧订单库存占用记录数据
                        commodity_stock_db_data=commodity_stock_db['data']['commodity_stock_record_data']
                        # 商品侧订单库存记录检查
                        commodity_stock_db_result=commodity_stock_record_check(order_no,third_platform_code,store_id,order_detail_data,order_stock_record_data,commodity_stock_db_data)
                        commodity_stock_compare_pass = commodity_stock_compare_pass+commodity_stock_db_result['commodity_stock_compare_pass']
                        commodity_stock_compare_fail = commodity_stock_compare_fail+commodity_stock_db_result['commodity_stock_compare_fail']
                        commodity_stock_result.extend(commodity_stock_db_result['data'])
                        result_data = {"code": 70001, "msg": "商品侧库存占用记录检查情况", "compare_pass": commodity_stock_compare_pass,
                                       "compare_fail": commodity_stock_compare_fail, "data": commodity_stock_result}
                        result.append(result_data)
                    else:
                        result_data = {"code": 50001, "msg": "商品侧查询商品详情及占用库存异常", "data": commodity_stock_db}
                        result.append(result_data)
                    # 商品可用库存情况比对
                    commodity_available_stock_result = commodity_available_stock_compare(store_id,
                                                                                         online_store_code,
                                                                                         order_erp_code_list)
                    commodity_available_stock_pass = commodity_available_stock_result['compare_pass']
                    commodity_available_stock_fail = commodity_available_stock_result['compare_fail']
                    commodity_available_stock_data = commodity_available_stock_result['data']
                    result_data = {"code": 80001, "msg": "商品可用库存比对结果",
                                   "compare_pass": commodity_available_stock_pass,
                                   "compare_fail": commodity_available_stock_fail,
                                   "data": commodity_available_stock_data}
                    result.append(result_data)

                else:
                    result_data = {"code": 40001, "msg": "订单侧查询商品详情及占用库存异常", "data": order_stock_db}
                    result.append(result_data)
            else:
                result_data = {"code": 30001, "msg": "根据组织机构码为查询到store_id", "data": store_result}
                result.append(result_data)
        else:
            result_data = {"code": 20001, "msg": "根据平台订单号未查询到系统订单号", "data": order_no_result}
            result.append(result_data)
        return result
    except Exception as e:
        raise e


"""
    订单侧库存占用记录情况
    
"""
@exception(logger)
def order_stock_record_check(store_id,order_no,erp_state,detail_id,detail_erp_code,detail_commodity_count,detail_refund_count,detail_status,detail_occupy_record_count,detail_occupy_record_sum,detail_free_record_count,detail_free_record_sum):
    try:
        order_stock_result = []
        # 比对数据统计
        order_stock_compare_pass = 0
        order_stock_compare_fail = 0
        # 判断商品是否占用失败
        if detail_occupy_record_sum==0:
            commodity_available_stock_result = xy_commodity_stock(store_id, detail_erp_code)
            commodity_available_stock_db_code = commodity_available_stock_result['code']
            if commodity_available_stock_db_code == 10000:
                commodity_available_stock=commodity_available_stock_result['data']['stock']
                if detail_commodity_count-detail_refund_count>commodity_available_stock:
                    order_stock_compare_pass = order_stock_compare_pass + 1
                    pass
                else:
                    order_stock_compare_fail = order_stock_compare_fail + 1
                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                    "detail_erp_code": detail_erp_code,
                                    "compare_msg": "查询到商品可用库存数量大于订单数量，但未占用成功，请人工确认",
                                    "right_value": commodity_available_stock,
                                    "err_value": detail_commodity_count-detail_refund_count}
                    order_stock_result.append(order_result)
        # 商品占用库存记录应始终大于1
        if detail_occupy_record_count >= 1:
            order_stock_compare_pass = order_stock_compare_pass + 1
            pass
        else:
            order_stock_compare_fail = order_stock_compare_fail + 1
            order_result = {"order_no": order_no, "detail_id": detail_id,
                            "detail_erp_code": detail_erp_code,
                            "compare_msg": "未查询到订单侧的占用记录",
                            "right_value": 1,
                            "err_value": detail_occupy_record_count}
            order_stock_result.append(order_result)
        # 商品释放成功数量小于商品占用成功数量
        if detail_free_record_sum <= detail_occupy_record_sum:
            order_stock_compare_pass = order_stock_compare_pass + 1
            pass
        else:
            order_stock_compare_fail = order_stock_compare_fail + 1
            order_result = {"order_no": order_no, "detail_id": detail_id,
                            "detail_erp_code": detail_erp_code,
                            "compare_msg": "商品释放库存成功数量不应大于占用数量",
                            "right_value": detail_occupy_record_sum,
                            "err_value": detail_free_record_sum}
            order_stock_result.append(order_result)
        # 订单下账状态为已下账(100)/已取消(110)时应释放所有占用库存
        if erp_state == 100 or erp_state == 110:
            # 商品占用成功数量大于0
            if detail_occupy_record_sum > 0:
                # 商品释放成功数量=商品占用成功数量
                if detail_free_record_sum == detail_occupy_record_sum:
                    order_stock_compare_pass = order_stock_compare_pass + 1
                    pass
                else:
                    order_stock_compare_fail = order_stock_compare_fail + 1
                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                    "detail_erp_code": detail_erp_code,
                                    "compare_msg": "商品释放数量应等于商品占用数量",
                                    "right_value": detail_occupy_record_sum,
                                    "err_value": detail_free_record_sum}
                    order_stock_result.append(order_result)
                # 商品释放记录数大于0
                if detail_free_record_count > 0:
                    order_stock_compare_pass = order_stock_compare_pass + 1
                    pass
                else:
                    order_stock_compare_fail = order_stock_compare_fail + 1
                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                    "detail_erp_code": detail_erp_code,
                                    "compare_msg": "商品释放记录应大于1",
                                    "right_value": 1,
                                    "err_value": detail_free_record_count}
                    order_stock_result.append(order_result)
            elif detail_occupy_record_sum == 0:
                # 商品释放成功数量等于0
                if detail_free_record_sum == 0:
                    order_stock_compare_pass = order_stock_compare_pass + 1
                    pass
                else:
                    order_stock_compare_fail = order_stock_compare_fail + 1
                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                    "detail_erp_code": detail_erp_code,
                                    "compare_msg": "商品占用成功数量为0的商品，释放数量应为0",
                                    "right_value": 0,
                                    "err_value": detail_free_record_sum}
                    order_stock_result.append(order_result)
                # 商品释放记录数等于0
                if detail_free_record_count == 0:
                    order_stock_compare_pass = order_stock_compare_pass + 1
                    pass
                else:
                    order_stock_compare_fail = order_stock_compare_fail + 1
                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                    "detail_erp_code": detail_erp_code,
                                    "compare_msg": "商品占用成功数量为0的商品，释放记录应为0",
                                    "right_value": 0,
                                    "err_value": detail_free_record_count}
                    order_stock_result.append(order_result)
            else:
                order_stock_compare_fail = order_stock_compare_fail + 1
                order_result = {"order_no": order_no, "detail_id": detail_id,
                                "detail_erp_code": detail_erp_code,
                                "compare_msg": "商品占用成功数量异常",
                                "right_value": detail_commodity_count,
                                "err_value": detail_occupy_record_sum}
                order_stock_result.append(order_result)
        else:
            # 商品库存不足或商品不存在时
            if detail_status == 1 or detail_status == 2:
                # 商品占用成功的库存应为0
                if detail_occupy_record_sum == 0:
                    order_stock_compare_pass = order_stock_compare_pass + 1
                    pass
                else:
                    order_stock_compare_fail = order_stock_compare_fail + 1
                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                    "detail_erp_code": detail_erp_code,
                                    "compare_msg": "商品不存在或库存不足时占用成功库存应为0",
                                    "right_value": 0,
                                    "err_value": detail_occupy_record_sum}
                    order_stock_result.append(order_result)
                # 商品释放记录数应等于0
                if detail_free_record_count == 0:
                    order_stock_compare_pass = order_stock_compare_pass + 1
                    pass
                else:
                    order_stock_compare_fail = order_stock_compare_fail + 1
                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                    "detail_erp_code": detail_erp_code,
                                    "compare_msg": "商品占用成功数量为0的商品，释放记录应为0",
                                    "right_value": 0,
                                    "err_value": detail_free_record_count}
                    order_stock_result.append(order_result)
            # 商品状态为正常
            elif detail_status == 0:
                # 占用成功数量等于订单详情中商品数量
                if detail_occupy_record_sum == detail_commodity_count:
                    #  退款数量等于释放数量
                    if detail_refund_count == detail_free_record_sum:
                        order_stock_compare_pass = order_stock_compare_pass + 1
                        pass
                    else:
                        order_stock_compare_fail = order_stock_compare_fail + 1
                        order_result = {"order_no": order_no, "detail_id": detail_id,
                                        "detail_erp_code": detail_erp_code,
                                        "compare_msg": "部分退款的商品，释放数量应等于退款数量",
                                        "right_value": detail_refund_count,
                                        "err_value": detail_free_record_sum}
                        order_stock_result.append(order_result)
                else:
                    # 占用成功数量应大于0
                    if detail_occupy_record_sum > 0:
                        order_stock_compare_pass = order_stock_compare_pass + 1
                        pass
                    else:
                        order_stock_compare_fail = order_stock_compare_fail + 1
                        order_result = {"order_no": order_no, "detail_id": detail_id,
                                        "detail_erp_code": detail_erp_code,
                                        "compare_msg": "状态正常的商品，占用成功数量应大于0",
                                        "right_value": detail_commodity_count,
                                        "err_value": detail_occupy_record_sum}
                        order_stock_result.append(order_result)
            else:
                # 商品占用成功数量大于0
                if detail_occupy_record_sum > 0:
                    # 商品释放成功数量=商品占用成功数量
                    if detail_free_record_sum == detail_occupy_record_sum:
                        order_stock_compare_pass = order_stock_compare_pass + 1
                        pass
                    else:
                        order_stock_compare_fail = order_stock_compare_fail + 1
                        order_result = {"order_no": order_no, "detail_id": detail_id,
                                        "detail_erp_code": detail_erp_code,
                                        "compare_msg": "商品释放数量应等于商品占用数量",
                                        "right_value": detail_occupy_record_sum,
                                        "err_value": detail_free_record_sum}
                        order_stock_result.append(order_result)
                    # 商品释放记录数大于0
                    if detail_free_record_count > 0:
                        order_stock_compare_pass = order_stock_compare_pass + 1
                        pass
                    else:
                        order_stock_compare_fail = order_stock_compare_fail + 1
                        order_result = {"order_no": order_no, "detail_id": detail_id,
                                        "detail_erp_code": detail_erp_code,
                                        "compare_msg": "商品释放记录应大于1",
                                        "right_value": 1,
                                        "err_value": detail_free_record_count}
                        order_stock_result.append(order_result)
                elif detail_occupy_record_sum == 0:
                    # 商品释放成功数量等于0
                    if detail_free_record_sum == 0:
                        order_stock_compare_pass = order_stock_compare_pass + 1
                        pass
                    else:
                        order_stock_compare_fail = order_stock_compare_fail + 1
                        order_result = {"order_no": order_no, "detail_id": detail_id,
                                        "detail_erp_code": detail_erp_code,
                                        "compare_msg": "商品占用成功数量为0的商品，释放数量应为0",
                                        "right_value": 0,
                                        "err_value": detail_free_record_sum}
                        order_stock_result.append(order_result)
                    # 商品释放记录数等于0
                    if detail_free_record_count == 0:
                        order_stock_compare_pass = order_stock_compare_pass + 1
                        pass
                    else:
                        order_stock_compare_fail = order_stock_compare_fail + 1
                        order_result = {"order_no": order_no, "detail_id": detail_id,
                                        "detail_erp_code": detail_erp_code,
                                        "compare_msg": "商品占用成功数量为0的商品，释放记录应为0",
                                        "right_value": 0,
                                        "err_value": detail_free_record_count}
                        order_stock_result.append(order_result)
                else:
                    order_stock_compare_fail = order_stock_compare_fail + 1
                    order_result = {"order_no": order_no, "detail_id": detail_id,
                                    "detail_erp_code": detail_erp_code,
                                    "compare_msg": "商品占用成功数量异常",
                                    "right_value": detail_commodity_count,
                                    "err_value": detail_occupy_record_sum}
                    order_stock_result.append(order_result)
        result={"order_stock_compare_pass":order_stock_compare_pass,"order_stock_compare_fail":order_stock_compare_fail,"data":order_stock_result}
        return result
    except Exception as e:
        raise e

"""
    商品侧数据库占用记录核对
"""
@exception(logger)
def commodity_stock_record_check(order_no,third_platform_code,store_id,order_detail_data,order_stock_record_data,commodity_stock_db_data):
    try:
        # 商品测试结果
        commodity_stock_result = []
        # 比对数据统计
        stock_compare_pass = 0
        stock_compare_fail = 0
        for order_detail_item in order_detail_data:
            # 详情ID
            detail_id = order_detail_item['id']
            # erp编码
            detail_erp_code = order_detail_item['erp_code']
            # 商品条码
            detail_bar_code=order_detail_item['bar_code']
            # 遍历订单侧库存占用记录数据
            for order_stock_item in order_stock_record_data:
                stock_order_detail_id = order_stock_item['order_detail_id']
                if detail_id==stock_order_detail_id:
                    stock_qty = filed_format_convert(order_stock_item['stock_qty'], 'int')
                    stock_organization_code = order_stock_item['organization_code']
                    stock_type = order_stock_item['type']
                    stock_serial_number = order_stock_item['serial_number']
                    stock_erp_code = order_stock_item['erp_code']
                    stock_online_store_code = order_stock_item['online_store_code']
                    # 遍历商品侧订单占用数据
                    for commodity_stock_item in commodity_stock_db_data:
                        commodity_serial_number=commodity_stock_item['serial_number']
                        commodity_erp_code=commodity_stock_item['erp_code']
                        if str(stock_serial_number)==str(commodity_serial_number) and str(stock_erp_code)==str(commodity_erp_code):
                            commodity_platform_code=commodity_stock_item['platform_code']
                            commodity_bar_code=commodity_stock_item['bar_code']
                            commodity_change_stock=filed_format_convert(commodity_stock_item['change_stock'],'int')
                            commodity_store_id=commodity_stock_item['store_id']
                            # 占用成功
                            if stock_type==1:
                                if filed_format_convert(value_mathematics(stock_qty,commodity_change_stock,"+"),'int')==0:
                                    stock_compare_pass=stock_compare_pass+1
                                    pass
                                else:
                                    stock_compare_fail=stock_compare_fail+1
                                    stok_result={"order_no": order_no, "detail_id": detail_id,"detail_erp_code": detail_erp_code,"compare_msg": "商品侧占用数量与订单侧不一致","right_value": stock_qty,"err_value": commodity_change_stock}
                                    commodity_stock_result.append(stok_result)
                            # 释放成功
                            elif stock_type==2:
                                if filed_format_convert(value_mathematics(stock_qty,commodity_change_stock,"-"),'int')==0:
                                    stock_compare_pass=stock_compare_pass+1
                                    pass
                                else:
                                    stock_compare_fail=stock_compare_fail+1
                                    stok_result={"order_no": order_no, "detail_id": detail_id,"detail_erp_code": detail_erp_code,"compare_msg": "商品侧释放数量与订单侧不一致","right_value": stock_qty,"err_value": commodity_change_stock}
                                    commodity_stock_result.append(stok_result)
                            # 其他状态
                            else:
                                stock_compare_fail = stock_compare_fail + 1
                                stok_result = {"order_no": order_no, "detail_id": detail_id,
                                               "detail_erp_code": detail_erp_code, "compare_msg": "订单侧占用/释放状态与商品侧不一致",
                                               "right_value": stock_qty, "err_value": commodity_change_stock}
                                commodity_stock_result.append(stok_result)
                            # 比对平台码是否正确
                            if str(commodity_platform_code)==str(third_platform_code):
                                stock_compare_pass = stock_compare_pass + 1
                                pass
                            else:
                                stock_compare_fail = stock_compare_fail + 1
                                stok_result = {"order_no": order_no, "detail_id": detail_id,
                                               "detail_erp_code": detail_erp_code, "compare_msg": "商品订单记录中平台码错误",
                                               "right_value": third_platform_code, "err_value": commodity_platform_code}
                                commodity_stock_result.append(stok_result)
                            # 比对商品条码是否正确
                            if str(detail_bar_code) == str(commodity_bar_code):
                                stock_compare_pass = stock_compare_pass + 1
                                pass
                            else:
                                stock_compare_fail = stock_compare_fail + 1
                                stok_result = {"order_no": order_no, "detail_id": detail_id,
                                               "detail_erp_code": detail_erp_code, "compare_msg": "商品订单记录中商品条码错误",
                                               "right_value": detail_bar_code,
                                               "err_value": commodity_bar_code}
                                commodity_stock_result.append(stok_result)
                            # 门店id是否正确
                            if str(commodity_store_id) == str(store_id):
                                stock_compare_pass = stock_compare_pass + 1
                                pass
                            else:
                                stock_compare_fail = stock_compare_fail + 1
                                stok_result = {"order_no": order_no, "detail_id": detail_id,
                                               "detail_erp_code": detail_erp_code, "compare_msg": "商品订单记录中门店ID错误",
                                               "right_value": store_id,
                                               "err_value": commodity_store_id}
                                commodity_stock_result.append(stok_result)
        # 订单侧占用记录数应大于商品侧记录
        if len(order_stock_record_data)>=len(commodity_stock_db_data):
            stock_compare_pass = stock_compare_pass + 1
            pass
        else:
            stock_compare_fail = stock_compare_fail + 1
            stok_result = {"order_no": order_no, "detail_id": detail_id,
                           "detail_erp_code": detail_erp_code, "compare_msg": "商品侧占用记录不能大于订单侧占用记录",
                           "right_value": store_id,
                           "err_value": commodity_store_id}
            commodity_stock_result.append(stok_result)
        result = {"commodity_stock_compare_pass": stock_compare_pass,"commodity_stock_compare_fail": stock_compare_fail, "data": commodity_stock_result}
        return result
    except Exception as e:
        raise e



"""
    商品可用库存数量核对
"""
@exception(logger)
def commodity_available_stock_compare(store_id,online_store_name,erp_code_list):
    try:
        result_info=[]
        compare_pass=0
        compare_fail=0
        for erp_code in erp_code_list:
            # 获取对应商品当前可用库存情况
            commodity_available_stock_db_result = xy_commodity_stock(store_id, erp_code)
            commodity_available_stock_db_code=commodity_available_stock_db_result['code']
            if commodity_available_stock_db_code==10000:
                commodity_available_stock_db_data=commodity_available_stock_db_result['data']
                # 数据库可用库存
                db_stock=commodity_available_stock_db_data['stock']
                # 数据库erp库存
                db_erp_stock=commodity_available_stock_db_data['erp_stock']
                # 数据库已占用库存
                db_occupy_stock=commodity_available_stock_db_data['occupy_stock']
                # 通过接口获取对应商品的可用库存情况
                commodity_available_stock_api_result=xy_commodity_stock_api(store_id,erp_code)
                commodity_available_stock_api_code=commodity_available_stock_api_result['code']
                if commodity_available_stock_api_code==10000:
                    commodity_available_stock_api_data=commodity_available_stock_api_result['data']
                    # 接口可用库存
                    api_stock=commodity_available_stock_api_data['stock']
                    # 接口erp库存
                    api_erp_stock=commodity_available_stock_api_data['erpstock']
                    # 接口已占用库存
                    api_occupy_stock=commodity_available_stock_api_data['occupystock']
                    # 可用库存比较
                    if db_stock==api_stock:
                        compare_pass=compare_pass+1
                        pass
                    else:
                        compare_fail=compare_fail+1
                        result_data={"msg":"心云系统数据库与接口可用库存不一致","erp_code":erp_code,"right_value":db_stock,"err_value":api_stock}
                        result_info.append(result_data)
                    # erp库存比较
                    if db_erp_stock==api_erp_stock:
                        compare_pass=compare_pass+1
                        pass
                    else:
                        compare_fail=compare_fail+1
                        result_data={"msg":"心云系统数据库与接口erp库存不一致","erp_code":erp_code,"right_value":db_erp_stock,"err_value":api_erp_stock}
                        result_info.append(result_data)
                    # 占用库存比较
                    if db_occupy_stock==api_occupy_stock:
                        compare_pass=compare_pass+1
                        pass
                    else:
                        compare_fail=compare_fail+1
                        result_data={"msg":"心云系统数据库与接口erp库存不一致","erp_code":erp_code,"right_value":db_occupy_stock,"err_value":api_occupy_stock}
                        result_info.append(result_data)

                    # 获取美团平台的库存情况
                    mt_available_stock_result = mt_commodity_stock_api(online_store_name, erp_code)
                    mt_available_stock_code = mt_available_stock_result['code']
                    if mt_available_stock_code == 10000:
                        mt_available_stock_data = mt_available_stock_result['data']
                        mt_stock=mt_available_stock_data['mt_stock']
                        # 可用库存比较
                        if db_stock == mt_stock:
                            compare_pass = compare_pass + 1
                            pass
                        else:
                            compare_fail = compare_fail + 1
                            result_data = {"msg": "心云系统美团可用库存不一致","erp_code":erp_code, "right_value": db_stock, "err_value": mt_stock}
                            result_info.append(result_data)

                    else:
                        result_data = {"code": 20001, "msg": f"根据门店ID:{online_store_name}及erp编码{erp_code}查询美团平台可用库存失败",
                                       "data": commodity_available_stock_db_result}
                        result_info.append(result_data)
                else:
                        result_data = {"code": 20001, "msg": f"根据门店ID:{store_id}及erp编码{erp_code}通过接口查询可用库存失败",
                                       "data": commodity_available_stock_db_result}
                        result_info.append(result_data)
            else:
                result_data = {"code": 20001, "msg": f"根据门店ID:{store_id}及erp编码{erp_code}通过数据库查询可用库存失败", "data": commodity_available_stock_db_result}
                result_info.append(result_data)
        result={"msg":"商品可用库存比对结果","compare_fail":compare_fail,"compare_pass":compare_pass,"data":result_info}
        return result
    except Exception as e:
        raise e

if __name__=="__main__":
    third_order_no="3801005770765544879"
    data=order_stock_check(third_order_no)
    print(data)
