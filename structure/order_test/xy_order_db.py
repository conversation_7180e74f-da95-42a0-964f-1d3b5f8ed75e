# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/6 14:53
@Auth ： 逗逗的小老鼠
@File ：xy_order_db.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

from lib.get_log import logger,exception
from lib.db_conf_test import db_yx_test_dsclound

"""
    根据平台订单号，查询系统订单号
    :param order_no:平台订单号
    :return result：订单详情
"""
@exception(logger)
def xy_order_no_query(third_order_no):
    "根据平台订单号，查询系统订单号"
    try:
        code=10000
        data={}
        query_sql=f"SELECT info.order_no AS 'order_no',info.lock_flag AS 'lock_flag',info.order_state AS 'order_state',info.erp_state AS 'erp_state',info.third_platform_code AS 'third_platform_code',info.organization_code AS 'organization_code',info.online_store_name AS 'online_store_name',info.online_store_code AS 'online_store_code',inner_dic.pos_mode AS 'pos_mode' FROM order_info info LEFT JOIN inner_store_dictionary inner_dic ON info.organization_code=inner_dic.organization_code WHERE third_order_no='{third_order_no}'"
        query_result=db_yx_test_dsclound(query_sql)
        if len(query_result['data'])==1:
            order_no=query_result['data'][0]['order_no']
            third_platform_code=query_result['data'][0]['third_platform_code']
            pos_mode=query_result['data'][0]['pos_mode']
            online_store_name=query_result['data'][0]['online_store_name']
            organization_code=query_result['data'][0]['organization_code']
            online_store_code=query_result['data'][0]['online_store_code']
            order_state=query_result['data'][0]['order_state']
            erp_state=query_result['data'][0]['erp_state']
            lock_flag=query_result['data'][0]['lock_flag']
            data={"code":code,"order_no":order_no,"order_state":order_state,"erp_state":erp_state,"lock_flag":lock_flag,"third_platform_code":third_platform_code,"online_store_name":online_store_name,"pos_mode":pos_mode,"organization_code":organization_code,"online_store_code":online_store_code}
        elif len(query_result['data'])>1:
            code=50000
        else:
            code=50001
        if code==10000:
            result={"code":code,"msg":"根据平台订单号查询系统订单成功","data":data}
        elif code==50000:
            result = {"code": code, "msg": "根据平台订单号出多条订单", "data": data}
        else:
            result = {"code": code, "msg": "根据平台订单号未查询到订单", "data": data}
        return result
    except Exception as e:
        raise e



"""
    根据系统订单号,查询订单相关信息
    :param order_no:平台订单号
    :return result：订单详情
"""
@exception(logger)
def xy_order_info_db(order_no):
    "根据系统订单号,查询订单基本信息"
    try:
        code=10000
        order_related_info={}
        # 订单基本信息查询
        order_info_sql=f"SELECT * FROM order_info WHERE order_no='{order_no}'"
        order_info_result=db_yx_test_dsclound(order_info_sql)
        order_info_data=order_info_result['data']
        if len(order_info_data)==1:
            order_info=order_info_data[0]
            order_related_info['order_info']=order_info
        else:
            code=50001
        # 订单收货信息查询
        order_delivery_address_sql = f"SELECT * FROM order_delivery_address WHERE order_no='{order_no}'"
        order_delivery_address_result = db_yx_test_dsclound(order_delivery_address_sql)
        order_delivery_address_data=order_delivery_address_result['data']
        if len(order_delivery_address_data) == 1:
            order_delivery_address = order_delivery_address_data[0]
            order_related_info['order_delivery_address'] = order_delivery_address
        else:
            code = 50002
        # 订单支付信息查询
        order_pay_info_sql = f"SELECT * FROM order_pay_info WHERE order_no='{order_no}'"
        order_pay_info_result = db_yx_test_dsclound(order_pay_info_sql)
        order_pay_info_data = order_pay_info_result['data']
        if len(order_pay_info_data) == 1:
            order_pay_info = order_pay_info_data[0]
            order_related_info['order_pay_info'] = order_pay_info
        else:
            code = 50003
        # 订单下账信息查询
        erp_bill_info_sql = f"SELECT * FROM erp_bill_info WHERE order_no='{order_no}'"
        erp_bill_info_result = db_yx_test_dsclound(erp_bill_info_sql)
        erp_bill_info_data = erp_bill_info_result['data']
        if len(erp_bill_info_data) == 1:
            erp_bill_info = erp_bill_info_data[0]
            order_related_info['erp_bill_info'] = erp_bill_info
        else:
            code = 50004
        # 订单商品详情查询
        order_detail_sql = f"SELECT * FROM order_detail WHERE order_no='{order_no}'"
        order_detail_result = db_yx_test_dsclound(order_detail_sql)
        order_detail_data = order_detail_result['data']
        if len(order_detail_data) >= 1:
            order_related_info['order_detail'] = order_detail_data
        else:
            code = 50005
        # 订单物流记录查询
        order_delivery_record_sql=f"SELECT * FROM order_delivery_record WHERE order_no='{order_no}' ORDER BY id DESC LIMIT 1"
        order_delivery_record_result=db_yx_test_dsclound(order_delivery_record_sql)
        order_delivery_record_data=order_delivery_record_result['data']
        if len(order_delivery_record_data)==1:
            order_related_info['order_delivery_record']=order_delivery_record_data[0]
        else:
            code = 50006
        if code==10000:
            result = {"code": code,"msg":"订单数据库关联信息查询成功", "order_info": order_related_info}
        elif code==50001:
            result = {"code": code, "msg": "订单基本信息查询失败", "order_info": order_related_info}
        elif code==50002:
            result = {"code": code, "msg": "订单收货信息查询失败", "order_info": order_related_info}
        elif code==50003:
            result = {"code": code, "msg": "订单支付信息查询失败", "order_info": order_related_info}
        elif code==50004:
            result = {"code": code, "msg": "订单下账信息查询失败", "order_info": order_related_info}
        elif code==50005:
            result = {"code": code, "msg": "订单商品详情查询失败", "order_info": order_related_info}
        else:
            result = {"code": code, "msg": "订单物流记录查询失败", "order_info": order_related_info}
        return result
    except Exception as e:
        raise e



if __name__=="__main__":
    order_no=1793466620258816770
    xy_order_info_db(order_no)
