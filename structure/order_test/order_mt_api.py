# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/4 10:33
@Auth ： 逗逗的小老鼠
@File ：order_mt_api.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import hashlib
import time
from lib.get_log import logger,exception
from lib.deal_ini import readini
from lib.deal_dict import deal_dict_del_dict
from lib.deal_format_convert import value_mathematics
from lib.db_conf import db_insight
import requests
import json
import urllib3
# 禁用https警告信息
urllib3.disable_warnings()

"""
    根据门店名称获取对应的美团appid和key
"""
@exception(logger)
def met_get_appvalue(online_store_name):
    try:
        app_id="123614"
        sign_key="ef5fdb477349f0301f8e7d924339c7b3"
        sql=f"""SELECT app_id,sign_key FROM mt_shop_test_list WHERE is_delete=0 AND shop_name='{online_store_name}'"""
        store_result=db_insight(sql)
        store_data=store_result['data']
        for item in store_data:
            app_id=item['app_id']
            sign_key=item['sign_key']
        return {"app_id":app_id,"sign_key":sign_key}
    except Exception as e:
        raise e


"""
    美团平台签名
    :param order_list:释放接口清单
    :return 
"""
@exception(logger)
def mt_get_sign(sign_key,data):
    try:
        data=data+sign_key
        print(data)
        sign = hashlib.md5(data.encode('utf-8')).hexdigest()
        return sign
    except Exception as e:
        raise e

"""
    美团平台入参调整
    :param data:接口业务请求参数
    :return param_dict：接口请求参数
"""
@exception(logger)
def mt_body_param(url,data,online_store_name):
    try:
        app_data=met_get_appvalue(online_store_name)
        sign_key=app_data['sign_key']
        app_id = app_data['app_id']
        sign_data=data
        sign_data["timestamp"] = int(time.time())
        # 对请求参数按照键值进行排序
        sorted_by_key = sorted(sign_data.items(), key=lambda x: x[0])
        # print("排序结果：", sorted_by_key)
        # 拼接请求地址
        param_str=url+f"?app_id={app_id}"
        for item in sorted_by_key:
            item_key=item[0]
            item_value=item[1]
            item_str=f"&{item_key}={item_value}"
            param_str=param_str+item_str
        # print(param_str)
        sig=mt_get_sign(sign_key,param_str)
        sign_url=param_str+f"&sig={sig}"
        # print(sign_url)
        return sign_url
    except Exception as e:
        raise e

"""
    美团订单详情获取
    :param data:接口业务请求参数
    :return param_dict：接口请求参数
"""
@exception(logger)
def xy_order_info_mt(order_id,online_store_name):
    try:
        order_info={}
        is_mt_logistics=1
        data={}
        data['order_id']=order_id
        data['is_mt_logistics']=is_mt_logistics
        url = "https://waimaiopen.meituan.com/api/v1/order/getOrderDetail"
        param=mt_body_param(url,data,online_store_name)
        res=requests.get(url=param)
        res_result = res.json()
        result_code=res_result['result_code']
        if result_code==1:
            order_result=res_result['data']
            base_info=deal_dict_del_dict(order_result)
            order_info['baseinfo']=base_info
            # 商品对账信息获取
            order_pay_info={}
            poi_receive_detail_yuan=json.loads(order_result['poi_receive_detail_yuan'])
            # 订单维度的打包袋金额
            package_bag_money_yuan=order_result['package_bag_money_yuan']
            for pay_item in poi_receive_detail_yuan:
                # 订单打包费
                if float(package_bag_money_yuan)==0 or package_bag_money_yuan=='0.00':
                    package_bag_money_yuan=0.5
                order_pay_info['package_bag_money_yuan']=package_bag_money_yuan
                # 订单佣金
                if pay_item=='foodShareFeeChargeByPoi':
                    foodShareFeeChargeByPoi=poi_receive_detail_yuan[pay_item]
                    order_pay_info['foodShareFeeChargeByPoi']=poi_receive_detail_yuan[pay_item]
                # 订单配送服务费
                if pay_item == "reconciliationExtras":
                    reconciliationExtras = json.loads(poi_receive_detail_yuan[pay_item])
                    if "performanceServiceFee" in reconciliationExtras.keys():
                        performanceServiceFee = reconciliationExtras['performanceServiceFee']
                        order_pay_info['foodShareFeeChargeByPoi']=value_mathematics(foodShareFeeChargeByPoi,performanceServiceFee,'+')
                # 用户实际支付
                if pay_item=="onlinePayment":
                    order_pay_info['onlinePayment']=poi_receive_detail_yuan[pay_item]
                # 商家预计收入
                if pay_item=="poiReceive":
                    order_pay_info['poiReceive']=poi_receive_detail_yuan[pay_item]
                # 原始配送费
                if pay_item=="logisticsFee":
                    order_pay_info['logisticsFee']=poi_receive_detail_yuan[pay_item]
            # 订单的商品详情获取
            order_detail=json.loads(order_result['detail'])
            order_detail_list=[]
            order_gift_list=[]
            for detail_item in order_detail:
                order_detail_info = {}
                # 订单内商品行维度的商品标识id。
                item_id=detail_item['item_id']
                # APP方商品id
                app_medicine_code=detail_item['app_medicine_code']
                # 商品名称
                food_name=detail_item['food_name']
                # 商品描述
                spec=detail_item['spec']
                # 商品的UPC码信息
                upc=detail_item['upc']
                # SKU码(商家的规格编码)
                sku_id=detail_item['sku_id']
                # 订单中此商品sku的购买数量
                quantity=detail_item['quantity']
                # 商品原价（打印小票时使用价格）
                original_price=detail_item['original_price']
                # 商品原价（订单详情显示价格，提供给计算下账金额）
                price=detail_item['original_price']
                # 商品现价
                actual_price=detail_item['actual_price']
                bill_price = actual_price
                order_detail_info['item_id']=item_id
                order_detail_info['app_medicine_code']=app_medicine_code
                order_detail_info['food_name'] = food_name
                order_detail_info['spec']=spec
                order_detail_info['upc']=upc
                order_detail_info['sku_id']=sku_id
                order_detail_info['quantity']=quantity
                order_detail_info['original_price']=original_price
                order_detail_info['price']=price
                order_detail_info['bill_price']=bill_price
                # 下账金额计算
                order_detail_info['actual_net_amount']=round((bill_price*quantity),2)
                # 商品原价总金额
                order_detail_info['total_amount']=round((price*quantity),2)
                # 优惠分摊金额
                discountShare=round((price * quantity), 2) - round((bill_price * quantity), 2)
                order_detail_info['discountShare'] = round(discountShare,2)
                order_detail_info['is_gift'] = 0
                order_detail_list.append(order_detail_info)
            order_detail_list=sorted(order_detail_list,key=lambda x: (x['item_id'] is None, x['item_id'] == "", x['item_id']))
            if "sku_benefit_detail" in order_result:
                order_sku_benefit_detail = json.loads(order_result['sku_benefit_detail'])
                for sku_benefit_item in order_sku_benefit_detail:
                    sku_sku_id = sku_benefit_item['sku_id']
                    sku_app_medicine_code = sku_benefit_item['app_medicine_code']
                    sku_upc = sku_benefit_item['upc']
                    activityPrice = sku_benefit_item['activityPrice']
                    # 活动商品数量
                    sku_count = sku_benefit_item['count']
                    # 商家承担优惠总金额
                    totalPoiCharge = sku_benefit_item['totalPoiCharge']
                    # 商品原价（订单详情显示价格，提供给计算下账金额）
                    price = sku_benefit_item['originPrice']
                    # 美团承担订单总优惠
                    totalMtCharge = sku_benefit_item['totalMtCharge']
                    # 商品推广总服务费
                    totalPromotionServiceFee = sku_benefit_item['totalPromotionServiceFee']
                    # 商品的总优惠金额
                    totalReducePrice = sku_benefit_item['totalReducePrice']
                    # 商品原始总价
                    totalOriginPrice = sku_benefit_item['totalOriginPrice']
                    benefit_quantity=0
                    benefit_totalPoiCharge=0.00
                    for order_detail_item in order_detail_list:
                        detail_app_medicine_code=order_detail_item['app_medicine_code']
                        detail_sku_id=order_detail_item['sku_id']
                        detail_upc=order_detail_item['upc']
                        detail_quantity=order_detail_item['quantity']
                        # 如果总优惠金额=商家优惠金额，则下账价格=活动价格
                        if detail_app_medicine_code == sku_app_medicine_code and detail_sku_id == sku_sku_id and detail_upc == sku_upc:
                            benefit_quantity=benefit_quantity+int(detail_quantity)
                            single_discount_share=round(totalPoiCharge/sku_count,2)
                            if int(sku_count)==int(benefit_quantity):
                                discountShare=round((totalPoiCharge-benefit_totalPoiCharge),2)
                            else:
                                # 详情中该条记录所分摊的商家商品优惠
                                discountShare=round((single_discount_share*detail_quantity),2)
                            # 订单中该条记录的下账价格
                            bill_price = round((price*detail_quantity - discountShare) / detail_quantity, 4)
                            benefit_totalPoiCharge=round((benefit_totalPoiCharge+discountShare),2)
                            #
                            order_detail_item['price']=price
                            # 下账单价计算
                            order_detail_item['bill_price'] = round(bill_price, 4)
                            # 下账金额计算
                            order_detail_item['actual_net_amount'] = round((bill_price * detail_quantity), 2)
                            # 商品原价总金额
                            order_detail_item['total_amount'] = round((price * detail_quantity), 2)
                            # 优惠分摊金额
                            order_detail_item['discountShare'] = round(discountShare, 2)
            # 美团承担商品优惠
            commodity_mt_charge=0
            # 商家承担商品优惠
            commodity_poi_charge = 0
            # 美团承担的运费优惠
            freight_mt_charge = 0
            # 商家承担的运费优惠
            freight_poi_charge=0
            # 美团承担赠品优惠
            gift_mt_charge = 0
            # 商家承担赠品优惠
            gift_poi_charge = 0
            # 从订单优惠信息中获取订单优惠详情
            if "extras" in order_result:
                order_extras=json.loads(order_result['extras'])
                for extras_item in order_extras:
                    order_gift_info = {}
                    order_detail_info = {}
                    # 美团承担的优惠金额
                    extras_mt_charge=extras_item['mt_charge']
                    # 商家承担的优惠金额
                    extras_poi_charge=extras_item['poi_charge']
                    extras_reduce_fee=extras_item['reduce_fee']
                    # 优惠活动类型
                    extras_type=extras_item['type']
                    # 总优惠金额=美团承担的金额+商家承担的金额
                    extras_total_charge=extras_poi_charge+extras_mt_charge
                    # 判断优惠信息类型：23为赠品类型
                    if extras_type==23:
                        # 判断是否存在赠品详情字段信息
                        if "act_extend_msg" in extras_item:
                            act_extend_msg=extras_item['act_extend_msg']
                            if "gift_num" in act_extend_msg:
                                gift_num=act_extend_msg['gift_num']
                            if "gifts_name" in act_extend_msg:
                                gift_name=act_extend_msg['gifts_name']
                            if "app_medicine_code" in act_extend_msg:
                                act_app_medicine_code=act_extend_msg['app_medicine_code']
                            # 如果存在gifts_app_medicine_code则该赠品为门店上架商品，会被放入订单商品详情列表中
                            if "gifts_app_medicine_code" in act_extend_msg:
                                gifts_app_medicine_code=act_extend_msg['gifts_app_medicine_code']
                                order_detail_info['item_id'] = ""
                                order_detail_info['app_medicine_code'] = gifts_app_medicine_code
                                order_detail_info['spec'] = ""
                                order_detail_info['upc'] = ""
                                order_detail_info['sku_id'] = gifts_app_medicine_code
                                order_detail_info['food_name'] = gift_name
                                order_detail_info['quantity'] = gift_num
                                order_detail_info['original_price'] = round((extras_total_charge)/gift_num,2)
                                order_detail_info['bill_price'] = 0.00
                                # 下账金额计算
                                order_detail_info['actual_net_amount'] = 0.00
                                # 商品原价总金额
                                order_detail_info['total_amount'] = extras_total_charge
                                # 优惠分摊金额
                                order_detail_info['discountShare'] = extras_total_charge
                                order_detail_info['is_gift'] = 1
                                order_detail_list.append(order_detail_info)
                            # 如果存在gifts_app_medicine_code则该赠品非门店上架商品，会进入order_gift_info库中，不会被放入订单商品详情列表中
                            else:
                                order_gift_info['app_medicine_code']=act_app_medicine_code
                                order_gift_info['gift_num']=gift_num
                                order_gift_info['gift_name']=gift_name
                                order_gift_list.append(order_gift_info)
                        gift_mt_charge=gift_mt_charge+extras_mt_charge
                        gift_poi_charge=gift_poi_charge+extras_poi_charge
                    # 判断优惠信息类型：25为运费优惠
                    elif extras_type==25:
                        freight_mt_charge=freight_mt_charge+extras_mt_charge
                        freight_poi_charge=freight_poi_charge+extras_poi_charge
                    else:
                        commodity_mt_charge=commodity_mt_charge+extras_mt_charge
                        commodity_poi_charge=commodity_poi_charge+extras_poi_charge
            # 美团承担的配送费优惠
            order_pay_info['freight_mt_charge']=round(freight_mt_charge,2)
            # 商家承担的配送费优惠
            order_pay_info['freight_poi_charge']=round(freight_poi_charge,2)
            # 美团承担商品优惠(不含赠品)
            order_pay_info['commodity_mt_charge']=round(commodity_mt_charge,2)
            # 商家承担商品优惠(不含赠品)
            order_pay_info['commodity_poi_charge']=round(commodity_poi_charge,2)
            # 美团承担商品优惠(含赠品)
            order_pay_info['commodity_gift_mt_charge'] = round(gift_mt_charge,2)+round(commodity_mt_charge,2)
            # 商家承担商品优惠(含赠品)
            order_pay_info['commodity_gift_poi_charge'] = round(gift_poi_charge,2)+round(commodity_poi_charge,2)
            # 美团承担的总优惠(不含赠品)
            order_pay_info['total_mt_charge']=round(freight_mt_charge,2)+round(commodity_mt_charge,2)
            # 美团承担的总优惠(含赠品)
            order_pay_info['total_gift_mt_charge'] = round(freight_mt_charge,2) + round(gift_mt_charge,2)+round(commodity_mt_charge,2)
            # 订单商品详情清单
            order_info['order_detail_list'] = order_detail_list
            # 订单非标品赠品清单
            order_info['order_gift_list'] = order_gift_list
            # 订单支付信息
            order_info['order_pay_info'] = order_pay_info
            result={"code":10000,"msg":"美团订单详情查询成功","order_info":order_info}
        else:
            err_msg=res_result['error']
            result={"code":50000,"msg":"美团订单详情查询失败","order_info":err_msg}
        print(result)
        return result
    except Exception as e:
        result = {"code": 50005, "msg": "美团订单详情查询异常", "order_info": e}
        return result



if __name__=="__main__":
    xy_order_info_mt(3801046452888637755,"t_ZJVcjLGmGm")
