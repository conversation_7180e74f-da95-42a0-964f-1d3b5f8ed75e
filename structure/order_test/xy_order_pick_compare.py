# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/3 14:33
@Auth ： 逗逗的小老鼠
@File ：xy_order_pick_compare.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception, logger
from structure.order_test.xy_order_db import xy_order_no_query
from structure.order_test.xy_order_pick_api import xy_order_getCachePickConfirmInfo,xy_order_querybatchstock,xy_order_queryDeliveryPlatform,xy_order_queryEmpByCondition
from structure.order_test.xy_order_pick_api import xy_order_upOrderBatchNo,xy_order_pickconfirm
from structure.order_test.xy_order_pick_db import xy_order_pick_info

"""
    订单拣货复核

"""
@exception(logger)
def order_pick(third_order_no):
    "订单拣货复核"
    try:
        # 根据三方订单号查询系统订单号
        order_no_result = xy_order_no_query(third_order_no)
        if order_no_result['code'] == 10000:
            order_no_data = order_no_result['data']
            order_no = order_no_data['order_no']
            online_store_code = order_no_data['online_store_code']
            organization_code = order_no_data['organization_code']
            order_state = order_no_data['order_state']
            erp_state = order_no_data['erp_state']
            third_platform_code = order_no_data['third_platform_code']
            lock_flag=order_no_data['lock_flag']
            # 判断订单状态是否为待拣货
            if order_state==20:
                # 判断订单是否为异常状态
                if lock_flag==0:
                    # 查询订单待拣货缓存信息
                    pick_cache_result=xy_order_getCachePickConfirmInfo(order_no)
                    if pick_cache_result['code']==10000:
                        pick_cache_data=pick_cache_result['data']
                        # 待拣货erp列表
                        cache_erp_list=[]
                        # 预存拣货信息[{"commodityBatchNo": "230307", "count": "1", "erpCode": "181351", "orderDetailId": 1617587,"purchasePrice": ""}]
                        cache_pick_list=[]
                        for pick_cache_item in pick_cache_data:
                            cache_pick_dict = {}
                            cache_erp_code=pick_cache_item['erpCode']
                            cache_detail_num=pick_cache_item['detailNum']
                            cache_detail_id=pick_cache_item['detailId']
                            cache_pick_dict['count']=cache_detail_num
                            cache_pick_dict['erpCode']=cache_erp_code
                            cache_pick_dict['orderDetailId']=cache_detail_id
                            cache_pick_dict['purchasePrice']=""
                            cache_pick_list.append(cache_pick_dict)
                            cache_erp_list.append(cache_erp_code)
                        # erp_list去重
                        batch_erp_list=list(set(cache_erp_list))
                        # 查询批号数据
                        batch_result=xy_order_querybatchstock(order_no,batch_erp_list,organization_code)
                        if batch_result['code']==10000:
                            batch_data=batch_result['data']
                            # 匹配商品批号
                            pick_batch_match_result=order_pick_batch_match(cache_pick_list,batch_data)
                            # 订单批号录入
                            up_batch_result=xy_order_upOrderBatchNo(order_no,pick_batch_match_result)
                            if up_batch_result['code']==10000:
                                # 获取配送方式
                                delivery_result=xy_order_queryDeliveryPlatform(order_no, third_platform_code, online_store_code)
                                if delivery_result['code']==10000:
                                    delivery_data=delivery_result['data']
                                    delivery_platformName=delivery_data[0]['platformName']
                                    # 获取拣货员信息
                                    emp_result=xy_order_queryEmpByCondition(organization_code)
                                    if emp_result['code']==10000:
                                        emp_data=emp_result['data']
                                        pickOperatorId=emp_data[0]['id']
                                        pickOperatorName=emp_data[0]['empName']
                                        # 订单拣货复核
                                        pick_confirm_result=xy_order_pickconfirm(order_no, delivery_platformName, pick_batch_match_result, pickOperatorId,
                                                             pickOperatorName)
                                        if pick_confirm_result['code']==10000:
                                            pick_confirm_data={"orderNo":order_no,"deliveryPlatform":delivery_platformName,"orderState":20,"pickDetailList":pick_batch_match_result,"pickOperatorId":pickOperatorId,"pickOperatorName":pickOperatorName}
                                            result = {"code": 10000, "msg": "订单拣货复核成功", "data": pick_confirm_data}
                                        else:
                                            result = {"code": 20009, "msg": "订单拣货复核失败", "data": pick_confirm_result}
                                    else:
                                        result = {"code": 20008, "msg": "订单拣货员信息获取失败", "data": emp_result}
                                else:
                                    result = {"code": 20007, "msg": "订单配送方式获取失败", "data": delivery_result}
                            else:
                                result = {"code": 20006, "msg": "订单批号录入失败", "data": up_batch_result}
                        else:
                            result = {"code": 20005, "msg": "查询批号信息失败", "data": pick_cache_result}
                    else:
                        result = {"code": 20004, "msg": "查询订单待拣货缓存信息失败", "data": pick_cache_result}
                else:
                    result = {"code": 20003, "msg": "该订单非处于异常状态，请先进行异常处理", "data": order_no_result}
            else:
                result = {"code": 20002, "msg": "该订单非“待拣货”状态，不可进行拣货操作", "data": order_no_result}
        else:
            result = {"code": 20001, "msg": "根据平台订单号未查询到系统订单号", "data": order_no_result}
        return result
    except Exception as e:
        raise e


"""
    拣货批号信息计算
"""
@exception(logger)
def order_pick_batch_match(pick_list,batch_list):
    try:
        # 重新定义拣货列表数据
        pick_data_list=[]
        # 遍历待拣货的商品列表
        for pick_item in pick_list:
            pick_data={}
            item_erpCode=pick_item['erpCode']
            item_count=pick_item['count']
            item_detailid=pick_item['orderDetailId']
            # 遍历商品批号列表
            for batch_item in batch_list:
                batch_erp=batch_item['erp_code']
                batch_data=batch_item['batch_list']
                # 批号erpcode与商品erpcode匹配
                if item_erpCode==batch_erp:
                    # 遍历对应erpcode下的批号列表
                    for batch_data_item in batch_data:
                        # 批号
                        makeno=batch_data_item['makeno']
                        # 数量
                        wareqty=int(float(batch_data_item['wareqty']))
                        used_qty=0
                        # 遍历重新组装的拣货信息中的已匹配的批号信息
                        for pick_data_item in pick_data_list:
                            pick_data_erpcode=pick_data_item['erpCode']
                            pick_data_commodityBatchNo=pick_data_item['commodityBatchNo']
                            pick_data_count=pick_data_item['count']
                            # erpcode和批号信息匹配，则计算其批号已占用的库存数量
                            if batch_erp==pick_data_erpcode and makeno==pick_data_commodityBatchNo:
                                used_qty=used_qty+int(pick_data_count)
                        # 判断当前批号库存数量是否大于待拣货数量和已匹配的库存数量，若大于，则重新组装拣货信息
                        if wareqty>=used_qty+int(item_count):
                            # {"commodityBatchNo": "230307", "count": "1", "erpCode": "181351", "orderDetailId": 1617587,"purchasePrice": ""}
                            pick_data["commodityBatchNo"]=makeno
                            pick_data['count']=int(item_count)
                            pick_data['erpCode']=item_erpCode
                            pick_data['orderDetailId']=item_detailid
                            pick_data['purchasePrice']=""
                            pick_data_list.append(pick_data)
                            break
        return pick_data_list
    except Exception as e:
        raise e


"""
    拣货信息比对
"""
@exception(logger)
def order_pick_info_compare(third_order_no):
    try:
        result=[]
        compare_pass=0
        compare_fail=0
        order_pick_result=order_pick(third_order_no)
        if order_pick_result['code']==10000:
            order_pick_data=order_pick_result['data']
            order_no=order_pick_data['order_no']
            deliveryPlatform=order_pick_data['deliveryPlatform']
            pickDetailList=order_pick_data['pickDetailList']
            pickOperatorId=order_pick_data['pickOperatorId']
            pickOperatorName=order_pick_data['pickOperatorName']
            # 订单拣货详情数据数据库查询
            order_pick_detail_result=xy_order_pick_info(order_no)
            if order_pick_detail_result['code']==10000:
                order_pick_detail_data=order_pick_detail_result['data']


            else:
                result_data = {"code": 20001, "msg": "订单拣货详情数据（数据库）查询失败", "compare_pass": compare_pass,
                               "compare_fail": compare_fail, "data": order_pick_detail_result}


        else:
            result_data = {"code": 20001, "msg": "订单拣货失败","compare_pass": compare_pass,"compare_fail": compare_fail, "data": order_pick_result}
            result.append(result_data)
        return result
    except Exception as e:
        raise e