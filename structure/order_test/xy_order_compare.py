# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/14 17:35
@Auth ： 逗逗的小老鼠
@File ：xy_order_compare.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json

from lib.get_log import logger,exception
from structure.order_test.xy_order_db import xy_order_no_query,xy_order_info_db
from structure.order_test.xy_order_api import xy_order_info_api
from structure.order_test.order_mt_api import xy_order_info_mt
from structure.order_test.order_eb_api import xy_order_info_eb
from structure.order_test.xy_order_filed_map_compare import xy_order_filed_compare,xy_order_filed_map_list,xy_order_filed_value_map_list
from lib.deal_format_convert import null_to_None
from lib.deal_format_convert import filed_format_convert,value_mathematics
from datetime import datetime
from lib.deal_excel import write_excel


"""
    订单数据比对
    :param 
    :return filed_map_data：字段映射清单
"""
@exception(logger)
def xy_order_info_compare(third_order_no):
    "订单数据比对"
    try:
        last_result=[]
        result=[]
        # 比对数据统计
        compare_pass = 0
        compare_fail = 0
        # 根据三方订单号查询系统订单号
        order_no_result=xy_order_no_query(third_order_no)
        if order_no_result['code']==10000:
            order_no_data=order_no_result['data']
            order_no=order_no_data['order_no']
            third_platform_code=order_no_data['third_platform_code']
            pos_mode=order_no_data['pos_mode']
            online_store_name=order_no_data['online_store_name']
            online_store_code=order_no_data['online_store_code']
            # 通过数据库查询订单信息
            db_order_info_result=xy_order_info_db(order_no)
            if db_order_info_result['code']==10000:
                db_order_info_data=db_order_info_result['order_info']
                # 通过api查询订单信息
                api_order_info_result=xy_order_info_api(order_no)
                if api_order_info_result['code']==10000:
                    api_order_info_data=api_order_info_result['order_info']
                    # 判断三方平台码，根据平台码通过对应平台查询订单详情
                    # 美团
                    if third_platform_code=='27':
                        platform_order_info_result=xy_order_info_mt(third_order_no,online_store_name)
                    # 饿百
                    if third_platform_code=='24':
                        platform_order_info_result = xy_order_info_eb(third_order_no, online_store_code)
                    if platform_order_info_result['code']==10000:
                        platform_order_info_data=platform_order_info_result['order_info']
                        # 平台配送码
                        logistics_code=platform_order_info_data['baseinfo']['logistics_code']
                        # 心云系统中订单信息自行比对
                        xy_compare_result=xy_order_compare_amount(api_order_info_data)
                        order_self_xy_result = {"compare_msg": "心云接口信息自行比对",
                                          "compare_pass": xy_compare_result['compare_pass'],
                                          "compare_fail": xy_compare_result['compare_fail'],
                                          "fail_info": xy_compare_result['fail_info']}
                        last_result.append(order_self_xy_result)

                        # 获取比对字段列表
                        compare_filed_result = xy_order_filed_map_list(pos_mode)
                        compare_filed_list = compare_filed_result
                        for compare_filed_item in compare_filed_list:
                            # 比对字段名称
                            comparison_name=compare_filed_item['comparison_name']
                            # 比对字段格式
                            comparison_format=compare_filed_item['comparison_format']
                            # 心云接口标识
                            xy_json_flag=compare_filed_item['xy_json_flag']
                            # 心云接口字段
                            xy_field=compare_filed_item['xy_field']
                            # 数据库表名
                            db_table=compare_filed_item['db_table']
                            # 数据库字段名称
                            db_field=compare_filed_item['db_field']
                            # 是否二次处理
                            is_secon_treat=compare_filed_item['is_secon_treat']
                            # 是否属于list
                            is_list_treat=compare_filed_item['is_list_treat']
                            # 是否区分POS平台
                            is_diff_pos=compare_filed_item['is_diff_pos']
                            # 是否进行值映射转换
                            is_map_trans=compare_filed_item['is_map_trans']

                            # 根据平台类型获取平台对应字段信息
                            if third_platform_code=='27':
                                platform_json_flag=compare_filed_item['platform_mt_json_flag']
                                platform_json_field=compare_filed_item['platform_mt_json_field']
                            if third_platform_code=='24':
                                platform_json_flag = compare_filed_item['platform_eb_json_flag']
                                platform_json_field = compare_filed_item['platform_eb_json_fleld']
                            # 判断xy_json_flag和xy_field是否为空值
                            if xy_json_flag != None and xy_field != None:
                                # 是否需要单独进行数据处理
                                if is_secon_treat==0:
                                    # 数据格式是否为列表，如果为列表需要遍历比对
                                    if is_list_treat==1:
                                        # 商品详情数据需要单独比对
                                        if xy_json_flag=="orderDetailList":
                                            xy_detail_list = api_order_info_data[xy_json_flag]
                                            for xy_detail_item in xy_detail_list:
                                                # 获取商品详情的平台详情ID
                                                xy_thirdDetailId=xy_detail_item['thirdDetailId']
                                                # 获取商品详情对应比对字段的值
                                                xy_value = xy_detail_item[xy_field]
                                                # 获取商品是否为赠品的标识
                                                xy_gift_flag=xy_detail_item['isGift']
                                                # 获取商品的skuid
                                                xy_platformSkuId=xy_detail_item['platformSkuId']
                                                # 判断心云数据库是否存在匹配的比对字段
                                                if null_to_None(db_table) != None and null_to_None(db_field) != None:
                                                    db_detail_list=db_order_info_data[db_table]
                                                    # 遍历心云数据库中对应的字段值
                                                    for db_detail_item in db_detail_list:
                                                        db_third_detail_id=db_detail_item['third_detail_id']
                                                        # 根据third_detail_id进行值的匹配
                                                        if db_third_detail_id==xy_thirdDetailId:
                                                            db_value=db_detail_item[db_field]
                                                else:
                                                    db_value=None
                                                # 判断平台是否存在匹配的比对字段
                                                if null_to_None(platform_json_flag) != None and null_to_None(platform_json_field) != None:
                                                    platform_detail_list=platform_order_info_data[platform_json_flag]
                                                    # 遍历平台数据
                                                    if third_platform_code=='27':
                                                        for platform_detail_item in platform_detail_list:
                                                            platform_item_id=platform_detail_item['item_id']
                                                            platform_gift_flag=platform_detail_item['is_gift']
                                                            platform_sku_id=platform_detail_item['sku_id']
                                                            # 如果心云接口中该商品为赠品,则根据平台信息中的sku_id和赠品标识判断
                                                            if xy_gift_flag==1:
                                                                if platform_gift_flag==1 and xy_platformSkuId==platform_sku_id:
                                                                    platform_value = platform_detail_item[platform_json_field]
                                                                    # 因平台返回的赠品信息中，无相关值，心云平台存值为自行生成值，故该字段直接赋值为心云的值
                                                                    if (platform_json_field=='item_id' or platform_json_field=='spec' or platform_json_field=='upc') and platform_json_flag=='order_detail_list':
                                                                        if platform_value=="":
                                                                            platform_value=xy_value
                                                            # 如果该商品不是赠品，则根据item_id与心云接口中的thirdDetailId进行数据匹配
                                                            else:
                                                                if str(platform_item_id)==xy_thirdDetailId:
                                                                    platform_value=platform_detail_item[platform_json_field]
                                                    if third_platform_code=='24':
                                                        for platform_detail_item in platform_detail_list:
                                                            platform_item_id=platform_detail_item['baidu_product_id']
                                                            if str(xy_platformSkuId)==str(platform_item_id):
                                                                platform_value = platform_detail_item[platform_json_field]
                                                else:
                                                    platform_value=None
                                                # # 分摊金额及下账金额,下账价格，暂不使用平台计算值
                                                # if xy_field == "discountShare" or xy_field == "actualNetAmount" or xy_field=='billPrice':
                                                #     xy_value=filed_format_convert(xy_value,'float')
                                                #     db_value=filed_format_convert(db_value,'float')
                                                #     if xy_value - db_value==0:
                                                #         compare_pass = compare_pass + 1
                                                #         pass
                                                #     else:
                                                #         compare_fail = compare_fail + 1
                                                #         result_data={"msg":"商品分摊金额和下账金额比对结果不一致","comparison_name":comparison_name,"xy_thirdDetailId":xy_thirdDetailId,"xy_value":xy_value,"db_value":db_value,"platform_value":platform_value}
                                                #         result.append(result_data)
                                                # else:
                                                compare_result=xy_order_filed_compare(comparison_format,xy_value,db_value,platform_value)
                                                if compare_result:
                                                    compare_pass=compare_pass+1
                                                    pass
                                                else:
                                                    compare_fail=compare_fail+1
                                                    result_data={"msg":"商品详情比对结果不一致","comparison_name":comparison_name,"xy_thirdDetailId":xy_thirdDetailId,"xy_value":xy_value,"db_value":db_value,"platform_value":platform_value}
                                                    result.append(result_data)
                                    else:
                                        xy_value = api_order_info_data[xy_json_flag][xy_field]
                                        if null_to_None(db_table) != None and null_to_None(db_field) != None:
                                            db_value = db_order_info_data[db_table][db_field]
                                        else:
                                            db_value = None
                                        if null_to_None(platform_json_flag) != None and null_to_None(platform_json_field) != None:
                                            platform_value = platform_order_info_data[platform_json_flag][platform_json_field]
                                        else:
                                            platform_value = None
                                        compare_result = xy_order_filed_compare(comparison_format, xy_value, db_value,platform_value)
                                        if compare_result:
                                            compare_pass = compare_pass + 1
                                            pass
                                        else:
                                            compare_fail = compare_fail + 1
                                            result_data = {"msg": "比对结果不一致", "comparison_name": comparison_name,"xy_value": xy_value,"db_value": db_value, "platform_value": platform_value}
                                            result.append(result_data)
                                # 需要进行二次处理的字段逻辑
                                else:
                                    xy_value = api_order_info_data[xy_json_flag][xy_field]
                                    if null_to_None(db_table) != None and null_to_None(db_field) != None:
                                        db_value = db_order_info_data[db_table][db_field]
                                    else:
                                        db_value = None
                                    if null_to_None(platform_json_flag) != None and null_to_None(platform_json_field) != None:
                                        platform_value = platform_order_info_data[platform_json_flag][platform_json_field]
                                    else:
                                        platform_value = None
                                    # thirdOrderState字段补充逻辑
                                    if xy_json_flag=='baseinfo' and xy_field=='thirdOrderState':
                                        compare_result=xy_order_compare_thirdOrderState(comparison_format,xy_value,db_value,platform_value,third_platform_code)
                                        if compare_result:
                                            compare_pass = compare_pass + 1
                                            pass
                                        else:
                                            compare_fail = compare_fail + 1
                                            result_data = {"msg": "比对结果不一致", "comparison_name": comparison_name,"xy_value": xy_value,"db_value": db_value, "platform_value": platform_value}
                                            result.append(result_data)
                                    if xy_json_flag=="orderDeliveryAddress":
                                        # 如果字段名称为receiverTelephone/receiverMobile，则只需要比对接口值与平台值，无需比对数据库值(数据库值为加密值)
                                        if xy_field=='receiverTelephone' or xy_field=='receiverMobile':
                                            if xy_value==platform_value:
                                                compare_pass = compare_pass + 1
                                                pass
                                            else:
                                                compare_fail = compare_fail + 1
                                                result_data = {"msg": "比对结果不一致", "comparison_name": comparison_name,"xy_value": xy_value, "db_value": db_value,"platform_value": platform_value}
                                                result.append(result_data)
                                    if xy_json_flag=="erpBillInfo":
                                        # 下账配置ID:clientConfId
                                        if xy_field=='clientConfId':
                                            bill_code=xy_order_compare_clientConfId(third_platform_code,logistics_code)
                                            compare_result = xy_order_filed_compare(comparison_format, xy_value,db_value, bill_code)
                                            if compare_result :
                                                compare_pass = compare_pass + 1
                                                pass
                                            else:
                                                compare_fail = compare_fail + 1
                                                result_data = {"msg": "比对结果不一致", "comparison_name": comparison_name,"xy_value": xy_value, "db_value": db_value,"platform_value": bill_code}
                                                result.append(result_data)

                                    # 需要进行值映射的字段
                                    if is_map_trans==1:
                                        compare_result=xy_order_compare_filed_map(platform_json_field,comparison_format,xy_value,db_value,platform_value,third_platform_code,extend_flag=xy_field)
                                        if compare_result:
                                            compare_pass = compare_pass + 1
                                            pass
                                        else:
                                            compare_fail = compare_fail + 1
                                            result_data = {"msg": "比对结果不一致", "comparison_name": comparison_name,"xy_value": xy_value,"db_value": db_value, "platform_value": platform_value}
                                            result.append(result_data)

                    else:
                        result_data = {"code": 50001, "msg": "根据平台订单号通过平台接口未查询到对应订单信息", "data": platform_order_info_result}
                        result.append(result_data)
                else:
                    result_data = {"code": 40001, "msg": "根据系统订单号通过接口未查询到对应订单信息", "data": api_order_info_result}
                    result.append(result_data)
            else:
                result_data = {"code": 30001, "msg": "根据系统订单号通过数据库未查询到对应订单信息", "data": db_order_info_result}
                result.append(result_data)
        else:
            result_data={"code":20001,"msg":"根据平台订单号未查询到系统订单号","data":order_no_result}
            result.append(result_data)
        order_cross_paltform_result={"compare_msg":"订单信息跨平台比对：数据库&心云接口&对应平台","compare_pass":compare_pass,"compare_fail":compare_fail,"fail_info":result}
        last_result.append(order_cross_paltform_result)
        return last_result
    except Exception as e:
        raise e


"""
    字段：三方平台状态比对
    :param xy_value：心云接口值
    :param db_value：心云数据库值
    :param platform_value：平台值
    :param platform_code：平台编码
    :param comparison_format:转换字符类型
    :return filed_map_data：字段映射清单
"""
@exception(logger)
def xy_order_compare_thirdOrderState(comparison_format,xy_value,db_value,platform_value,platform_code):
    "订单数据比对"
    try:
        # 心云存储订单状态应为：平台编码+_+平台状态码
        platform_val=f"{platform_code}_{platform_value}"
        compare_result = xy_order_filed_compare(comparison_format, xy_value, db_value, platform_val)
        return compare_result
    except Exception as e:
        raise e


"""
    字段映射值比对
    :param comparison_filed:平台比对字段
    :param xy_value：心云接口值
    :param db_value：心云数据库值
    :param platform_value：平台值
    :param platform_code：平台编码
    :param comparison_format:转换字符类型
    :param kwargs：扩展查询字段信息
    :return filed_map_data：字段映射清单
"""
@exception(logger)
def xy_order_compare_filed_map(platform_filed,comparison_format,xy_value,db_value,platform_value,platform_code,**kwargs):
    "字段映射值比对"
    try:
        compare_result=False
        platform_val_result=xy_order_filed_value_map_list(platform_filed,platform_value,platform_code,**kwargs)
        for item in platform_val_result:
            platform_val=item['xy_map_value']
            compare_result = xy_order_filed_compare(comparison_format, xy_value, db_value, platform_val)
            if compare_result:
                break
        print(compare_result)
        return compare_result
    except Exception as e:
        raise e


"""
    订单详情中，金额信息自行比对
    :param order_info:订单详情信息
    :return filed_map_data：字段映射清单
"""
@exception(logger)
def xy_order_compare_amount(order_info):
    try:
        result=[]
        com_pass = 0
        com_fail = 0
        '''
            订单支付信息比对
        '''
        # 订单支付信息
        orderPayInfo=order_info['orderPayInfo']
        # 商品总金额
        totalAmount=filed_format_convert(orderPayInfo['totalAmount'],'float')
        # 订单总优惠
        totalDiscount = filed_format_convert(orderPayInfo['totalDiscount'], 'float')
        # 商家总优惠
        merchantTotalDiscountSum=filed_format_convert(orderPayInfo['merchantTotalDiscountSum'],'float')
        # 商家商品优惠
        merchantTotalDiscountSumNotDeliveryFee=filed_format_convert(orderPayInfo['merchantTotalDiscountSumNotDeliveryFee'],'float')
        # 商家配送费优惠
        merchantDeliveryFeeDiscount=filed_format_convert(orderPayInfo['merchantDeliveryFeeDiscount'],'float')
        # 配送费原始值
        deliveryFee=filed_format_convert(orderPayInfo['deliveryFee'],'float')
        # 平台总优惠
        platformDiscount=filed_format_convert(orderPayInfo['platformDiscount'],'float')
        # 订单总优惠=商家总优惠+平台总优惠
        if totalDiscount==value_mathematics(merchantTotalDiscountSum,platformDiscount,'+'):
            com_pass=com_pass+1
            pass
        else:
            com_fail = com_fail + 1
            com_result = {'msg': "订单总优惠应等于商家总优惠+平台总优惠", "right_value": totalAmount,"error_value": merchantTotalDiscountSum + platformDiscount}
            result.append(com_result)
        # 商家总优惠=商家商品优惠+商家配送费优惠
        if merchantTotalDiscountSum == value_mathematics(merchantTotalDiscountSumNotDeliveryFee,merchantDeliveryFeeDiscount,'+'):
            com_pass = com_pass + 1
            pass
        else:
            com_fail = com_fail + 1
            com_result = {'msg': "商家总优惠应等于商家商品优惠+商家配送费优惠", "right_value": merchantTotalDiscountSum,"error_value": merchantTotalDiscountSumNotDeliveryFee + merchantDeliveryFeeDiscount}
            result.append(com_result)
        '''
            订单支付信息比对
        '''
        # 订单商品详情
        # 商品总下账金额
        total_bill_amount=0
        # 商品分摊总金额
        total_discountShare=0
        orderDetailList=order_info['orderDetailList']
        for detail_item in orderDetailList:
            # 第三方详情ID
            thirdDetailId=detail_item['thirdDetailId']
            # 优惠分摊
            discountShare=filed_format_convert(detail_item['discountShare'],'float')
            total_discountShare=value_mathematics(total_discountShare,discountShare,'+')
            # 下账金额
            actualNetAmount=filed_format_convert(detail_item['actualNetAmount'],'float')
            total_bill_amount=value_mathematics(total_bill_amount,actualNetAmount,'+')
            # 商品原价总金额
            totalAmount=filed_format_convert(detail_item['totalAmount'],'float')
            # 商品原价总金额=优惠分摊+下账金额
            if totalAmount==value_mathematics(discountShare,actualNetAmount,'+'):
                com_pass=com_pass+1
                pass
            else:
                com_fail = com_fail + 1
                com_result = {'msg': f"{thirdDetailId}:商品原价总金额应等于优惠分摊+下账金额", "right_value": totalAmount,"error_value": discountShare + actualNetAmount}
                result.append(com_result)
        # 商品分摊总金额=商家商品优惠
        if total_discountShare==merchantTotalDiscountSumNotDeliveryFee:
            com_pass=com_pass+1
            pass
        else:
            com_result = {'msg': "商品分摊总金额应等于商家商品优惠", "right_value": total_discountShare,"error_value": merchantTotalDiscountSumNotDeliveryFee}
            com_fail=com_fail+1
            result.append(com_result)
        '''
            下账信息比对        
        '''
        # 订单下账信息
        erpBillInfo = order_info['erpBillInfo']
        # 下账配置ID
        clientConfId = filed_format_convert(erpBillInfo['clientConfId'], 'float')
        # 下账总金额
        billTotalAmount = filed_format_convert(erpBillInfo['billTotalAmount'], 'float')
        # 下账商品金额
        billCommodityAmount = filed_format_convert(erpBillInfo['billCommodityAmount'], 'float')
        # 下账商家配送费
        merchantDeliveryFee = filed_format_convert(erpBillInfo['merchantDeliveryFee'], 'float')
        # 下账总金额应=下账商品金额+商家配送费
        if billTotalAmount == value_mathematics(billCommodityAmount,merchantDeliveryFee,'+'):
            com_pass = com_pass + 1
            pass
        else:
            com_fail = com_fail + 1
            com_result = {'msg': "下账总金额应等于下账商品金额+商家配送费", "right_value": billTotalAmount,"error_value": billCommodityAmount + merchantDeliveryFee}
            result.append(com_result)
        # 下账商品金额=商品总下账金额
        if billCommodityAmount==total_bill_amount:
            com_pass=com_pass+1
            pass
        else:
            com_fail=com_fail+1
            com_result = {'msg': "下账商品金额应等于商品总下账金额", "right_value": billCommodityAmount,"error_value": total_bill_amount}
            result.append(com_result)
        # 自配送下账
        if clientConfId==404:
            # 下账商家配送费=配送费-商家配送费优惠
            if merchantDeliveryFee==value_mathematics(deliveryFee,merchantDeliveryFeeDiscount,'-'):
                com_pass=com_pass+1
                pass
            else:
                com_fail = com_fail + 1
                com_result = {'msg': "自配送下账：下账商家配送费应等于配送费-商家配送费优惠", "right_value": merchantDeliveryFee,"error_value": deliveryFee-merchantDeliveryFeeDiscount}
                result.append(com_result)
        # 平台配送给
        elif clientConfId==202:
            # 下账商家配送费=配送费-商家配送费优惠
            if merchantDeliveryFee == 0:
                com_pass = com_pass + 1
                pass
            else:
                com_fail = com_fail + 1
                com_result = {'msg': "平台配送下账：下账商家配送费应等于0", "right_value": 0,"error_value": merchantDeliveryFee}
                result.append(com_result)
        bill_zero_list=["platformDiscount","merchantDiscount","platformDeliveryFee","merchantPackFee","platformPackFee","detailDiscountAmount","platBrokerageAmount","orderTotalAmount","merchantActualAmount"]
        for bill_key in erpBillInfo:
            if bill_key in bill_zero_list:
                bill_value=filed_format_convert(erpBillInfo[bill_key],'float')
                if bill_value==0:
                    com_pass=com_pass+1
                    pass
                else:
                    com_fail=com_fail+1
                    com_result = {'msg': f"下账字段：{bill_key}应等于0", "right_value": 0, "error_value": bill_value}
                    result.append(com_result)
        return {"compare_fail":com_fail,"compare_pass":com_pass,"fail_info":result}
    except Exception as e:
        raise e


"""
    订单下账配置id比对
    :param thirdPlatformCode:平台编码
    :param logistics_code:平台配送码
    :return bill_code:下账编码
    
    
"""
def xy_order_compare_clientConfId(thirdPlatformCode,logistics_code):
    try:
        bill_code=0
        # 美团平台判断下账配置ID
        if thirdPlatformCode=='27':
            # 1001：美团加盟；2002：快送；3001：混合送
            if logistics_code=='1001' or logistics_code=='2002' or logistics_code=='3001':
                bill_code=202
            else:
                bill_code=404
        if thirdPlatformCode=='24':
            # 4：蜂鸟众包（已开启自动呼单）；5：蜂鸟平台配送；6：蜂鸟众包（未开启自动呼单）
            if logistics_code == '4' or logistics_code == '5' or logistics_code == '6':
                bill_code = 202
            else:
                bill_code = 404
        return bill_code
    except Exception as e:
        raise e


"""
    批量比对数据
"""
def xy_order_info_compare_batch(third_order_list):
    try:
        data_list=[]
        for third_order_no in third_order_list:
            result_code = 50000
            result_msg = "金额信息不一致"
            compare_result=xy_order_info_compare(third_order_no)
            for compare_item in compare_result:
                compare_msg=compare_item['compare_msg']
                compare_fail=compare_item['compare_fail']
                if compare_msg=="订单信息跨平台比对：数据库&心云接口&对应平台":
                    if compare_fail==0:
                        result_code=10000
                        result_msg="金额信息一致"
            data_result=[third_order_no,result_code,result_msg,str(compare_result)]
            data_list.append(data_result)



        now_time = datetime.now()
        now_date = datetime.strftime(now_time, "%Y-%m-%d %H&%M&%S")
        excel_name = "order_info_batch_test.xlsx"
        sheet_name = f"路由比对结果_{now_date}"
        column_list = ["订单号", "金额比对结果", "金额比对信息", "比对结果"]
        write_excel(excel_name, sheet_name, column_list, data_list)
        return data_list
    except Exception as e:
        raise e

if __name__=="__main__":
    # xy_order_info_compare('3800989931637728820')
    order_list=['3800989931637728820']
    xy_order_compare_batch_result=xy_order_info_compare_batch(order_list)
    print(xy_order_compare_batch_result)

    # xy_order_compare_filed_map('status',"int","40","40","4","27",extend_flag='orderState')