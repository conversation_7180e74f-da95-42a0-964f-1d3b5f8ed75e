# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/29 17:04
@Auth ： 逗逗的小老鼠
@File ：xy_order_pick_api.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

from lib.get_log import logger,exception
import requests,json
from structure.order_test.xy_login import xy_user_token
from structure.order_test.xy_order_api import xy_order_info_api
from lib.deal_ini import readini


"""
    获取待拣货缓存信息
    :param order_no:订单号
"""
@exception(logger)
def xy_order_getCachePickConfirmInfo(order_no):
    "获取拣货缓存信息"
    try:
        userid=readini("xy_login_param.ini", "test-user", "userId")
        host = readini("xy_login_param.ini", "test-host", "host")
        url = host + f"/businesses-gateway/dscloud/1.0/ds/order/detail/getCachePickConfirmInfo"
        token = xy_user_token()
        headers = {"Authorization": token, "Content-Type": "application/json;charset=UTF-8"}
        data = {"barCode":"","erpCode":"","orderNo":order_no,"userId":userid}
        res = requests.post(url=url, data=json.dumps(data), headers=headers,verify=False)
        if res.status_code==200:
            res_json=res.json()
            res_code=res_json['code']
            if res_code=='10000':
                res_date=res_json['data']
                notPickedInfo_data=res_date['notPickedInfo']
                if len(notPickedInfo_data)>0:
                    result={"code":10000,"msg":"心云待拣货商品列表查询成功","data":notPickedInfo_data}
                else:
                    result = {"code": 20000, "msg": "心云未查询出待拣货商品列表待拣货商品记录", "data": notPickedInfo_data}
            else:
                result={"code":res_code,"msg":"心云待拣货商品列表查询失败","data":res_json['msg']}
        else:
            result={"code":res.status_code,"msg":"心云待拣货商品列表接口访问异常","data":res}
        return result
    except Exception as e:
        raise e


"""
    获取配送方式
    :param order_no:订单号
    :param platformCode:平台码
    :param onlineStoreCode:线上门店码
"""
@exception(logger)
def xy_order_queryDeliveryPlatform(order_no,platformCode,onlineStoreCode):
    "获取配送方式"
    try:
        host = readini("xy_login_param.ini", "test-host", "host")
        url = host + f"/businesses-gateway/dscloud/1.0/ds/baseinfo/queryDeliveryPlatform"
        token = xy_user_token()
        headers = {"Authorization": token, "Content-Type": "application/json;charset=UTF-8"}
        data = {"filterFlag":1,"orderNo":order_no,"platformCode":platformCode,"onlineStoreCode":onlineStoreCode}
        res = requests.post(url=url, data=json.dumps(data), headers=headers,verify=False)
        if res.status_code==200:
            res_json=res.json()
            res_code=res_json['code']
            if res_code=='10000':
                delivery_data=res_json['data']
                if len(delivery_data)>0:
                    result={"code":10000,"msg":"心云配送方式查询成功","data":delivery_data}
                else:
                    result = {"code": 20000, "msg": "心云未查询出配送方式记录", "data": delivery_data}
            else:
                result={"code":res_code,"msg":"心云配送方式查询失败","data":res_json['msg']}
        else:
            result={"code":res.status_code,"msg":"心云配送方式接口访问异常","data":res}
        return result
    except Exception as e:
        raise e


"""
    获取商品批号信息
    :param order_no:订单号
    :param erp_list:商品erp清单
    :param organCode:组织机构码
"""
@exception(logger)
def  xy_order_querybatchstock(order_no,erp_list,organCode):
    "获取商品批号信息"
    try:
        host = readini("xy_login_param.ini", "test-host", "host")
        url = host + f"/businesses-gateway/dscloud/1.0/ds/order/detail/batch/stock"
        token = xy_user_token()
        headers = {"Authorization": token, "Content-Type": "application/json;charset=UTF-8"}
        data = {"orderNo":order_no,"erpCodeList":erp_list,"organCode":organCode}
        res = requests.post(url=url, data=json.dumps(data), headers=headers,verify=False)
        if res.status_code==200:
            res_json=res.json()
            res_code=res_json['code']
            if res_code=='10000':
                batch_data=res_json['data']
                if len(batch_data)>0:
                    # 未查询到批号的商品
                    no_batch_list=[]
                    # hana批号库存信息
                    hana_batch_stock=batch_data['batchStockListFromHana']
                    # 预填批号库存信息--（此处逻辑需要修改）
                    manual_batch_stock=batch_data['batchStockListFromManual']

                    erp_stock_list = []
                    # 遍历商品列表
                    for erp_item in erp_list:
                        erp_stock_data={}
                        erp_stock_data['erp_code']=erp_item
                        batch_list=[]
                        erp_stock_flag=0
                        # 查询hana库存信息
                        for hana_item in hana_batch_stock:
                            ware_code = hana_item['warecode']
                            ware_make_list = hana_item['make']
                            if ware_code == erp_item:
                                for ware_make_item in ware_make_list:
                                    wareqty_value = int(float(ware_make_item['wareqty']))
                                    if wareqty_value > 0:
                                        batch_list.append(ware_make_item)
                                        erp_stock_flag=1
                        # 若未获取到批号信息，则添加erp编码到未获取到批号列表中
                        if erp_stock_flag==0:
                            no_batch_list.append(erp_item)
                        erp_stock_data['batch_list']=batch_list
                        erp_stock_list.append(erp_stock_data)
                    if len(no_batch_list)==0:
                        result={"code":10000,"msg":"心云获取商品批号信息成功","data":erp_stock_list}
                    else:
                        result = {"code": 30000, "msg": "部分商品未获取到批号信息，请人工确认", "data": no_batch_list}
                else:
                    result = {"code": 20000, "msg": "心云未查询出商品批号信息记录", "data": batch_data}
            else:
                result={"code":res_code,"msg":"心云商品批号信息查询失败","data":res_json['msg']}
        else:
            result={"code":res.status_code,"msg":"心云商品批号信息查询接口访问异常","data":res}
        return result
    except Exception as e:
        raise e




"""
    获取拣货员工清单
    :param organ_code:组织机构编码
"""
@exception(logger)
def xy_order_queryEmpByCondition(organ_code):
    "获取拣货员工清单"
    try:
        host = readini("xy_login_param.ini", "test-host", "host")
        url = host + f"/businesses-gateway/dscloud/1.0/ds/baseinfo/queryEmpByCondition"
        token = xy_user_token()
        headers = {"Authorization": token, "Content-Type": "application/json;charset=UTF-8"}
        data = {"merCode":"500001","subOrgCode":organ_code,"status":1,"accountType":0,"currentPage":1,"pageSize":1000}
        res = requests.post(url=url, data=json.dumps(data), headers=headers,verify=False)
        if res.status_code==200:
            res_json=res.json()
            res_code=res_json['code']
            if res_code=='10000':
                res_data=res_json['data']
                emp_data=res_data['data']
                if len(emp_data)>0:
                    result={"code":10000,"msg":"心云拣货员工清单查询成功","data":emp_data}
                else:
                    result = {"code": 20000, "msg": "心云未查询出拣货员工记录", "data": res_data}
            else:
                result={"code":res_code,"msg":"心云拣货员工清单查询失败","data":res_json['msg']}
        else:
            result={"code":res.status_code,"msg":"心云拣货员工清单接口访问异常","data":res}
        return result
    except Exception as e:
        raise e



"""
    录批号
    :param organ_code:组织机构编码
    :param pickDetailList:批号数据，数据格式如下：
        [{"commodityBatchNo":"230307","count":"1","erpCode":"181351","orderDetailId":1617587,"purchasePrice":""}]
"""
@exception(logger)
def xy_order_upOrderBatchNo(order_no,pickDetailList):
    "录批号"
    try:
        host = readini("xy_login_param.ini", "test-host", "host")
        url = host + f"/businesses-gateway/dscloud/1.0/ds/order/upOrderBatchNo"
        token = xy_user_token()
        headers = {"Authorization": token, "Content-Type": "application/json;charset=UTF-8"}
        data = {"orderNo":order_no,"pickDetailList":pickDetailList}
        res = requests.post(url=url, data=json.dumps(data), headers=headers,verify=False)
        if res.status_code==200:
            res_json=res.json()
            res_code=res_json['code']
            if res_code=='10000':
                result = {"code": 10000, "msg": "心云批号录入成功", "data": res_json['msg']}
            else:
                result={"code":res_code,"msg":"心云批号录入失败","data":res_json['msg']}
        else:
            result={"code":res.status_code,"msg":"心云批号录入接口访问异常","data":res}
        return result
    except Exception as e:
        raise e


"""
    拣货复核
    :param organ_code:组织机构编码
    :param pickDetailList:批号数据，数据格式如下：
        [{"commodityBatchNo":"230307","count":"1","erpCode":"181351","orderDetailId":1617587,"purchasePrice":""}]
    :param deliveryPlatform:配送方式   eg：美团骑手
    :param pickOperatorId:拣货人id
    :param pickOperatorName:拣货人姓名
"""
@exception(logger)
def xy_order_pickconfirm(order_no,deliveryPlatform,pickDetailList,pickOperatorId,pickOperatorName):
    "拣货复核"
    try:
        host = readini("xy_login_param.ini", "test-host", "host")
        url = host + f"/businesses-gateway/dscloud/2.0/ds/order/pick/confirm"
        token = xy_user_token()
        headers = {"Authorization": token, "Content-Type": "application/json;charset=UTF-8"}
        data = {"orderNo":order_no,"deliveryPlatform":deliveryPlatform,"orderState":20,"pickDetailList":pickDetailList,"pickOperatorId":pickOperatorId,"pickOperatorName":pickOperatorName}
        res = requests.post(url=url, data=json.dumps(data), headers=headers,verify=False)
        if res.status_code==200:
            res_json=res.json()
            res_code=res_json['code']
            if res_code=='10000':
                result = {"code": 10000, "msg": "心云订单拣货复核成功", "data": res_json['msg']}
            else:
                result={"code":res_code,"msg":"心云订单拣货复核失败","data":res_json['msg']}
        else:
            result={"code":res.status_code,"msg":"心云订单拣货复核接口访问异常","data":res}
        return result
    except Exception as e:
        raise e


"""
    订单详情中的拣货信息查询
"""
@exception(logger)
def xy_order_detail_pick_info(order_no):
    try:
        order_info_result=xy_order_info_api(order_no)
        if order_info_result['code']==10000:
            order_info_data=order_info_result['order_info']
            order_detail=order_info_data['orderDetailList']
            order_pick_info=[]
            for order_detail_item in order_detail:
                order_pick_data={}
                erpCode=order_detail_item['erpCode']
                detail_id=order_detail_item['id']
                orderPickInfoList=order_detail_item['orderPickInfoList']
                pickCount=order_detail_item['pickCount']
                order_pick_data['erpCode']=erpCode
                order_pick_data['detail_id'] = detail_id
                order_pick_data['pickCount'] = pickCount
                order_pick_data['orderPickInfoList'] = orderPickInfoList
                order_pick_info.append(order_pick_data)
            result = {"code": 10000, "msg": "心云订单详情拣货数据查询成功", "data": order_pick_info}
        else:
            result = {"code": 20000, "msg": "心云订单详情查询失败", "data": order_info_result}
        return result
    except Exception as e:
        raise e