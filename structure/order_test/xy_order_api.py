# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/5 18:02
@Auth ： 逗逗的小老鼠
@File ：xy_order_api.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""


from structure.order_test.xy_login import xy_user_token
from lib.get_log import logger,exception
from lib.deal_dict import deal_dict_del_dict
from lib.deal_ini import readini
import requests
import urllib3
# 禁用https警告信息
urllib3.disable_warnings()
"""
    订单详情查询
    :param order_no:系统订单号
    :return result：订单详情
"""
@exception(logger)
def xy_order_info_api(order_no):
    "订单详情查询"
    try:
        xy_order_info={}
        host=readini("xy_login_param.ini","test-host","host")
        url=host+f"/businesses-gateway/dscloud/1.0/ds/order/detail/all/{order_no}?orderNo={order_no}"
        token=xy_user_token()
        headers={"Authorization":token,"Accept":"application/json, text/plain, */*"}
        res=requests.get(url=url,headers=headers,verify=False)
        if res.status_code==200:
            res_json=res.json()
            res_code=res_json['code']
            if res_code=='10000':
                res_date=res_json['data']
                base_info=deal_dict_del_dict(res_date)
                xy_order_info['baseinfo']=base_info
                xy_order_info['orderDeliveryAddress'] = res_date['orderDeliveryAddress']
                xy_order_info['orderDeliveryRecord']=res_date['orderDeliveryRecord']
                xy_order_info['orderPayInfo']=res_date['orderPayInfo']
                xy_order_info['erpBillInfo']=res_date['erpBillInfo']
                xy_order_info['orderDetailList']=res_date['orderDetailList']
                result={"code":10000,"msg":"心云订单详情查询成功","order_info":xy_order_info}
            else:
                result={"code":res_code,"msg":"心云接口订单信息查询失败","order_info":res_json['msg']}
        else:
            result={"code":res.status_code,"msg":"用户商品详情接口访问异常","order_info":xy_order_info}
        print(result)
        return result
    except Exception as e:
        raise e



if __name__=="__main__":
    xy_order_info_api("1792674778370744066")