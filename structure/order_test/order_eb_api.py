# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/18 17:43
@Auth ： 逗逗的小老鼠
@File ：order_eb_api.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import hashlib,requests
import time,json,uuid
import urllib.parse
from lib.db_conf_test import db_yx_test_dsclound
from lib.get_log import logger,exception
import urllib3
# 禁用https警告信息
urllib3.disable_warnings()

"""
    饿百平台计算签名
"""
def sign_get(params):
    # 原始参数
    biz_params={}
    biz_params['cmd'] = params['cmd']
    biz_params['source'] = params['source']
    biz_params['secret'] = params['secret']
    biz_params['ticket'] = params['ticket']
    biz_params['version'] = 3
    biz_params['encrypt'] = params['encrypt']
    biz_params['timestamp'] = params['timestamp']
    biz_params['body'] = params['body']
    params_list = []
    # sort排序后的参数
    params_key = sorted(biz_params.keys())
    for key in params_key:
        params_list.append(key + "=" + str(biz_params[key]))
    # //用&拼接成字符串
    source_str = "&".join(params_list)
    # md5 32位
    return hashlib.md5(source_str.encode(encoding='UTF-8')).hexdigest().upper()



"""
    饿百平台签名获取
"""
@exception(logger)
def eb_request_url(cmd,body,online_store_code):
    try:
        # 获取门店appid和secret值
        app_result=eb_secret(online_store_code)
        appid=app_result['appid']
        app_secret=app_result['app_secret']
        # 生成uuid
        my_uuid=uuid.uuid4()
        # 饿百请求基础路径
        base_url="https://api-be.ele.me/?"
        ticket=str(my_uuid).upper()
        encrypt="aes"
        source=appid
        secret=app_secret
        cmd=cmd
        body=json.dumps(body)
        version=3
        timestamp=int(time.time())
        sign_map={"ticket":ticket,"encrypt":encrypt,"source":source,"secret":secret,"cmd":cmd,"body":body,"version":version,"timestamp":timestamp}

        sign = sign_get(sign_map)
        print(sign)
        sign_map['sign']=sign
        sign_map.pop('secret')
        body=urllib.parse.quote(body)
        sign_map['body']=body
        url_sign=[]
        url_sort = sorted(sign_map.items(), key=lambda x: x[0])
        for url_item in url_sort:
            url_value = url_item[1]
            url_key = url_item[0]
            sign_pairs = f"{url_key}={url_value}"
            url_sign.append(sign_pairs)
        url_param = "&".join(url_sign)
        url_request=base_url+url_param
        print(url_request)
        return url_request

    except Exception as e:
        raise e
"""
    获取饿百平台的appid和appsecret
"""

def eb_secret(online_store_code):
    "获取饿百平台的appid和appsecret"
    try:
        app_sql=f"""SELECT client.appid AS 'appid',client.app_secret AS 'app_secret' FROM ds_online_store store LEFT JOIN ds_online_client client ON store.online_client_code=client.online_client_code WHERE store.online_store_code='{online_store_code}' AND client.platform_code=24 ORDER BY client.modify_time DESC LIMIT 1"""
        app_result=db_yx_test_dsclound(app_sql)
        app_data=app_result['data']
        appid=''
        app_secret=''
        for app_item in app_data:
            appid=app_item['appid']
            app_secret=app_item['app_secret']
        result={"app_secret":app_secret,"appid":appid}
        return result
    except Exception as e:
        raise e




"""
    饿百平台店铺获取
"""
def xy_order_info_eb(order_id,online_store_code):
    try:
        order_info={}
        cmd = "order.get"
        body = {"order_id": order_id}
        order_info_url=eb_request_url(cmd,body,online_store_code)
        headers={"Content-Type":"application/json;charset=UTF-8"}
        order_info_res=requests.post(url=order_info_url,data={},headers=headers)
        if order_info_res.status_code==200:
            res_json=order_info_res.json()
            res_body=res_json['body']
            if res_body['error']=="success":
                res_data=res_body['data']
                # 订单基础信息获取
                base_info={}
                # 订单支付信息
                order_pay_info = {}
                # 订单商品详情
                order_detail_list=[]
                if "shop" in res_data:
                    # 线上门店名称
                    baidu_shop_name=res_data['shop']['name']
                    # 线上门店编码
                    baidu_shop_code=res_data['shop']['id']
                base_info['baidu_shop_code']=baidu_shop_code.upper()
                base_info['baidu_shop_name']=baidu_shop_name
                if "order" in res_data:
                    # 订单号
                    eleme_order_id=res_data['order']['eleme_order_id']
                    base_info['eleme_order_id']=eleme_order_id
                    # 配送类型
                    delivery_party=res_data['order']['delivery_party']
                    base_info['logistics_code']=delivery_party
                    # 佣金
                    commission=res_data['order']['commission']
                    order_pay_info['commission']=fen_to_yuan(commission)
                    # 顾客实付金额
                    user_fee=res_data['order']['user_fee']
                    order_pay_info['user_fee']=fen_to_yuan(user_fee)
                    # 上架实收金额
                    shop_fee=res_data['order']['shop_fee']
                    order_pay_info['shop_fee']=fen_to_yuan(shop_fee)
                    # 包装费
                    package_fee=res_data['order']['package_fee']
                    order_pay_info['package_fee']=fen_to_yuan(package_fee)
                    # 订单总优惠
                    discount_fee=res_data['order']['discount_fee']
                    order_pay_info['discount_fee']=fen_to_yuan(discount_fee)
                    # 配送费
                    send_fee=res_data['order']['send_fee']
                    order_pay_info['send_fee']=fen_to_yuan(send_fee)
                    # 平台配送费优惠金额
                    platform_delivery_fee=res_data['order']['delivery_fee']['platform_delivery_fee']
                    order_pay_info['platform_delivery_fee']=fen_to_yuan(platform_delivery_fee)
                    # 商家配送费优惠金额
                    shop_delivery_fee = res_data['order']['delivery_fee']['shop_delivery_fee']
                    order_pay_info['shop_delivery_fee']=fen_to_yuan(shop_delivery_fee)
                # 饿百承担的商品优惠
                total_eb_charge=0
                # 商家承担的商品优惠
                commodity_poi_charge=0
                if "products" in res_data:
                    product_info =res_data['products']
                    for product_item in product_info[0]:
                        detail_info={}
                        # 商品三方平台编码
                        baidu_product_id=product_item['baidu_product_id']
                        detail_info['baidu_product_id']=baidu_product_id
                        # 商品erp编码
                        custom_sku_id=product_item['custom_sku_id']
                        detail_info['custom_sku_id']=custom_sku_id
                        # upc
                        upc=product_item['upc']
                        detail_info['upc'] = upc
                        # 商品名称
                        product_name=product_item['product_name']
                        detail_info['product_name'] = product_name
                        # 商品数量
                        product_amount=product_item['product_amount']
                        detail_info['product_amount']=int(product_amount)
                        # 商品原单价
                        product_price=product_item['product_price']
                        detail_info['product_price']=fen_to_yuan(product_price)
                        # 商品原总价
                        product_fee=product_item['product_fee']
                        detail_info['product_fee']=fen_to_yuan(product_fee)
                        sub_biz_order_id=product_item['sub_biz_order_id']
                        detail_info['sub_biz_order_id']=sub_biz_order_id
                        # 是否赠品
                        is_free_gift=product_item['is_free_gift']
                        if is_free_gift==1:
                            is_free_gift=1
                        else:
                            is_free_gift=0
                        detail_info['is_gift']=is_free_gift

                        shop_rate=0
                        baidu_rate=0
                        if "product_subsidy" in product_item:
                            # 上架承担的商品优惠
                            shop_rate=product_item['product_subsidy']['shop_rate']
                            # 平台承担的商品优惠
                            baidu_rate=product_item['product_subsidy']['baidu_rate']
                        # 下账金额
                        actual_net_amount=int(product_fee)-int(shop_rate)
                        detail_info['actual_net_amount'] = fen_to_yuan(actual_net_amount)
                        actual_net_amount_yuan=fen_to_yuan(actual_net_amount)
                        bill_price=float(actual_net_amount_yuan)/int(product_amount)
                        detail_info['bill_price']=round(bill_price,4)
                        total_eb_charge=total_eb_charge+baidu_rate
                        commodity_poi_charge=commodity_poi_charge+shop_rate
                        detail_info['shop_rate']=fen_to_yuan(shop_rate)
                        detail_info['baidu_rate']=fen_to_yuan(baidu_rate)
                        order_detail_list.append(detail_info)
                order_pay_info['total_eb_charge'] = fen_to_yuan(total_eb_charge)
                order_pay_info['commodity_poi_charge'] = fen_to_yuan(commodity_poi_charge)
                order_info['baseinfo']=base_info
                order_info['order_pay_info'] = order_pay_info
                order_info['order_detail_list'] = order_detail_list
                result = {"code": 10000, "msg": "饿百订单详情查询成功", "order_info": order_info}
            else:
                result = {"code": 50000, "msg": "饿百订单详情查询请求异常", "order_info": res_json}
        else:
            result = {"code": 50000, "msg": "饿百订单详情查询请求异常", "order_info": order_info_res.status_code}
        print(result)
        return result
    except Exception as e:
        result = {"code": 50005, "msg": "饿百订单详情查询异常", "order_info": e}
        return result

"""
    订单金额处理
"""
def fen_to_yuan(amount):
    try:
        amount_value=amount/100.0
        yuan_value=round(float(amount_value),2)
        return yuan_value
    except Exception as e:
        raise e


if __name__=="__main__":
    cmd="order.get"
    body={"order_id":"4073360148255974933"}
    online_store_code="K001"
    eb_request_url(cmd,body,online_store_code)
    order_id="4073360148255974933"
    xy_order_info_eb(order_id,online_store_code)

    # sign_map={"ticket":"ticket","encrypt":"encrypt","source":"source","secret":"secret","cmd":"cmd","body":"body","version":"version","timestamp":"timestamp"}
    # for sign_item in sign_map:
    #     print(sign_item)
    # print(int(time.time()))