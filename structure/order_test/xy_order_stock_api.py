# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/22 10:50
@Auth ： 逗逗的小老鼠
@File ：xy_order_stock_api.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from structure.order_test.xy_login import xy_user_token
from lib.get_log import logger,exception
from lib.deal_dict import deal_dict_del_dict
from structure.order_test.order_mt_api import mt_body_param
from lib.deal_ini import readini
import requests
import json

"""
    心云线上商品查询
    :param store_id:门店ID
    :param erp_code:ERP编码
    :return result：订单详情
"""
@exception(logger)
def xy_commodity_stock_api(store_id,erp_code):
    "心云线上商品查询"
    try:
        commodity_api_data={}
        host=readini("xy_login_param.ini","test-host","host")
        url=host+f"/businesses-gateway/merchandise/1.0/ds/_search"
        token=xy_user_token()
        headers={"Authorization":token,"Content-Type":"application/json;charset=UTF-8"}
        data={"storeIds": [store_id],"erpCodes": [],"name": "","erpCode": erp_code,"stockMin": "","stockMax": "","priceMin": "","priceMax": "","barCode": "","admin": 0,"source": 1,"merCode": "500001","currentPage": 1,"pageSize": 20}
        res = requests.post(url=url, data=json.dumps(data), headers=headers,verify=False)
        if res.status_code==200:
            res_json=res.json()
            res_code=res_json['code']
            if res_code=='10000':
                res_date=res_json['data']
                commodity_data=res_date['data']
                if len(commodity_data)==1:
                    commodity_stock=commodity_data[0]['stock']
                    commodity_erpstock=commodity_data[0]['erpStock']
                    commodity_occupystock=commodity_data[0]['occupyStock']
                    commodity_api_data['stock']=commodity_stock
                    commodity_api_data['erpstock']=commodity_erpstock
                    commodity_api_data['occupystock']=commodity_occupystock
                    result={"code":10000,"msg":"心云线上商品查询成功","data":commodity_api_data}
                elif len(commodity_data)>1:
                    result = {"code": 20000, "msg": "心云线上商品查询出多条记录", "data": commodity_api_data}
                else:
                    result = {"code": 20000, "msg": "心云线上商品未查询出记录", "data": commodity_api_data}
            else:
                result={"code":res_code,"msg":"心云线上商品查询查询失败","data":res_json['msg']}
        else:
            result={"code":res.status_code,"msg":"心云线上商品查询接口访问异常","data":commodity_api_data}
        return result
    except Exception as e:
        raise e



"""
    美团商品库存查询
    :param store_id:门店ID
    :param erp_code:ERP编码
    :return result：订单详情
"""
@exception(logger)
def mt_commodity_stock_api(online_store_name,erp_code):
    try:
        code=10000
        data={}
        mt_stock_info={}
        url="https://waimaiopen.meituan.com/api/v1/gw/medicine/sku/get"
        data['app_poi_code']=online_store_name
        data['sku_id']=erp_code
        param=mt_body_param(url,data,online_store_name)
        res = requests.get(url=param,verify=False)
        res_result = res.json()
        result_code = res_result['code']
        if result_code==0:
            mt_data=res_result['data']['hits']
            if len(mt_data)==1:
                mt_stock=int(mt_data[0]['stock'])
                mt_price=mt_data[0]['price']
                mt_stock_info['mt_stock']=mt_stock
                mt_stock_info['mt_price']=mt_price
                msg = "美团商品库存查询记录查询成功"
            elif len(mt_data)>1:
                code=50000
                msg="美团商品库存查询记录超过1条"
            else:
                code=50001
                msg = "美团商品库存查询未查询到记录"
        else:
            code=result_code
            msg=res_result
        result={"code":code,"msg":msg,'data':mt_stock_info}
        return result
    except Exception as e:
        raise e



if __name__=="__main__":
    store_id='10010445'
    erp_code='162606'
    online_store_name="123614_2694687"
    # xy_commodity_stock=xy_commodity_stock_api(store_id,erp_code)
    # print(xy_commodity_stock)
    mt_commodity_stock=mt_commodity_stock_api(online_store_name,erp_code)
    print(mt_commodity_stock)


