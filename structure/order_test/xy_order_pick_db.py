# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/3 10:18
@Auth ： 逗逗的小老鼠
@File ：xy_order_pick_db.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from lib.db_conf_test import db_yx_test_dsclound,yx_test_base_info,yx_test_commodity_base,yx_test_commodity

"""
    心云订单拣货信息-订单侧
    :param order_no:系统订单号
    :return result：订单详情
"""
@exception(logger)
def xy_order_pick_info(order_no):
    "心云订单拣货信息-订单侧"
    try:
        code = 10000
        pick_sql=f"""SELECT pick.order_detail_id AS 'order_detail_id',pick.erp_code AS 'erp_code',pick.commodity_batch_no AS 'commodity_batch_no',pick.count AS 'count' FROM order_pick_info pick LEFT JOIN order_detail detail ON pick.order_detail_id = detail.id WHERE detail.order_no='{order_no}' AND pick.is_valid=1"""
        pick_result=db_yx_test_dsclound(pick_sql)
        pick_data=pick_result['data']
        if len(pick_data)>0:
            result={"code":code,"msg":"数据库-心云订单拣货信息查询成功","data":pick_data}
        else:
            code=20000
            result = {"code": code, "msg": "数据库-未查询到订单拣货信息记录", "data": pick_data}
        return result
    except Exception as e:
        raise e


"""
    心云订单拣货员信息-订单侧
    :param order_no:系统订单号
    :return result：订单详情
"""
@exception(logger)
def xy_order_pick_operator_info(order_no):
    "心云订单拣货员信息-订单侧"
    try:
        code = 10000
        pick_operator_sql=f"""SELECT order_no,order_state,pick_operator_id,pick_operator_name FROM order_info WHERE order_no='{order_no}'"""
        pick_operator_result=db_yx_test_dsclound(pick_operator_sql)
        pick_operator_data=pick_operator_result['data']
        if len(pick_operator_data)>0:
            order_no=pick_operator_data[0]['order_no']
            order_state=pick_operator_data[0]['order_state']
            pick_operator_id=pick_operator_data[0]['pick_operator_id']
            pick_operator_name=pick_operator_data[0]['pick_operator_name']
            emp_result=xy_emp_info(pick_operator_id)
            if emp_result['code']==10000:
                emp_data=emp_result['data']
                emp_name=emp_data[0]['emp_name']
                user_id=emp_data[0]['user_id']
                if emp_name==pick_operator_name:
                    pick_operator_info={"order_no":order_no,"order_state":order_state,"user_id":user_id,"emp_name":emp_name}
                    result = {"code": code, "msg": "数据库-心云订单拣货员信息查询成功", "data": pick_operator_info}
                else:
                    code = 40000
                    result = {"code": code, "msg": f"订单侧员工姓名【{emp_name}】与基础服务中员工姓名【{pick_operator_name}】不一致", "data": emp_result}
            else:
                code = 30000
                result = {"code": code, "msg": "数据库-心云员工信息查询失败", "data": emp_result}
        else:
            code=20000
            result = {"code": code, "msg": "数据库-未查询到订单拣货员信息记录", "data": pick_operator_data}
        return result
    except Exception as e:
        raise e


"""
    心云订单配送员信息-订单侧
    :param order_no:系统订单号
    :return result：订单详情
"""
@exception(logger)
def xy_order_delivery_info(order_no):
    "心云订单配送员信息-订单侧"
    try:
        code = 10000
        order_delivery_sql=f"""SELECT order_no,state,delivery_type,delivery_plat_name,rider_name,rider_phone FROM order_delivery_record WHERE order_no='{order_no}'"""
        order_delivery_result=db_yx_test_dsclound(order_delivery_sql)
        order_delivery_data=order_delivery_result['data']
        if len(order_delivery_data)>0:
            result={"code":code,"msg":"数据库-心云订单配送员信息查询成功","data":order_delivery_data}
        else:
            code=20000
            result = {"code": code, "msg": "数据库-未查询到订单配送员信息记录", "data": order_delivery_data}
        return result
    except Exception as e:
        raise e


"""
    心云员工信息-基础侧
    :param emp_code:工号或者用户id
    :return result：
"""
@exception(logger)
def xy_emp_info(emp_code):
    "心云员工信息-基础侧"
    try:
        code = 10000
        emp_info_sql=f"""SELECT emp.id AS 'user_id',syser.mobile AS 'mobile',emp.emp_code AS 'emp_code',emp.emp_name AS 'emp_name' FROM sys_employee emp LEFT JOIN sys_user syser ON emp.id=syser.id WHERE emp.id='{emp_code}' OR emp.emp_code='{emp_code}'"""
        emp_info_result=yx_test_base_info(emp_info_sql)
        emp_info_data=emp_info_result['data']
        if len(emp_info_data)>0:
            result={"code":code,"msg":"数据库-心云员工信息查询成功","data":emp_info_data}
        else:
            code=20000
            result = {"code": code, "msg": "数据库-未查询到心云员工信息记录", "data": emp_info_result}
        return result
    except Exception as e:
        raise e