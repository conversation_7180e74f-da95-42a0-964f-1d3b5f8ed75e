# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/22 13:49
@Auth ： 逗逗的小老鼠
@File ：xy_order_stock_db.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from lib.db_conf_test import db_yx_test_dsclound,yx_test_base_info,yx_test_commodity_base,yx_test_commodity

"""
    心云订单库存校验-订单侧
    :param order_no:系统订单号
    :return result：订单详情
"""
@exception(logger)
def xy_order_stock_order_record(order_no):
    "心云订单库存校验-订单侧"
    try:
        result_info={}
        code=10000
        # 订单详情数据查询
        order_detail_sql=f"""SELECT id,order_no,erp_code,commodity_count,status,refund_count,bar_code FROM order_detail WHERE order_no='{order_no}'"""
        order_detail_result=db_yx_test_dsclound(order_detail_sql)
        order_detail_data=order_detail_result['data']
        if len(order_detail_data)>0:
            result_info['order_detail_data']=order_detail_data
        else:
            code=50001
        # 订单商品占用记录查询
        order_stock_record_sql=f"""SELECT * FROM commodity_stock WHERE order_no='{order_no}' ORDER BY modify_time DESC"""
        order_stock_record_result=db_yx_test_dsclound(order_stock_record_sql)
        order_stock_record_data=order_stock_record_result['data']
        if len(order_stock_record_data)>0:
            result_info['order_stock_record_data']=order_stock_record_data
        else:
            code=50002
        if code==10000:
            result={"code":code,"msg":"数据库-订单侧占用记录查询成功","data":result_info}
        elif code==50001:
            result = {"code": code, "msg": "数据库-订单侧订单详情数据查询查询失败", "data": result_info}
        else:
            result = {"code": code, "msg": "数据库-订单侧订单商品占用记录查询失败", "data": result_info}
        return result
    except Exception as e:
        raise e


"""
    心云订单库存校验-商品侧
    :param order_no:系统订单号
    :return result：库存占用记录
"""
@exception(logger)
def xy_order_stock_commodity_record(order_no):
    "心云订单库存校验-商品侧"
    try:
        result_info={}
        code=10000
        # 订单商品占用记录查询
        commodity_stock_record_sql=f"""SELECT * FROM commodity_stock_order_log WHERE order_no='{order_no}' ORDER BY modify_time DESC"""
        commodity_stock_record_result=yx_test_commodity_base(commodity_stock_record_sql)
        commodity_stock_record_data=commodity_stock_record_result['data']
        if len(commodity_stock_record_data)>0:
            result_info['commodity_stock_record_data']=commodity_stock_record_data
        else:
            code==50000
        if code==10000:
            result={"code":code,"msg":"数据库-商品侧占用记录查询成功","data":result_info}
        else:
            result = {"code": code, "msg": "数据库-商品侧占用记录查询失败", "data": result_info}
        return result
    except Exception as e:
        raise e


"""
    根据组织机构码获取store_id
    :param organization_code:组织机构编码
    :return result：store_id
"""
@exception(logger)
def xy_organization_info(organization_code):
    "根据组织机构码获取store_id"
    try:
        result_info={}
        store_id=''
        code=10000
        # 订单商品占用记录查询
        store_sql=f"""SELECT id FROM sys_store WHERE st_code='{organization_code}'"""
        store_result=yx_test_base_info(store_sql)
        store_data=store_result['data']
        if len(store_data)==1:
            result_info['store_id']=store_data[0]['id']
        elif len(store_data)>1:
            code=50001
        else:
            code=50002
        if code==10000:
            result={"code":code,"msg":"根据组织机构码获取store_id成功","data":result_info}
        elif code==50001:
            result = {"code": code, "msg": "根据组织机构码查询到多个store_id", "data": result_info}
        else:
            result = {"code": code, "msg": "根据组织机构码查询store_id失败", "data": result_info}
        return result
    except Exception as e:
        raise e



"""
    根据门店ID和ERPcode获取当前商品的库存情况
    :param store_id:门店ID
    :param erp_code：erp编码
    :return result：store_id
"""
@exception(logger)
def xy_commodity_stock(store_id,erp_code):
    try:
        result_info = {}
        code = 10000
        msg="商品库存信息查询成功"
        # 根据erp编码查询spec_id
        commodity_spec_sql=f"""SELECT id FROM commodity_spec_1 WHERE erp_code='{erp_code}'"""
        commodity_spec_result=yx_test_commodity_base(commodity_spec_sql)
        commodity_spec_data=commodity_spec_result['data']
        if len(commodity_spec_data)==1:
            spec_id=commodity_spec_data[0]['id']
            result_info['spec_id']=spec_id
            # 获取分表ID值
            table_no = int(store_id) % 30
            table_name = f"commodity_store_spec_{table_no}"
            # 根据store_id和spec_id查询商品库存信息
            commodity_stock_sql = f"""SELECT * FROM {table_name} WHERE store_id='{store_id}' AND spec_id='{spec_id}'"""
            commodity_stock_result=yx_test_commodity(commodity_stock_sql)
            commodity_stock_data=commodity_stock_result['data']
            if len(commodity_stock_data)==1:
                # 可用库存
                result_info['stock']=commodity_stock_data[0]['stock']
                # erp同步库存
                result_info['erp_stock']=commodity_stock_data[0]['erp_stock']
                # O2O占用库存
                result_info['occupy_stock']=commodity_stock_data[0]['occupy_stock']
            elif len(commodity_stock_data)>1:
                code=50002
                msg = "根据store_id和spec_id查询出多条商品库存信息"
            else:
                code=50003
                msg = "根据store_id和spec_id未查询出商品库存信息"
        elif len(commodity_spec_data)>1:
            code=50000
            msg = "根据erp编码查询出多个spec_id"
        else:
            code=50001
            msg = "根据erp编码未查询出spec_id"
        result={"code":code,"msg":msg,"data":result_info}
        return result
    except Exception as e:
        raise e
