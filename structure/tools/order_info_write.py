# -*- coding: utf-8 -*-
"""
@Time ： 2024/10/11 11:00
@Auth ： 逗逗的小老鼠
@File ：order_info_write.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.deal_db_mysql import db_mysql_connect
from datetime import datetime,timedelta
import random
import threading

def test_order_info_write(**kwargs):
    "模拟订单表数据写入"
    try:
        random_number = random.randint(10, 99)
        # print(random_number)
        current_thread_id = threading.get_ident()
        thread_value=f"{current_thread_id:05d}"[-5:]
        # print(thread_value)

        now = datetime.now()
        milliseconds = int(now.timestamp() * 1000) + now.microsecond
        # print(milliseconds)
        milli_value=f"{milliseconds:09d}"[-11:]
        # print(milli_value)
        order_no_value=f"6{random_number}{thread_value}{milli_value}"
        print(order_no_value)
        third_order_no_value=f"T_{order_no_value}"
        print(third_order_no_value)
        info_sql = f"""
            INSERT INTO `dscloud`.`order_info` ( `order_no`, `order_state`, `third_platform_code`, `third_order_no`, `third_order_id`, `third_order_state`, `off_state`, `mer_code`, `client_code`, `online_store_code`, `online_store_name`, `organization_code`, `organization_name`, `delivery_time_type`, `delivery_time_desc`, `seller_remark`, `buyer_remark`, `buyer_message`, `lock_flag`, `lock_msg`, `locker_id`, `remind_flag`, `buyer_name`, `receiver_lat`, `receiver_lng`, `acceptor_id`, `acceptor_name`, `accept_time`, `picker_id`, `picker_name`, `pick_operator_id`, `pick_operator_name`, `pick_time`, `canceller_id`, `canceller_name`, `cancel_reason`, `cancel_time`, `ex_operator_id`, `ex_operator_name`, `ex_operator_time`, `complete_time`, `created`, `day_num`, `modified`, `erp_adjust_no`, `erp_sale_no`, `create_time`, `modify_time`, `prescription_flag`, `self_verify_code`, `erp_state`, `bill_time`, `call_erp_flag`, `member_no`, `transfer_delivery`, `client_conf_id`, `bill_operator`, `is_prescription`, `prescription_status`, `is_push_check`, `appointment`, `appointment_business_flag`, `appointment_business_type`, `new_customer_flag`, `integral_flag`, `request_deliver_goods_result`, `deliver_goods_refuse_reason`, `invoice_content`, `invoice_title`, `invoice_type`, `need_invoice`, `taxer_id`, `source_online_store_code`, `source_online_store_name`, `source_organization_code`, `source_organization_name`, `remark`, `service_mode`, `pay_time`, `vat_taxpayer_number`, `invoice_name`, `order_type`, `order_is_new`, `data_version`, `complex_modify_flag`, `medical_insurance`, `cancel_bill_times`, `wsc_ext_json`, `top_hold`, `order_pick_type`, `is_video_flag`, `source_channel_type`, `migration_order_no`, `extend_info`, `freight_order_no`, `deleted`) VALUES ( {order_no_value}, 77, '27', '{third_order_no_value}', '3801287970690139488', '2', 2, '500001', '48775e71e85c48a8b971ac8b3cff1dcc', '123614_21615812', '压测测试门店_t_c7IZagKp_003', 'A002', '一心堂药业集团股份有限公司昆明虫草参茸连锁店', 0, NULL, NULL, '收货人隐私号 18400620859_5031，手机号 181****0161', '收货人隐私号 18400620859_5031，手机号 181****0161', 0, '强审通过', NULL, 0, NULL, '36.814241', '81.662146', NULL, '系统自动', '2024-10-11 10:20:02', '4086467932735181914', '杨飞', '1007095', '陈萍', '2024-10-11 10:36:53', NULL, NULL, NULL, NULL, '4086467932735181914', '杨飞', '2024-10-11 10:21:33', NULL, '2024-10-11 10:19:50', '3', '2024-10-11 10:19:50', NULL, 'S010002563', '2024-10-11 10:20:02', '2024-10-11 10:39:40', 0, '3', 100, '2024-10-11 10:39:04', 1, NULL, 0, 404, 'system', 0, 0, 0, 0, 0, NULL, '0', '0', NULL, NULL, NULL, '', NULL, '2', '', '123614_21615812', '压测测试门店_t_c7IZagKp_003', 'A002', '一心堂药业集团股份有限公司昆明虫草参茸连锁店', NULL, 'O2O', '2024-10-11 10:19:50', NULL, NULL, 0, '1', 3, 0, 0, 0, NULL, '0', 1, 0, NULL, NULL, NULL, NULL, 0);
        """
        detail_sql = f"""
            INSERT INTO `dscloud`.`order_detail` ( `order_no`, `platform_sku_id`, `erp_code`, `bar_code`, `commodity_name`, `main_pic`, `commodity_spec`, `commodity_count`, `original_price`, `price`, `total_amount`, `discount_amount`, `actual_amount`, `discount_share`, `actual_net_amount`, `different_share`, `status`, `manufacture`, `swap_id`, `create_time`, `modify_time`, `adjust_amount`, `third_detail_id`, `bill_price`, `is_gift`, `goods_type`, `refund_count`, `origin_type`, `st_code`, `expect_delivery_time`, `direct_delivery_type`, `original_erp_code`, `original_erp_code_num`, `oms_order_no`, `is_joint`, `erp_gift`, `old_erp_code`, `weight`, `relation_code`, `is_reduce_stock`, `chailing`, `platform_discount_fee`, `merchant_discount_fee`, `brokerage_amount`, `vip_different_amt`, `first_type_name`, `second_type_name`, `type_name`, `drug_type`, `chai_ling_original_erp_code`, `chai_ling_original_num`, `settle_price`, `modify_price_diff`, `produce_no`, `health_value`, `extend`, `original_oms_order_no`, `third_order_no`, `payment`, `alloc`, `alloc_name`, `near_effective_status`, `average_price`, `chai_ling_num`, `detail_discount`, `storage_type`, `detail_settlement_status`, `is_medicare_item`) VALUES ( {order_no_value}, '113555*10', '128076', '6936292120727', '阿莫西林胶囊_石药_0.5g*12粒*2板', 'https://shop-cdn.yxtmart.cn/shop/101-100/128076/128076-1.jpg', '0.5G*12粒*2板', 10, 0.32, 0.32, 3.20, 0.00, 3.20, 0.00, 3.20, 0.00, 0, '石药集团中诺药业(石家庄)有限公司', NULL, '2024-10-11 10:20:02', '2024-10-11 10:21:33', 0.00, '7831170278731_1', 0.3200, 0, 1, 0, NULL, NULL, NULL, NULL, '113555*10', 1, 1, 0, 1, NULL, NULL, NULL, 1, 1, 0.00, 0.00, 0.00, 0.00, '中西药品', '其他', '其他', NULL, NULL, NULL, 0.00, NULL, NULL, NULL, NULL, NULL, NULL, 3.20, NULL, NULL, 0, 0.00, NULL, NULL, NULL, NULL, 0);
        """
        pay_sql=f"""
            INSERT INTO `dscloud`.`order_pay_info` ( `order_no`, `pay_status`, `pay_type`, `pay_channel`, `buyer_actual_amount`, `merchant_actual_amount`, `total_amount`, `total_discount`, `merchant_total_discount_sum`, `merchant_total_discount_sum_not_delivery_fee`, `merchant_discount_sum`, `discount_fee_dtl`, `platform_discount`, `post_fee_discount`, `merchant_delivery_fee`, `merchant_delivery_fee_discount`, `merchant_pack_fee`, `platform_delivery_fee`, `platform_delivery_fee_discount`, `platform_pack_fee`, `buyer_cod_service_fee`, `seller_cod_service_fee`, `brokerage_amount`, `buyer_cod_amount`, `platform_fee_collection`, `manual_fix_amount`, `detail_discount_collect`, `different_amount`, `create_time`, `modify_time`, `pay_code`, `delivery_fee`, `pack_fee`, `oms_order_no`, `remain_brokerage_amount`, `health_num`, `health_value`, `warehouse_agency_fee`, `apportion_type`, `medicare_amount`, `medicare_order_id`, `pay_sale_info`, `total_discount_sum_not_delivery_fee`, `accounts_receivable`, `platform_discount_sum_not_delivery_fee`) VALUES ( {order_no_value}, '1', '1', NULL, 6.80, 5.87, 4.30, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0.00, 2.00, 0.00, 0.00, 0.00, 0.00, 0.50, 0.00, 0.00, 0.43, 0.00, 0.00, 0.00, 0.00, 0.00, '2024-10-11 10:20:02', '2024-10-11 10:20:02', '809080', 2.00, 0.50, 0, 0.43, 0, NULL, 0.00, NULL, 0.00, NULL, NULL, NULL, NULL, NULL);
        """
        info_result = db_mysql_connect("dscloud", info_sql,environment_flag='test', **kwargs)
        detail_result = db_mysql_connect("dscloud", detail_sql,environment_flag='test', **kwargs)
        pay_result = db_mysql_connect("dscloud", pay_sql,environment_flag='test', **kwargs)
        for i in range(20):
            # 定义起始和结束日期
            start_date = datetime(2023, 1, 1)
            end_date = datetime(2025, 12, 31, 23, 59, 59)  # 包括2025年的最后一秒

            # 生成一个随机的时间delta对象
            delta = end_date - start_date
            int_delta = (random.randint(0, int(delta.total_seconds())))  # 随机秒数
            random_date = start_date + timedelta(seconds=int_delta)

            # 格式化输出
            formatted_date = random_date.strftime("%Y-%m-%d %H:%M:%S")
            # print(formatted_date)
            update_sql = f"""
                UPDATE `dscloud`.`order_info` SET complete_time='{formatted_date}' WHERE order_no='{order_no_value}'
            """
            update_result = db_mysql_connect("dscloud", update_sql,environment_flag='test', **kwargs)
        return order_no_value
    except Exception as e:
        raise e


if __name__ == '__main__':
    print(test_order_info_write(order_no='6812582315137258501'))