# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/14 15:34
@Auth ： 逗逗的小老鼠
@File ：data_monitoring_tools.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：数据监测系统工具集合
"""
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect
from lib.deal_json import json_loads, json_dumps


def data_query_field_json(schema_name,table_name,**kwargs):
    try:
        query_field_json=[]

        sql=f"SELECT * FROM {table_name} WHERE 1=2"
        query_result = db_mysql_connect(schema_name, sql,**kwargs)
        query_columns = query_result['column']
        for item_column in query_columns:
            field_json={"table_name": table_name, "field_name": item_column}
            query_field_json.append(field_json)
        return query_field_json
    except Exception as e:
        raise




if __name__ == '__main__':
    schema_name = "dscloud_offline"
    table_name = "offline_order_pay_2504"
    data=data_query_field_json(schema_name,table_name)
    print(json_dumps(data))
