# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/29 16:18
@Auth ： 逗逗的小老鼠
@File ：diff_json_map.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：支持通过映射关系进行JSON数据的比对
"""
from lib.get_log import logger, exception
import json,re
from decimal import Decimal
from structure.tools.diff_json import diff_json_value
from structure.tools.tool_jsonpath import data_by_jsonpath
from lib.deal_json import json_dumps

"""
比较JSON值的差异。

参数:
- reference_data: 比对时参照的数据。
- comparison_data：比对时比对的数据，实际比对为比较comparison_data中映射关系中的值与reference_data中的值是否一致/存在

返回:
- result: 包含每次比较结果的列表，每个结果包含比较信息、相似度和差异结果。

异常:
- Exception: 捕获并抛出任何异常，确保异常信息被记录。

"""
@exception(logger)
def diff_data(body):
    "比对json数据"
    try:
        # 对传入的参数进行预处理
        data = diff_json_preprocessing(body)
        # 获取需要比对的数据
        reference_data = data.get("reference_data")
        comparison_data = data.get("comparison_data")
        # 忽略数据类型检查
        ignore_data_type_changes=data.get("ignore_data_type_changes")
        # 忽略数值类型的检查
        ignore_numeric_type_changes=data.get("ignore_numeric_type_changes")
        # 忽略字符串（字符串前+b变为二进制）
        ignore_string_type_changes=data.get("ignore_string_type_changes")
        # 忽略大小写
        ignore_string_case=data.get("ignore_string_case")
        # 忽略排序,该值仅针对list类型的数据比对
        ignore_sort=data.get("ignore_sort")
        if type(reference_data) != type(comparison_data):
            raise Exception("参照数据与比对数据类型不一致，无法进行比对")
        else:
            result=[]
            # 获取映射关系解析情况
            map_jsonpath_list=json_map_parse(data)
            # 通过遍历获取字段的映射关系表
            for map_jsonpath_item in map_jsonpath_list:
                map_comparison_jsonpath=map_jsonpath_item.get("comparison_field_jsonpath")
                map_field_type=map_jsonpath_item.get("field_type")
                map_field_desc=map_jsonpath_item.get("field_desc")
                map_reference_field = map_jsonpath_item.get("reference_field")
                map_comparison_field=map_jsonpath_item.get("comparison_field")
                map_diff_detail=[]
                # 结果一致的数量
                consistent_num=0
                # 结果不一致的数量
                inconsistent_num=0
                # 遍历每个字段的映射jsonpath关系
                for map_comparison_jsonpath_item in map_comparison_jsonpath:
                    # 获取映射关系中的jsonpath
                    comparison_jsonpath=map_comparison_jsonpath_item.get("comparison_jsonpath")
                    reference_jsonpath=map_comparison_jsonpath_item.get("reference_jsonpath")
                    # 根据jsonpath获取数据
                    comparison_value=data_by_jsonpath(comparison_data,comparison_jsonpath)
                    reference_value = data_by_jsonpath(reference_data, reference_jsonpath)
                    # 进行数据比对
                    diff_value_result=diff_json_subobject_value(reference_value, comparison_value, map_field_type, ignore_data_type_changes,
                              ignore_numeric_type_changes, ignore_string_type_changes, ignore_sort, ignore_string_case)
                    # 获取比对结果
                    map_consistency=diff_value_result.get("consistency")
                    # 结果一致时累加数量
                    if map_consistency == "true":
                        consistent_num+=1
                    else:
                        inconsistent_num+=1
                    diff_value_result.update({"comparison_jsonpath": comparison_jsonpath, "reference_jsonpath": reference_jsonpath})
                    map_diff_detail.append(diff_value_result)
                map_result={"field_desc": map_field_desc, "field_type": map_field_type, "consistent_num": consistent_num,"inconsistent_num": inconsistent_num,"reference_field": map_reference_field,"comparison_field":map_comparison_field,"diff_detail":map_diff_detail}
                result.append(map_result)
            return result
    except Exception as e:
        raise e

@exception(logger)
def diff_json_preprocessing(body):
    # 进行json数据的入参前置判断
    try:
        # 忽略数据类型检查
        ignore_data_type_changes = body.get("ignore_data_type_changes", False)
        # 忽略数值类型的检查
        ignore_numeric_type_changes = body.get("ignore_numeric_type_changes", False)
        # 忽略字符串（字符串前+b变为二进制）
        ignore_string_type_changes = body.get("ignore_string_type_changes", False)
        # 忽略排序,该值仅针对list类型的数据比对
        ignore_sort = body.get("ignore_sort", False)
        # 忽略大小写
        ignore_string_case = body.get("ignore_string_case", False)
        # 获取映射关系的比对字典
        map_list = body.get("map_list", [])
        # 如果映射关系为空，或映射关系不是list类型，则直接返回异常
        if len(map_list) == 0 or not isinstance(map_list, list):
            raise Exception("映射关系参数【map_list】必须为数组类型，且不能为空!")
        # 比对参照字段，默认为list
        reference_data = body.get("reference_data", None)
        if reference_data == None:
            raise Exception("缺少参照数据【reference_data】，建议提交对应参数值，理想格式为数组")
        # 参与比对数据，默认为list
        comparison_data = body.get("comparison_data", None)
        if comparison_data == None:
            raise Exception("缺少参照数据【comparison_data】，建议提交对应参数值，理想格式为数组")
        data = {"map_list": map_list, "reference_data": reference_data, "comparison_data": comparison_data,
                "ignore_numeric_type_changes": ignore_numeric_type_changes,
                "ignore_string_type_changes": ignore_string_type_changes, "ignore_sort": ignore_sort,
                "ignore_string_case": ignore_string_case,"ignore_data_type_changes":ignore_data_type_changes}
        return data
    except Exception as e:
        raise e

"""

    解析JSON映射关系
    该函数根据提供的数据和映射关系，解析并生成比较数据和参考数据之间的JSON路径映射
    这对于自动化比较和验证数据结构非常有用

    参数:
    data (dict): 包含参考数据、比较数据和映射关系的字典
        map_list：映射字典格式：{"reference_field":"参照字段","comparison_field":"比对字段","field_type":"标准类型","field_desc":"字段描述"}


    返回:
    list: 解析后的JSON路径映射列表，每个元素包含比较字段的JSON路径、字段类型和字段描述
    
    本方法主要讲原映射关系全部解析为jsonpath表达式，以方便进行取值，考虑到JSON数据中存在list类型数据，需要按照统一的字段进行标准化比较，如【id】类型
    
    故而实现逻辑如下：
        1、首先获取到映射关系的列表【map_list】，并对列表进行遍历，取出每个item的各个元素值，参照字段【reference_field】，比对字段【comparison_field】，数据类型【field_type】，字段描述【field_desc】
        2、初始化一个jsonpath格式的映射列表【map_jsonpath_list_old】，列表中存值为字典类型的映射关系【map_jsonpath_dict_old】，
            a、映射关系中包含：参照字段jsonpath表达式【comparison_jsonpath】，比对字段jsonpath表达式【reference_jsonpath】
            b、参照字段jsonpath表达式【comparison_jsonpath】，比对字段jsonpath表达式【reference_jsonpath】均初始化为jsonpath根节点表达式【$】
        3、将参照字段【reference_field】和比对字段【comparison_field】按照【.】进行拆解为列表【comparison_field_split】【reference_field_split】
        4、对比较字段的拆解结果【comparison_field_split】进行遍历，取出值【comparison_field_split_item】和下标【split_index】
        5、跟下标在参照字段的拆解结果【reference_field_split】中取值对应的值【reference_field_split_item】
        6、判断若两个字段的对应值均为【$】则判定为根节点表达式，自动进入下一次循环；
        7、如果拆解值中存在【[]】则当前节点为列表类型，
            A、如果为列表类型，
                a、则【[]】中的值为过滤条件值【comparison_list_filter_key】，获取【[】前的值为列表所属的键值【comparison_list_key】；
                b、通过列表键值【comparison_list_key】和【[*]】的方式获取到所有的过滤条件值【comparison_list_filter_key】，结果存值为【comparison_filter_value_list】；
                c、遍历获取的结果【comparison_filter_value_list】，通过将原表达式+【.{reference_list_key}[?(@.{reference_list_filter_key}=={filter_value})]】的方式拼接处新的表达式；
            B、如果不是列表类型，则直接拼接新的拆解值即可生成新的jsonpath表达式。
        8、将新的表达式映射关系，存进新的关系列表中【map_jsonpath_list_new】；
        9、清除原来的关系列表【map_jsonpath_list_old】，将新的关系列表【map_jsonpath_list_new】赋值重新赋值进入。
"""
@exception(logger)
def json_map_parse(data):
    # 映射关系解析
    try:
        # 获取需要比对的数据
        reference_data = data.get("reference_data")
        comparison_data = data.get("comparison_data")
        # 获取需要比对的映射关系
        map_list = data.get("map_list")
        map_jsonpath_list=[]
        for map_item in map_list:
            # 获取映射关系中的字段
            reference_field=map_item.get("reference_field")
            comparison_field=map_item.get("comparison_field")
            field_type=map_item.get("field_type")
            field_desc=map_item.get("field_desc")
            # 初始化映射关系字典列表
            map_jsonpath_dict_old={"comparison_jsonpath":"$","reference_jsonpath":"$"}
            map_jsonpath_list_old=[map_jsonpath_dict_old]
            # 分解比较字段和参考字段的路径
            comparison_field_split=comparison_field.split(".")
            reference_field_split=reference_field.split(".")
            # 遍历比较字段的路径部分
            for split_index,comparison_field_split_item in enumerate(comparison_field_split):
                map_jsonpath_list_new=[]
                reference_field_split_item=reference_field_split[split_index]
                # 如果为$，则跳过本次循环，进入下一次循环
                if comparison_field_split_item=="$" and reference_field_split_item=="$":
                    continue
                # 处理列表类型的字段
                if "[" in comparison_field_split_item and "]" in comparison_field_split_item:
                    # 查找方括号[的索引位置
                    comparison_list_key_index = comparison_field_split_item.find('[')
                    # 如果找到了方括号[
                    if comparison_list_key_index != -1:
                        # 获取方括号[之前的部分
                        comparison_list_key = comparison_field_split_item[:comparison_list_key_index]
                    # 查找方括号[的索引位置
                    reference_list_key_index = reference_field_split_item.find('[')
                    # 如果找到了方括号[
                    if reference_list_key_index != -1:
                        # 获取方括号[之前的部分
                        reference_list_key = reference_field_split_item[:reference_list_key_index]
                    # 使用正则表达式获取方括号内的内容
                    pattern = r'\[(.*?)\]'
                    comparison_matches = re.findall(pattern, comparison_field_split_item)
                    comparison_list_filter_key=comparison_matches[0]
                    reference_matches = re.findall(pattern, reference_field_split_item)
                    reference_list_filter_key = reference_matches[0]
                    # 遍历旧的映射关系，生成新的映射关系
                    for map_jsonpath_old_item in map_jsonpath_list_old:
                        comparison_jsonpath_old_item=map_jsonpath_old_item["comparison_jsonpath"]
                        reference_jsonpath_old_item=map_jsonpath_old_item["reference_jsonpath"]
                        comparison_list_parse = comparison_jsonpath_old_item + f".{comparison_list_key}[*].{comparison_list_filter_key}"
                        comparison_filter_value_list=data_by_jsonpath(comparison_data,comparison_list_parse)
                        if comparison_filter_value_list:
                            for filter_value in comparison_filter_value_list:
                                comparison_field_parse=comparison_jsonpath_old_item+f".{comparison_list_key}[?(@.{comparison_list_filter_key}=={filter_value})]"
                                reference_field_parse=reference_jsonpath_old_item+f".{reference_list_key}[?(@.{reference_list_filter_key}=={filter_value})]"
                                map_jsonpath_dict_new = {"comparison_jsonpath": comparison_field_parse, "reference_jsonpath": reference_field_parse}
                                map_jsonpath_list_new.append(map_jsonpath_dict_new)
                        else:
                            print(f"根据jsonpath 【{comparison_list_parse}】未获取到任何数据，请检查jsonpath是否正确")
                else:
                    # 处理非列表类型的字段
                    for map_jsonpath_old_item in map_jsonpath_list_old:
                        comparison_jsonpath_old_item = map_jsonpath_old_item["comparison_jsonpath"]
                        reference_jsonpath_old_item = map_jsonpath_old_item["reference_jsonpath"]
                        comparison_field_parse = comparison_jsonpath_old_item + f".{comparison_field_split_item}"
                        reference_field_parse = reference_jsonpath_old_item + f".{reference_field_split_item}"
                        map_jsonpath_dict_new = {"comparison_jsonpath": comparison_field_parse,
                                                 "reference_jsonpath": reference_field_parse}
                        map_jsonpath_list_new.append(map_jsonpath_dict_new)
                # 清空旧的映射关系，准备下一轮迭代
                map_jsonpath_list_old.clear()
                map_jsonpath_list_old.extend(map_jsonpath_list_new)
            # 构建最终的映射关系字典
            jsonpath_map_dict={"field_type":field_type,"field_desc":field_desc,"reference_field":reference_field,"comparison_field":comparison_field,"comparison_field_jsonpath":map_jsonpath_list_old}
            map_jsonpath_list.append(jsonpath_map_dict)
        return map_jsonpath_list
    except Exception as e:
        # 捕获并抛出异常
        raise e

@exception(logger)
def diff_json_subobject_value(reference_value, comparison_value, field_type, ignore_data_type_changes,
                              ignore_numeric_type_changes, ignore_string_type_changes, ignore_sort, ignore_string_case):
    "比对json中的子对象的数据情况"
    try:
        diff_value_result = {}
        # 需要检查数据类型
        if ignore_data_type_changes != False and ignore_data_type_changes != "false":
            # 如果比对数据中要求的数据类型为空或者未定义，则按照参数数据的值类型进行比对
            if field_type == "" or field_type == None:
                field_type = type(reference_value)
            if field_type != type(comparison_value):
                diff_value_result = {"consistency": "false", "reference_value": reference_value,
                                     "comparison_value": comparison_value, "expect_type": field_type,
                                     "actul_type": type(comparison_value),
                                     "msg": f"数据类型不一致，参照数据类型为：【{field_type}】，值为【{reference_value}】，比对数据类型为：【{type(comparison_value)}】，值为：【{comparison_value}】",
                                     "compare_detail": []}
                return diff_value_result
        # 将需要比对的数据转为列表
        compare_list = [reference_value, comparison_value]
        json_value = {"compare_list": compare_list, "ignore_numeric_type_changes": ignore_numeric_type_changes,
                      "ignore_string_type_changes": ignore_string_type_changes, "ignore_sort": ignore_sort,
                      "ignore_string_case": ignore_string_case,
                      "ignore_string_type_changes": ignore_string_type_changes}
        # 进行数据比对
        diff_value_result = diff_json_value(json_value)
        if diff_value_result and diff_value_result[0]['similarity'] == 1.0:
            diff_value_result = {"consistency": "true", "reference_value": reference_value,
                                 "comparison_value": comparison_value, "expect_type": field_type,
                                 "actul_type": type(comparison_value),
                                 "msg": f"数据比对值完全一致", "compare_detail": diff_value_result}
        else:
            diff_value_result = {"consistency": "false", "reference_value": reference_value,
                                 "comparison_value": comparison_value, "expect_type": field_type,
                                 "actul_type": type(comparison_value),
                                 "msg": f"数据比对值不一致", "compare_detail": diff_value_result}
        return diff_value_result
    except Exception as e:
        raise e


if __name__ == '__main__':
    json_data = [{
        "store": {
            "book": [
                {
                    "category": "reference",
                    "author": "Nigel Rees",
                    "title": "Sayings of the Century",
                    "price": 8.95
                },
                {
                    "category": "fiction",
                    "author": "Evelyn Waugh",
                    "title": "Sword of Honour",
                    "price": 12.99
                },
                {
                    "category": "fiction",
                    "author": "love",
                    "title": "see how much i love you",
                    "price": 15.99
                },
                # ... 其他书籍数据
            ],
            "bicycle": {
                "color": "red",
                "price": 19.95
            }},
        "id": 2
    },
        {
        "store": {
            "book": [
                {
                    "category": "reference",
                    "author": "Nigel Rees",
                    "title": "Sayings of the Century",
                    "price": 8.95
                },
                {
                    "category": "fiction",
                    "author": "Evelyn Waugh",
                    "title": "Sword of Honour",
                    "price": 12.99
                },
                {
                    "category": "fiction",
                    "author": "love",
                    "title": "see how much i love you",
                    "price": 15.99
                },
                # ... 其他书籍数据
            ],
            "bicycle": {
                "color": "red",
                "price": 19.95
            }
        },
        "id":1
    }]
    comparison_data = [{
        "store": {
            "book": [
                {
                    "category": "reference1",
                    "author": "Nigel Rees1",
                    "title": "Sayings of the Century",
                    "price": 8.95
                },
                {
                    "category": "fiction",
                    "author": "Evelyn Waugh",
                    "title": "Sword of Honour",
                    "price": 12.99
                },
                {
                    "category": "fiction",
                    "author": "love",
                    "title": "how much i love you",
                    "price": 15.99
                },
                # ... 其他书籍数据
            ],
            "bicycle": {
                "color": "blue",
                "price": 19.95
            }},
        "id": 2
    },
        {
            "store": {
                "book": [
                    {
                        "category": "reference",
                        "author": "Nigel Rees",
                        "title": "Sayings of the Century",
                        "price": 8.95
                    },
                    {
                        "category": "fiction1",
                        "author": "Evelyn Waugh",
                        "title": "Sword of Honour",
                        "price": 12.99
                    },
                    {
                        "category": "fiction",
                        "author": "love",
                        "title": "see how much i love you",
                        "price": 15.99
                    },
                    # ... 其他书籍数据
                ],
                "bicycle": {
                    "color": "yellow",
                    "price": 19.95
                }
            },
            "id": 1
        }]
    map_list=[
        {"reference_field":"$.[id].store.book[price].title","comparison_field":"$.[id].store.book[price].title","field_type":"","field_desc":"书名"},
        {"reference_field": "$.[id].store.book[price].category", "comparison_field": "$.[id].store.book[price].category",
         "field_type": "", "field_desc": "书本类别"},
        {"reference_field": "$.[id].store.book[price].author", "comparison_field": "$.[id].store.book[price].author",
         "field_type": "", "field_desc": "书本作者"},
        {"reference_field": "$.[id].store.bicycle.color",
         "comparison_field": "$.[id].store.bicycle.color",
         "field_type": "", "field_desc": "自行车作者"}

    ]
    data={"reference_data": json_data,"comparison_data": comparison_data, "map_list": map_list}
    result=diff_data(data)
    # print(result)
    print(json_dumps(result))


