# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/23 15:21
@Auth ： 逗逗的小老鼠
@File ：offline_order_info_mock.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
class offline_order_data:
    def __init__(self, **kwargs):
        self.startCreated = kwargs.get("startCreated")
        self.endCreated = kwargs.get("endCreated")

    def baseOrderInfo(self):
        """基础订单信息"""
        try:
            baseOrderInfo = {
                "orderNo": "1025041700000003",
                "thirdOrderNo": "1025041700000003",
                "erpCode": "112470",
                "storeCode": "H812",
                "storeName": "H812",
            }
        except Exception as e:
            raise