# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/2 14:54
@Auth ： 逗逗的小老鼠
@File ：tool_jsonpath.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：jsonpath测试工具
"""

import jsonpath
import json
from collections import defaultdict
"""
比较JSON值的差异。

参数:
- json_data: 需要获取的json数据。
- json_path：jsonpath表达式

返回:
- result: 若获取到数据则返回数据，数据格式为list，若未获取到数据则返回False。

异常:
- Exception: 捕获并抛出任何异常，确保异常信息被记录。
"""
def data_by_jsonpath(json_data,json_path):
    # 通过jsonpath获取json中对应的值
    try:
        result=jsonpath.jsonpath(json_data, json_path)
        return result

    except Exception as e:
        raise e




"""
递归遍历 JSON 对象并生成 JSONPath 表达式。

:param json_obj: 要遍历的 JSON 对象（字典或列表）。
:param parent_key: 当前字段的父级键（用于构建路径）。
:param path: 当前 JSONPath 表达式（初始为 '$.'）。
:return: 包含 JSON 字段路径和对应 JSONPath 表达式的字典。
"""
def generate_jsonpath_expressions(json_obj, parent_key='', path='$.'):
    expressions = defaultdict(list)

    if isinstance(json_obj, dict):
        for key, value in json_obj.items():
            current_key = f"{parent_key}.{key}" if parent_key else key
            current_path = f"{path}{key}"

            # 处理字典中的值（可能是嵌套字典或列表）
            if isinstance(value, (dict, list)):
                nested_expressions = generate_jsonpath_expressions(value, current_key, current_path)
                for k, v in nested_expressions.items():
                    expressions[k].extend(v)
            else:
                # 对于非嵌套值，直接添加 JSONPath 表达式
                expressions[current_key].append(current_path)

    elif isinstance(json_obj, list):
        for index, item in enumerate(json_obj):
            current_path_item = f"{path}[{index}]"

            # 处理列表中的项（可能是字典或进一步嵌套的列表）
            if isinstance(item, (dict, list)):
                nested_expressions = generate_jsonpath_expressions(item, f"{parent_key}[{index}]", current_path_item)
                for k, v in nested_expressions.items():
                    expressions[k].extend(v)
            else:
                # 对于列表中的非字典/列表值，这种情况在纯值列表中很少见，但也可以处理
                # 注意：这里我们可能想要一个更具体的键来区分不同的列表项，但在没有键的情况下，我们只能使用索引
                # 由于索引可能不唯一（如果列表中有重复的值），这里我们简单地使用父级键和索引的组合（如果存在）
                # 或者只使用索引（如果这是顶层列表）
                key_for_value = f"{parent_key}[{index}]" if parent_key else str(index)
                expressions[key_for_value].append(current_path_item)

    # 由于我们使用了 defaultdict(list)，每个键都对应一个列表，即使只有一个表达式
    # 为了简化输出，我们可以将每个键的列表转换为单个字符串（如果只有一个元素）或保持为列表（如果有多个元素）
    # 但在这个特定的情况下，由于我们为每个字段生成一个唯一的 JSONPath，所以列表将始终只有一个元素
    # 因此，我们可以选择简化输出，只保留字符串而不是列表
    simplified_expressions = {k: v[0] if len(v) == 1 else v for k, v in expressions.items()}

    return simplified_expressions









if __name__ == '__main__':
    json_test_data = [
    {
        "store": {
            "book": [
                {
                    "category": "reference",
                    "author": "Nigel Rees",
                    "title": "Sayings of the Century",
                    "price": 8.95
                },
                {
                    "category": "fiction",
                    "author": "Evelyn Waugh",
                    "title": "Sword of Honour",
                    "price": 12.99
                },
                {
                    "category": "fiction",
                    "author": "love",
                    "title": "see how much i love you",
                    "price": 15.99
                },
                # ... 其他书籍数据
            ],
            "bicycle": {
                "color": "red",
                "price": 19.95
            }
        },
        "id": 2
    }
]
    # 示例 JSON 数据
    json_data = {
        "name": "John",
        "age": 30,
        "address": {
            "street": "123 Main St",
            "city": "New York"
        },
        "phones": [
            {"type": "home", "number": "************"},
            {"type": "work", "number": "************"}
        ]
    }

    # 生成 JSONPath 表达式
    jsonpath_expressions = generate_jsonpath_expressions(json_test_data)

    # 打印结果
    for field, expression in jsonpath_expressions.items():
        print(f"Field: {field}, JSONPath: {expression}")
    # jsonpath_2=generate_jsonpaths(json_data)
    # print(jsonpath_2)
    # json_path='$.[?(@.id==2)].store.book[*].price'
    # $.[*].id[?(@.id == 2)].store.book[*].price
    # print(data_by_jsonpath(json_data,json_path))
