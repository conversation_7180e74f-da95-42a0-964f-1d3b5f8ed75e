# -*- coding: utf-8 -*-
"""
@Time ： 2024/8/6 10:47
@Auth ： 逗逗的小老鼠
@File ：diff_json.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：diff 包的使用及封装
"""

import json
from difflib import SequenceMatcher
from deepdiff import DeepDiff, DeepSearch
from lib.get_log import logger, exception
import json
from lib.deal_json import json_loads
from decimal import Decimal

"""
比较JSON值的差异。

参数:
- list_value: 包含多个JSON对象、字典或列表的列表，用于相互比较。

返回:
- result: 包含每次比较结果的列表，每个结果包含比较信息、相似度和差异结果。

异常:
- Exception: 捕获并抛出任何异常，确保异常信息被记录。
"""
@exception(logger)
def diff_json_value(diff_param):
    """比较JSON值的差异"""
    try:
        result = []
        # 忽略数值类型的检查
        ignore_numeric_type_changes=diff_param.get('ignore_numeric_type_changes',False)
        if ignore_numeric_type_changes !=False and ignore_numeric_type_changes !="false":
            ignore_numeric_type_changes = True
        else:
            ignore_numeric_type_changes = False
        # 忽略字符串（字符串前+b变为二进制）
        ignore_string_type_changes=diff_param.get('ignore_string_type_changes',False)
        if ignore_string_type_changes !=False and ignore_string_type_changes !="false":
            ignore_string_type_changes = True
        else:
            ignore_string_type_changes = False
        # 忽略排序
        ignore_sort=diff_param.get('ignore_sort',True)
        if ignore_sort !=True and ignore_sort !="true":
            ignore_sort = False
        # 忽略大小写
        ignore_string_case=diff_param.get('ignore_string_case',False)
        if ignore_string_case !=False and ignore_string_case !="false" :
            ignore_string_case = True
        else:
            ignore_string_case = False
        # 排除指定字段
        exclude_paths=diff_param.get('exclude_paths',[])
        # 比对深度:1>=float>0,值越大，则结果中展示的差异深度越大。
        cutoff_distance_for_pairs=diff_param.get('cutoff_distance_for_pairs',1)
        if isinstance(cutoff_distance_for_pairs, float) and cutoff_distance_for_pairs <= 1 and cutoff_distance_for_pairs >= 0:
            cutoff_distance_for_pairs = cutoff_distance_for_pairs
        else:
            cutoff_distance_for_pairs = 1
        # 比对标准值,默认为第一个参数
        compare_standard = {}
        num = 1
        # 判断是否存在对比值
        compare_list = diff_param.get('compare_list',[])
        # 仅支持比对list数据
        if isinstance(compare_list, list):
            for list_item in compare_list:
                # 根据不同的输入类型处理数据
                if isinstance(list_item, str):
                    compare_item = json_loads(list_item)
                else:
                    compare_item = list_item
                if num > 1:
                # 第一个参数无需比较
                    diff_result = DeepDiff(compare_standard, compare_item, ignore_order=ignore_sort,
                                           ignore_numeric_type_changes=ignore_numeric_type_changes,
                                           ignore_string_type_changes=ignore_string_type_changes,
                                           ignore_string_case=ignore_string_case, exclude_paths=exclude_paths,
                                           cutoff_distance_for_pairs=cutoff_distance_for_pairs)
                    macth_standard = compare_standard
                    macth_item = compare_item
                    # 判断是否需要进行排序后比对相似度
                    if ignore_sort == True:
                        # 仅字典和列表进行排序
                        if isinstance(compare_standard, dict):
                            macth_standard = sorted(compare_standard.items())
                        if isinstance(compare_standard, list):
                            macth_standard = sorted(compare_standard)
                        if isinstance(compare_item, dict):
                            macth_item = sorted(compare_item.items())
                        if isinstance(compare_item, list):
                            macth_item = sorted(compare_item)

                    # 计算相似度
                    similarity = SequenceMatcher(None, str(macth_standard), str(macth_item)).ratio()
                    compare_msg = "第{}个参数与第{}参数个相似度：{:.2f}%".format(num, num - 1, similarity * 100)
                    # 构造比较结果并添加到结果列表
                    compare_result = {"compare_msg": compare_msg, "similarity": round(similarity,4), "diff_result": str(diff_result)}
                    result.append(compare_result)
                # 将当前项作为下一次对比的标准
                compare_standard = compare_item
                # 更新对比次数
                num += 1
            # print("对比结果：", result)
            return result
        else:
            raise ValueError("比对数据值【compare_list】类型错误，请输入list类型")
    except Exception as e:
        # 捕获并抛出异常，确保异常信息被记录
        raise e


"""判断是否为json格式"""


@exception(logger)
def is_json(data):
    try:
        json.loads(data)
        return True
    except ValueError:
        return False



# 处理特殊信息
def process_diff_result(diff_result):
    if isinstance(diff_result, dict):
        for key, value in diff_result.items():
            if isinstance(value, DeepDiff.PrettyOrderedSet):
                diff_result[key] = list(value)
            elif isinstance(value, dict):
                process_diff_result(value)
    elif isinstance(diff_result, list):
        for i, item in enumerate(diff_result):
            if isinstance(item, DeepDiff.PrettyOrderedSet):
                diff_result[i] = list(item)

if __name__ == '__main__':
    dict_1 = {"name": "John", "age": 11, "city": "New York"}
    dict_2 = {"name": "John", "age": 30, "city": "New York"}
    list_1 = ["1", "2", "3"]
    list_2 = ["1", "2", "4"]
    # compare_list=[["1","2","3"],["2","2"]]
    # print(DeepDiff(list_1,list_2))
    value = SequenceMatcher(None, str(sorted(dict_1)), str(sorted(dict_2))).ratio()


    # compare_list = [{"name": "John", "age": 11, "city": "New York", "data": {"code": 200, "msg": "success"}},
    #                 {"age": 30, "city": "New York", "name": "John", "data": {"code": 500, "msg": "fail"}},
    #                 {"age": 32, "kuqi": "New York", "name": "John"}]
    compare_list=["sssss",'sssss']
    compare_dict = {"compare_list": compare_list, "ignore_string_case": False, "exclude_paths": ["age"]}
    print(diff_json_value(compare_dict))
