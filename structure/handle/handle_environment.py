# -*- coding: utf-8 -*-
"""
@Time ： 2024/8/19 14:25
@Auth ： 逗逗的小老鼠
@File ：handle_environment.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import importlib
from lib.get_log import logger, exception


"""
    根据环境变量实现自动切换数据库
    :param module_path：模块名称
    :param method_name：方法名称
    :param body：传参
"""
@exception(logger)
def dynamically_call_db_method(db_entity,module_path, method_name, body):
    "根据环境变量实现自动切换数据库"
    try:
        # 获取环境变量，若无环境变量，默认为test
        if "environment_flag" in db_entity.keys():
            environment_flag=db_entity["environment_flag"]
        else:
            environment_flag = "test"
        # 获取执行SQL，若无SQL，默认为查询1
        if "sql" in db_entity.keys():
            sql=db_entity["sql"]
        else:
            sql = "SELECT * FROM (SELECT 1) AS temp;"
        # 获取执行的数据库名称，若无数据库名称，默认为dsclound
        if "database_name" in db_entity.keys():
            database_name=db_entity["database_name"]
        else:
            database_name = "dsclound"

        if environment_flag == "test":
            if database_name == "dsclound":
                module_path="lib.db_conf_test"
                method_name="db_yx_dscloud"


        # 导入模块
        module = importlib.import_module(module_path)
        # 获取模块中的方法
        method = getattr(module, method_name, None)
        if method is not None and callable(method):
            # 调用方法
            return method(body)
        else:
            raise AttributeError(f"模块 '{module_path}' 不存在方法 '{method_name}' 或未调用成功")
    except ImportError as e:
        raise ImportError(f"导入模块失败 '{module_path}': {e}")
