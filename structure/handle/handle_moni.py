# -*- coding: utf-8 -*-
"""
@Time ： 2024/5/22 14:40
@Auth ： 逗逗的小老鼠
@File ：handle_moni.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json

from lib.get_log import exception, logger
from lib.deal_wx_msg import message_wechat_push
from lib.db_conf import db_insight
import datetime
import importlib
from dateutil.parser import parse
from lib.deal_sql import dict_to_sql_insert
from lib.deal_ini import readini

"""
    监测结果数据处理
"""


# @exception(logger)
def handle_moni_data(task_data):
    try:
        data = {}
        # 获取当前时间
        current_date = datetime.datetime.now()
        # task_code 参数为必须穿
        if "task_code" in task_data.keys():
            task_code = task_data['task_code']
            wx_config_sql = f"""SELECT msg_task_code,msg_url,moni_module_path,moni_method_name,moni_duration,msg_content,notification_person,msg_title,msg_summary_desc_format,msg_detail_desc_format,task_info_table,task_detail_table,is_access_business_monitor,is_write_task_table,is_push_webhook FROM wx_wehook_msg_config WHERE is_delete=0 AND msg_task_code='{task_code}'"""
            wx_config_result = db_insight(wx_config_sql)
            wx_config_data = wx_config_result['data']
            if len(wx_config_data) > 0:
                # 获取消息推送的相关配置
                wx_config_item = wx_config_data[0]
                # 消息推送地址
                msg_url = wx_config_item['msg_url']
                # 任务监控模块地址
                moni_module_path = wx_config_item['moni_module_path']
                # 任务监控方法名称
                moni_method_name = wx_config_item['moni_method_name']
                # 默认监控时长(小时)
                moni_duration = wx_config_item['moni_duration']
                # 消息发送内容说明
                msg_content = wx_config_item['msg_content']
                # 需要提醒的人员清单
                notification_person = wx_config_item['notification_person']
                # 消息标题
                msg_title = wx_config_item['msg_title']
                # 推送汇总信息格式
                msg_summary_desc_format = wx_config_item['msg_summary_desc_format']
                # 推送消息详情格式
                msg_detail_desc_format = wx_config_item['msg_detail_desc_format']
                # 监测汇总数据表
                task_info_table = wx_config_item['task_info_table']
                # 监测详情数据写入表
                task_detail_table = wx_config_item['task_detail_table']
                # 是否推送到任务系统
                if "is_access_business_monitor" in task_data.keys():
                    is_access_business_monitor = task_data['is_access_business_monitor']
                else:
                    is_access_business_monitor = wx_config_item['is_access_business_monitor']
                # 是否发送企微推送消息
                if "is_push_webhook" in task_data.keys():
                    is_push_webhook = task_data['is_push_webhook']
                else:
                    is_push_webhook = wx_config_item['is_push_webhook']
                # 是否写入数据表
                if "is_write_task_table" in task_data.keys():
                    is_write_task_table = task_data['is_write_task_table']
                else:
                    is_write_task_table = wx_config_item['is_write_task_table']
                # 获取监控时间段
                # 若传入值中包含开始结束时间，则直接取对应时间
                if "end_time" in task_data.keys() and "start_time" in task_data.keys():
                    end_time = task_data['end_time']
                    start_time = task_data['start_time']
                else:
                    # 若只存在开始时间，则结束时间为开始时间+监控时间区间时长
                    if "start_time" in task_data.keys():
                        start_time = task_data['start_time']
                        parse_time = parse(start_time)
                        next_date = parse_time + datetime.timedelta(hours=int(moni_duration))
                        end_time = next_date.replace(minute=0, second=0, microsecond=0)
                    # 若只存在结束时间，则开始时间为结束时间-监控时间区间时长
                    elif "end_time" in task_data.keys():
                        end_time = task_data['end_time']
                        parse_time = parse(end_time)
                        next_date = parse_time - datetime.timedelta(hours=int(moni_duration))
                        start_time = next_date.replace(minute=0, second=0, microsecond=0)
                    # 若不存在开始结束时间，则取当前时间前5分钟~当前时间-监控时间区间时长
                    else:
                        next_date = current_date - datetime.timedelta(minutes=5)
                        end_time = next_date.replace(second=0, microsecond=0)
                        yesterday = current_date - datetime.timedelta(hours=int(moni_duration))
                        start_time = yesterday.replace(minute=0, second=0, microsecond=0)
                moni_time = {"start_time": str(start_time), "end_time": str(end_time)}
                moni_result = dynamically_import_and_call_method(moni_module_path, moni_method_name, moni_time)
                content_result = message_content_desc(moni_result, msg_detail_desc_format)
                # 推送企业微信消息
                if is_push_webhook == 1:
                    webhook_push_result = moni_msg_push(task_code, start_time, end_time, msg_title, content_result,
                                                        msg_summary_desc_format)
                    data['webhook_push_result'] = webhook_push_result
                # 数据写入
                if is_write_task_table == 1:
                    moni_data_insert_result = moni_data_insert(task_code, start_time, end_time, msg_content,
                                                               task_info_table, task_detail_table,
                                                               content_result)
                    data['moni_data_insert_result'] = moni_data_insert_result
                if is_access_business_monitor == 1:
                    access_business_monitor_result = access_business_monitor(task_code, content_result)
                    data['access_business_monitor_result'] = access_business_monitor_result
                result = {"code": 10000, "msg": "检测数据处理成功", "data": data}
            else:
                result = {"code": 20000, "msg": "未找到task_code对应的配置信息", "data": []}
        else:
            result = {"code": 50000, "msg": "传参中必须包含task_code", "data": []}
        return result
    except Exception as e:
        raise e


"""
    生成描述信息内容
    :param data：监控结果数据
    :param msg_detail_desc_format：消息数据格式
"""


@exception(logger)
def message_content_desc(data, msg_detail_desc_format):
    try:
        exec_list = []
        for exec_item in data:
            msg_detail_desc = msg_detail_desc_format
            exec_dict = {}
            # 通过关键字模式替换对应的值
            for exec_key, exec_value in exec_item.items():
                primitive_value = {}
                msg_detail_desc = msg_detail_desc.replace(exec_key, str(exec_value))
                if isinstance(exec_value, str):
                    exec_value = str(exec_value)
                primitive_value[exec_key] = exec_value
                # 消息原始数据
                exec_dict['primitive_value'] = json.dumps(str(primitive_value), ensure_ascii=False)
            exec_dict['msg_value'] = msg_detail_desc
            exec_list.append(exec_dict)
        return exec_list
    except Exception as e:
        raise e


"""
    实现动态引入包
    :param module_path：模块名称
    :param method_name：方法名称
    :param body：传参
"""
@exception(logger)
def dynamically_import_and_call_method(module_path, method_name, body):
    "实现动态引入包"
    try:
        # 导入模块
        module = importlib.import_module(module_path)
        # 获取模块中的方法
        method = getattr(module, method_name, None)
        if method is not None and callable(method):
            # 调用方法
            return method(body)
        else:
            raise AttributeError(f"模块 '{module_path}' 不存在方法 '{method_name}' 或未调用成功")
    except ImportError as e:
        raise ImportError(f"导入模块失败 '{module_path}': {e}")


"""
    企业微信消息推送
"""


@exception(logger)
def moni_msg_push(task_code, start_time, end_time, msg_title, content_result, msg_summary_desc_format):
    try:
        wx_msg = f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**{msg_title}：**\n"""
        msg_count = len(content_result)
        detail_msg = ""
        # 微信机器人推送条数
        push_num = readini("order_moni_config.ini", "handle_moni", "webhook_push_num")
        if msg_count > 0:
            sale_stock_msg = f"""><font color=\"warning\">{msg_summary_desc_format}：【{msg_count}】</font>\n"""
            max_length = 0
            for content_item in content_result:
                msg_detail = content_item['msg_value']
                diff_msg = f"""><font color=\"warning\">{msg_detail}</font>\n"""
                detail_msg = detail_msg + diff_msg
                max_length = max_length + 1
                if max_length >= int(push_num):
                    break
        else:
            sale_stock_msg = f"""><font color=\"info\">{msg_summary_desc_format}：【{msg_count}】</font>\n"""

        wx_msg = wx_msg + sale_stock_msg + f"""**监测详情：**\n""" + detail_msg
        push_result = message_wechat_push(wx_msg, task_code)
        return push_result
    except Exception as e:
        raise e


"""
    批量写入监控数据表
"""


@exception(logger)
def moni_data_insert(task_code, start_time, end_time, msg_content, task_info_table, task_detail_table, content_result):
    try:
        insert_info_success = 0
        insert_info_fail = 0
        insert_detail_success = 0
        insert_detail_fail = 0
        info_list = []
        detail_result = []
        # 获取时间戳作为关联ID
        related_id = int(datetime.datetime.now().timestamp())
        exec_count = len(content_result)
        start_time = str(start_time)
        end_time = str(end_time)
        info_result = {"related_id": related_id, "task_code": task_code, "task_content": msg_content,
                       "start_time": start_time, "end_time": end_time,
                       "exec_count": exec_count}
        info_list.append(info_result)
        for content_item in content_result:
            msg_content = content_item['msg_value']
            primitive_content = content_item['primitive_value']
            content_dict = {"task_code": task_code, "related_id": related_id, "msg_content": msg_content,
                            "primitive_content": primitive_content, "start_time": start_time, "end_time": end_time}
            detail_result.append(content_dict)
        if task_info_table != None and task_info_table != "":
            # 将结果信息写入汇总信息表
            info_sql_list = dict_to_sql_insert(task_info_table, info_list)
            for info_sql_item in info_sql_list:
                info_insert_result = db_insight(info_sql_item)
                if info_insert_result['data'] == 1:
                    insert_info_success = insert_info_success + 1
                else:
                    insert_info_fail = insert_info_fail + 1
        if task_detail_table != None and task_detail_table != "":
            # 将详情数据写入详情数据表
            detail_sql_list = dict_to_sql_insert(task_detail_table, detail_result)
            # 将监测数据写入库
            for detail_sql_item in detail_sql_list:
                detail_insert_result = db_insight(detail_sql_item)
                if detail_insert_result['data'] == 1:
                    insert_detail_success = insert_detail_success + 1
                else:
                    insert_detail_fail = insert_detail_fail + 1
        insert_detail_result = {"insert_success": insert_detail_success, "insert_fail": insert_detail_fail}
        insert_info_result = {"insert_success": insert_info_success, "insert_fail": insert_info_fail}
        result = {"insert_detail_result": insert_detail_result, "insert_info_result": insert_info_result}
        return result
    except Exception as e:
        raise e


"""
    数据写入任务系统
"""


@exception(logger)
def access_business_monitor(task_code, content_result):
    try:
        access_success = 0
        access_fail = 0
        access_business_config_sql = f"""SELECT task_code,risk_type_id,risk_level,risk_type_name FROM business_monitor_task_config WHERE task_code='{task_code}' AND is_delete=0"""
        access_business_config_result = db_insight(access_business_config_sql)
        access_business_config_data = access_business_config_result['data']
        if len(access_business_config_data) > 0:
            # 获取写入配置信息
            access_business_config_item = access_business_config_data[0]
            risk_type_id = access_business_config_item['risk_type_id']
            risk_level = access_business_config_item['risk_level']
            risk_type_name = access_business_config_item['risk_type_name']
            source = 1
            business_result = []
            business_table = "business_monitor"
            # 遍历msg信息
            for content_item in content_result:
                msg_content = content_item['msg_value']
                business_dict = {"type": risk_type_name, "type_id": risk_type_id, "description": msg_content,
                                 "level": risk_level, "source": source}
                business_result.append(business_dict)
            # 将写入数据转为SQL
            business_sql_list = dict_to_sql_insert(business_table, business_result)
            # 数据写入
            for business_sql_item in business_sql_list:
                business_sql_result = db_insight(business_sql_item)
                if business_sql_result['data'] == 1:
                    access_success = access_success + 1
                else:
                    access_fail = access_fail + 1
        access_business_result = {"access_success": access_success, "access_fail": access_fail}
        return access_business_result
    except Exception as e:
        raise e


# @log_exception(logger)
def my_test_job(value):
    try:
        print(f"定时任务执行中...{value}")
        # 在这里编写你的任务逻辑
        # ...
        return "任务执行成功"
    except Exception as e:
        raise e


if __name__ == "__main__":
    task_code = "order_bill_amount_error"
    moni_data = [{"a": "222", "b": "1111", "c": "3333"}]
    task_data = {"task_code": task_code,"start_time":"2024-06-11 00:00:00", "end_time": "2024-06-12 00:00:00"}
    # deal_moni_content(task_data)
    # message_content(task_code,moni_data)
    moni_result=handle_moni_data(task_data)
    print(moni_result)