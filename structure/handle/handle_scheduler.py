# -*- coding: utf-8 -*-
"""
@Time ： 2024/5/27 9:52
@Auth ： 逗逗的小老鼠
@File ：handle_scheduler.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from lib.db_conf import db_insight
import importlib
from lib.deal_scheduler import sch_add_job, sch_pause_job, sch_remove_job, sch_get_job, sch_resume_job
from lib.deal_cron import parse_cron_expression
from lib.deal_sql import dict_to_sql_update
from structure.handle.handle_moni import handle_moni_data
from lib.deal_db_mysql import db_mysql_connect
import json

"""
    获取定时任务添加列表
"""

@exception(logger)
def schedul_task_config_add(task_info):
    try:
        result = []
        filter_sql = ""
        sql_val=()
        if "scheduler_id" in task_info:
            scheduler_id = task_info.get('scheduler_id',"")
            sql_val = (scheduler_id,)
            filter_sql = f" AND scheduler_id=%s"
        task_config_sql = f"""SELECT id,scheduler_id,scheduler_name,scheduler_path,scheduler_func,func_param,trigger_type,trigger_cron,start_date,end_date,scheduler_comment FROM scheduler_task_config WHERE is_delete=0 {filter_sql}"""
        task_config_result = db_mysql_connect("insight", task_config_sql,environment_flag="common",sql_val=sql_val)
        task_config_data = task_config_result['data']
        for task_config_item in task_config_data:
            id = task_config_item['id']
            scheduler_id = task_config_item['scheduler_id']
            scheduler_name = task_config_item['scheduler_name']
            scheduler_path = task_config_item['scheduler_path']
            scheduler_func = task_config_item['scheduler_func']
            func_param = task_config_item['func_param']
            trigger_type = task_config_item['trigger_type']
            trigger_cron = task_config_item['trigger_cron']
            start_date = task_config_item['start_date']
            end_date = task_config_item['end_date']
            scheduler_comment = task_config_item['scheduler_comment']
            # 判断开始时间是否为空，为空则设置为1999-01-01 00:00:00
            if start_date == "" or start_date is None:
                start_date = "1999-01-01 00:00:00"
            # 判断结束时间是否为空，为空则设置为2099-01-01 00:00:00
            if end_date == "" or end_date is None:
                end_date = "2099-01-01 00:00:00"
            if func_param == "" or func_param is None:
                func_param = {}
            else:
                func_param = json.loads(func_param)

            # 导入模块
            module = importlib.import_module(scheduler_path)
            # 获取模块中的方法
            method = getattr(module, scheduler_func, None)
            if method is not None and callable(method):
                if trigger_type == "cron":
                    cron_expresssion = parse_cron_expression(trigger_cron)
                    seconds = cron_expresssion['seconds']
                    minutes = cron_expresssion['minutes']
                    hours = cron_expresssion['hours']
                    day_of_month = cron_expresssion['day_of_month']
                    month = cron_expresssion['month']
                    day_of_week = cron_expresssion['day_of_week']
                    sch_add_job(method, scheduler_id, scheduler_name,
                                trigger_type, second=seconds, minute=minutes, hour=hours, day_of_week=day_of_week,
                                day=day_of_month, month=month, start_date=start_date, end_date=end_date,
                                kwargs=func_param)
                    msg = "cron定时任务添加成功"
                elif trigger_type == "interval":
                    sch_add_job(method, scheduler_id, scheduler_name,
                                trigger_type, minutes=int(trigger_cron), start_date=start_date, end_date=end_date,
                                kwargs=func_param)
                    msg = "interval定时任务添加成功"
                elif trigger_type == "date":
                    sch_add_job(method, scheduler_id, scheduler_name,
                                trigger_type, run_date=trigger_cron, kwargs=func_param)
                    msg = "date定时任务添加成功"
                else:
                    msg = f"{trigger_type}定时任务类型非法"
            else:
                msg = (f"模块 '{scheduler_path}' 不存在方法 '{scheduler_func}' 或未调用成功")
            data = {"msg": msg, "cron_detail": task_config_item}
            result.append(data)
        return result
    except Exception as e:
        raise e


"""
    修改配置的定时任务
"""
@exception(logger)
def schedul_task_config_update(task_info):
    try:
        if "scheduler_id" in task_info:
            scheduler_id = task_info['scheduler_id']
            filter_dict = {"scheduler_id": scheduler_id}
            table_name = "scheduler_task_config"
            # 更新任务配置信息
            update_sql = dict_to_sql_update(table_name, task_info, filter_dict)
            update_result = db_insight(update_sql)
            # 获取当前运行任务情况
            job_data = sch_get_job()
            for job_item in job_data:
                job_id = job_item['job_id']
                next_run_time = job_item['next_run_time']
                # 检查是否存在对应任务
                if str(scheduler_id) == str(job_id):
                    # 移除原任务
                    remove_result = sch_remove_job(scheduler_id)
                    msg = "任务移除完成"
            # 添加新任务
            schedul_task_config_add(filter_dict)
            msg = "任务更新完成"
        else:
            msg = "请提供参数”scheduler_id“"
        # 查询当前任务情况
        data = sch_get_job()
        result = {"msg": msg, "data": data}
        return result
    except Exception as e:
        raise e


"""
    移除配置的定时任务
"""

@exception(logger)
def schedul_task_config_remove(task_info):
    try:
        if "scheduler_id" in task_info:
            scheduler_id = task_info['scheduler_id']
            filter_dict = {"scheduler_id": scheduler_id}
            update_dict = {"is_delete": 1}
            table_name = "scheduler_task_config"
            # 更新任务配置信息,标识为已删除
            update_sql = dict_to_sql_update(table_name, update_dict, filter_dict)
            update_result = db_insight(update_sql)
            # 获取当前运行任务情况
            job_data = sch_get_job()
            for job_item in job_data:
                job_id = job_item['job_id']
                next_run_time = job_item['next_run_time']
                # 检查是否存在对应任务
                if str(scheduler_id) == str(job_id):
                    # 移除原任务
                    remove_result = sch_remove_job(scheduler_id)
                    msg = "任务移除完成"
                else:
                    msg = "未查询到对应任务，请重新确认"

        else:
            msg = "请提供参数”scheduler_id“"
        # 查询当前任务情况
        data = sch_get_job()
        result = {"msg": msg, "data": data}
        return result
    except Exception as e:
        raise e


"""
    暂停定时任务
"""


@exception(logger)
def schedul_task_config_pause(task_info):
    try:
        if "scheduler_id" in task_info:
            scheduler_id = task_info['scheduler_id']
            # 获取当前运行任务情况
            job_data = sch_get_job()
            for job_item in job_data:
                job_id = job_item['job_id']
                next_run_time = job_item['next_run_time']
                # 检查是否存在对应任务
                if str(scheduler_id) == str(job_id):
                    # 判断任务是否为暂停状态
                    if next_run_time == None or next_run_time == "None":
                        msg = "任务已暂停，无需重复暂停"
                    else:
                        # 暂停任务
                        pause_result = sch_pause_job(scheduler_id)
                        msg = "任务暂停完成"
                else:
                    msg = "未查询到对应任务，请重新确认"
        else:
            msg = "请提供参数”scheduler_id“"
        # 查询当前任务情况
        data = sch_get_job()
        result = {"msg": msg, "data": data}
        return result
    except Exception as e:
        raise e


"""
    恢复暂停定时任务
"""


@exception(logger)
def schedul_task_config_resume(task_info):
    try:
        if "scheduler_id" in task_info:
            scheduler_id = task_info['scheduler_id']
            job_data = sch_get_job()
            for job_item in job_data:
                job_id = job_item['job_id']
                next_run_time = job_item['next_run_time']
                if str(scheduler_id) == str(job_id):
                    if next_run_time == None or next_run_time == "None":
                        # 恢复原任务
                        resume_result = sch_resume_job(scheduler_id)
                        msg = "任务恢复完成"
                    else:
                        msg = "任务未暂停，无需恢复"
                else:
                    msg = "未查询到对应任务，请重新确认"
        else:
            msg = "请提供参数”scheduler_id“"
        # 查询当前任务情况
        data = sch_get_job()
        result = {"msg": msg, "data": data}
        return result
    except Exception as e:
        raise e


"""
    定时任务管理
"""


@exception(logger)
def schedul_task_config_manager(manage_type, task_info):
    try:
        # 暂停任务
        if manage_type == "pause":
            manage_result = schedul_task_config_pause(task_info)
        elif manage_type == "add":
            manage_result = schedul_task_config_add(task_info)
        # 恢复任务
        elif manage_type == "resume":
            manage_result = schedul_task_config_resume(task_info)
        # 移除任务
        elif manage_type == "remove":
            manage_result = schedul_task_config_remove(task_info)
        elif manage_type == "update":
            manage_result = schedul_task_config_update(task_info)
        else:
            manage_result = sch_get_job()
        return manage_result
    except Exception as e:
        raise e


if __name__ == "__main__":
    try:
        module = importlib.import_module('structure.handle.handle_moni')
        print(module)
        method = getattr(module, "handle_moni_data", None)
        print(method)
    except Exception as e:
        print(f'Error: {e}')
