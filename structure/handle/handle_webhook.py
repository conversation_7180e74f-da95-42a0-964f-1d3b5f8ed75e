# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/12 14:40
@Auth ： 逗逗的小老鼠
@File ：handle_webhook.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/6 19:07
@Auth ： 逗逗的小老鼠
@File ：deal_wx_msg.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
import requests
import json
import datetime
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect
from lib.deal_sql import dict_to_sql_insert,dict_to_sql_update,dict_to_convert_sql_insert
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

"""
    发送消息
    :param query_dict:查询条件
    :return defect_info：查询结果
"""
@exception(logger)
def message_wechat_push(message_info,msg_task_code,**kwargs):
    "推送企业微信机器人消息"
    try:
        push_list=[]
        msg_conf_result = message_config_data(msg_task_code)
        if not msg_conf_result:
            logger.error(f"消息任务编码为{msg_task_code}的消息配置信息不存在")
            return None
        msg_url = msg_conf_result.get('msg_url', "")
        notification_person = msg_conf_result.get('notification_person', "")
        msg_title=msg_conf_result.get('msg_title',"")
        msg_type = msg_conf_result.get('msg_type', "markdown")
        is_write_log = msg_conf_result.get('is_write_log', 1)
        is_push_webhook = msg_conf_result.get('is_push_webhook', 1)
        msg_summary_desc_format = msg_conf_result.get('msg_summary_desc_format', "")
        msg_detail_desc_format = msg_conf_result.get('msg_detail_desc_format', "")
        # msg_detail_desc_format = msg_detail_desc_format.replace("\\\\", "\\")
        noto_msg = r""
        headers = {"Content-Type": "application/json; charset=utf-8"}
        if isinstance(notification_person, str):
            notification_person = notification_person.split(',')
        else:
            notification_person=[]
        if is_push_webhook ==1 or is_push_webhook =='1':
            msg_content = message_content_info(message_info, msg_detail_desc_format)
            for msg_item in msg_content:
                if msg_type == "markdown":
                    for item in list(notification_person):
                        noto_msg = noto_msg + f"<@{item}>"
                        # print(msg_item)
                    data = {"msgtype": "markdown", "markdown": {"content": msg_item}}
                if msg_type == "text":
                    data = {
                            "msgtype": "text",
                            "text": {
                                "content": msg_item,
                                "mentioned_mobile_list":notification_person
                            }
                        }
                # print(json.dumps(data))
                response=requests.post(msg_url,data=json.dumps(data),headers=headers,verify=False)
                result=response.json()
                # print(result)
                if 'errmsg' in result and 'errcode' in result:
                    if result['errcode']==0 and result['errmsg']=='ok':
                        send_result="发送成功"
                        errmsg=""
                    else:
                        send_result = "发送失败"
                        errmsg=result['errmsg']
                send_date = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                push_result={"msg_task_code":msg_task_code,"msg_url":msg_url,"notification_person":notification_person,"msg_type":msg_type,"msg_data":msg_item,"msg_title":msg_title,"send_result":send_result,"send_date":send_date,"fail_msg":errmsg}
                push_list.append(push_result)
            if is_write_log==1 or is_write_log=='1':
                message_push_record=message_push_record_write(push_list)
                return message_push_record
            else:
                return push_list
        else:
            logger.error(f"任务编码：{msg_task_code}，未启用消息配送，请核实后进行处理")
            return None
    except Exception as e:
        raise

"""
    消息发送地址及通知人列表处理
    :param msg_task_code:消息任务编码
    :return defect_info：查询结果
"""
@exception(logger)
def message_content_info(message_info,msg_detail_desc_format,**kwargs):
    "消息发送地址及通知人列表处理"
    try:
        msg_list=[]
        # 如果消息表达式不为空时，需要进行关键字替换
        if msg_detail_desc_format != "" and msg_detail_desc_format != None:

            if isinstance(message_info,list):
                for message_item in message_info:
                    msg_detail_desc = msg_detail_desc_format
                    for key, value in message_item.items():
                        msg_detail_desc=msg_detail_desc.replace(str(key), str(value))
                    msg_list.append(msg_detail_desc)
            elif isinstance(message_info,dict):
                msg_detail_desc = msg_detail_desc_format
                for key, value in message_info.items():
                    msg_detail_desc = msg_detail_desc.replace(str(key), str(value))
                msg_list.append(msg_detail_desc)
            else:
                msg_detail_desc=str(message_info)
                msg_list.append(msg_detail_desc)
        else:
            msg_detail_desc = str(message_info)
            msg_list.append(msg_detail_desc)
        return msg_list
    except Exception as e:
        raise



"""
    消息发送内容处理
    :param msg_task_code:消息任务编码
    :return defect_info：查询结果
"""
@exception(logger)
def message_config_data(msg_task_code,**kwargs):
    "消息发送地址及通知人列表处理"
    try:
        msg_url = ""
        noti_person = []
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        query_sql=f"""
            SELECT * FROM business_webhook_msg_config WHERE msg_task_code='{msg_task_code}' AND is_delete=0 LIMIT 1
        """
        query_result = db_mysql_connect("insight", query_sql,environment_flag="common", **kwargs)
        query_data = query_result['data']
        if query_data:
            config_info=query_data[0]
            return config_info
        else:
            logger.error(f"任务编码：{msg_task_code}，查询不到消息配置信息")
            return {}
    except Exception as e:
        raise

"""
    消息发送记录日志
    :param msg_task_code:消息任务编码
    :return defect_info：查询结果
"""
@exception(logger)
def message_push_record_write(data):
    "消息发送记录日志"
    try:
        info_insert_num=0
        record_sql,record_val=dict_to_convert_sql_insert('business_webhook_msg_record', data)
        info_result = db_mysql_connect('insight', record_sql,sql_val=record_val, environment_flag="common")
        info_insert_num += info_result.get('data', 0)
        return info_insert_num
    except Exception as e:
        raise

if __name__=="__main__":
    msg_task="function_excute_timeout"
    message_info= [{"id":111,"name":456,"ex":"ces"},{"id":222,"name":789,"ex":"ces"}]
    msg_result=message_wechat_push(message_info,msg_task)
    # print(msg_result)
