# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/15 18:36
@Auth ： 逗逗的小老鼠
@File ：handle_rocketmq_consumer.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

import threading
import time
from threading import Event
from rocketmq.client import PushConsumer
from lib.get_log import exception, logger
from lib.deal_ThreadPool import combined_decorator
from lib.deal_config_json import read_json_file
from structure.data_monitoring_system.monitoring_task.rocketmq_task import rocketmq_message_task

@exception(logger)
@combined_decorator(timeout_seconds=60, max_workers=10,scheduler_name="rocketmq_consumer")
class RocketMQConsumer:
    def __init__(self,**kwargs):
        """
        参数说明：
        - name_servers: NameServer地址(多个用分号分隔)
        - consumer_group: 消费者组名
        - topics: 订阅主题列表
        - retry_policy: 自定义重试策略(默认：{"max_attempts": 5, "base_delay": 2})
        """
        self.environment_flag = kwargs.get("environment_flag", "test")
        if self.environment_flag not in ["prod", "pred"]:
            self.environment_flag = "test"
        mq_config = read_json_file("rocketmq_conf.json")
        env_config = mq_config.get(self.environment_flag)
        # MQ服务地址
        self.name_servers = env_config.get("name_servers")
        # MQ消费组名
        self.consumer_group = env_config.get("consumer_group")
        # MQ订阅主题列表
        self.topics = env_config.get("topics")
        # MQ消费线程数
        self.thread_count = env_config.get("thread_count")
        # MQ重试策略
        self._retry_policy = env_config.get("retry_policy") or {"max_attempts": 5, "base_delay": 2}
        self._consumer = None
        self._running = Event()
        # 添加连接状态标志
        self._connection_status = {
            "last_message_time": 0,  # 最后一次收到消息的时间
            "connection_verified": False,  # 连接是否已验证
            "connection_error": None  # 连接错误信息
        }

    def validate_config(self):
        """验证MQ配置是否有效"""
        try:
            # 验证消费组
            if not self.consumer_group:
                logger.error("消费组名称为空")
                return False

            # 验证NameServer地址
            if not self.name_servers:
                logger.error("NameServer地址为空")
                return False

            # 验证主题列表
            if not self.topics or not isinstance(self.topics, list) or len(self.topics) == 0:
                logger.error("订阅主题列表为空")
                return False

            # 验证NameServer地址格式
            for server in self.name_servers.split(';'):
                if not server or ':' not in server:
                    logger.error(f"NameServer地址格式错误: {server}")
                    return False

                host, port = server.split(':')
                if not host or not port or not port.isdigit():
                    logger.error(f"NameServer地址格式错误: {server}")
                    return False

            # 尝试创建消费者实例进行验证
            test_consumer = PushConsumer(group_id=self.consumer_group)

            # 尝试设置NameServer地址
            try:
                test_consumer.set_namesrv_addr(self.name_servers)
                logger.info(f"NameServer地址设置成功：{self.name_servers}")
            except Exception as e:
                logger.error(f"NameServer地址设置失败: {e}")
                return False

            # 尝试连接并立即关闭
            try:
                # 设置一个简单的回调函数进行测试
                def test_callback(msg):
                    pass

                # 订阅一个主题进行测试
                test_consumer.subscribe(self.topics[0], test_callback)

                # 尝试启动消费者
                logger.info("尝试启动消费者并连接到RocketMQ服务器...")

                # 在启动前记录时间，用于检测启动是否异常快速返回
                start_time = time.time()

                try:
                    # 尝试启动消费者
                    test_consumer.start()

                    # 计算启动消费者所花费的时间
                    elapsed_time = time.time() - start_time
                    logger.info(f"消费者启动耗时: {elapsed_time:.2f} 秒")

                    # 等待一段时间，给消费者连接的时间
                    logger.info("等待消费者连接建立...")
                    time.sleep(3)

                    # 检查消费者实例的属性
                    attrs = dir(test_consumer)
                    logger.info(f"消费者实例的属性: {attrs}")

                    # 检查消费者实例的类型
                    logger.info(f"消费者实例类型: {type(test_consumer)}")

                    # 在这里我们不能真正验证连接是否成功
                    # 但是我们可以假设如果启动没有抛出异常，并且耗时合理，则连接可能是成功的
                    logger.info("消费者启动成功，但无法完全验证连接状态")

                    # 关闭测试消费者
                    test_consumer.shutdown()
                    logger.info("测试消费者已关闭")

                    # 返回成功
                    return True
                except Exception as e:
                    logger.error(f"RocketMQ连接测试失败: {e}")
                    try:
                        test_consumer.shutdown()
                    except:
                        pass
                    return False




            except Exception as e:
                logger.error(f"RocketMQ连接测试失败: {e}")
                try:
                    test_consumer.shutdown()
                except:
                    pass
                return False
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False




    def _init_consumer(self):
        """初始化消费者实例"""
        try:
            # 创建消费者实例
            self._consumer = PushConsumer(
                group_id=self.consumer_group
            )
            logger.info(f"消费者实例创建，消费组为：{self.consumer_group}")

            # 设置NameServer地址
            if not self.name_servers:
                logger.error("NameServer地址为空，初始化失败")
                return False

            self._consumer.set_namesrv_addr(self.name_servers)
            logger.info(f"设置NameServer地址：{self.name_servers}")

            # 设置线程数
            self._consumer.set_thread_count(self.thread_count)
            logger.info(f"设置消费线程数：{self.thread_count}")

            # 订阅主题
            if not self.topics:
                logger.error("没有指定订阅主题，初始化失败")
                return False

            self._check_connection()

            for topic in self.topics:
                self._consumer.subscribe(topic, self._message_callback)
                logger.info(f"订阅主题：{topic}")

            logger.info("消费者初始化完成，等待连接验证")
            return True
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False

    def _message_callback(self, msg):
        """消息处理回调函数"""
        try:
            # 更新最后一次收到消息的时间
            self._connection_status["last_message_time"] = time.time()
            self._connection_status["connection_verified"] = True
            self._connection_status["connection_error"] = None

            # print(msg)
            logger.info("消息任务已经开始执行")
            msg_topic=msg.topic
            msg_tags=msg.tags.decode('utf-8')
            data_group_name=f"{msg_topic}_{msg_tags}"
            data_srouce = "RocketMQ"
            logger.info(f"收到消息 [Topic={msg.topic}],Tag={msg.tags}, ID={msg.id}，queue_offset={msg.queue_offset}，commit_log_offset={msg.commit_log_offset}，prepared_transaction_offset={msg.prepared_transaction_offset}")
            rocketmq_message_task(msg,data_group_name=data_group_name,data_srouce=data_srouce,environment_flag=self.environment_flag)

            return True
        except Exception as e:
            logger.error(f"消息处理失败: {str(e)}")
            raise

    def _check_connection(self):
        """检查连接状态

        注意：由于 RocketMQ Python 客户端没有提供直接的方法来验证连接状态，
        我们只能通过间接方式来检测。这不是完美的解决方案，但是当前最好的选择。
        """
        try:
            # 检查消费者实例是否存在
            if self._consumer is None:
                logger.error("消费者实例为空，连接失败")
                return False

            # 尝试获取消费者实例的属性列表
            # 这不能直接验证连接，但可以提供一些信息
            attrs = dir(self._consumer)
            logger.info(f"消费者实例的属性: {attrs}")

            # 检查消费者实例的类型
            logger.info(f"消费者实例类型: {type(self._consumer)}")

            # 检查消费者实例的内存地址
            # 这可以确认实例是否有效
            logger.info(f"消费者实例内存地址: {id(self._consumer)}")

            # 检查namesrv_addr是否正确设置
            # 这不能直接验证连接，但可以确认配置是否正确
            logger.info(f"配置的NameServer地址: {self.name_servers}")

            # 由于没有直接的方法来验证连接，我们只能假设如果消费者实例存在并且没有抛出异常，则连接可能是成功的
            # 这不是完全可靠的，但是当前最好的选择
            logger.info("没有直接的方法来验证连接状态，假设连接可能成功")

            # 在这里返回 False，这样在地址错误的情况下不会误报连接成功
            # 实际的连接状态将在收到消息时更新
            return False
        except Exception as e:
            logger.error(f"连接检查失败: {e}")
            return False

    def _exponential_backoff(self, attempt):
        """指数退避重试策略"""
        delay = self._retry_policy["base_delay"] * (2 ** (attempt - 1))
        return min(delay, 30)  # 最大延迟30秒

    def _reconnect(self):
        """自动重连机制"""
        attempt = 1
        while attempt <= self._retry_policy["max_attempts"] and self._running.is_set():
            try:
                logger.error(f"尝试重连 ({attempt}/{self._retry_policy['max_attempts']})")

                if self._init_consumer():
                    try:
                        self._consumer.start()

                        # 在启动前记录时间，用于检测启动是否异常快速返回
                        start_time = time.time()

                        # 计算启动消费者所花费的时间
                        elapsed_time = time.time() - start_time
                        logger.error(f"消费者重连启动耗时: {elapsed_time:.2f} 秒")


                        # 等待一段时间，给消费者连接的时间
                        logger.info("等待消费者重连建立...")
                        time.sleep(3)

                        # 记录警告信息，说明无法完全验证连接状态
                        logger.error("消费者已重新启动，但无法完全验证连接状态")
                        logger.error("如果地址错误，可能会在日志中看到连接失败的错误信息")
                        logger.error(f"当前配置的NameServer地址: {self.name_servers}")

                        return True
                    except Exception as e:
                        logger.error(f"启动消费者失败: {e}")

                attempt += 1
                delay = self._exponential_backoff(attempt)
                logger.error(f"重试等待 {delay} 秒")
                time.sleep(delay)
            except Exception as e:
                logger.error(f"重连失败: {e}")
                attempt += 1
                time.sleep(self._exponential_backoff(attempt))
        logger.error("达到最大重试次数，重连失败")
        return False

    def start(self):
        """启动消费者"""
        self._running.set()
        logger.info("启动消费者...")

        try:
            while self._running.is_set():
                try:
                    if not self._consumer or not self._consumer.is_running():
                        logger.info("开始初始化消费者...")
                        if not self._init_consumer():
                            logger.error("消费者初始化失败，无法启动")
                            raise Exception("初始化失败")

                        try:
                            # 尝试启动消费者并验证连接
                            logger.info("尝试启动消费者并连接到RocketMQ服务器...")
                            self._consumer.start()

                            # 在启动前记录时间，用于检测启动是否异常快速返回
                            start_time = time.time()

                            # 计算启动消费者所花费的时间
                            elapsed_time = time.time() - start_time
                            logger.info(f"消费者启动耗时: {elapsed_time:.2f} 秒")

                            # 如果启动非常快（小于0.1秒），可能是因为地址错误导致的快速失败
                            # if elapsed_time < 0.1:
                            #     logger.error("消费者启动过快，可能是因为NameServer地址错误")
                            #     raise Exception("消费者启动过快，可能是因为NameServer地址错误")

                            # 等待一段时间，给消费者连接的时间
                            logger.info("等待消费者连接建立...")
                            time.sleep(3)

                            # 记录警告信息，说明无法完全验证连接状态
                            logger.info("消费者已启动，但无法完全验证连接状态")
                            logger.info("如果地址错误，可能会在日志中看到连接失败的错误信息")
                            logger.info(f"当前配置的NameServer地址: {self.name_servers}")
                        except Exception as e:
                            logger.error(f"消费者连接失败: {e}")
                            raise

                    # 保持主线程活跃
                    connection_check_count = 0
                    connection_timeout = 30  # 连接超时时间（秒）
                    while self._running.is_set():
                        time.sleep(30)  # 心跳间隔
                        logger.info("消费者保持活跃")

                        # 检查连接状态
                        connection_check_count += 1

                        # 每6次检查一次连接状态（大约30秒）
                        if connection_check_count >= 6:
                            connection_check_count = 0

                            # 如果连接已验证，则跳过
                            if self._connection_status["connection_verified"]:
                                logger.info("连接状态正常，已收到消息")
                                continue

                            # 如果连接未验证，检查是否超时
                            current_time = time.time()
                            start_time = self._connection_status["last_message_time"]

                            # 如果还没有收到过消息，使用启动时间作为起始时间
                            if start_time == 0:
                                start_time = current_time - 5  # 假设刚启动5秒

                            # 如果超过连接超时时间还没有收到消息，可能连接失败
                            if current_time - start_time > connection_timeout:
                                logger.error(f"连接可能失败，{connection_timeout}秒内没有收到消息")
                                self._connection_status["connection_error"] = f"连接超时，{connection_timeout}秒内没有收到消息"

                                # 尝试重新连接
                                logger.error("尝试重新连接...")
                                if not self._reconnect():
                                    logger.error("重连失败，可能是NameServer地址错误")
                                    # 这里不抛出异常，而是继续尝试
                            else:
                                logger.error(f"等待连接验证，已等待{int(current_time - start_time)}秒")
                except RuntimeError as e:
                    logger.error(f"启动失败（连接问题）: {e}")
                    self._reconnect()
                except ConnectionRefusedError as e:
                    logger.error(f"连接被拒绝，检查网络和端口: {e}")
                except TimeoutError as e:
                    logger.error(f"连接超时: {e}")
                except (Exception, ConnectionError) as e:
                    logger.error(f"连接异常: {str(e)}")
                    if not self._reconnect():
                        logger.error("达到最大重试次数，停止重连")
                        break
        finally:
            self.stop()

    def stop(self):
        """停止消费者"""
        if self._running.is_set():
            self._running.clear()
            logger.error("停止消费者...")
            try:
                if self._consumer:
                    self._consumer.shutdown()
            except Exception as e:
                logger.error(f"关闭异常: {str(e)}")
            logger.error("消费者已停止")

if __name__ == "__main__":
    # 生成模拟消息
    # mock_message = mock_offline_order_message()
    consumer = RocketMQConsumer()
    consumer.start()
