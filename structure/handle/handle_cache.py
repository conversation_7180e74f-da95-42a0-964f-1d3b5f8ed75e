# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/6 10:42
@Auth ： 逗逗的小老鼠
@File ：handle_cache.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from typing import List, Dict, Any
from lib.get_log import logger,exception

def update_cache():
    """更新内存缓存"""
    global cached_data
    new_data = {"test":1}
    if new_data:
        # 原子操作替换缓存引用
        cached_data = new_data
        logger.info("内存缓存更新完成")

def get_cached_data() -> List[Dict[str, Any]]:
    """获取当前缓存数据"""
    return cached_data.copy()  # 返回副本防止原始数据被修改


if __name__ == "__main__":
    update_cache()
    print(get_cached_data())