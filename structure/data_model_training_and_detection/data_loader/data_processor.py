# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/15 09:36
@Auth ： 逗逗的小老鼠
@File ：data_processor.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import dask.dataframe as dd
import pandas as pd
from featuretools import EntitySet, dfs


class DataProcessor:
    def __init__(self, config_path="configs/feature_rules.json"):
        self.config = self._load_config(config_path)

    def _load_config(self, path):
        # 加载特征生成规则
        ...

    def _type_optimization(self, df):
        # 内存优化类型转换
        for col in df.columns:
            if df[col].dtype == 'float64':
                df[col] = df[col].astype('float32')
            elif df[col].dtype == 'int64':
                df[col] = df[col].astype('int32')
        return df

    def _generate_auto_features(self, es):
        # 自动化特征生成
        feature_matrix, features = dfs(
            entityset=es,
            target_entity="orders",
            agg_primitives=["sum", "mean", "count"],
            trans_primitives=["month", "weekday"]
        )
        return feature_matrix

    def process(self, data_path):
        # 分块读取数据
        df = dd.read_parquet(data_path)
        df = self._type_optimization(df)

        # 构建实体集
        es = EntitySet()
        es = es.entity_from_dataframe(
            entity_id="orders",
            dataframe=df.compute(),
            index="offline_order_order_no",
            time_index="offline_order_created"
        )

        # 生成特征
        feature_matrix = self._generate_auto_features(es)
        return feature_matrix.persist()