# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/3 10:49
@Auth ： 逗逗的小老鼠
@File ：role_config_data.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception, logger
import os,json
import threading
# 创建一个字典来存储每个文件的锁
_file_locks = {}
_file_locks_lock = threading.RLock()

@exception(logger)
class RoleConfigData:
    def __init__(self):
        self.data = []

    def add_config_submitter_data(self, **kwargs):
        "新增报批人"
        try:
            case_data=[]
            # 从kwargs中获取用例编号
            case_no = kwargs.get("case_no")
            submitter_data = self.read_json_file("submitter_data.json")

            for submitter_item in submitter_data:
                case_value = submitter_item.get("data")
                case_data_no=submitter_item.get("case_no")
                if case_no:
                    if str(case_data_no) == str(case_no):
                        case_data.append(case_value)
                else:
                    case_data.append(case_value)
            return case_data
        except Exception as e:
            raise e

    def read_json_file(self,json_name):
        "获取json配置文件数据，线程安全"
        try:
            # 获取当前文件路径
            current_directory = os.path.dirname(os.path.abspath(__file__))
            # 配置yaml文件路径
            conf_path = os.path.abspath(os.path.dirname(current_directory) + os.path.sep + "case_data/" + json_name)


            # 打开JSON文件，使用utf-8编码读取文件内容
            with open(conf_path, 'r', encoding='utf-8') as f:
                # 读取文件内容
                file_content = f.read().strip()

                # 检查文件是否为空
                if not file_content:
                    logger.error(f"JSON file is empty: {conf_path}")
                    return {}

                try:
                    # 尝试解析JSON
                    json_data = json.loads(file_content)
                    return json_data
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON format in file {conf_path}: {str(e)}")
                    # 返回空字典而不是抛出异常，以便程序可以继续运行
                    return {}
        except FileNotFoundError as e:
            logger.error(f"File not found: {json_name}")
            # 如果文件不存在，返回空字典
            return {}
        except IOError as e:
            logger.error(f"IO error occurred while reading file: {json_name}")
            # 如果发生IO错误，返回空字典
            return {}
        except Exception as e:
            logger.error(f"An error occurred while reading the JSON file {json_name}: {str(e)}")
            # 如果发生其他异常，返回空字典
            return {}


if __name__ == "__main__":
    role_config_data = RoleConfigData()
    data = role_config_data.read_json_file("submitter_data.json")
    print(data)