# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/3 19:52
@Auth ： 逗逗的小老鼠
@File ：flow_config_data.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
import os,json
import itertools

@exception(logger)
class FlowConfigData:
    def __init__(self):
        self.data = []
    def generate_flow_config_data(self, **kwargs):
        "新增流程配置"
        try:
            flow_data = self.read_json_file("flow_config_data.json")
            case_no=0
            result = []

            for flow_item in flow_data:
                # 获取审批流程审批接口配置角色信息
                approverRoleCode = flow_item.get("approverRoleCode")
                # 移除审批角色信息
                flow_item.pop("approverRoleCode")
                # 获取所有字段名
                field_names = list(flow_item.keys())

                # 获取所有字段的取值列表
                value_lists = list(flow_item.values())

                # 使用 itertools.product 生成所有可能的组合
                for values in itertools.product(*value_lists):
                    record = dict(zip(field_names, values))
                    case_no += 1
                    config_data = {
                        "case_no": case_no,
                        "base_config": record,
                        "approver_config": approverRoleCode
                    }
                    result.append(config_data)
            return result

        except Exception as e:
            logger.error(f"生成流程配置数据失败:{e}")
            raise e

    def add_flow_config_data(self, **kwargs):
        "新增流程配置"
        try:
            scope = kwargs.get("scope")
            companyCode = kwargs.get("companyCode")
            submiterRoleCode = kwargs.get("submiterRoleCode")
            adjustType = kwargs.get("adjustType")
            flowType = kwargs.get("flowType")
            case_data = self.generate_flow_config_data()
            # 根据传入的参数进行过滤
            for case_item in case_data:
                base_config = case_item.get("base_config")
                # 项目属性
                if scope:
                    if base_config.get("scope") != scope and case_item in case_data:
                        case_data.remove(case_item)
                # 所属公司编码
                if companyCode:
                    if base_config.get("companyCode") != companyCode and case_item in case_data:
                        case_data.remove(case_item)
                # 报批人角色编码
                if submiterRoleCode:
                    if base_config.get("submiterRoleCode") != submiterRoleCode and case_item in case_data:
                        case_data.remove(case_item)
                # 定调价维度
                if adjustType:
                    if base_config.get("adjustType") != adjustType and case_item in case_data:
                        case_data.remove(case_item)
                # 长短流程
                if flowType:
                    if base_config.get("flowType") != flowType and case_item in case_data:
                        case_data.remove(case_item)
            request_list=[]
            # 组装提交参数
            for case_item in case_data:
                request_data={}
                base_config = case_item.get("base_config")
                name_value=[]
                for key,value in base_config.items():
                    key_value=self.get_map_value(key,value)
                    name_value.append(key_value)
                    request_data[key]=value
                name="_".join(name_value)
                approver_config = case_item.get("approver_config")
                chinese_digits = ['一', '二', '三', '四', '五', '六', '七', '八', '九']
                approver_index=0
                approverConfigList=[]
                for approver_item in approver_config:
                    code=f"{chinese_digits[approver_index]}审"
                    approver_name=self.get_map_value("approverRoleCode",approver_item)
                    node_name = f"{code}_{approver_name}"
                    description=f"{node_name}_自动化配置"
                    approver_value={"id": approver_index, "code": code, "name": node_name,"roleCode":approver_item, "description": description}
                    approverConfigList.append(approver_value)
                    approver_index+=1
                request_data["configNodeList"]=approverConfigList
                request_list.append(request_data)
            return request_list
        except Exception as e:
            raise e

    def get_map_value(self,file_name,field_key):
        "获取字典中key对应的值"
        try:
            key_value=""
            flow_data = self.read_json_file("field_map.json")
            file_data=flow_data.get(file_name)
            if file_data:
                key_value=file_data.get(field_key,"")
            return key_value
        except Exception as e:
            raise e


    def read_json_file(self,json_name):
        "获取json配置文件数据，线程安全"
        try:
            # 获取当前文件路径
            current_directory = os.path.dirname(os.path.abspath(__file__))
            # 配置yaml文件路径
            conf_path = os.path.abspath(os.path.dirname(current_directory) + os.path.sep + "case_data/" + json_name)


            # 打开JSON文件，使用utf-8编码读取文件内容
            with open(conf_path, 'r', encoding='utf-8') as f:
                # 读取文件内容
                file_content = f.read().strip()

                # 检查文件是否为空
                if not file_content:
                    logger.error(f"JSON file is empty: {conf_path}")
                    return {}

                try:
                    # 尝试解析JSON
                    json_data = json.loads(file_content)
                    return json_data
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON format in file {conf_path}: {str(e)}")
                    # 返回空字典而不是抛出异常，以便程序可以继续运行
                    return {}
        except FileNotFoundError as e:
            logger.error(f"File not found: {json_name}")
            # 如果文件不存在，返回空字典
            return {}
        except IOError as e:
            logger.error(f"IO error occurred while reading file: {json_name}")
            # 如果发生IO错误，返回空字典
            return {}
        except Exception as e:
            logger.error(f"An error occurred while reading the JSON file {json_name}: {str(e)}")
            # 如果发生其他异常，返回空字典
            return {}


if __name__ == "__main__":
    flow_config_data=FlowConfigData()
    data=flow_config_data.add_flow_config_data(scope="COMPANY",companyCode="1000",submiterRoleCode="STORE_S_1",adjustType="ADJUSTMENT",flowType="LONG")
    print(data)