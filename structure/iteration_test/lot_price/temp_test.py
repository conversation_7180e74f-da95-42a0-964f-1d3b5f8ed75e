
# -*- coding: utf-8 -*-
"""
@Time ： 2025/6/3 18:46
@Auth ： 逗逗的小老鼠
@File ：temp_test.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import itertools

input_data = {
    "scope": ["COMPANY"],
    "companyCode": ["1000", "1001", "1002", "1004", "1005", "1006", "1007", "1008", "1009", "1010", "1012", "1015", "1017", "1018", "1024", "1003"],
    "submiterRoleCode": ["STORE_S_1"],
    "adjustType": ["ADJUSTMENT"],
    "flowType": ["LONG", "SHORT"]
}

# 获取所有字段名
field_names = list(input_data.keys())

# 获取所有字段的取值列表
value_lists = list(input_data.values())

# 使用 itertools.product 生成所有可能的组合
result = []
for values in itertools.product(*value_lists):
    record = dict(zip(field_names, values))
    result.append(record)

# 输出结果
import json
print(json.dumps(result, ensure_ascii=False, indent=2))