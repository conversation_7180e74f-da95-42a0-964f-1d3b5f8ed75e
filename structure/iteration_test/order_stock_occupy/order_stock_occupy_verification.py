# -*- coding: utf-8 -*-
"""
@Time ： 2024/8/23 10:55
@Auth ： 逗逗的小老鼠
@File ：order_stock_occupy_verification.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json

from lib.get_log import exception, logger
from structure.iteration_test.order_stock_occupy.order_base_info import order_commodity_stock_record, \
    order_correlation_info,order_b2c_oms_info,order_b2c_ex_info
from datetime import datetime


@exception(logger)
def order_stock_verification(third_order_no, **kwargs):
    try:
        order_info = order_correlation_info(third_order_no, **kwargs)
        order_stock_record = order_commodity_stock_record(third_order_no, **kwargs)
        verfication_list=[]
        verfication_result=1
        verfication_num=0
        for order_item in order_info:
            order_no = order_item['order_no']
            service_mode = order_item['service_mode']
            # 处理库存占用释放记录，区分占用和释放
            # 占用记录列表
            order_stock_occupy_record = []
            # 释放记录列表
            order_stock_release_record = []
            # 异常记录列表
            order_stock_exception_record = []
            order_stock_record.sort(key=lambda x: (
                x["stock_create_time"] is None, x["stock_create_time"] == "", x["stock_create_time"]))
            # 遍历库存占用释放记录，按照订单号进行分类
            for stock_item in order_stock_record:
                stock_order_no = stock_item['order_no']
                stock_type = stock_item['stock_type']
                if str(stock_order_no) == str(order_no):
                    # 类型为1、3、5为占用，2、4、6为释放，7为异常
                    if stock_type == 1 or stock_type == 3 or stock_type == 5:
                        order_stock_occupy_record.append(stock_item)
                    elif stock_type == 2 or stock_type == 4 or stock_type == 6:
                        order_stock_release_record.append(stock_item)
                    else:
                        order_stock_exception_record.append(stock_item)
                # 将记录分别按照时间排序
                order_stock_occupy_record.sort(key=lambda x: (
                    x["stock_create_time"] is None, x["stock_create_time"] == "", x["stock_create_time"]))
                order_stock_release_record.sort(key=lambda x: (
                    x["stock_create_time"] is None, x["stock_create_time"] == "", x["stock_create_time"]))
                order_stock_exception_record.sort(key=lambda x: (
                    x["stock_create_time"] is None, x["stock_create_time"] == "", x["stock_create_time"]))
            if service_mode == 'B2C':
                # 获取B2C的仓库列表信息
                warehouselist = order_item['warehouseList']
                default_warehouse = []
                no_default_warehouse = []
                # 将仓库列表按照默认仓库和非默认仓库进行分类处理
                for warehouse in warehouselist:
                    if warehouse['is_default'] == 1:
                        default_warehouse.append(warehouse)
                    else:
                        no_default_warehouse.append(warehouse)
                # no_default_warehouse.sort(key=lambda x:x['priority'])
                # 非默认仓库类型仓库按照优先级排序
                no_default_warehouse.sort(key=lambda x: (x["priority"] is None, x["priority"] == "", x["priority"]))
                verfication_data = B2C_order_stock_verification(order_item, default_warehouse, no_default_warehouse, order_stock_record, **kwargs)
                verfication_list.extend(verfication_data)
            if len(verfication_list)>0:
                verfication_result=0
                verfication_num=len(verfication_list)
            else:
                # 此处为O2O订单库存验证逻辑
                pass
        result={"verfication":{"verfication_result":verfication_result,"verfication_num":verfication_num,"verfication_list":verfication_list},"data":{"order_info":order_info,"order_stock_record":order_stock_record}}
        return result
    except Exception as e:
        raise e


def B2C_order_stock_verification(order_info, default_warehouse, no_default_warehouse, order_stock_record, **kwargs):
    "B2C的库存占用释放记录验证"
    try:
        order_stock_occupy_verification_result = []
        order_detail = order_info['orderDetailList']
        order_erp_state = order_info['erp_state']
        order_deleted = order_info['deleted']
        order_no = order_info['order_no']
        order_state = order_info['order_state']
        # 订单异常状态，默认为0，非异常
        order_lock_flag= 0
        b2c_ex_info=order_b2c_ex_info(order_no,**kwargs)
        for ex_item in b2c_ex_info:
            # 异常订单状态
            ex_order_status=ex_item['ex_order_status']
            # 异常类型
            ex_type=ex_item['ex_type']
            # 异常描述
            ex_type_desc=ex_item['ex_type_desc']
            # 异常原因
            ex_reason=ex_item['ex_reason']
            # 异常处理状态
            operate_status=ex_item['operate_status']
            # 如果异常类型为5-商品编码不存在 6-OMS缺货 时，判断为异常订单
            if ex_type == 5 or ex_type == 6:
                order_lock_flag = 1
        # 判断是否为wms发货,默认值为0，非wms发货
        join_wms = 0
        oms_info=order_b2c_oms_info(order_no,**kwargs)
        for oms_item in oms_info:
            join_wms=oms_item['join_wms']
        # wms发货订单，不验证库存占用释放记录
        if join_wms != 0:
            if len(order_stock_record)>0:
                verification_fail_result = {"order_no": order_no, "erp_code": "",
                                            "detail_id": "",
                                            "serial_number": "",
                                            "verifica_desc": "该订单为wms发货订单，不应存在库存占用记录",
                                            "order_value": join_wms,
                                            "stock_value": ""}
                order_stock_occupy_verification_result.append(verification_fail_result)
        else:
            # 占用记录验证
            for detail_item in order_detail:
                detail_id = detail_item['detail_id']
                erp_code = detail_item['erp_code']
                bar_code = detail_item['bar_code']
                detail_status=detail_item['detail_status']
                commodity_count = detail_item['commodity_count']
                is_joint = detail_item['is_joint']
                # 组合商品类型为2时，不进行库存占用验证
                if is_joint == 2:
                    pass
                else:
                    default_warehouse_item = default_warehouse[0]
                    default_warehouseId = default_warehouse_item['warehouseId']
                    # 默认仓库的占用成功记录数量标识，默认值为0，占用成功+1
                    default_warehouse_occupy_success_flag = 0
                    # 默认仓库的占用失败记录数量标识，默认值为0，占用成功+1
                    default_warehouse_occupy_fail_flag = 0
                    # 默认仓库的占用成功时间，默认值为空，占用成功赋值
                    default_warehouse_occupy_success_time = ""
                    # 默认仓库的占用成功序号，默认值为空，占用成功赋值
                    default_warehouse_occupy_success_serial_number = ""
                    # 非默认仓库的占用成功记录数量标识，默认值为0，占用成功+1
                    no_default_warehouse_occupy_success_flag = 0
                    # 非默认仓库的占用失败记录数量标识，默认值为0，占用成功+1
                    no_default_warehouse_occupy_fail_flag = 0
                    # 非默认仓库的占用成功时间，默认值为空，占用成功赋值
                    no_default_warehouse_occupy_success_time = ""
                    # 默认仓库的占用成功序号，默认值为空，占用成功赋值
                    no_default_warehouse_occupy_success_serial_number = ""
                    # 默认仓库的释放成功记录数量标识，默认值为0，占用成功+1
                    default_warehouse_release_success_flag = 0
                    # 默认仓库的释放失败记录数量标识，默认值为0，占用成功+1
                    default_warehouse_release_fail_flag = 0
                    # 默认仓库的释放成功时间，默认值为空，占用成功赋值
                    default_warehouse_release_success_time = ""
                    # 非默认仓库的释放成功序号，默认值为空，占用成功赋值
                    default_warehouse_release_success_serial_number = ""
                    # 非默认仓库的释放成功记录数量标识，默认值为0，占用成功+1
                    no_default_warehouse_release_success_flag = 0
                    # 非默认仓库的释放失败记录数量标识，默认值为0，占用成功+1
                    no_default_warehouse_release_fail_flag = 0
                    # 非默认仓库的释放成功时间，默认值为空，占用成功赋值
                    no_default_warehouse_release_success_time = ""
                    # 非默认仓库的释放成功序号，默认值为空，占用成功赋值
                    no_default_warehouse_release_success_serial_number = ""
                    # 占用仓库记录列表
                    goods_occupy_warehouseId = []
                    for occupy_item in order_stock_record:
                        occupy_order_detail_id = occupy_item['order_detail_id']
                        stock_type = occupy_item['stock_type']
                        stock_qty = occupy_item['stock_qty']
                        commodity_bar_code = occupy_item['commodity_bar_code']
                        store_id = occupy_item['store_id']
                        commodity_occupy_num = occupy_item['commodity_occupy_num']
                        commodity_release_num= occupy_item['commodity_release_num']
                        stock_create_time = occupy_item['stock_create_time']
                        serial_number = occupy_item['serial_number']
                        if str(occupy_order_detail_id) == str(detail_id):
                            # 如果仓库不在占用仓库列表中，则添加
                            if str(store_id) not in goods_occupy_warehouseId:
                                goods_occupy_warehouseId.append(str(store_id))
                            # 判断默认仓库的占用记录是否正确
                            if str(default_warehouseId) == str(store_id):
                                if stock_type == 1:
                                    default_warehouse_occupy_success_flag = default_warehouse_occupy_success_flag + 1
                                    default_warehouse_occupy_success_time = stock_create_time
                                    default_warehouse_occupy_success_serial_number = serial_number
                                    if commodity_count+commodity_occupy_num !=0:
                                        verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                    "detail_id": detail_id, "serial_number": serial_number,
                                                                    "verifica_desc": "商品侧占用成功数量与商品数量不一致",
                                                                    "order_value": commodity_count,
                                                                    "stock_value": commodity_occupy_num}
                                        order_stock_occupy_verification_result.append(verification_fail_result)
                                elif stock_type == 2:
                                    default_warehouse_release_success_flag = default_warehouse_release_success_flag + 1
                                    default_warehouse_release_success_time = stock_create_time
                                    default_warehouse_release_success_serial_number = serial_number
                                    if commodity_count-commodity_release_num !=0:
                                        verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                    "detail_id": detail_id, "serial_number": serial_number,
                                                                    "verifica_desc": "商品侧释放成功数量与商品数量不一致",
                                                                    "order_value": commodity_count,
                                                                    "stock_value": commodity_release_num}
                                        order_stock_occupy_verification_result.append(verification_fail_result)
                                elif stock_type == 3 or stock_type == 4:
                                    verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                "detail_id": detail_id, "serial_number": serial_number,
                                                                "verifica_desc": "订单存在占用/释放中间态记录，请稍后重试检测",
                                                                "order_value": stock_type, "stock_value": 1}
                                    order_stock_occupy_verification_result.append(verification_fail_result)
                                elif stock_type == 6:
                                    default_warehouse_occupy_fail_flag = default_warehouse_occupy_fail_flag + 1
                                elif stock_type == 5:
                                    default_warehouse_release_fail_flag = default_warehouse_release_fail_flag + 1
                                else:
                                    verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                "detail_id": detail_id, "serial_number": serial_number,
                                                                "verifica_desc": "订单存在占用/释放异常记录，请联系商品中台进行排查",
                                                                "order_value": stock_type, "stock_value": 1}
                                    order_stock_occupy_verification_result.append(verification_fail_result)
                            # 判断非默认仓库的占用记录是否正确
                            else:
                                for no_default_warehouse_item in no_default_warehouse:
                                    no_default_warehouse_item_id = no_default_warehouse_item['warehouseId']
                                    if str(no_default_warehouse_item_id) == str(store_id):
                                        if stock_type == 1:
                                            no_default_warehouse_occupy_success_flag = no_default_warehouse_occupy_success_flag + 1
                                            no_default_warehouse_occupy_success_time = stock_create_time
                                            no_default_warehouse_occupy_success_serial_number = serial_number
                                            if commodity_count + commodity_occupy_num != 0:
                                                verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                            "detail_id": detail_id,
                                                                            "serial_number": serial_number,
                                                                            "verifica_desc": "商品侧占用成功数量与商品数量不一致",
                                                                            "order_value": commodity_count,
                                                                            "stock_value": commodity_occupy_num}
                                                order_stock_occupy_verification_result.append(verification_fail_result)
                                        elif stock_type == 2:
                                            no_default_warehouse_release_success_flag = no_default_warehouse_release_success_flag + 1
                                            no_default_warehouse_release_success_time = stock_create_time
                                            no_default_warehouse_release_success_serial_number = serial_number
                                            if commodity_count - commodity_release_num != 0:
                                                verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                            "detail_id": detail_id,
                                                                            "serial_number": serial_number,
                                                                            "verifica_desc": "商品侧释放成功数量与商品数量不一致",
                                                                            "order_value": commodity_count,
                                                                            "stock_value": commodity_release_num}
                                                order_stock_occupy_verification_result.append(verification_fail_result)
                                        elif stock_type == 3 or stock_type == 4:
                                            verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                        "detail_id": detail_id,
                                                                        "serial_number": serial_number,
                                                                        "verifica_desc": "订单存在占用/释放中间态记录，请稍后重试检测",
                                                                        "order_value": stock_type, "stock_value": 1}
                                            order_stock_occupy_verification_result.append(verification_fail_result)
                                        elif stock_type == 6:
                                            no_default_warehouse_occupy_fail_flag = no_default_warehouse_occupy_fail_flag + 1
                                        elif stock_type == 5:
                                            no_default_warehouse_release_fail_flag = no_default_warehouse_release_fail_flag + 1
                                        else:
                                            verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                        "detail_id": detail_id,
                                                                        "serial_number": serial_number,
                                                                        "verifica_desc": "订单存在占用/释放异常记录，请联系商品中台进行排查",
                                                                        "order_value": stock_type, "stock_value": 1}
                                            order_stock_occupy_verification_result.append(verification_fail_result)
                            # 判断占用/释放数量是否与订单商品数量一致
                            if commodity_count - abs(stock_qty) != 0:
                                verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                            "detail_id": detail_id,
                                                            "serial_number": serial_number,
                                                            "verifica_desc": "订单商品数量与占用/释放记录数量不一致",
                                                            "order_value": commodity_count,
                                                            "stock_value": stock_qty}
                                order_stock_occupy_verification_result.append(verification_fail_result)
                            # 所有占用记录均需要判断69码是否一致
                            if str(commodity_bar_code) != str(
                                    bar_code) and commodity_bar_code != "" and commodity_bar_code != None:
                                verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                            "detail_id": detail_id, "serial_number": serial_number,
                                                            "verifica_desc": "订单商品中69码与占用记录69码不一致", "order_value": bar_code,
                                                            "stock_value": commodity_bar_code}
                                order_stock_occupy_verification_result.append(verification_fail_result)
                            # 所有占用记录均需要判断数量是否一致
                            if stock_qty != commodity_count:
                                verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                            "detail_id": detail_id, "serial_number": serial_number,
                                                            "verifica_desc": "订单商品数量与占用记录数量不一致",
                                                            "order_value": commodity_count, "stock_value": stock_qty}
                                order_stock_occupy_verification_result.append(verification_fail_result)

                    """以下逻辑为新逻辑"""
                    no_default_warehouseid_list=[]
                    for no_default_warehouseid_item in no_default_warehouse:
                        no_default_warehouseid = no_default_warehouseid_item['warehouseId']
                        no_default_warehouseid_list.append(no_default_warehouseid)
                    # 判断占用记录中仓库是否存在非该订单的仓库
                    for goods_occupy_warehouseid_item in goods_occupy_warehouseId:
                        if goods_occupy_warehouseid_item != default_warehouseId and goods_occupy_warehouseid_item not in no_default_warehouseid_list:
                            verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                        "detail_id": detail_id,
                                                        "serial_number": "",
                                                        "verifica_desc": "商品占用记录中存在非该订单的仓库id",
                                                        "order_value": 0,
                                                        "stock_value": goods_occupy_warehouseid_item}
                            order_stock_occupy_verification_result.append(verification_fail_result)
                    # 默认仓库占用成功和占用失败记录不能同时为0
                    if default_warehouse_occupy_success_flag ==0 and default_warehouse_occupy_fail_flag ==0:
                        verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                    "detail_id": detail_id,
                                                    "serial_number": "",
                                                    "verifica_desc": "默认仓库占用成功和占用失败记录不能同时为0",
                                                    "order_value": 0,
                                                    "stock_value": 0}
                        order_stock_occupy_verification_result.append(verification_fail_result)
                    # 如果默认仓库占用失败>0，则表示首次占用失败
                    if default_warehouse_occupy_fail_flag > 0:
                        # 默认首次占用失败，非默认仓库占用次数不能为0
                        if no_default_warehouse_occupy_success_flag == 0 and no_default_warehouse_occupy_fail_flag == 0:
                            verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                        "detail_id": detail_id,
                                                        "serial_number": "",
                                                        "verifica_desc": "默认首次占用失败，非默认仓库占用次数不能为0",
                                                        "order_value": 0,
                                                        "stock_value": 0}
                            order_stock_occupy_verification_result.append(verification_fail_result)
                        # 首次占用失败，订单异常单标识必须为10 || 20
                        if order_lock_flag ==0:
                            verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                        "detail_id": detail_id,
                                                        "serial_number": "",
                                                        "verifica_desc": "转仓成功的订单，异常单标识错误",
                                                        "order_value": "10 || 20",
                                                        "stock_value": order_lock_flag}
                            order_stock_occupy_verification_result.append(verification_fail_result)
                        # 如果默认仓库占用失败和成功均大于0，则表示异常单处理时重占成功
                        if default_warehouse_occupy_success_flag > 0:
                            # 异常单非默认仓库占用成功，转仓成功需要释放对应占用
                            if no_default_warehouse_occupy_success_flag > 0:
                                if no_default_warehouse_release_success_flag != default_warehouse_occupy_success_flag:
                                    verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                "detail_id": detail_id,
                                                                "serial_number": "",
                                                                "verifica_desc": "异常单处理时转仓成功，需要释放对应非默认仓库占用成功记录",
                                                                "order_value": default_warehouse_occupy_success_flag,
                                                                "stock_value": no_default_warehouse_release_success_flag}
                                    order_stock_occupy_verification_result.append(verification_fail_result)
                                # 转仓成功的订单，商品状态应为非异常
                                if order_lock_flag ==0:
                                    verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                "detail_id": detail_id,
                                                                "serial_number": "",
                                                                "verifica_desc": "转仓成功的订单，异常单标识错误",
                                                                "order_value": "10 || 20",
                                                                "stock_value": order_lock_flag}
                                    order_stock_occupy_verification_result.append(verification_fail_result)
                                # 转仓成功的记录，默认仓库占用成功时间、非默认仓库占用成功时间、释放成功时间必须同时存在
                                if default_warehouse_occupy_success_time!="" and no_default_warehouse_occupy_success_time !="" and no_default_warehouse_release_success_time!="":
                                    # 转仓后，默认仓库占用成功时间不应小于非默认仓库占用成功时间
                                    if datetime.strptime(default_warehouse_occupy_success_time,
                                                         '%Y-%m-%d %H:%M:%S') < datetime.strptime(
                                        no_default_warehouse_occupy_success_time, '%Y-%m-%d %H:%M:%S'):
                                        verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                    "detail_id": detail_id,
                                                                    "serial_number": "",
                                                                    "verifica_desc": "转仓后，默认仓库占用成功时间不应小于非默认仓库占用成功时间",
                                                                    "order_value": default_warehouse_occupy_success_time,
                                                                    "stock_value": no_default_warehouse_occupy_success_time}
                                        order_stock_occupy_verification_result.append(verification_fail_result)
                                    # 转仓后，默认仓库占用成功时间不应小于非默认仓库释放成功时间
                                    if datetime.strptime(default_warehouse_occupy_success_time,
                                                         '%Y-%m-%d %H:%M:%S') < datetime.strptime(
                                        no_default_warehouse_release_success_time, '%Y-%m-%d %H:%M:%S'):
                                        verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                    "detail_id": detail_id,
                                                                    "serial_number": "",
                                                                    "verifica_desc": "转仓后，默认仓库占用成功时间不应小于非默认仓库释放成功时间",
                                                                    "order_value": default_warehouse_occupy_success_time,
                                                                    "stock_value": no_default_warehouse_release_success_time}
                                        order_stock_occupy_verification_result.append(verification_fail_result)
                                else:
                                    verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                    "detail_id": detail_id,
                                                                    "serial_number": "",
                                                                    "verifica_desc": "转仓成功的记录，默认仓库占用成功时间、非默认仓库占用成功时间、释放成功时间必须同时存在",
                                                                    "order_value": default_warehouse_occupy_success_time,
                                                                    "stock_value": f"非默认仓库占用成功时间：{no_default_warehouse_occupy_success_time}，非默认仓库释放成功时间：{no_default_warehouse_release_success_time}"}
                                    order_stock_occupy_verification_result.append(verification_fail_result)
                        else:
                            # 默认仓库和非默认仓库占用成功均为0，非默认占用失败记录应与非默认仓库数一致
                            if no_default_warehouse_occupy_success_flag == 0:
                                if no_default_warehouse_occupy_fail_flag != len(no_default_warehouse):
                                    verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                "detail_id": detail_id,
                                                                "serial_number": "",
                                                                "verifica_desc": "订单已下账、订单已删除、订单已取消，默认仓库占用成功时，需要释放对应记录",
                                                                "order_value": len(no_default_warehouse),
                                                               "stock_value": no_default_warehouse_occupy_fail_flag}
                                    order_stock_occupy_verification_result.append(verification_fail_result)
                    else:
                        # 默认仓库占用成功和占用失败记录不能同时为0
                        if default_warehouse_occupy_success_flag == 0:
                            verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                        "detail_id": detail_id,
                                                        "serial_number": "",
                                                        "verifica_desc": "默认仓库占用成功和占用失败记录不能同时为0",
                                                        "order_value": 0,
                                                        "stock_value": 0}
                            order_stock_occupy_verification_result.append(verification_fail_result)
                        # 首次占用成功，非默认仓库占用记录应为0
                        if no_default_warehouse_occupy_success_flag!=0 or no_default_warehouse_occupy_fail_flag !=0:
                            verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                        "detail_id": detail_id,
                                                        "serial_number": "",
                                                        "verifica_desc": "默认仓库占用成功和占用失败记录不能同时为0",
                                                        "order_value": 0,
                                                        "stock_value": no_default_warehouse_occupy_success_flag+no_default_warehouse_occupy_fail_flag}
                            order_stock_occupy_verification_result.append(verification_fail_result)
                    # 订单已下账、订单已删除、订单已取消，默认仓库占用成功时，需要释放对应记录
                    if order_erp_state == 100 or order_deleted != 0 or order_state == 110:
                        # 默认仓库占用成功时
                        if default_warehouse_occupy_success_flag > 0:
                            # 默认仓库占用成功时，释放成功记录应等于占用成功次数
                            if default_warehouse_release_success_flag - default_warehouse_occupy_success_flag !=0:
                                verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                            "detail_id": detail_id,
                                                            "serial_number": "", "verifica_desc": "默认仓库占用成功时，释放成功记录应等于占用成功次数",
                                                            "order_value": default_warehouse_occupy_success_flag,
                                                            "stock_value": default_warehouse_release_success_flag}
                                order_stock_occupy_verification_result.append(verification_fail_result)
                        # 默认仓库占用失败时
                        else:
                            # 默认仓库占用失败，但存在非默认仓库占用成功，非默认仓库占用成功时，需要释放对应记录
                            if no_default_warehouse_occupy_success_flag > 0:
                                if no_default_warehouse_release_success_flag != no_default_warehouse_occupy_success_flag:
                                    verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                "detail_id": detail_id,
                                                                "serial_number": "",
                                                                "verifica_desc": "非默认仓库占用成功，但未成功释放",
                                                                "order_value": no_default_warehouse_occupy_success_flag,
                                                                "stock_value": no_default_warehouse_release_success_flag}
                                    order_stock_occupy_verification_result.append(verification_fail_result)
                            # 非默认仓库占用成功为0时,释放记录应为0
                            else:
                                if no_default_warehouse_release_success_flag !=0 or no_default_warehouse_release_fail_flag !=0:
                                    verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                                "detail_id": detail_id,
                                                                "serial_number": "",
                                                                "verifica_desc": "非默认仓库占用成功为0时,释放记录应为0",
                                                                "order_value": no_default_warehouse_occupy_success_flag,
                                                                "stock_value": no_default_warehouse_release_success_flag + no_default_warehouse_release_fail_flag}
                                    order_stock_occupy_verification_result.append(verification_fail_result)
                            # 默认仓库占用失败时，释放库存记录不能存在
                            if default_warehouse_release_success_flag != 0 or default_warehouse_release_fail_flag != 0:
                                verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                            "detail_id": detail_id,
                                                            "serial_number": "", "verifica_desc": "默认仓库占用失败，但释放库存记录存在",
                                                            "order_value": 0,
                                                            "stock_value": default_warehouse_release_success_flag}
                                order_stock_occupy_verification_result.append(verification_fail_result)
                    # 以下为通用检测逻辑
                    # 默认仓库占用成功和释放成功记录不能大于1
                    if default_warehouse_occupy_success_flag > 1 or default_warehouse_release_success_flag>1:
                        verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                    "detail_id": detail_id,
                                                    "serial_number": "",
                                                    "verifica_desc": "默认仓库占用成功和释放成功记录不能大于1",
                                                    "order_value": 0,
                                                    "stock_value": default_warehouse_occupy_success_flag + default_warehouse_release_success_flag}
                        order_stock_occupy_verification_result.append(verification_fail_result)
                    # 非默认仓库占用成功和释放成功记录不能大于1
                    if no_default_warehouse_occupy_success_flag > 1 or no_default_warehouse_release_success_flag > 1:
                        verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                    "detail_id": detail_id,
                                                    "serial_number": "",
                                                    "verifica_desc": "非默认仓库占用成功和释放成功记录不能大于1",
                                                    "order_value": 0,
                                                    "stock_value": no_default_warehouse_occupy_success_flag + no_default_warehouse_release_success_flag}
                        order_stock_occupy_verification_result.append(verification_fail_result)
                    # 非默认仓库占用记录总数不能大于非默认仓库数
                    if no_default_warehouse_occupy_success_flag+no_default_warehouse_occupy_fail_flag>len(no_default_warehouse):
                        verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                    "detail_id": detail_id,
                                                    "serial_number": "",
                                                    "verifica_desc": "非默认仓库占用记录总数不能大于非默认仓库数",
                                                    "order_value": no_default_warehouse_occupy_success_flag + no_default_warehouse_occupy_fail_flag,
                                                    "stock_value": len(no_default_warehouse)}
                        order_stock_occupy_verification_result.append(verification_fail_result)
                    # 存在释放失败记录
                    if default_warehouse_release_fail_flag > 0 or no_default_warehouse_release_fail_flag > 0:
                        verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                    "detail_id": detail_id,
                                                    "serial_number": "",
                                                    "verifica_desc": "存在释放失败记录，请核查",
                                                    "order_value": 0,
                                                    "stock_value": default_warehouse_release_fail_flag + no_default_warehouse_release_fail_flag}
                        order_stock_occupy_verification_result.append(verification_fail_result)
                    # 同时存在默认仓库占用和释放成功记录
                    if default_warehouse_occupy_success_flag>0 and default_warehouse_release_success_flag>0:
                        # 默认仓库占用成功和释放成功记录序列号不一致
                        if default_warehouse_occupy_success_serial_number != default_warehouse_release_success_serial_number:
                            verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                        "detail_id": detail_id,
                                                        "serial_number": "",
                                                        "verifica_desc": "默认仓库占用成功和释放成功记录序列号不一致，请核查",
                                                        "order_value": default_warehouse_occupy_success_serial_number,
                                                        "stock_value": default_warehouse_release_success_serial_number}
                            order_stock_occupy_verification_result.append(verification_fail_result)
                    # 同时存在非默认仓库占用和释放成功记录
                    if no_default_warehouse_occupy_success_flag > 0 and no_default_warehouse_release_success_flag > 0:
                        # 非默认仓库占用成功和释放成功记录序列号不一致
                        if no_default_warehouse_occupy_success_serial_number != no_default_warehouse_release_success_serial_number:
                            verification_fail_result = {"order_no": order_no, "erp_code": erp_code,
                                                        "detail_id": detail_id,
                                                        "serial_number": "",
                                                        "verifica_desc": "非默认仓库占用成功和释放成功记录序列号不一致，请核查",
                                                        "order_value": no_default_warehouse_occupy_success_serial_number,
                                                        "stock_value": no_default_warehouse_release_success_serial_number}
                            order_stock_occupy_verification_result.append(verification_fail_result)
            # 对占用释放记录中存在异常的数据进行提取
            for stock_record_item in order_stock_record:
                # 订单侧占用记录数据信息提取：订单号
                order_order_no = stock_record_item['order_no']
                # 订单侧占用/释放记录数据信息提取：erp编码
                order_erp_code = stock_record_item['erp_code']
                # 订单侧占用/释放记录数据信息提取：记录序列码
                order_serial_number = stock_record_item['serial_number']
                # 订单侧占用/释放记录数据信息提取：订单明细id
                order_detail_id=stock_record_item['order_detail_id']
                # 订单侧占用/释放记录数据信息提取：商品与订单记录一致性标识
                consistent_flag=stock_record_item['consistent_flag']
                # 订单侧占用/释放记录数据信息提取：差异清单
                diff_list=stock_record_item['diff_list']
                if consistent_flag==0:
                    verification_fail_result = {"order_no": order_order_no, "erp_code": order_erp_code,
                                                "detail_id": order_detail_id,
                                                "serial_number": order_serial_number,
                                                "verifica_desc": "库存占用记录，商品侧与订单侧存在不一致",
                                                "order_value": "",
                                                "stock_value": diff_list}
                    order_stock_occupy_verification_result.append(verification_fail_result)
        return order_stock_occupy_verification_result
    except Exception as e:
        raise e



if __name__ == '__main__':
    third_order_no="3801223600745365694"
    result=order_stock_verification(third_order_no)
    print(result)
    print(json.dumps(result,ensure_ascii=False))