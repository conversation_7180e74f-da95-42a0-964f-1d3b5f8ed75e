# -*- coding: utf-8 -*-
"""
@Time ： 2024/8/19 14:19
@Auth ： 逗逗的小老鼠
@File ：order_base_info.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect
import requests
from datetime import datetime

"""
    根据第三方订单号查询订单相关信息

    参数:
    third_order_no: 第三方订单号，用于查询订单信息
    **kwargs: 可变关键字参数，通常用于数据库连接配置等

    返回:
    包含组织信息的列表，每个元素是一个字典，包含订单相关信息和仓库信息
"""
def order_correlation_info(third_order_no, **kwargs):
    "根据第三方订单号查询订单相关信息"
    try:
        # 初始化结果列表
        result = []
        # 构建SQL查询语句，用于从订单信息表中获取特定第三方订单号且未被删除的订单信息
        query_sql = f"""
            SELECT
                order_no,
                order_state,
                erp_state,
                third_platform_code AS 'platformCode',
                online_store_code AS 'onlineStoreCode',
                organization_code AS 'organizationCode',
                organization_name AS 'organizationName',
                service_mode,
			    deleted 
            FROM
                order_info 
            WHERE
                third_order_no = %s 
                AND mer_code = '500001' 
        """
        # 查询参数，用于防止SQL注入
        query_val = (third_order_no)
        # 调用数据库连接函数，执行查询，获取查询结果
        query_result = db_mysql_connect("dscloud", query_sql, sql_val=query_val, **kwargs)
        # 获取查询结果的数据部分
        query_data = query_result['data']
        # 遍历查询结果，为每个订单获取仓库信息
        for item in query_data:
            # 提取订单号
            order_no = item['order_no']
            # 提取平台代码
            platform_code = item['platformCode']
            # 提取网店代码
            online_store_code = item['onlineStoreCode']
            # 提取服务模式
            service_mode = item['service_mode']
            # 调用函数获取网店仓库信息
            warehouse_info = onlinestore_warehouse_info(online_store_code, service_mode, platform_code, **kwargs)
            # 调用函数获取订单详情
            detail_info=order_detail_info(order_no, **kwargs)
            # 调用函数获取订单退货详情
            refund_detail = order_refund_info(order_no, **kwargs)
            # 将仓库信息添加到订单信息中
            item['onlineShopId'] = warehouse_info['onlineShopId']
            item['online_client_code'] = warehouse_info['online_client_code']
            item['warehouseList'] = warehouse_info['warehouseList']
            item['orderDetailList'] = detail_info
            item['refundDetailList'] = refund_detail
            # 将增强后的订单信息添加到结果列表
            result.append(item)
        # 返回结果列表
        return result
    # 捕获并抛出异常，确保函数失败时异常能被上层处理
    except Exception as e:
        raise e

"""
    根据系统订单号查询订单详情

    参数:
    order_no (str): 系统订单号，用于查询特定订单的详细信息
    **kwargs: 可变关键字参数，通常用于传递数据库连接配置信息

    返回:
    list: 包含符合查询条件的订单详情记录的列表

    抛出:
    Exception: 在查询过程中发生异常时抛出
"""
def order_detail_info(order_no, **kwargs):
    "根据系统订单号查询订单详情"
    try:
        # 构造SQL查询语句，用于获取指定系统订单号的订单详情
        query_sql=f"""
            SELECT
                info.order_no AS 'order_no',
                detail.id AS 'detail_id',
                detail.erp_code AS 'erp_code',
                detail.bar_code AS 'bar_code',
                detail.commodity_count AS 'commodity_count',
                detail.status AS 'detail_status',
                detail.is_joint AS 'is_joint',
                detail.third_detail_id AS 'third_detail_id'	
            FROM
                order_info info
                LEFT JOIN order_detail detail ON info.order_no = detail.order_no 
            WHERE
                info.order_no = %s 
                AND info.mer_code = '500001'
        """
        # 准备SQL查询参数
        query_val=(order_no)
        # 调用数据库连接函数，执行SQL查询
        query_result = db_mysql_connect("dscloud", query_sql, sql_val=query_val, **kwargs)
        # 提取查询结果中的数据
        query_data = query_result['data']
        return query_data
    except Exception as e:
        # 如果发生异常，将其抛出
        raise e




"""
    根据三方订单号查询退款信息

    参数:
    order_no -- 系统订单号
    **kwargs -- 可变关键字参数，通常用于数据库连接配置等

    返回:
    query_data -- 查询到的退款信息数据
"""
@exception(logger)
def order_refund_info(order_no, **kwargs):
    "根据系统订单号查询退款信息"
    try:
        # 构造SQL查询语句，用于获取指定系统订单号的退款详情
        query_sql=f"""
            SELECT
                info.order_no AS 'order_no',
                refund.refund_no AS 'refund_no',
                refund.state AS 'refund_state',
                refund.erp_state AS 'refund_erp_state',
                refund.re_calculate_origin_order_flag AS 're_calculate_origin_order_flag',
                re_detail.erp_code AS 'refund_erp_code',
                re_detail.refund_count AS 'refund_count',
                re_detail.third_detail_id AS 'refund_third_detail_id'
            FROM
                order_info info
                LEFT JOIN refund_order refund ON info.order_no = refund.order_no 
                LEFT JOIN refund_detail re_detail ON refund.refund_no=re_detail.refund_no
            WHERE
                info.order_no = %s  -- 示例数据，实际应用中应动态绑定order_no
                AND info.mer_code = '500001'  -- 示例数据，实际应用中可能需要动态绑定或移除条件
        """
        # 准备查询参数
        query_val = (order_no)
        # 调用数据库连接函数，执行查询
        query_result = db_mysql_connect("dscloud", query_sql, sql_val=query_val, **kwargs)
        # 提取查询结果中的数据部分
        query_data = query_result['data']
        return query_data
    except Exception as e:
        # 如果发生异常，重新抛出以便于上层处理
        raise e



"""
    根据线上店铺code及平台码获取仓库信息

    参数:
    online_store_code (str): 线上店铺代码
    platform_code (str): 平台码
    service_mode (str): 服务模式
    **kwargs: 可变关键字参数，通常用于传递数据库连接配置

    返回:
    dict: 包含仓库信息的字典
"""
def onlinestore_warehouse_info(online_store_code, service_mode, platform_code, **kwargs):
    "根据线上店铺code及平台码获取仓库信息"
    try:
        # 初始化结果字典
        result = {}
        # 根据线上店铺code,服务模式,平台码获取仓库信息的SQL查询
        query_sql = f"""
            SELECT
                store.id AS 'onlineShopId',
                store.platform_code AS 'platform_code',
                store.online_client_code AS 'online_client_code',
                store.online_store_code AS 'online_store_code',
                store.online_store_name AS 'online_store_name',
                store.organization_code AS 'organization_code',
                warehouse.warehouse_id AS 'warehouse_id',
                warehouse.warehouse_code AS 'warehouse_code',
                warehouse.warehouse_name AS 'warehouse_name',
                warehouse.priority AS 'priority',
                warehouse.is_default AS 'is_default'
            FROM
                ds_online_store store
                LEFT JOIN ds_online_store_warehouse warehouse ON store.id = warehouse.online_shop_id 
            WHERE
                store.online_store_code = %s
                AND store.service_mode= %s
                AND store.platform_code = %s
                AND store.mer_code = '500001'
        """
        # 查询参数
        query_value = (online_store_code, service_mode, platform_code)
        # 调用数据库连接函数执行查询
        query_result = db_mysql_connect("dscloud", query_sql, sql_val=query_value, **kwargs)
        query_data = query_result['data']
        # 初始化仓库列表
        warehouse_list = []
        for item in query_data:
            # 从查询结果中提取信息
            onlineShopId = item['onlineShopId']
            platform_code = item['platform_code']
            online_client_code = item['online_client_code']
            online_store_code = item['online_store_code']
            online_store_name = item['online_store_name']
            organization_code = item['organization_code']
            warehouse_id = item['warehouse_id']
            warehouse_code = item['warehouse_code']
            warehouse_name = item['warehouse_name']
            warehouse_priority = item['priority']
            warehouse_is_default = item['is_default']
            # 填充结果字典中的基础信息
            result['onlineStoreCode'] = online_store_code
            result['organizationCode'] = organization_code
            result['platformCode'] = platform_code
            result['onlineShopId'] = onlineShopId
            result['online_client_code'] = online_client_code
            result['online_store_name'] = online_store_name
            # 构建单个仓库信息字典
            warehouse_info = {
                "onlineShopId": onlineShopId,
                "merCode": '500001',
                "onlineShopId": onlineShopId,
                "warehouseId": warehouse_id,
                "warehouseCode": warehouse_code,
                "warehouseName": warehouse_name,
                "priority": warehouse_priority,
                "is_default": warehouse_is_default
            }
            # 将仓库信息添加到列表中
            warehouse_list.append(warehouse_info)
        # 将所有仓库信息添加到结果字典中
        result['warehouseList'] = warehouse_list
        return result
    except Exception as e:
        # 如果发生异常，抛出异常
        raise e

@exception(logger)
def order_commodity_stock_record(third_order_no, **kwargs):
    try:
        # 查询订单侧的商品库存占用记录
        order_query_sql = f"""
                    SELECT
                        info.order_no AS 'order_no',
                        info.erp_state AS 'erp_state',
                        info.deleted AS 'order_deleted',
                        info.third_platform_code AS 'third_platform_code',
                        info.service_mode AS 'service_mode',
                        stock.erp_code AS 'erp_code',
                        stock.online_store_code AS 'online_store_code',
                        stock.order_detail_id AS 'order_detail_id',
                        stock.stock_qty AS 'stock_qty',
                        stock.organization_code AS 'organization_code',
                        stock.type AS 'stock_type',
                        stock.serial_number AS 'serial_number',
                        stock.store_id AS 'store_id',
                        stock.create_time AS 'stock_create_time' 
                    FROM
                        order_info info
                        LEFT JOIN commodity_stock stock ON info.order_no = stock.order_no 
                    WHERE
                        info.third_order_no = %s 
                        AND info.mer_code = '500001'
                """
        order_query_val = (third_order_no)
        # 调用数据库连接函数执行查询
        order_query_result = db_mysql_connect("dscloud", order_query_sql, sql_val=order_query_val, **kwargs)
        order_query_data = order_query_result['data']
        # 商品侧对应平台订单号的占用/释放记录情况
        commodity_stock_record_data=commodity_stock_record(third_order_no, **kwargs)
        # 商品侧对应平台订单号的占用/释放记录数量
        commodity_stock_record_count=len(commodity_stock_record_data)
        # 订单侧对应平台订单号的占用/释放记录数量
        order_stock_record_count=len(order_query_data)
        # 订单侧对应平台订单号的有效占用/释放记录数量
        order_stock_record_valid_count=0
        # 数据遍历标识
        order_stock_record_iterate_num=0
        # 订单侧占用/释放记录遍历
        for order_item in order_query_data:
            # 订单侧占用/释放记录遍历标识+1
            order_stock_record_iterate_num+=1
            # 订单侧占用记录数据信息提取：订单号
            order_order_no = order_item['order_no']
            # 订单侧占用/释放记录数据信息提取：erp编码
            order_erp_code=order_item['erp_code']
            # 订单侧占用/释放记录数据信息提取：记录序列码
            order_serial_number=order_item['serial_number']
            # 订单侧占用/释放记录数据信息提取：占用状态
            order_stock_type=order_item['stock_type']
            # 订单侧占用/释放记录数据信息提取：订单服务模式
            order_service_mode=order_item['service_mode']
            # 订单侧占用/释放记录数据信息提取：在线门店编码
            order_online_store_code=order_item['online_store_code']
            # 订单侧占用/释放记录数据信息提取：订单平台编码
            order_platform_code=order_item['third_platform_code']
            # 订单侧占用/释放记录数据信息提取：数量
            order_stock_qty=order_item['stock_qty']
            # 订单侧占用/释放记录数据信息提取：机构编码
            order_organization_code=order_item['organization_code']
            # 订单侧占用/释放记录数据信息提取：门店ID，仅B2C模式有此值，为仓库编码
            order_store_id=order_item['store_id']
            # 订单侧占用/释放记录数据信息提取：操作时间
            order_stock_create_time=order_item['stock_create_time']
            # 如果订单侧占用/释放记录状态为1或2，则订单侧占用/释放记录有效数量+1
            if str(order_stock_type)=='1' or str(order_stock_type)=='2':
                order_stock_record_valid_count+=1
            # 订单侧与商品侧库存占用释放记录一致性标识，默认为True
            consistent_flag=1
            # 订单侧与商品侧库存占用释放记录差异点清单，默认为空
            diff_list=[]
            # 商品侧占用总数
            commodity_occupy_num = 0
            # 商品释放总数
            commodity_release_num = 0
            # 占用记录的69码
            stock_bar_code=""
            # 商品侧占用/释放记录遍历
            for commodity_item in commodity_stock_record_data:
                # 商品侧占用/释放记录数据信息提取：订单号
                commodity_order_no=commodity_item['order_no']
                # 商品侧占用/释放记录数据信息提取：记录序列码
                commodity_serial_number=commodity_item['serial_number']
                # 商品侧占用/释放记录数据信息提取：操作数量
                commodity_change_stock=commodity_item['change_stock']
                # 商品侧占用/释放记录数据信息提取：操作描述
                commodity_operation=commodity_item['operation']
                # 商品侧占用/释放记录数据信息提取：操作类型 系统自动/手动释放
                commodity_operation_type=commodity_item['operation_type']
                # 商品侧占用/释放记录数据信息提取: erp编码
                commodity_erp_code=commodity_item['erp_code']
                # 商品侧占用/释放记录数据信息提取：69码
                commodity_bar_code=commodity_item['bar_code']
                # 商品侧占用/释放记录数据信息提取：机构id，O2O为对应机构id，B2C为仓库id
                commodity_store_id=commodity_item['store_id']
                # 商品侧占用/释放记录数据信息提取：订单类型 1:O2O，2:B2C
                commodity_order_type=commodity_item['order_type']
                # 商品侧占用/释放记录数据信息提取：平台编码
                commodity_platform_code=commodity_item['platform_code']
                # 商品侧占用/释放记录数据信息提取：操作前库存数量
                commodity_history_count=commodity_item['history_count']
                # 商品侧占用/释放记录数据信息提取：操作时间
                commodity_create_time=commodity_item['create_time']

                # 订单号和记录序列码一致时，判断订单侧占用/释放记录数量与商品侧占用/释放记录数量是否一致
                if str(order_order_no)==str(commodity_order_no) and str(order_serial_number)==str(commodity_serial_number):
                    if commodity_change_stock<0:
                        commodity_occupy_num=commodity_occupy_num+commodity_change_stock
                    else:
                        commodity_release_num=commodity_release_num+commodity_change_stock
                    # 获取商品侧与订单侧的时间差
                    time_difference=commodity_create_time-order_stock_create_time
                    # 获取时间差（秒）
                    seconds_difference=time_difference.total_seconds()
                    # 订单侧占用/释放记录状态为1时，为占用记录，商品侧对应值为负
                    if str(order_stock_type)=='1':
                        if commodity_change_stock<0:
                            if order_stock_qty+commodity_change_stock!=0:
                                consistent_flag=0
                                diff_detail={"description":"订单侧占用数量与商品侧不一致","order_value":order_stock_qty,"commodity_value":commodity_change_stock}
                                diff_list.append(diff_detail)
                            if seconds_difference>3:
                                consistent_flag=0
                                diff_detail={"description":"订单侧占用时间与商品侧相差超过3秒","order_value":order_stock_create_time.strftime("%Y-%m-%d %H:%M:%S"),"commodity_value":commodity_create_time.strftime("%Y-%m-%d %H:%M:%S")}
                                diff_list.append(diff_detail)
                    # 订单侧占用/释放记录状态为2时，为释放记录，商品侧对应值为正值
                    elif str(order_stock_type)=='2':
                        if commodity_change_stock>0:
                            # 判断5s内释放记录一致性判断
                            if seconds_difference<=3:
                                if order_stock_qty-commodity_change_stock!=0:
                                    consistent_flag=0
                                    diff_detail={"description":"3秒内订单侧释放数量与商品侧不一致","order_value":order_stock_qty,"commodity_value":commodity_change_stock}
                                    diff_list.append(diff_detail)
                    # 订单侧占用/释放记录状态为其他状态时，3秒内不应存在占用释放记录
                    else:
                        if seconds_difference <= 3:
                            consistent_flag=0
                            diff_detail={"description":"该状态下商品侧不应占用成功","order_value":order_stock_type,"commodity_value":commodity_operation}
                            diff_list.append(diff_detail)
                    # 订单侧&商品侧占用/释放记录erp编码一致性判断
                    if str(order_erp_code)!=str(commodity_erp_code):
                        consistent_flag=0
                        diff_detail={"description":"订单侧erp_code与商品侧erp_code不一致","order_value":order_erp_code,"commodity_value":commodity_erp_code}
                        diff_list.append(diff_detail)
                    # 商品侧占用/释放记录操作类型判断，默认应为系统自动
                    if str(commodity_operation_type)!="系统自动":
                        consistent_flag=0
                        diff_detail={"description":"商品侧操作类型不为系统自动","order_value":"","commodity_value":commodity_operation_type}
                        diff_list.append(diff_detail)
                    # 订单侧&商品侧占用/释放记录平台编码一致性判断
                    if str(order_platform_code)!=str(commodity_platform_code):
                        consistent_flag=0
                        diff_detail={"description":"订单侧平台编码与商品侧平台编码不一致","order_value":order_platform_code,"commodity_value":commodity_platform_code}
                        diff_list.append(diff_detail)
                    # 订单类型为O2O时，商品侧订单类型应为1
                    if order_service_mode=="O2O":
                        # 订单类型为O2O时，商品侧订单类型应为1
                        if str(commodity_order_type)!='1':
                            consistent_flag=0
                            diff_detail={"description":"订单侧服务模式为O2O，商品侧订单类型不为1","order_value":order_service_mode,"commodity_value":commodity_order_type}
                            diff_list.append(diff_detail)
                        # 根据订单侧门店编码获取门店id
                        order_organization_data=organization_info(order_organization_code, **kwargs)
                        # 默认取第一个ID
                        order_organization_id=order_organization_data[0]['store_id']
                        # 订单类型为O2O时，商品侧订单类型应为1
                        if str(commodity_store_id)!=str(order_organization_id):
                            consistent_flag=0
                            diff_detail={"description":"订单侧门店id与商品侧门店id不一致","order_value":f"{order_organization_id}({order_organization_code})","commodity_value":commodity_store_id}
                            diff_list.append(diff_detail)
                        # 订单类型为O2O时，订单侧store_id应为空
                        if order_store_id!="" or order_store_id!=None:
                            consistent_flag = 0
                            diff_detail={"description":"O2O订单侧占用记录store_id应为空","order_value":order_store_id,"commodity_value":""}
                            diff_list.append(diff_detail)
                    # 订单类型为B2C时，商品侧订单类型应为2
                    elif order_service_mode=="B2C":
                        # 订单类型为B2C时，商品侧订单类型应为2
                        if str(commodity_order_type)!='2':
                            consistent_flag=0
                            diff_detail={"description":"订单侧服务模式为O2O，商品侧订单类型不为2","order_value":order_service_mode,"commodity_value":commodity_order_type}
                            diff_list.append(diff_detail)
                        # 订单类型为B2C时，商品侧store_id应于订单侧store_id一致
                        if str(commodity_store_id)!=str(order_store_id):
                            consistent_flag=0
                            diff_detail={"description":"订单侧门店id与商品侧门店id不一致","order_value":order_store_id,"commodity_value":commodity_store_id}
                            diff_list.append(diff_detail)
                    # 订单服务模式错误
                    else:
                        consistent_flag=0
                        diff_detail={"description":"服务模式错误，订单无此服务模式","order_value":order_service_mode,"commodity_value":commodity_order_type}
                        diff_list.append(diff_detail)
                    stock_bar_code=commodity_bar_code

                    order_item['commodity_history_count']=commodity_history_count
            if order_stock_record_iterate_num==order_stock_record_count:
                if order_stock_record_valid_count!=commodity_stock_record_count:
                    consistent_flag=0
                    diff_detail={"description":"订单侧占用/释放有效记录数量与商品侧占用记录数量不一致","order_value":order_stock_record_valid_count,"commodity_value":commodity_stock_record_count}
                    diff_list.append(diff_detail)
            order_item['stock_create_time']=order_stock_create_time.strftime('%Y-%m-%d %H:%M:%S')
            order_item['commodity_occupy_num']=commodity_occupy_num
            order_item['commodity_release_num']=commodity_release_num
            order_item['commodity_bar_code'] = stock_bar_code
            order_item['consistent_flag']=consistent_flag
            order_item['diff_list']=diff_list
        return order_query_data
    except Exception as e:
        raise e


"""
    根据三方订单号查询商品库存记录

    该函数首先通过三方订单号查询对应的系统订单号，然后根据系统订单号查询商品库存操作日志

    参数:
    - third_order_no: 三方订单号，用于查询系统订单号
    - **kwargs: 可变关键字参数，用于传递额外的配置或信息

    返回:
    - 商品库存操作日志的列表
"""
@exception(logger)
def commodity_stock_record(third_order_no, **kwargs):
    try:
        result=[]
        # 根据三方订单号获取系统订单号
        order_no_query_sql=f"""
            SELECT
                order_no
            FROM
                order_info 
            WHERE
                third_order_no = %s 
                AND mer_code = '500001' 
        """
        order_no_query_val=(third_order_no)
        order_no_query_result = db_mysql_connect("dscloud", order_no_query_sql, sql_val=order_no_query_val, **kwargs)
        order_no_query_data = order_no_query_result['data']
        # 遍历查询到的系统订单号，查询每个订单的商品库存占用释放成功记录
        for order_no_item in order_no_query_data:
            order_no = order_no_item['order_no']
            commodity_stock_record_sql=f"""
                SELECT
                    platform_code AS 'platform_code',
                    erp_code AS 'erp_code',
                    bar_code AS 'bar_code',
                    order_no AS 'order_no',
                    change_stock AS 'change_stock',
                    history_count AS 'history_count',
                    operation AS 'operation',
                    `name` AS 'operation_type',
                    serial_number AS 'serial_number',
                    store_id AS 'store_id',
                    order_type AS 'order_type',
                    create_time AS 'create_time'
                FROM
                    commodity_stock_order_log 
                WHERE
                    order_no = %s
            """
            commodity_stock_record_val=(order_no)
            commodity_stock_record_result = db_mysql_connect("commodity_base", commodity_stock_record_sql, sql_val=commodity_stock_record_val, **kwargs)
            commodity_stock_record_data = commodity_stock_record_result['data']
            result.extend(commodity_stock_record_data)
        return result
    except Exception as e:
        raise e


def organization_info(organization_code, **kwargs):
    try:
        query_sql=f"""
        SELECT
            id AS 'store_id' 
        FROM
            sys_store 
        WHERE
            st_code = 'G246' 
            AND mer_code = '500001'
        """
        query_val=(organization_code)
        query_result = db_mysql_connect("base_info", query_sql, sql_val=query_val, **kwargs)
        query_data = query_result['data']
        if len(query_data)>0:
            result=query_data
        else:
            result=[{"store_id":""}]
        return result
    except Exception as e:
        raise e


"""
    根据订单号查询B2C订单oms信息
    该函数首先通过三方订单号查询对应的系统订单号，然后根据系统订单号查询B2C订单oms信息
    参数:
    - order_no: 系统订单号，用于查询系统订单号
    - **kwargs: 可变关键字参数，用于传递额外的配置或信息
    返回:
    - B2C订单oms信息的列表
"""
def order_b2c_oms_info(order_no, **kwargs):
    # B2C订单oms信息查询
    try:
        query_sql=f"""
            SELECT
                order_no,
                oms_order_no,
                order_status,
                join_wms,
                ex_reason,
                ex_status,
                ex_operator_time 
            FROM
                oms_order_info 
            WHERE
                order_no = %s       
        """
        query_val=(order_no)
        query_result = db_mysql_connect("dscloud", query_sql, sql_val=query_val, **kwargs)
        query_data = query_result['data']
        return query_data
    except Exception as e:
        raise e

"""
    根据订单号查询B2C订单异常信息
    该函数首先通过三方订单号查询对应的系统订单号，然后根据系统订单号查询B2C订单异常信息
    参数:
    - order_no: 系统订单号，用于查询系统订单号
    - **kwargs: 可变关键字参数，用于传递额外的配置或信息
    返回:
    - B2C订单异常信息的列表
"""
def order_b2c_ex_info(order_no, **kwargs):
    # B2C订单异常信息查询
    try:
        query_sql=f"""
            SELECT
                oms.order_no AS 'order_no',
                oms.oms_order_no AS 'oms_order_no',
                ex.order_status AS 'ex_order_status',
                ex.ex_type AS 'ex_type',
                ex.ex_type_desc AS 'ex_type_desc',
                ex.ex_reason AS 'ex_reason',
                ex.operate_status AS 'operate_status' 
            FROM
                oms_order_info oms
                LEFT JOIN oms_order_ex ex ON oms.oms_order_no = ex.oms_order_no 
            WHERE
                order_no = %s
        """
        query_val=(order_no)
        query_result = db_mysql_connect("dscloud", query_sql, sql_val=query_val, **kwargs)
        query_data = query_result['data']
        return query_data
    except Exception as e:
        raise e

if __name__ == '__main__':
    # 测试代码
    third_order_no = "3801221960309556861"
    # data = order_correlation_info(third_order_no)
    data=order_commodity_stock_record(third_order_no)
    print(json.dumps(data))
