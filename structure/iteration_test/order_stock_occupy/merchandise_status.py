# -*- coding: utf-8 -*-
"""
@Time ： 2024/8/16 15:38
@Auth ： 逗逗的小老鼠
@File ：merchandise_status.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json
from lib.get_log import logger, exception
import requests


# 获取商品库存信息
@exception(logger)
def merchandise_stock_info(goods_data,environment_flag):
    try:
        merchandise_result=[]
        goods_list=[]
        goods_url=f"""http://hydee-middle-merchandise.svc.k8s.{environment_flag}.hxyxt.com/1.0/ecc/product/B2C/list"""
        goods_headers={"Content-Type": "application/json"}
        goods_response=requests.post(url=goods_url,headers=goods_headers,data=json.dumps(goods_data))
        msg="获取商品信息失败"
        code=goods_response.status_code
        if code==200:
            goods_response_json=goods_response.json()
            code=goods_response_json["code"]
            if code=='10000':
                goods_list=goods_response_json["data"]
                msg="获取商品信息成功"
                for good_item in goods_list['data']:
                    # 判断商品列表是否存在
                    if "goodsList" in good_item.keys():
                        good_info_list=good_item["goodsList"]
                    else:
                        good_info_list=[]
                    # 判断库存列表是否存在
                    if "stockInfoList" in good_item.keys():
                        good_stock_list=good_item["stockInfoList"]
                    else:
                        good_stock_list=[]
                    # 遍历商品列表
                    for good_base_info in good_info_list:
                        erp_code=good_base_info["erpCode"]
                        stock_num=0
                        # 遍历库存列表，根据erp_code判断，将对应库存数量累加
                        for good_stock_info in good_stock_list:
                            if good_stock_info["erpCode"]==erp_code:
                                stock_info_list=good_stock_info['stockInfoList']
                                for stock_info_item in stock_info_list:
                                    stock_value=stock_info_item["stock"]
                                    stock_num+=stock_value
                                break
                        good_stock_result={"erp_code":erp_code,"stock_num":stock_num}
                        merchandise_result.append(good_stock_result)

        result={"code":code,"msg":msg,"data":{"aftertreatment":merchandise_result,"original_result":goods_list}}
        return result

    except Exception as e:
        raise e


if __name__=="__main__":
    good_data={
    "currentPage": 1,
    "erpCodes": [
        "131427","134956","134636"
    ],
    "merCode": "500001",
    "onlineStoreCode": "a0d0e219578b4aacb030810323c26c27",
    "organizationCode": "D102",
    "pageSize": 100,
    "platformCode": "3003",
    "warehouseList": [
        {
            "merCode": "500001",
            "onlineShopId": "142106",
            "priority": 1,
            "synProportion": 10,
            "warehouseId": "100516529",
            "warehouseName": "(D)贵州公司物流部"
        },
        {
            "merCode": "500001",
            "onlineShopId": "142106",
            "priority": 2,
            "synProportion": 0,
            "warehouseId": "10061583",
            "warehouseName": "一心堂贵阳市小孟工业园分店"
        }
    ]
}
    environment_flag='test'
    pri=merchandise_stock_info(good_data,environment_flag)
    print(pri)
