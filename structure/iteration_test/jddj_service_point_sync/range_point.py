# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/16 11:07
@Auth ： 逗逗的小老鼠
@File ：range_point.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：获取点位范围内的经纬度点位集合&与已知集合去并集
"""
from geopy.distance import geodesic
import random
import numpy as np
import math


# 生成目标点5公里范围内的随机点（简化示例，只生成少数几个点）
def generate_points_within_radius(lat, lon, radius_km, **kwargs):
    num_points = kwargs.get("num_points", 10)
    points = []
    for _ in range(num_points):
        # 随机生成方向和距离（简化的随机方法）
        angle = random.uniform(0, 2 * 3.14159)
        distance = random.uniform(0, radius_km)

        # 计算新的纬度和经度
        new_lat = lat + (distance / 6371) * 360 * (angle / (2 * 3.14159)) * (1 / np.cos(np.radians(lat)))
        new_lon = lon + (distance / 6371) * 360 * (angle / (2 * 3.14159))

        # 将点添加到列表中
        points.append((new_lat, new_lon))
    return points


def get_union_set(existing_points, generated_points):
    "获取集合点的并集"
    try:
        # 转换为集合以便于取并集
        existing_points_set = set(map(tuple, existing_points))
        generated_points_set = set(map(tuple, generated_points))

        # 取并集
        union_set = existing_points_set.union(generated_points_set)
        return union_set
    except Exception as e:
        raise e


def haversine(lon1, lat1, destination_set):
    "根据点位进行距离计算"
    for set_item in destination_set:
        lon2, lat2 = set_item
        # 将十进制度数转化为弧度
        lon1, lat1, lon2, lat2 = map(math.radians, [lon1, lat1, lon2, lat2])

        # Haversine公式
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = math.sin(dlat / 2) ** 2 + math.cos(lat1) * math.cos(lat2) * math.sin(dlon / 2) ** 2
        c = 2 * math.asin(math.sqrt(a))

        # 地球半径（单位：公里）
        r = 6371

        # 计算距离
        distance = c * r
        result = []
        if distance > 5:
            out_point = (lon2, lat2)
            result.append(out_point)
        return result


if __name__ == '__main__':
    # 示例目标经纬度
    target_lat, target_lon = 39.319837, 117.082343

    # 定义距离范围（5公里）
    radius_km = 5
    # 示例经纬度集合
    existing_points = [
        ("39.340666", "116.993585"),
        ("39.338089", "116.996967"),
        ("39.340802", "116.995727"),
        ("39.341873", "116.999861"),
        ("39.344361", "117.005986"),
        ("39.345678", "117.010047"),
        ("39.346233", "117.011259"),
        ("39.345435", "117.012053"),
        ("39.34429", "117.014961"),
        ("39.342499", "117.027321"),
        ("39.331118", "117.041933"),
        ("39.328282", "117.031162"),
        ("39.327469", "117.02977"),
        ("39.324565", "117.032528"),
        ("39.323651", "117.038162"),
        ("39.328613", "117.045052"),
        ("39.326777", "117.047035"),
        ("39.320789", "117.054578"),
        ("39.319215", "117.056116"),
        ("39.317269", "117.057092"),
        ("39.317668", "117.060248"),
        ("39.323023", "117.05855"),
        ("39.323621", "117.064442"),
        ("39.32518", "117.064635"),
        ("39.328097", "117.063136"),
        ("39.330991", "117.061974"),
        ("39.334183", "117.060316"),
        ("39.335215", "117.065125"),
        ("39.332811", "117.065303"),
        ("39.330507", "117.065799"),
        ("39.328536", "117.066007"),
        ("39.328657", "117.068125"),
        ("39.32852", "117.069241"),
        ("39.329834", "117.075447"),
        ("39.330893", "117.074981"),
        ("39.331347", "117.076823"),
        ("39.331041", "117.07717"),
        ("39.330966", "117.076975"),
        ("39.330193", "117.077291"),
        ("39.33131", "117.083107"),
        ("39.332328", "117.082417"),
        ("39.332758", "117.081672"),
        ("39.333232", "117.081292"),
        ("39.333977", "117.08303"),
        ("39.329684", "117.086423"),
        ("39.321974", "117.090174"),
        ("39.321127", "117.090335"),
        ("39.322108", "117.090923"),
        ("39.323944", "117.092635"),
        ("39.32486", "117.093206"),
        ("39.325922", "117.093425"),
        ("39.327123", "117.093134"),
        ("39.327742", "117.092771"),
        ("39.328613", "117.091782"),
        ("39.331442", "117.087086"),
        ("39.332331", "117.086064"),
        ("39.332456", "117.085955"),
        ("39.332599", "117.086116"),
        ("39.333514", "117.08826"),
        ("39.334865", "117.087317"),
        ("39.33481", "117.087179"),
        ("39.335345", "117.086807"),
        ("39.334839", "117.085584"),
        ("39.336292", "117.084664"),
        ("39.342528", "117.099165"),
        ("39.34479", "117.099268"),
        ("39.345691", "117.09946"),
        ("39.345874", "117.098456"),
        ("39.345549", "117.098373"),
        ("39.34555", "117.093559"),
        ("39.345896", "117.093516"),
        ("39.345919", "117.093321"),
        ("39.347056", "117.093309"),
        ("39.347242", "117.100245"),
        ("39.349042", "117.10129"),
        ("39.350233", "117.101404"),
        ("39.352984", "117.100966"),
        ("39.352916", "117.100097"),
        ("39.355118", "117.099369"),
        ("39.355339", "117.101849"),
        ("39.355956", "117.102916"),
        ("39.356325", "117.103269"),
        ("39.357209", "117.103592"),
        ("39.359787", "117.103166"),
        ("39.360055", "117.106557"),
        ("39.36072", "117.108751"),
        ("39.367337", "117.096383"),
        ("39.369652", "117.108933"),
        ("39.373933", "117.11006"),
        ("39.374621", "117.111911"),
        ("39.37668", "117.110775"),
        ("39.377187", "117.110631"),
        ("39.377157", "117.111155"),
        ("39.37815", "117.110839"),
        ("39.377114", "117.111762"),
        ("39.376797", "117.116264"),
        ("39.375325", "117.116808"),
        ("39.374079", "117.117042"),
        ("39.374004", "117.117809"),
        ("39.372269", "117.118777"),
        ("39.372325", "117.122005"),
        ("39.37279", "117.12303"),
        ("39.373046", "117.123467"),
        ("39.373479", "117.123775"),
        ("39.375367", "117.124187"),
        ("39.381509", "117.126378"),
        ("39.385828", "117.117805"),
        ("39.3863", "117.116328"),
        ("39.38711", "117.112368"),
        ("39.387524", "117.111226"),
        ("39.388276", "117.110171"),
        ("39.389542", "117.108983"),
        ("39.39097", "117.106705"),
        ("39.390705", "117.106116"),
        ("39.389802", "117.106098"),
        ("39.38889", "117.106362"),
        ("39.386211", "117.104281"),
        ("39.389542", "117.100412"),
        ("39.389739", "117.098963"),
        ("39.390269", "117.098617"),
        ("39.391379", "117.095929"),
        ("39.392192", "117.096646"),
        ("39.39386", "117.101104"),
        ("39.395802", "117.102552"),
        ("39.396367", "117.104145"),
        ("39.396982", "117.103316"),
        ("39.399201", "117.104756"),
        ("39.40046", "117.1044"),
        ("39.402502", "117.099341"),
        ("39.407415", "117.098165"),
        ("39.406602", "117.097761"),
        ("39.407501", "117.094674"),
        ("39.410565", "117.096249"),
        ("39.412446", "117.095782"),
        ("39.412642", "117.092553"),
        ("39.411203", "117.091724"),
        ("39.411344", "117.091232"),
        ("39.411024", "117.090343"),
        ("39.411549", "117.086124"),
        ("39.410308", "117.085063"),
        ("39.409253", "117.082924"),
        ("39.409809", "117.081028"),
        ("39.410609", "117.080463"),
        ("39.411034", "117.08066"),
        ("39.411021", "117.080375"),
        ("39.411219", "117.080361"),
        ("39.411233", "117.080705"),
        ("39.411648", "117.080744"),
        ("39.412216", "117.080647"),
        ("39.412208", "117.080289"),
        ("39.414064", "117.080138"),
        ("39.413925", "117.07881"),
        ("39.416685", "117.080003"),
        ("39.418715", "117.080374"),
        ("39.419146", "117.078226"),
        ("39.418756", "117.07807"),
        ("39.418853", "117.077487"),
        ("39.419176", "117.076733"),
        ("39.419753", "117.076123"),
        ("39.420254", "117.076379"),
        ("39.421312", "117.077494"),
        ("39.422102", "117.077873"),
        ("39.425018", "117.078209"),
        ("39.426244", "117.077942"),
        ("39.426952", "117.07806"),
        ("39.427352", "117.077729"),
        ("39.42854", "117.077697"),
        ("39.429401", "117.077376"),
        ("39.430896", "117.077943"),
        ("39.431689", "117.078789"),
        ("39.432557", "117.078626"),
        ("39.432704", "117.078474"),
        ("39.432424", "117.074805"),
        ("39.43176", "117.072641"),
        ("39.431863", "117.065931"),
        ("39.431694", "117.065778"),
        ("39.431546", "117.065117"),
        ("39.431347", "117.065135"),
        ("39.431256", "117.064957"),
        ("39.426286", "117.068057"),
        ("39.426642", "117.071333"),
        ("39.426507", "117.071476"),
        ("39.424894", "117.07206"),
        ("39.423872", "117.072682"),
        ("39.423586", "117.072479"),
        ("39.422656", "117.070052"),
        ("39.421857", "117.070329"),
        ("39.421753", "117.068779"),
        ("39.4228", "117.068347"),
        ("39.428813", "117.064601"),
        ("39.428907", "117.064337"),
        ("39.428679", "117.062726"),
        ("39.428712", "117.061101"),
        ("39.427345", "117.061154"),
        ("39.419686", "117.053144"),
        ("39.420524", "117.053704"),
        ("39.421596", "117.051101"),
        ("39.419597", "117.049647"),
        ("39.41992", "117.048578"),
        ("39.416077", "117.045753"),
        ("39.413708", "117.04434"),
        ("39.419842", "117.025087"),
        ("39.415799", "117.022928"),
        ("39.416697", "117.020057"),
        ("39.416579", "117.01999"),
        ("39.420904", "117.00615"),
        ("39.419011", "117.005116"),
        ("39.420072", "117.001651"),
        ("39.415962", "117.001233"),
        ("39.417042", "116.997824"),
        ("39.409592", "116.99625"),
        ("39.409484", "116.994632"),
        ("39.408671", "116.992358"),
        ("39.406922", "116.991945"),
        ("39.406788", "116.991741"),
        ("39.406583", "116.991082"),
        ("39.407152", "116.986185"),
        ("39.406975", "116.986165"),
        ("39.407546", "116.980899"),
        ("39.407485", "116.979689"),
        ("39.407157", "116.97931"),
        ("39.402632", "116.978261"),
        ("39.398837", "116.990326"),
        ("39.392801", "116.988936"),
        ("39.391978", "116.988138"),
        ("39.392177", "116.987503"),
        ("39.392561", "116.987207"),
        ("39.390221", "116.986474"),
        ("39.388786", "116.986382"),
        ("39.389599", "116.978883"),
        ("39.389521", "116.978869"),
        ("39.389656", "116.977771"),
        ("39.385972", "116.982959"),
        ("39.385554", "116.982994"),
        ("39.384766", "116.980344"),
        ("39.383763", "116.979802"),
        ("39.383014", "116.980579"),
        ("39.382437", "116.979829"),
        ("39.372335", "116.973313"),
        ("39.371028", "116.976506"),
        ("39.36965", "116.975639"),
        ("39.368045", "116.975611"),
        ("39.365476", "116.983707"),
        ("39.364565", "116.985634"),
        ("39.362301", "116.985106"),
        ("39.362333", "116.986497"),
        ("39.361003", "116.986725"),
        ("39.358698", "116.986588"),
        ("39.3573", "116.986907"),
        ("39.356541", "116.986555"),
        ("39.356426", "116.990769"),
        ("39.355886", "116.992102"),
        ("39.347736", "116.989757"),
        ("39.348392", "116.985912"),
        ("39.344595", "116.989013"),
        ("39.344619", "116.988868"),
        ("39.341663", "116.988066"),
        ("39.340206", "116.98804"),
        ("39.338868", "116.988375"),
        ("39.340272", "116.993788"),
        ("39.340666", "116.993585")
    ]

    # 生成目标点5公里范围内的点
    generated_points = generate_points_within_radius(target_lat, target_lon, radius_km, num_points=360)
    print("Generated Points:", generated_points)
    union_point_set = get_union_set(existing_points, generated_points)
    print("Union Points:", union_point_set)

    # # 示例用法
    # lon1, lat1 = 39.319837, 117.082343  # 纽约市的经纬度
    # # lon2, lat2 = 39.364693474792226, 117.08640014040586  # 洛杉矶市的经纬度
    # #
    # distance = haversine(lon1, lat1, existing_points)
    # print(distance)
    # # print(f"The distance between New York and Los Angeles is {distance:.2f} kilometers.")
