# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/11 10:58
@Auth ： 逗逗的小老鼠
@File ：processing_point.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：去除共线的点位
"""

import numpy as np

"""
    判断三个点是否共线
    p1, p2, p3: 点的坐标 (lat, lon)
    tol: 容忍误差
    
    实现逻辑：
        1、首先将经纬度转为笛卡尔坐标
        2、两两计算向量
        3、计算叉积(向量积)
        4、根据叉积(向量积)判断是否共线
    """


def is_collinear(p1, p2, p3, tol=1e-9):
    # 将经纬度转换为笛卡尔坐标
    def latlon_to_cartesian(lat, lon):
        R = 6371e3  # 地球半径，单位：米
        # 将度数转换为弧度
        lat_rad = np.radians(lat)
        lon_rad = np.radians(lon)
        # 将地理坐标转换为笛卡尔坐标
        x = R * np.cos(lat_rad) * np.cos(lon_rad)
        y = R * np.cos(lat_rad) * np.sin(lon_rad)
        z = R * np.sin(lat_rad)
        return np.array([x, y, z])

    # 将每个坐标转为笛卡尔坐标
    p1_cart = latlon_to_cartesian(p1[0], p1[1])
    p2_cart = latlon_to_cartesian(p2[0], p2[1])
    p3_cart = latlon_to_cartesian(p3[0], p3[1])

    # 计算向量
    v1 = p2_cart - p1_cart
    v2 = p3_cart - p1_cart

    # 计算叉积的模型
    cross_product = np.cross(v1, v2)
    # 判断是否共线
    return np.linalg.norm(cross_product) < tol


def process_points(points):
    """
    处理经纬度坐标点集合，根据规则保留或删除点
    points: 经纬度点集合 [(lat, lon), ...]
    """
    try:

        if len(points) < 3:
            return points  # 如果点少于3个，则直接返回原集合
        new_points = [points[0]]  # 初始化新集合，保留第一个点
        i = 0
        while i < len(points) - 2:
            p1, p2, p3 = new_points[-1], points[i + 1], points[i + 2]
            if is_collinear(p1, p2, p3):
                # 如果共线，则只保留p1和p3，并跳过p2
                new_points.append(p3)
                i += 2  # 跳过p2，检查下一个三元组
            else:
                # 如果不共线，则保留p1, p2, p3
                new_points.extend([p2])
                # 如果p3为最后一个点，则也保留
                if i + 2 == len(points) - 1:
                    new_points.extend([p3])
                i += 1  # 只移动到下一个点，因为p2和p3都已经被考虑了

            # 如果还有剩余的点并且没有检查完，则继续检查下一个三元组
            # 注意：由于我们已经更新了new_points，所以p1始终是new_points中的最后一个点
        return new_points
    except Exception as e:
        raise e


if __name__ == '__main__':
    # 示例数据
    points = [(31.656878,105.154925),(31.657632,105.156055),(31.658522,105.156802),(31.659651,105.157165),(31.661623,105.157424),(31.662412,105.157795),(31.663596,105.159425),(31.664751,105.161656),(31.666602,105.16597),(31.666737,105.1664),(31.66662,105.166885),(31.666184,105.166771),(31.665944,105.166471),(31.663338,105.161053),(31.662005,105.159602),(31.660709,105.158763),(31.659553,105.158547),(31.658762,105.158576),(31.658244,105.159005),(31.658209,105.159342),(31.658723,105.1604),(31.659417,105.16136),(31.664804,105.167798),(31.665186,105.168515),(31.666452,105.173159),(31.667695,105.175795),(31.667528,105.175893),(31.667334,105.175568),(31.666767,105.176197),(31.666713,105.177469),(31.666274,105.176788),(31.665712,105.177299),(31.664675,105.176281),(31.663018,105.174252),(31.661611,105.172016),(31.660004,105.16998),(31.657455,105.166102),(31.655037,105.163534),(31.653993,105.162835),(31.652602,105.162457),(31.64944,105.162575),(31.646481,105.166566),(31.647963,105.167986),(31.649688,105.17038),(31.650437,105.170633),(31.65367,105.170785),(31.655774,105.171399),(31.656691,105.171915),(31.657801,105.172815),(31.660017,105.17583),(31.662509,105.176701),(31.665809,105.180011),(31.666096,105.180665),(31.666615,105.183877),(31.667131,105.185487),(31.667198,105.185612),(31.664383,105.187958),(31.661034,105.193408),(31.658376,105.202074),(31.646226,105.217796),(31.606975,105.211489),(31.623001,105.191716),(31.623017,105.190689),(31.62275,105.188557),(31.621298,105.182749),(31.62145,105.176011),(31.622737,105.174956),(31.623821,105.173179),(31.621386,105.171805),(31.621348,105.169785),(31.621319,105.166004),(31.618902,105.156929),(31.619012,105.157084),(31.621417,105.159101),(31.622165,105.160086),(31.624338,105.157567),(31.624998,105.156175),(31.625977,105.155269),(31.626632,105.153862),(31.627529,105.152667),(31.629286,105.149622),(31.629805,105.148979),(31.630718,105.148613),(31.632127,105.148701),(31.632557,105.148596),(31.632919,105.150348),(31.633612,105.148803),(31.634695,105.147608),(31.635621,105.148413),(31.636441,105.148024),(31.637071,105.148094),(31.637304,105.148327),(31.637327,105.14926),(31.637079,105.149839),(31.636327,105.1505),(31.636893,105.15134),(31.638865,105.14914),(31.639531,105.148681),(31.641361,105.147871),(31.643746,105.147363),(31.646961,105.147668),(31.648851,105.148308),(31.650652,105.149228),(31.651662,105.149868),(31.652573,105.150686),(31.65355,105.151993),(31.655357,105.156255),(31.656725,105.154903),(31.656878,105.154925)]

    result = process_points(points)
    print(result)
