# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/9 10:18
@Auth ： 逗逗的小老鼠
@File ：order_route_assign.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from structure.iteration_test.order_route.order_route_info import store_todo_count
from structure.iteration_test.order_route.order_route_delivery_compute import distance_tx_Calculation,distance_Calculation
from datetime import datetime

"""
    订单分单-分单门店判断
    :param order_store:订单下单门店
    :param policy_store：策略配置值
"""
@exception(logger)
def order_assign_store(order_store,policy_store):
    try:
        if order_store in policy_store:
            result=True
        else:
            result=False
        return result
    except Exception as e:
        raise e


"""
    订单分单-分单订单类型判断
    :param order_type:订单下单门店
    :param policy_ordertype：策略配置值
"""
@exception(logger)
def order_assign_order_type(order_type,policy_ordertype):
    try:
        if policy_ordertype=='ALL_ORDER':
            result=True
        else:
            if order_type==1:
                order_type_value='TIMELY_ORDER'
            else:
                order_type_value = 'RESERVE_ORDER'
            if order_type_value==policy_ordertype:
                result = True
            else:
                result=False
        return result
    except Exception as e:
        raise e


"""
    订单分单-分单时段判断
    :param order_time:订单值
    :param policy_time：策略配置值
"""
@exception(logger)
def order_assign_time(order_create,policy_start_time,policy_end_time):
    try:
        result = False
        current_date = datetime.now()
        order_time=datetime.strftime(order_create,"%Y-%m-%d %H:%M:%S")
        order_date = order_create.replace(hour=0, minute=0, second=0, microsecond=0)

        # 计算时间差（单位为秒）
        time_difference_seconds = (current_date - order_create).total_seconds()

        begin_time=order_date+policy_start_time
        end_time = order_date + policy_end_time
        start_time=datetime.strftime(begin_time,"%Y-%m-%d %H:%M:%S")
        fanish_time = datetime.strftime(end_time, "%Y-%m-%d %H:%M:%S")
        if start_time<=order_time<=fanish_time:
            result = True
        print(result)
        return result
    except Exception as e:
        raise e


"""
    订单分单-分单距离判断
    :param order_time:订单值
    :param policy_value：策略配置值
"""
@exception(logger)
def order_assign_distance(third_order_no,order_lat,order_lng,store_code,policy_value):
    try:
        distance_result=distance_Calculation(third_order_no,order_lat,order_lng,store_code)
        delivery_result=distance_result['delivery_result']
        if delivery_result:
            order_distance=distance_result['distance_min']['distance_value']

        else:
            order_distance=distance_tx_Calculation(order_lat, order_lng,store_code)
        result = False
        if order_distance is None:
            pass
        else:
            if policy_value is None:
                result = True
            else:
                if float(order_distance)>float(policy_value):
                    result=True
        return result
    except Exception as e:
        raise e


"""
    订单分单-分单待拣货数量判断
    :param order_time:订单值
    :param policy_time：策略配置值
"""
@exception(logger)
def order_assign_notpick(organization_code,policy_num):
    try:
        if policy_num is None:
            result = True
        else:
            result = False
            # 获取当前门店待拣货数量
            todo_num=store_todo_count(organization_code)
            policy_count=int(policy_num)
            if todo_num>policy_count:
                result=True
        return result
    except Exception as e:
        raise e



if __name__=="__main__":
    order_time="2024-04-12 05:06:14"
    bigen_time="04:00:00"
    end_time="08:00:00"
    order_assign_time(order_time,bigen_time,end_time)