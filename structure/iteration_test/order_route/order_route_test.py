# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/9 10:18
@Auth ： 逗逗的小老鼠
@File ：order_route_test.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import time

from lib.get_log import logger, exception
from structure.iteration_test.order_route.order_route_info import order_base_info, order_route_rule, order_route_strategy_info, \
    order_delivery_time_desc
from structure.iteration_test.order_route.order_route_assign import order_assign_order_type, order_assign_time, \
    order_assign_distance, order_assign_notpick
from structure.iteration_test.order_route.order_route_receive import order_receive_stock, order_receive_mt_fence, \
    order_receive_goods, order_receive_time, order_first_stacknum, order_first_distanceFee, order_store_stack_threshold, \
    order_store_priority, order_store_acquiesce, order_receive_shop_list
import requests, json
from datetime import datetime
from lib.deal_excel import write_excel

"""
分单策略判断    
"""


@exception(logger)
def order_route_store_test(third_order_no, strategy_id, strategy_rule_list):
    try:
        result = {}
        receive_result = []
        order_info_result = order_base_info(third_order_no)
        if len(order_info_result) > 0:
            # 订单号
            order_no = order_info_result[0]['order_no']
            # 订单类型（送达方式）：即时单/预约单
            delivery_time_type = order_info_result[0]['delivery_time_type']
            # 预约送达时间
            delivery_time_desc = order_info_result[0]['delivery_time_desc']
            # 下单门店编码
            source_organization_code = order_info_result[0]['source_organization_code']
            # 下单网点编码
            source_online_store_code = order_info_result[0]['source_online_store_code']
            # 配送方式
            delivery_type = order_info_result[0]['delivery_type']
            # 下单时间
            created = order_info_result[0]['created']
            # 下单经纬度
            receiver_lat = order_info_result[0]['receiver_lat']
            receiver_lng = order_info_result[0]['receiver_lng']
            # 预约单取送达时间的开始时间
            if delivery_time_type == 1:
                appointment_delivery_time = order_delivery_time_desc(delivery_time_desc)
            # 非预约单取订单下单时间
            else:
                appointment_delivery_time = created
            # 根据配送方式判断是否允许分单
            if delivery_type == '3':
                # 策略基本信息
                strategy_info = order_route_strategy_info(strategy_id)
                strategy_store_code = strategy_info['store_code']
                strategy_online_store = strategy_info['online_store_list']
                # 策略规则信息
                strategy_rule_info = order_route_rule(strategy_id)
                # 设置是全部订单或者及时订单或者是预约订单
                OUT_ORDER_TYPE_HANDLE = strategy_value(strategy_rule_info, "assign_rule", 'OUT_ORDER_TYPE_HANDLE')
                # 设置哪些时段可以允许分单
                OUT_TIME_FRAME_HANDLE = strategy_value(strategy_rule_info, "assign_rule", 'OUT_TIME_FRAME_HANDLE')
                # 设置超过xx距离的才允许分单
                OUT_DISTANCE_HANDLE = strategy_value(strategy_rule_info, "assign_rule", 'OUT_DISTANCE_HANDLE')
                # 分单时，是否校验接单门店库存是否充足
                OUT_STOCK_HANDLE = strategy_value(strategy_rule_info, "assign_rule", 'OUT_STOCK_HANDLE')
                # 设置接单门店只有美团配送时，是否校验在接单门店电子围栏中
                OUT_MT_FENCE_HANDLE = strategy_value(strategy_rule_info, "assign_rule", 'OUT_MT_FENCE_HANDLE')
                # 设置门店待拣货订单超过xx，即允许被分单
                OUT_ORDER_NUM_HANDLE = strategy_value(strategy_rule_info, "assign_rule", 'OUT_ORDER_NUM_HANDLE')
                # 接单门店清单
                RECEIVE_SHOP_LIST_HANDLE = strategy_value(strategy_rule_info, "receive_rule",
                                                          'RECEIVE_SHOP_LIST_HANDLE')
                # 按订单时段设置指定门店
                RECEIVE_TIME_FRAME_HANDLE = strategy_value(strategy_rule_info, "receive_rule",
                                                           "RECEIVE_TIME_FRAME_HANDLE")
                # 多门店符合分单时，设置按距离优先，按配送费优先，按订单堆积量优先
                RECEIVE_MORE_SHOP_ACCORD_HANDLE = strategy_value(strategy_rule_info, 'receive_rule',
                                                                 'RECEIVE_MORE_SHOP_ACCORD_HANDLE')
                # 接单门店订单堆积量多余xx时，不分单
                RECEIVE_ORDER_NUM_HANDLE = strategy_value(strategy_rule_info, 'receive_rule',
                                                          'RECEIVE_ORDER_NUM_HANDLE')
                # 无门店符合分单时，设置是否需要强制分单
                RECEIVE_FORCE_SHOP_HANDLE = strategy_value(strategy_rule_info, 'receive_rule',
                                                           'RECEIVE_FORCE_SHOP_HANDLE')
                # 设置指定商品指定门店发出
                RECEIVE_APPOINT_GOOD_HANDLE = strategy_value(strategy_rule_info, 'receive_rule',
                                                             'RECEIVE_APPOINT_GOOD_HANDLE')
                # 接单门店列表
                accept_shop_list = order_receive_shop_list(RECEIVE_SHOP_LIST_HANDLE)
                # 根据下单门店编码判断是否允许分单
                if strategy_store_code == source_organization_code:
                    # 根据下单网店编码判断是否允许分单
                    if source_online_store_code in strategy_online_store:
                        # 判断传入规则中是否有订单类型要求
                        if "OUT_ORDER_TYPE_HANDLE" in strategy_rule_list:
                            if OUT_ORDER_TYPE_HANDLE is None:
                                order_assign_order_type_result = True
                            else:
                                # 根据订单送达方式判断是否允许分单
                                order_assign_order_type_result = order_assign_order_type(delivery_time_type,
                                                                                         OUT_ORDER_TYPE_HANDLE)
                        else:
                            order_assign_order_type_result = True
                        # 判断传入规则中是否有分单时间段要求
                        if "OUT_TIME_FRAME_HANDLE" in strategy_rule_list:
                            order_assign_time_result = False
                            # 若分单时间为空，则默认所有时间允许分单
                            if OUT_TIME_FRAME_HANDLE is None:
                                order_assign_time_result = True
                            else:
                                if len(OUT_TIME_FRAME_HANDLE) == 0:
                                    order_assign_time_result = True
                                else:
                                    # 遍历策略规则中所有分单时间段
                                    for out_time_item in OUT_TIME_FRAME_HANDLE:
                                        out_start_time = out_time_item['start_time']
                                        out_end_time = out_time_item['end_time']
                                        # 根据下单时间判断分单时间是否满足要求
                                        out_time_result = order_assign_time(created, out_start_time, out_end_time)
                                        if out_time_result:
                                            order_assign_time_result = True
                        else:
                            order_assign_time_result = True
                        # 判断传入规则中是否有分单距离要求
                        if "OUT_DISTANCE_HANDLE" in strategy_rule_list:
                            if OUT_DISTANCE_HANDLE is None:
                                order_assign_distance_result = True
                            else:
                                order_assign_distance_result = order_assign_distance(third_order_no, receiver_lat,
                                                                                     receiver_lng,
                                                                                     source_organization_code,
                                                                                     OUT_DISTANCE_HANDLE)
                        else:
                            order_assign_distance_result = True
                        # 判断传入参数中是否校验门店待拣货数量
                        if "OUT_ORDER_NUM_HANDLE" in strategy_rule_list:
                            if OUT_ORDER_NUM_HANDLE is None:
                                order_assign_notpick_result = True
                            else:
                                # 判断待拣货数量是否满足要求
                                order_assign_notpick_result = order_assign_notpick(source_organization_code,
                                                                                   OUT_ORDER_NUM_HANDLE)
                        else:
                            order_assign_notpick_result = True

                        # 根据选用条件结果，判断是否进行分单
                        if order_assign_order_type_result and order_assign_time_result and order_assign_distance_result and order_assign_notpick_result:
                            # 接单门店库存计算
                            # 判断传入规则中是否有校验接单门店库存是否满足的要求
                            if "OUT_STOCK_HANDLE" in strategy_rule_list:
                                if OUT_STOCK_HANDLE == "TRUE":
                                    order_receive_stock_result = order_receive_stock(order_no, RECEIVE_SHOP_LIST_HANDLE)
                                    if order_receive_stock_result['code'] == 10000:
                                        accept_shop_list = order_receive_stock_result['data']
                                    else:
                                        accept_data = {"code": 50001, "msg": "接单门店库存计算异常",
                                                       "data": order_receive_stock_result}
                                        receive_result.append(accept_data)
                            # 设置接单门店只有美团配送时，是否校验在接单门店电子围栏中
                            if "OUT_MT_FENCE_HANDLE" in strategy_rule_list:
                                if OUT_MT_FENCE_HANDLE == "TRUE":
                                    order_receive_mt_fence_result = order_receive_mt_fence(third_order_no, receiver_lat,
                                                                                           receiver_lng,
                                                                                           accept_shop_list)
                                    if order_receive_mt_fence_result['code'] == 10000:
                                        accept_shop_list = order_receive_mt_fence_result['data']
                                    else:
                                        accept_data = {"code": 50002, "msg": "美团电子围栏计算异常",
                                                       "data": order_receive_mt_fence_result}
                                        receive_result.append(accept_data)

                            # 设置指定商品指定门店发出
                            if "RECEIVE_APPOINT_GOOD_HANDLE" in strategy_rule_list:
                                if RECEIVE_APPOINT_GOOD_HANDLE is not None:
                                    order_receive_goods_result = order_receive_goods(order_no,
                                                                                     RECEIVE_APPOINT_GOOD_HANDLE,
                                                                                     accept_shop_list)
                                    if order_receive_goods_result['code'] == 10000:
                                        accept_shop_list = order_receive_goods_result['data']
                                    else:
                                        accept_data = {"code": 50003, "msg": "指定商品指定门店发出计算异常",
                                                       "data": order_receive_goods_result}
                                        receive_result.append(accept_data)
                            # 按订单时段设置指定门店
                            if "RECEIVE_TIME_FRAME_HANDLE" in strategy_rule_list:
                                if RECEIVE_TIME_FRAME_HANDLE is not None:
                                    order_receive_time_result = order_receive_time(appointment_delivery_time,
                                                                                   RECEIVE_TIME_FRAME_HANDLE,
                                                                                   accept_shop_list)
                                    if order_receive_time_result['code'] == 10000:
                                        accept_shop_list = order_receive_time_result['data']
                                    else:
                                        accept_data = {"code": 50004, "msg": "按订单时段设置指定门店计算异常",
                                                       "data": order_receive_time_result}
                                        receive_result.append(accept_data)
                            # 接单门店订单堆积量多余xx时，不分单
                            if "RECEIVE_ORDER_NUM_HANDLE" in strategy_rule_list:
                                # if RECEIVE_ORDER_NUM_HANDLE is not None:
                                order_store_stack_threshold_result = order_store_stack_threshold(
                                    accept_shop_list, RECEIVE_SHOP_LIST_HANDLE)
                                if order_store_stack_threshold_result['code'] == 10000:
                                    accept_shop_list = order_store_stack_threshold_result['data']
                                else:
                                    accept_data = {"code": 50006, "msg": "接单门店订单堆积量计算异常",
                                                   "data": order_store_stack_threshold_result}
                                    receive_result.append(accept_data)
                            # 多门店符合分单时，设置按距离优先，按配送费优先，按订单堆积量优先
                            if "RECEIVE_MORE_SHOP_ACCORD_HANDLE" in strategy_rule_list:
                                if RECEIVE_MORE_SHOP_ACCORD_HANDLE is not None:
                                    if RECEIVE_MORE_SHOP_ACCORD_HANDLE == 'ORDER_NUM_LESS_REJECT':
                                        order_first_result = order_first_stacknum(accept_shop_list)
                                    else:
                                        order_first_result = order_first_distanceFee(RECEIVE_MORE_SHOP_ACCORD_HANDLE,
                                                                                     third_order_no, receiver_lat,
                                                                                     receiver_lng, accept_shop_list)
                                    if order_first_result['code'] == 10000:
                                        accept_shop_list = order_first_result['data']
                                    else:
                                        accept_data = {"code": 50005, "msg": "多门店符合分单时,优先策略计算异常",
                                                       "data": order_first_result}
                                        receive_result.append(accept_data)

                            if len(accept_shop_list) > 0:
                                # 计算接单门店的优先级
                                order_store_priority_result = order_store_priority(accept_shop_list,
                                                                                   RECEIVE_SHOP_LIST_HANDLE)
                                if order_store_priority_result['code'] == 10000:
                                    accept_shop_list = order_store_priority_result['data']
                                else:
                                    accept_data = {"code": 50007, "msg": "计算接单门店的优先级计算异常",
                                                   "data": order_store_priority_result}
                                    receive_result.append(accept_data)
                            else:
                                # 是否强制分单
                                if "RECEIVE_FORCE_SHOP_HANDLE" in strategy_rule_list:
                                    # 计算兜底门店
                                    if RECEIVE_FORCE_SHOP_HANDLE == "COMPEL_TRANSFER":
                                        order_store_acquiesce_result = order_store_acquiesce(strategy_id)
                                        accept_shop_list = order_store_acquiesce_result['data']
                                    else:
                                        accept_shop_list.append(source_organization_code)
                            if len(receive_result) > 0:
                                result = {"code": 50000, "msg": "订单接单门店计算存在错误", "data": receive_result}
                            else:
                                result = {"code": 10000, "msg": "订单接单门店计算成功", "data": accept_shop_list}
                        else:
                            assign_result = {"order_assign_order_type_result": order_assign_order_type_result,
                                             "order_assign_time_result": order_assign_time_result,
                                             "order_assign_distance_result": order_assign_distance_result,
                                             "order_assign_notpick_result": order_assign_notpick_result}
                            msg = "分单条件有误，请核对"
                            if order_assign_order_type_result == False:
                                msg = "不允许分单订单类型"
                            else:
                                if order_assign_time_result == False:
                                    msg = "当前时间不在允许分单时段范围内"
                                else:
                                    if order_assign_distance_result == False:
                                        msg = "允许分单距离校验"
                                    else:
                                        if order_assign_notpick_result == False:
                                            msg = f"分单门店待拣货订单堆积量未超过 {OUT_ORDER_NUM_HANDLE} 单，不允许分单"

                            result = {"code": 30004, "msg": msg, "data": assign_result}
                    else:
                        result = {"code": 30003, "msg": "订单网点编码与分单策略限制不一致，不允许分单", "data": False}
                else:
                    result = {"code": 30002, "msg": "订单门店编码与分单策略限制不一致，不允许分单", "data": False}
            else:
                result = {"code": 30001, "msg": "订单非自配送单，不允许分单", "data": False}
        else:
            result = {"code": 20001, "msg": "订单不存在", "data": False}

        return result

    except Exception as e:
        raise e


"""
    判断策略值是否存在
"""


def strategy_value(strategy_info, strategy_type, strategy_key):
    try:
        strategy_value = None
        if strategy_key in strategy_info:
            strategy_value = strategy_info[strategy_key]
        return strategy_value
    except Exception as e:
        raise e


"""
    比对开发接口
"""


def compare_order_route(thirdOrderNo, strategy_id, rule_key_list):
    try:

        dev_body = {"handleTypes": rule_key_list, "strategyId": strategy_id, "thirdOrderNo": thirdOrderNo}
        dev_url = "https://hydee-business-order.svc.k8s.test.hxyxt.com/route/test/ruleCheck"
        dev_headers = {"Content-Type": "application/json;charset=UTF-8"}
        dev_res = requests.post(url=dev_url, headers=dev_headers, data=json.dumps(dev_body), verify=False)
        if dev_res.status_code == 200:
            res_json = dev_res.json()
            res_code = res_json['code']
            dev_data = res_json['data']
            dev_msg = res_json['msg']
        else:
            dev_data = []
            dev_msg = dev_res.status_code
        dev_result = {"data": dev_data, "msg": dev_msg}
        test_order_route_result = order_route_store_test(thirdOrderNo, strategy_id, rule_key_list)
        test_data = test_order_route_result['data']
        test_msg = test_order_route_result['msg']
        test_result = {"data": test_data, "msg": test_msg}
        if test_data == dev_data:
            com_msg = "比较结果一致"
            com_code = 10000
        else:
            com_msg = "比较结果不一致"
            com_code = 50000
            if test_order_route_result['code'] == 30004:
                if dev_msg == test_msg or str(test_msg) in str(dev_msg):
                    com_msg = "比较结果一致"
                    com_code = 10000
        result = {"code": com_code, "msg": com_msg, "dev_result": dev_result, "test_result": test_result}
        return result
    except Exception as e:
        raise e


"""
    批量进行比对，并将结果写入excel中
"""


def order_route_aoturule(thirdOrderNo_list, strategy_id):
    try:
        data_list = []
        rule_list = ["OUT_ORDER_TYPE_HANDLE", "OUT_TIME_FRAME_HANDLE", "OUT_DISTANCE_HANDLE", "OUT_STOCK_HANDLE",
                     "OUT_MT_FENCE_HANDLE", "RECEIVE_APPOINT_GOOD_HANDLE", "RECEIVE_TIME_FRAME_HANDLE",
                     "RECEIVE_MORE_SHOP_ACCORD_HANDLE", "RECEIVE_ORDER_NUM_HANDLE", "OUT_ORDER_NUM_HANDLE",
                     "RECEIVE_FORCE_SHOP_HANDLE"]

        # rule_list=["RECEIVE_MORE_SHOP_ACCORD_HANDLE"]
        for third_order in thirdOrderNo_list:
            for i in range(len(rule_list)):
                single_rule = [rule_list[i]]
                compound_rule = []
                for j in range(i + 1):
                    compound_rule.append(rule_list[j])
                if single_rule != compound_rule:
                    time.sleep(3)

                    compound_compare_result = compare_order_route(third_order, strategy_id, compound_rule)
                    compound_compare_code = compound_compare_result['code']
                    compound_compare_msg = compound_compare_result['msg']
                    compound_compare_dev_res = compound_compare_result['dev_result']
                    compound_compare_test_res = compound_compare_result['test_result']
                    compound_data_list = [third_order, strategy_id, compound_rule, compound_compare_code,
                                          compound_compare_msg, compound_compare_dev_res, compound_compare_test_res]
                    data_list.append(compound_data_list)
                time.sleep(3)
                single_compare_result = compare_order_route(third_order, strategy_id, single_rule)
                single_compare_code = single_compare_result['code']
                single_compare_msg = single_compare_result['msg']
                single_compare_dev_res = single_compare_result['dev_result']
                single_compare_test_res = single_compare_result['test_result']
                single_data_list = [third_order, strategy_id, single_rule, single_compare_code, single_compare_msg,
                                    single_compare_dev_res, single_compare_test_res]
                data_list.append(single_data_list)
        now_time = datetime.now()
        now_date = datetime.strftime(now_time, "%Y-%m-%d %H&%M&%S")
        excel_name = "order_route_test.xlsx"
        sheet_name = f"路由比对结果_{now_date}"
        column_list = ["订单号", "策略号", "规则码", "结果码", "结果描述", "返回结果", "比对结果"]
        write_excel(excel_name, sheet_name, column_list, data_list)
        return data_list
    except Exception as e:
        raise e


if __name__ == "__main__":
    rule_key_list = ["RECEIVE_TIME_FRAME_HANDLE"]
    strategy_id = 153
    thirdOrderNo = "301066023636226057"
    # order_route_test=compare_order_route(thirdOrderNo,strategy_id,rule_key_list)
    third_list = [ "301071924248600435",
                    "4084300148770252329"]
    autorule_result = order_route_aoturule(third_list, strategy_id)
    print(autorule_result)
