# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/10 11:09
@Auth ： 逗逗的小老鼠
@File ：order_route_receive.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from structure.iteration_test.order_route.order_route_info import order_detail_count,xy_commodity_stock_api,store_todo_count
from structure.order_test.xy_order_stock_db import xy_organization_info
from structure.iteration_test.order_route.order_route_delivery_compute import distance_Calculation
from lib.db_conf_test import db_yx_test_dsclound
from datetime import datetime
from dateutil.parser import parse

"""
    接单门店初始化
    :param receive_shop_list：策略中的接单门店清单
"""
def order_receive_shop_list(receive_shop_list):
    try:
        store_list=[]
        for shop_item in receive_shop_list:
            re_store_code = shop_item['re_store_code']
            delivery_sql = f"""
                SELECT
                d_store.platform_code AS 'platform_code',
                d_store.delivery_store_code AS 'delivery_store_code' 
            FROM
                ds_online_store store
                INNER JOIN ds_online_store_delivery delivery ON store.id = delivery.online_store_id
                INNER JOIN ds_delivery_store d_store ON delivery.delivery_store_id = d_store.id 
            WHERE
                delivery.`status` = 1 
                AND store.organization_code = '{re_store_code}'
            GROUP BY d_store.platform_code
            
            """
            delivery_result = db_yx_test_dsclound(delivery_sql)
            delivery_data = delivery_result['data']
            if len(delivery_data) > 0:
                store_list.append(re_store_code)
        return store_list
    except Exception as e:
        raise e






"""
    接单门店库存筛选
    :param order_no:订单号
    :param receive_shop_list：策略中的接单门店清单
"""
def order_receive_stock(order_no,receive_shop_list):
    try:
        # 获取订单商品信息
        order_detail_result=order_detail_count(order_no)
        order_erp_list=order_detail_result['order_erp_list']
        order_detail_data=order_detail_result['order_detail_data']
        # 根据接单列表列表处理store_list
        store_id_list=[]
        store_code_list=[]
        for shop_item in receive_shop_list:
            re_store_code=shop_item['re_store_code']
            # 根据门店编码获取门店ID
            re_store_data=xy_organization_info(re_store_code)
            re_store_id=re_store_data['data']['store_id']
            store_code_list.append(re_store_code)
            store_id_list.append(re_store_id)
        # 查询接单门店库存是否满足要求
        store_code_list_cp=store_code_list.copy()
        commodity_stock_result=xy_commodity_stock_api(store_id_list,order_erp_list)
        if commodity_stock_result['code']==10000:
            commodity_stock_data=commodity_stock_result['data']
            for store_code_item in store_code_list:
                store_code_flag=0
                for order_detail_item in order_detail_data:
                    detail_erp_flag=0
                    detail_erp_code=order_detail_item['erp_code']
                    detail_commodity_num=order_detail_item['commodity_num']
                    for commodity_stock_item in commodity_stock_data:
                        storecode = commodity_stock_item['storecode']
                        erpcode = commodity_stock_item['erpcode']
                        stock = commodity_stock_item['stock']
                        if storecode==store_code_item:
                            store_code_flag=1
                            if erpcode==detail_erp_code:
                                detail_erp_flag=1
                                # 如果实际库存小于订单库存，则从门店列表中删除对应门店
                                if int(stock) < int(detail_commodity_num):
                                    if store_code_item in store_code_list_cp:
                                        store_code_list_cp.remove(store_code_item)
                    # 如果没有找到对应商品，则删除对应门店
                    if detail_erp_flag==0:
                        if store_code_item in store_code_list_cp:
                            store_code_list_cp.remove(store_code_item)
                # 如果没有找到对应门店，则删除对应门店
                if store_code_flag == 0:
                    if store_code_item in store_code_list_cp:
                        store_code_list_cp.remove(store_code_item)
            result = {"code": 10000, "msg": "门店库存校验成功", "data": store_code_list_cp}
        else:
            result={"code":commodity_stock_result['code'],"msg":commodity_stock_result['code'],"data":[]}
        return result
    except Exception as e:
        raise e


"""
    设置接单门店只有美团配送时，是否校验在接单门店电子围栏中
"""
def order_receive_mt_fence(third_order_no,order_lat,order_lng,receive_store_list):
    try:
        store_code_list=[]
        for store_item in receive_store_list:
            distance_result=distance_Calculation(third_order_no, order_lat, order_lng, store_item)
            delivery_result=distance_result['delivery_result']
            if delivery_result:
                distance_fence=distance_result['Electronic_fence']
                if distance_fence:
                    store_code_list.append(store_item)
        result = {"code": 10000, "msg": "门店库存校验成功", "data": store_code_list}
        return result
    except Exception as e:
        raise e

"""
    指定商品接单门店筛选
    :param order_no:订单号
    :param receive_appoint_good：策略中的指定商品接单门店清单
    :param receive_store_list:上一步筛选出的接单门店清单
"""
def order_receive_goods(order_no,receive_appoint_good,receive_store_list):
    try:
        # 获取订单商品信息
        order_detail_result = order_detail_count(order_no)
        order_erp_list = order_detail_result['order_erp_list']
        good_store_list=[]
        # 遍历策略中指定商品的店铺清单
        for good_item in receive_appoint_good:
            re_store_code=good_item['re_store_code']
            erp_code=good_item['erp_code']
            # 遍历订单中的erp_code
            for order_erp_item in order_erp_list:
                # 若订单中存在对应商品，则将对应的门店CODE存入列表中
                if erp_code==order_erp_item:
                    good_store_list.append(re_store_code)
        # 如果存在制定商品，则取上一步预选门店和指定门店的交集
        if len(good_store_list)>0:
            store_code_list=list(set(good_store_list)&set(receive_store_list))
        # 若不存在指定商品，则返回上一步筛选的结果
        else:
            store_code_list=receive_store_list
        result={"code":10000,"msg":"指定商品门店接单筛选成功","data":store_code_list}
        return result
    except Exception as e:
        raise e


"""
    指定时间门店接单筛选
    :param order_create:订单创建时间
    :param receive_appoint_good：策略中的指定商品接单门店清单
    :param receive_store_list:上一步筛选出的接单门店清单
"""
def order_receive_time(order_create,receive_time_frame,receive_store_list):
    try:
        # 获取订单商品信息
        if isinstance(order_create,str):
            order_create=parse(order_create)
        order_time=datetime.strftime(order_create,"%Y-%m-%d %H:%M:%S")
        order_date = order_create.replace(hour=0, minute=0, second=0, microsecond=0)
        exclude_store_list=[]
        for receive_time_item in receive_time_frame:
            start_time=receive_time_item['start_time']
            end_time=receive_time_item['end_time']
            restorecode=receive_time_item['restorecode']

            today_begin_time = order_date + start_time
            today_end_time = order_date + end_time

            receive_start_time = datetime.strftime(today_begin_time, "%Y-%m-%d %H:%M:%S")
            receive_end_time = datetime.strftime(today_end_time, "%Y-%m-%d %H:%M:%S")
            # 若下单时间未在接单时间内，则计入排除门店列表中
            if order_time>receive_end_time or order_time<receive_start_time:
                exclude_store_list.append(restorecode)
        receive_time_store_list=[]
        for receive_store_item in receive_store_list:
            if receive_store_item in exclude_store_list:
                pass
            else:
                receive_time_store_list.append(receive_store_item)
        result={"code":10000,"msg":"指定门店指定时间接单筛选成功","data":receive_time_store_list}
        return result
    except Exception as e:
        raise e


"""
    优先策略：订单堆积量优先
    :param receive_store_list：待接单的门店列表
    
"""
def order_first_stacknum(receive_store_list):
    try:
        store_list=[]
        for receive_store_item in receive_store_list:
            receive_store_dict={}
            receive_store_dict['store_code']=receive_store_item
            store_stack_count=store_todo_count(receive_store_item)
            receive_store_dict['stacknum'] = store_stack_count
            store_list.append(receive_store_dict)
        store_list.sort(key=lambda x:x['stacknum'])
        stacknum_store_list=[]
        if len(store_list)>0:
            store_code=store_list[0]['store_code']
            stacknum_store_list.append(store_code)
        result = {"code": 10000, "msg": "优先策略：订单堆积量优先排序成功", "data": stacknum_store_list}
        return result
    except Exception as e:
        raise e


"""
    优先策略：距离优先/费用优先
    :param handle_value:优先策略的具体值
    :param order_no：订单号
    :param order_lat/order_lng：订单的经纬度
    :param receive_store_list：待接单的门店列表
"""
def order_first_distanceFee(handle_value,order_no, order_lat, order_lng,receive_store_list):
    try:
        store_list = []
        for receive_store_item in receive_store_list:
            # 查询对应门店的配送平台信息
            delivery_sql = f"""
                    SELECT
                        d_store.platform_code AS 'platform_code',
                        d_store.delivery_store_code AS 'delivery_store_code' 
                    FROM
                        ds_online_store store
                        INNER JOIN ds_online_store_delivery delivery ON store.id = delivery.online_store_id
                        INNER JOIN ds_delivery_store d_store ON delivery.delivery_store_id = d_store.id 
                    WHERE
                        delivery.`status` = 1 
                        AND store.organization_code = '{receive_store_item}'
                    GROUP BY d_store.platform_code
            """
            delivery_result = db_yx_test_dsclound(delivery_sql)
            delivery_data = delivery_result['data']
            if len(delivery_data)>0:
                # 获取门店距离值
                distance_result=distance_Calculation(order_no, order_lat, order_lng, receive_store_item)
                delivery_result = distance_result['delivery_result']
                if delivery_result:

                    # 距离优先
                    if handle_value=='DELIVERY_DISTANCE_PRIORITY':
                        distance_info=distance_result['distance_min']
                        distance_info['store_code']=receive_store_item
                    # 费用优先
                    elif handle_value=='DELIVERY_CHARGE_REJECT':
                        distance_info = distance_result['distance_min']
                        distance_info['store_code'] = receive_store_item
                    else:
                        distance_info={}
                    store_list.append(distance_info)
        if len(store_list)>0:
            if handle_value=="DELIVERY_DISTANCE_PRIORITY":
                store_list = sorted(store_list, key=lambda x: (
                    x['distance_value'] is None, x['distance_value'] == "", x['distance_value']))
            elif handle_value=='DELIVERY_CHARGE_REJECT':
                store_list = sorted(store_list, key=lambda x: (
                    x['total_price_value'] is None, x['total_price_value'] == "", x['total_price_value']))
            else:
                pass
        distance_store_list = []
        if len(store_list)>0:
            store_code = store_list[0]['store_code']
        # for store_item in distance_sort_list:
        #     store_code = store_item['store_code']
            distance_store_list.append(store_code)
        result = {"code": 10000, "msg": "优先策略：距离优先/费用优先排序成功", "data": distance_store_list}
        return result
    except Exception as e:
        raise e


"""
    门店堆积量阈值计算
    :param receive_store_list：待接单的门店列表
    :param policy_receive_shop_list:策略中的接单列表信息

"""
def order_store_stack_threshold(receive_store_list,policy_receive_shop_list):
    try:
        store_list = []
        for receive_store_item in receive_store_list:
            store_stack_count = store_todo_count(receive_store_item)
            for policy_shop_item in policy_receive_shop_list:
                policy_store_code=policy_shop_item['re_store_code']
                policy_pick_num=policy_shop_item['pick_num']
                if receive_store_item==policy_store_code:
                    # 如果未配置值，则不进行校验
                    if policy_pick_num is None:
                        store_list.append(receive_store_item)
                    else:
                        if store_stack_count<=policy_pick_num:
                            store_list.append(receive_store_item)
        result = {"code": 10000, "msg": "门店堆积量阈值计算成功", "data": store_list}
        return result
    except Exception as e:
        raise e


"""
    门店优先级排序
    :param receive_store_list：待接单的门店列表
    :param policy_receive_shop_list:策略中的接单列表信息

"""
def order_store_priority(receive_store_list,policy_receive_shop_list):
    try:
        priority_store_list = []
        store_list=[]
        for receive_store_item in receive_store_list:
            for policy_shop_item in policy_receive_shop_list:
                policy_store_code=policy_shop_item['re_store_code']
                policy_priority=policy_shop_item['priority']
                if receive_store_item==policy_store_code:
                    priority_store={"store_code":receive_store_item,"policy_priority":policy_priority}
                    priority_store_list.append(priority_store)
        priority_store_list.sort(key=lambda x: x['policy_priority'])
        for store_code_item in priority_store_list:
            store_code = store_code_item['store_code']
            store_list.append(store_code)
        result = {"code": 10000, "msg": "门店优先级排序成功", "data": store_list}
        return result
    except Exception as e:
        raise e

"""
    兜底门店查询
    :param strategy_id：策略id
"""
def order_store_acquiesce(strategy_id):
    try:
        store_list=[]
        acquiesce_sql=f"""SELECT store.re_store_code AS 're_store_code',store.acquiesce AS 'acquiesce' FROM route_strategy_rule strategy LEFT JOIN route_receive_store_set store ON strategy.id=store.strategy_rule_id LEFT JOIN route_rule rule ON strategy.rule_id=rule.id WHERE strategy.strategy_id={strategy_id} AND rule.handle_type='RECEIVE_SHOP_LIST_HANDLE' AND store.acquiesce=1"""
        acquiesce_result=db_yx_test_dsclound(acquiesce_sql)
        acquiesce_data=acquiesce_result['data']
        for acquiesce_item in acquiesce_data:
            acquiesce_store_code=acquiesce_item['re_store_code']
            store_list.append(acquiesce_store_code)
        result = {"code": 10000, "msg": "兜底门店查询成功", "data": store_list}
        return result
    except Exception as e:
        raise e


