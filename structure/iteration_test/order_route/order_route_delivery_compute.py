# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/10 16:53
@Auth ： 逗逗的小老鼠
@File ：order_route_delivery_compute.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from structure.iteration_test.order_route.order_route_info import store_place,order_base_info
from lib.db_conf_test import db_yx_test_dsclound
import requests,time
from datetime import datetime
import urllib3
# 禁用https警告信息
urllib3.disable_warnings()



"""
    订单距离计算
"""
@exception(logger)
def distance_Calculation(third_order_no,order_lat,order_lng,store_code):
    "订单距离计算"
    try:
        result={}
        # 查询对应门店的配送平台信息
        delivery_sql=f"""
            SELECT
                d_store.platform_code AS 'platform_code',
                d_store.delivery_store_code AS 'delivery_store_code' 
            FROM
                ds_online_store store
                INNER JOIN ds_online_store_delivery delivery ON store.id = delivery.online_store_id
                INNER JOIN ds_delivery_store d_store ON delivery.delivery_store_id = d_store.id 
            WHERE
                delivery.`status` = 1 
                AND store.organization_code = '{store_code}'
            GROUP BY d_store.platform_code
        """
        delivery_result=db_yx_test_dsclound(delivery_sql)
        delivery_data=delivery_result['data']
        distance_list=[]
        delivery_mt_flag=0
        if len(delivery_data)>0:
            result['delivery_result']=True
            # 遍历配送平台信息
            for delivery_item in delivery_data:
                platform_code=delivery_item['platform_code']
                delivery_store_code=delivery_item['delivery_store_code']
                distance_data=distancefee_platform_Calculation(third_order_no, platform_code, order_lat, order_lng, delivery_store_code)
                distance_value=distance_data['distance_value']
                total_price_value=distance_data['total_price_value']

                if distance_value is None or total_price_value is None:
                    pass
                else:
                    distance_list.append(distance_data)
                # 配送平台为美团平台
                if platform_code=="2002":
                    delivery_mt_flag=1
            # 如果在平台获取到了距离和费用数据
            if len(distance_list)>0:
                print(f"{store_code}门店从平台获取到费用和距离")
                # 根据距离进行排序
                distance_sort_list = sorted(distance_list,key=lambda x: (x['distance_value'] is None, x['distance_value'] == "", x['distance_value']))
                # 根据配送费用排序
                disprice_sort_list=sorted(distance_list,key=lambda x: (x['total_price_value'] is None, x['total_price_value'] == "", x['total_price_value']))
                # 判断是否仅支持美团且是否在美团的电子围栏中
                Electronic_fence = True
                if delivery_mt_flag == 1 and len(delivery_data) == 1:
                    distance_value = distance_list[0]['distance_value']
                    if distance_value is None:
                        Electronic_fence = False
                result['Electronic_fence']=Electronic_fence
            # 如果平台未获取到距离和费用数据，则通过自行计算距离和费用
            else:
                print(f"{store_code}门店未从平台获取到费用和距离，需要调用腾讯进行计算")
                # 通过腾讯地图进行距离计算
                destance_value = distance_tx_Calculation(order_lat, order_lng,store_code)
                # 通过心云平台进行费用计算
                destance_list=distancefee_tx_Calculation(third_order_no,destance_value,store_code)
                # 根据距离进行排序
                distance_sort_list = sorted(destance_list, key=lambda x: (
                x['distance_value'] is None, x['distance_value'] == "", x['distance_value']))
                # 根据配送费用排序
                disprice_sort_list = sorted(destance_list, key=lambda x: (
                x['total_price_value'] is None, x['total_price_value'] == "", x['total_price_value']))
                if delivery_mt_flag==1 and len(delivery_data) == 1:
                    result['Electronic_fence'] = False
                else:
                    result['Electronic_fence'] = True
            # 根据优先级取最优记录
            priority_delivery_result = priority_delivery_platform(distance_sort_list, disprice_sort_list,
                                                                  store_code)
            # 取距离值最小的一条数据
            result['distance_min'] = priority_delivery_result["distance_min"]
            # 取配送费最小的一条数据
            result['disprice_min'] = priority_delivery_result["disprice_min"]
        else:
            result['delivery_result'] = False
            result['Electronic_fence'] = False
            result['disprice_min'] = None
            result['distance_min'] = None
        return result
    except Exception as e:
        raise e

"""
    腾讯距离计算
"""
@exception(logger)
def distance_tx_Calculation(source_lat, source_lng,store_code):
    "获取门店经纬度"
    try:
        store_result = store_place(store_code)
        if store_result['code'] == 10000:
            dest_lat = store_result['store_lat']
            dest_lng = store_result['store_lng']
            print(f"门店code:{store_code},经纬度：{dest_lng}，{dest_lat}")
            key = "NBVBZ-HE7W3-AWW3Y-YZHEU-RAAL5-U4FB2"
            url = f"https://apis.map.qq.com/ws/direction/v1/driving/?from={dest_lat},{dest_lng}&to={source_lat},{source_lng}&key={key}"
            destance_result = requests.get(url=url)
            if destance_result.status_code == 200:
                res_json = destance_result.json()
                destance_data = res_json['result']
                destance_value = destance_data['views'][0]['distance']
            else:
                destance_value = 999999999
        else:
            destance_value=None
        return destance_value
    except Exception as e:
        raise e


"""
    心云系统中计算配送费
"""
@exception(logger)
def distancefee_tx_Calculation(third_order_no,destance_value,storecode):
    "心云系统中计算配送费"
    try:
        time.sleep(0.5)
        distance_list=[]
        # 查询下单时间
        order_info_result=order_base_info(third_order_no)
        order_created=order_info_result[0]['created']
        created_time=datetime.strftime(order_created,'%H:%M:%S')
        # 获取门店的配送方式
        delivery_store_sql=f"""SELECT platform_code,delivery_store_code,city FROM ds_delivery_store WHERE delivery_store_code='{storecode}' GROUP BY platform_code"""
        delivery_store_result=db_yx_test_dsclound(delivery_store_sql)
        delivery_store_data=delivery_store_result['data']
        for delivery_store_item in delivery_store_data:
            platform_code=delivery_store_item['platform_code']
            city=delivery_store_item['city']
            # 查询平台对应配送费规则
            fee_sql=f"""
                SELECT
                    rule.id AS 'delivery_rule_id',
                    addprice.add_type AS 'add_type',
                    addprice.start_range AS 'start_range',
                    addprice.end_range AS 'end_range',
                    addprice.add_price AS 'add_price',
                    rule.start_price AS 'start_price',
                    rule.start_distance AS 'start_distance' 
                FROM
                    delivery_fee_rule_addprice addprice
                    LEFT JOIN delivery_fee_rule rule ON addprice.delivery_rule_id = rule.id 
                WHERE
                    addprice.city_name = '{city}' 
                    AND rule.delivery_platform_code = '{platform_code}'
            """
            fee_result=db_yx_test_dsclound(fee_sql)
            fee_data=fee_result['data']
            base_fee=0.00
            time_fee=0.00
            distance_fee=0.00
            max_distance=0.00
            max_add_distance_fee=0.00
            for fee_item in fee_data:
                delivery_rule_id=fee_item['delivery_rule_id']
                add_type=fee_item['add_type']

                add_price=fee_item['add_price']
                start_price=fee_item['start_price']
                start_distance=fee_item['start_distance']
                base_fee=float(start_price)
                # 时间加价
                if add_type=="TIME_ADD_PRICE":
                    begin_range_time=fee_item['start_range']
                    end_range_time=fee_item['end_range']
                    if begin_range_time<created_time<=end_range_time:
                        time_fee=float(add_price)
                # 距离加价
                if add_type=="DISTANCE_ADD_PRICE":
                    start_range = fee_item['start_range']
                    end_range = fee_item['end_range']
                    # 如果结束距离大于当前最大距离，则更新最大距离和对应加价费用
                    if float(end_range)>max_distance:
                        max_distance=float(end_range)
                        max_add_distance_fee=float(add_price)
                    if float(start_range)<float(destance_value)<=float(end_range):
                        distance_fee=float(add_price)
            # 如果配送距离大于最大加价距离，则加价费用去最大加价费用
            if float(destance_value)>float(max_distance):
                distance_fee=max_add_distance_fee
            total_price_value=base_fee+distance_fee+time_fee
            print(f"{storecode}门店与订单{third_order_no}距离计算结果，距离为：{destance_value}，配送费为：{total_price_value}")
            disprice_data={"store_code":storecode,"platform_code":platform_code,"distance_value":destance_value,"total_price_value":round(total_price_value,2)}
            distance_list.append(disprice_data)
        return distance_list
    except Exception as e:
        raise e


"""
    从三方系统中获取配送距离和配送服务费
"""
@exception(logger)
def distancefee_platform_Calculation(order_no,platform_code,order_lat,order_lng,delivery_store_code):
    try:
        distance_data = {}
        request_param = {"lat": order_lat, "lng": order_lng, "platformCode": platform_code,
                         "storeCode": delivery_store_code, "thirdOrderNo": order_no}
        request_url = "https://hydee-business-order.svc.k8s.test.hxyxt.com/route/tes/getThirdPlatFormDistance"
        # 根据配送平台获取配送距离和配送费用
        distance_paltform_request = requests.get(url=request_url, params=request_param,verify=False)
        if distance_paltform_request.status_code == 200:
            distance_res = distance_paltform_request.json()
            if distance_res['code'] == '10000':
                distance_res_data = distance_res['data']
                # 配送距离值
                distance_value = distance_res_data['distance']
                # 配送费用值
                total_price_value = distance_res_data['total_price']
                if distance_value == "" or distance_value is None:
                    distance_data['distance_value'] = None
                else:
                    distance_data['distance_value'] = round(float(distance_value), 2)
                if total_price_value == "" or total_price_value is None:
                    distance_data['total_price_value'] = None
                else:
                    distance_data['total_price_value'] = round(float(total_price_value), 2)
            else:
                distance_data['distance_value'] = None
                distance_data['total_price_value'] = None

        else:
            distance_data['distance_value'] = None
            distance_data['total_price_value'] = None

        distance_data['platform_code'] = platform_code
        distance_data['store_code'] = delivery_store_code
        return distance_data
    except Exception as e:
        raise e


# 配送费相同时，根据配送优先级计算
@exception(logger)
def priority_delivery_platform(distance_sort_list,disprice_sort_list,storecode):
    try:
        # 获取门店的配送门店所在城市
        delivery_store_sql = f"""
                SELECT
                    d_store.platform_code AS 'platform_code',
                    d_store.delivery_store_code AS 'delivery_store_code',
                    d_store.city 
                FROM
                    ds_online_store store
                    INNER JOIN ds_online_store_delivery delivery ON store.id = delivery.online_store_id
                    INNER JOIN ds_delivery_store d_store ON delivery.delivery_store_id = d_store.id 
                WHERE
                    delivery.`status` = 1 
                    AND store.organization_code = '{storecode}' 
                GROUP BY
                    d_store.platform_code        
        """
        delivery_store_result = db_yx_test_dsclound(delivery_store_sql)
        delivery_store_data = delivery_store_result['data']
        city = delivery_store_data[0]['city']
        # 查询平台对应配送费规则
        fee_rule_sql = f"""
                    SELECT
                        rule.id AS 'delivery_rule_id',
                        rule.trans_priority AS 'trans_priority' ,
                        rule.delivery_platform_code AS 'delivery_platform_code'
                    FROM
                        delivery_fee_rule_addprice addprice
                        LEFT JOIN delivery_fee_rule rule ON addprice.delivery_rule_id = rule.id 
                    WHERE
                        addprice.city_name = '{city}' 
                    GROUP BY 
                        rule.delivery_platform_code
                    ORDER BY rule.trans_priority
                """
        fee_rule_result = db_yx_test_dsclound(fee_rule_sql)
        fee_rule_data = fee_rule_result['data']
        # 获取序列中第一条值
        distance_min=distance_sort_list[0]
        disprice_min=disprice_sort_list[0]
        # 获取配送距离最小值
        distance_min_value = distance_min['distance_value']
        # 获取配送费最小值
        disprice_min_value = disprice_min['total_price_value']
        for fee_rule_item in fee_rule_data:
            rule_delivery_platform_code=fee_rule_item['delivery_platform_code']
            # 获取配送记录记录优先级最高的记录
            for distance_item in distance_sort_list:
                distance_value=distance_item['distance_value']
                distance_platform_code=distance_item['platform_code']
                # 如何平台编码一致，且值为最小值则为最小
                if rule_delivery_platform_code==distance_platform_code and float(distance_value)==float(distance_min_value):
                    distance_min=distance_item
            # 获取配送费记录优先级最高的记录
            for disprice_item in disprice_sort_list:
                disprice_value = disprice_item['total_price_value']
                disprice_platform_code = disprice_item['platform_code']
                # 如何平台编码一致，且值为最小值则为最小
                if rule_delivery_platform_code == disprice_platform_code and float(disprice_value) == float(
                        disprice_min_value):
                    disprice_min = disprice_item
        result={"distance_min":distance_min,"disprice_min":disprice_min}
        return result
    except Exception as e:
        raise e

if __name__=="__main__":
    test=[{'distance_value': 1.0, 'total_price_value': 1.02, 'platform_code': '2002', 'store_code': 'test_0001'},{'distance_value': 0.01, 'total_price_value': 0.02, 'platform_code': '2002', 'store_code': 'test_0001'},{'distance_value': 2.01, 'total_price_value': None, 'platform_code': '2002', 'store_code': 'test_0001'}]
    test1=sorted(test,key=lambda x: (x['total_price_value'] is None, x['total_price_value'] == "", x['total_price_value']))
    print(test1)
    distance_sort_list = test.sort(
        key=lambda x: (x['distance_value'] is None, x['distance_value'] == "", x['distance_value']))
    print(distance_sort_list)