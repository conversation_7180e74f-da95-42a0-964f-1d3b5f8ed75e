# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/9 10:46
@Auth ： 逗逗的小老鼠
@File ：order_route_info.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.db_conf_test import db_yx_test_dsclound,yx_test_base_info
from structure.order_test.xy_login import xy_user_token
from lib.get_log import logger,exception
from lib.deal_ini import readini
import requests
import json,re

"""
    订单基本信息查询
"""
@exception(logger)
def order_base_info(third_order_no):
    "订单基本信息查询"
    try:
        order_sql = f"""
            SELECT
                info.order_no AS 'order_no',
                info.created AS 'created',
                info.receiver_lat AS 'receiver_lat',
                info.receiver_lng AS 'receiver_lng',
                info.delivery_time_type AS 'delivery_time_type',
                info.delivery_time_desc AS 'delivery_time_desc',
                info.source_organization_code AS 'source_organization_code',
                info.source_organization_name AS 'source_organization_name',
                info.source_online_store_code AS 'source_online_store_code',
                info.source_online_store_name AS 'source_online_store_name',
                info.organization_name AS 'organization_name',
                info.organization_code AS 'organization_code',
                record.delivery_type AS 'delivery_type' 
            FROM
                order_info info
                LEFT JOIN order_delivery_record record ON info.order_no = record.order_no 
            WHERE
                third_order_no = '{third_order_no}'
        """
        order_result = db_yx_test_dsclound(order_sql)
        order_data = order_result['data']
        return order_data
    except Exception as e:
        raise e


"""
    获取门店经纬度
"""
@exception(logger)
def store_place(online_store_code):
    "获取门店经纬度"
    try:
        result = {"online_store_code": online_store_code, "store_lat": "", "store_lng": "", "code": 20000}
        # place_sql = f"""SELECT delivery_store_code,longitude,latitude FROM ds_delivery_store WHERE delivery_store_code='{online_store_code}'  LIMIT 1"""
        place_sql=f"""SELECT st_code,longitude,latitude FROM sys_store WHERE st_code='{online_store_code}' LIMIT 1"""
        place_result = yx_test_base_info(place_sql)
        place_data = place_result['data']
        if len(place_data) == 1:
            longitude = place_data[0]['longitude']
            latitude = place_data[0]['latitude']
            result['store_lat'] = latitude
            result['store_lng'] = longitude
            result["code"] = 10000
        return result
    except Exception as e:
        raise e



"""
    门店待拣货数量
"""
@exception(logger)
def store_todo_count(organization_code):
    "门店待拣货数量"
    try:
        todo_sql = f"""SELECT COUNT(*) AS 'count' FROM order_info WHERE organization_code='{organization_code}' AND order_state<30 AND service_mode='o2o'"""
        todo_result = db_yx_test_dsclound(todo_sql)
        todo_data = todo_result['data']
        count = todo_data[0]['count']
        return int(count)
    except Exception as e:
        raise e

"""
    策略详情查询
"""
@exception(logger)
def order_route_strategy_info(strategy_id):
    try:
        strategy_sql=f"""SELECT id,store_code,online_store_ids,state FROM route_strategy WHERE id='{strategy_id}'"""
        strategy_result=db_yx_test_dsclound(strategy_sql)
        strategy_data=strategy_result['data']
        strategy_id=strategy_data[0]['id']
        store_code=strategy_data[0]['store_code']
        online_store_ids=strategy_data[0]['online_store_ids']
        state=strategy_data[0]['state']
        online_store_list=[]
        # 根据;分割online_store_id
        online_store_value=online_store_ids.split(';')
        # 查询online_store_code
        for online_store_item in online_store_value:
            online_store_sql=f"""SELECT online_store_code FROM ds_online_store WHERE id='{online_store_item}'"""
            online_store_result=db_yx_test_dsclound(online_store_sql)
            online_store_data=online_store_result['data']
            online_store_code=online_store_data[0]['online_store_code']
            online_store_list.append(online_store_code)
        result={"strategy_id":strategy_id,"store_code":store_code,"online_store_list":online_store_list,"state":state}
        return result
    except Exception as e:
        raise e



"""
    分单规则数据
"""
@exception(logger)
def order_route_rule(strategy_id):
    "分单规则数据"
    try:
        result = {}
        rule_sql = f"""SELECT strategy.id AS 'strategy_rule_id',strategy.strategy_id AS 'strategy_id',strategy.rule_id AS 'rule_id',rule.rule_type AS 'rule_type',rule.rule_name AS 'rule_name',rule.handle_type AS 'handle_type',strategy.rule_value AS 'rule_value' FROM route_strategy_rule strategy LEFT JOIN route_rule rule ON strategy.rule_id=rule.id WHERE strategy_id='{strategy_id}'"""
        rule_result = db_yx_test_dsclound(rule_sql)
        rule_data = rule_result['data']
        # 获取分单策略具体数据
        for rule_item in rule_data:
            # 策略规则id
            strategy_rule_id = rule_item['strategy_rule_id']
            # 策略id
            strategy_id = rule_item['strategy_id']
            # 规则id
            rule_id = rule_item['rule_id']
            # 分接单类型
            rule_type = rule_item['rule_type']
            # 规则名称
            rule_name = rule_item['rule_name']
            # 规则键值
            handle_type = rule_item['handle_type']
            # 规则值
            rule_value = rule_item['rule_value']
            # 分单规则
            if rule_type == "HAND_OUT":
                # 分单时间段判断
                if handle_type == "OUT_TIME_FRAME_HANDLE":
                    assign_time_sql = f"""SELECT start_time,end_time FROM route_time_period_set WHERE strategy_rule_id={strategy_rule_id}"""
                    assign_time_result = db_yx_test_dsclound(assign_time_sql)
                    assign_time_data = assign_time_result['data']
                    assign_time_list=assign_time_data
                    result[handle_type] = assign_time_list
                else:
                    result[handle_type] = rule_value
            # 接单规则
            elif rule_type == "RECEIVE":
                # 按订单时段指定门店
                if handle_type=="RECEIVE_TIME_FRAME_HANDLE":
                    receive_time_sql=f"""SELECT start_time,end_time,ext_info FROM route_time_period_set WHERE strategy_rule_id={strategy_rule_id}"""
                    receive_time_result=db_yx_test_dsclound(receive_time_sql)
                    receive_time_data=receive_time_result['data']
                    receive_time_list=[]
                    for receive_time_item in receive_time_data:
                        receive_time_dict={}
                        start_time=receive_time_item['start_time']
                        end_time=receive_time_item['end_time']
                        ext_info=receive_time_item['ext_info']
                        # 若数据为空则不处理
                        if ext_info=="" or ext_info==None:
                            pass
                        else:
                            ext_json=json.loads(ext_info)
                            restorecode=ext_json['reStoreCode']
                            receive_time_dict['start_time']=start_time
                            receive_time_dict['end_time'] = end_time
                            receive_time_dict['restorecode'] = restorecode
                            receive_time_list.append(receive_time_dict)
                    result[handle_type] = receive_time_list
                # 指定商品指定门店
                elif handle_type=="RECEIVE_APPOINT_GOOD_HANDLE":
                    receive_good_sql=f"""SELECT re_store_code,erp_code FROM route_good_set WHERE strategy_rule_id={strategy_rule_id}"""
                    receive_good_result=db_yx_test_dsclound(receive_good_sql)
                    receive_good_data=receive_good_result['data']
                    result[handle_type]=receive_good_data
                # 接单门店列表
                elif handle_type=="RECEIVE_SHOP_LIST_HANDLE":
                    receive_shop_sql=f"""SELECT re_store_code,distance,priority,pick_num,acquiesce FROM route_receive_store_set WHERE strategy_rule_id={strategy_rule_id}"""
                    receive_shop_result=db_yx_test_dsclound(receive_shop_sql)
                    receive_shop_data=receive_shop_result['data']
                    result[handle_type] = receive_shop_data
                else:
                    result[handle_type] = rule_value
            else:
                pass
        return result
    except Exception as e:
        raise e



"""
    订单详情商品数量
"""
def order_detail_count(order_no):
    try:
        order_detail_sql=f"""SELECT erp_code,(SUM(commodity_count)-SUM(refund_count)) AS 'commodity_num' FROM order_detail WHERE order_no='{order_no}' AND status !=10 AND status!=11 GROUP BY erp_code"""
        order_detail_result=db_yx_test_dsclound(order_detail_sql)
        order_detail_data=order_detail_result['data']
        order_erp_list=[]
        for order_detail_item in order_detail_data:
            erp_code=order_detail_item['erp_code']
            order_erp_list.append(erp_code)
        result={"order_erp_list":order_erp_list,"order_detail_data":order_detail_data}
        return result
    except Exception as e:
        raise e



"""
    心云线上商品查询
    :param store_id:门店ID
    :param erp_code:ERP编码
    :return result：订单详情
"""
@exception(logger)
def xy_commodity_stock_api(store_list,erp_list):
    "心云线上商品查询"
    try:
        commodity_api_data=[]
        host=readini("xy_login_param.ini","test-host","host")
        url=host+f"/businesses-gateway/merchandise/1.0/ds/_search"
        token=xy_user_token()
        headers={"Authorization":token,"Content-Type":"application/json;charset=UTF-8"}
        data={"storeIds": store_list,"erpCodes": erp_list,"name": "","erpCode": "","stockMin": "","stockMax": "","priceMin": "","priceMax": "","barCode": "","admin": 0,"source": 1,"merCode": "500001","currentPage": 1,"pageSize": 99999}
        res = requests.post(url=url, data=json.dumps(data), headers=headers,verify=False)
        if res.status_code==200:
            res_json=res.json()
            res_code=res_json['code']
            if res_code=='10000':
                res_date=res_json['data']
                commodity_data=res_date['data']
                for commodity_item in commodity_data:
                    commodity_dict={}
                    storeCode=commodity_item['storeCode']
                    erpCode=commodity_item['erpCode']
                    stock=commodity_item['stock']
                    commodity_dict["storecode"]=storeCode
                    commodity_dict["erpcode"] = erpCode
                    commodity_dict["stock"] = stock
                    commodity_api_data.append(commodity_dict)
                result={"code":10000,"msg":"心云线上商品查询成功","data":commodity_api_data}
            else:
                result={"code":res_code,"msg":"心云线上商品查询查询失败","data":res_json['msg']}
        else:
            result={"code":res.status_code,"msg":"心云线上商品查询接口访问异常","data":commodity_api_data}
        return result
    except Exception as e:
        raise e



"""
    心云订单配送方式
    :param store_id:门店ID
    :param erp_code:ERP编码
    :return result：订单详情
"""
@exception(logger)
def xy_order_queryDeliveryPlatform_api(onlineStoreCode,orderNo,platformCode):
    "心云线上商品查询"
    try:
        commodity_api_data=[]
        host=readini("xy_login_param.ini","test-host","host")
        url=host+f"/businesses-gateway/dscloud/1.0/ds/baseinfo/queryDeliveryPlatform"
        token=xy_user_token()
        headers={"Authorization":token,"Content-Type":"application/json;charset=UTF-8"}
        data={'filterFlag': 1, 'orderNo': orderNo, 'platformCode': platformCode, 'onlineStoreCode': onlineStoreCode}
        res = requests.post(url=url, data=json.dumps(data), headers=headers,verify=False)
        if res.status_code==200:
            res_json=res.json()
            res_code=res_json['code']
            if res_code=='10000':
                res_date=res_json['data']
                commodity_data=res_date['data']
                for commodity_item in commodity_data:
                    commodity_dict={}
                    storeCode=commodity_item['storeCode']
                    erpCode=commodity_item['erpCode']
                    stock=commodity_item['stock']
                    commodity_dict["storecode"]=storeCode
                    commodity_dict["erpcode"] = erpCode
                    commodity_dict["stock"] = stock
                    commodity_api_data.append(commodity_dict)
                result={"code":10000,"msg":"心云线上商品查询成功","data":commodity_api_data}
            else:
                result={"code":res_code,"msg":"心云线上商品查询查询失败","data":res_json['msg']}
        else:
            result={"code":res.status_code,"msg":"心云线上商品查询接口访问异常","data":commodity_api_data}
        return result
    except Exception as e:
        raise e


"""
    预约送达时间处理
"""
def order_delivery_time_desc(delivery_time_desc):
    try:
        # 定义正则表达式模式，匹配形如"年-月-日 时:分:秒"的字符串
        pattern = r'(\d{4})-(\d{1,2})-(\d{1,2}) (\d{1,2}):(\d{1,2}):(\d{1,2})'
        delivery_time=re.split(pattern,delivery_time_desc)
        if len(delivery_time)>6:
            delivery_time_year=delivery_time[1]
            delivery_time_month = delivery_time[2]
            delivery_time_day = delivery_time[3]
            delivery_time_hour = delivery_time[4]
            delivery_time_min = delivery_time[5]
            delivery_time_sec = delivery_time[6]
            delivery_time_value=f"{delivery_time_year}-{delivery_time_month}-{delivery_time_day} {delivery_time_hour}:{delivery_time_min}:{delivery_time_sec}"

            print(delivery_time_value)
        else:
            delivery_time_value=""
        return delivery_time_value
    except Exception as e:
        raise e

if __name__=="__main__":

    delivery_time_desc=""
    order_delivery_time_desc(delivery_time_desc)
