# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/19 10:10
@Auth ： 逗逗的小老鼠
@File ：store_company_match.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.deal_db_mysql import db_mysql_connect
import json
import requests

@exception(logger)
class StoreCompanyMatch:
    """
    店铺和公司匹配
    """
    def __init__(self):
        self.org_tree = []

    def get_org_tree(self):
        "获取组织树"
        sql = f"""
                SELECT org_parent,org_code,org_name FROM t_organization_tree WHERE org_type=4 
                """
        db_mysql = db_mysql_connect("h3_pay_core", sql)
        result = db_mysql.get('data')
        return result


    def get_mdm_company(self,store_code):
        "获取mdm公司信息"
        try:
            url="http://yxt-org-read.svc.k8s.test.hxyxt.com/open-sdk/org/r/1.0/listOrgByCondition"
            headers={"Content-Type": "application/json;charset=UTF-8"}
            data={
                    "merCode":"500001",
                    "orgCodeList":[store_code]
                }
            response = requests.post(url=url,headers=headers,data=json.dumps(data))
            result = response.json()
            if result['code']=='10000':
                if len(result['data'])>0:
                    data=result['data'][0]
                else:
                    data={}
            else:
                data={}
            return data
        except Exception as e:
            logger.error(e)
            raise e

    def diff_match(self):
        "店铺和公司匹配"
        try:
            org_tree_list=self.get_org_tree()
            index=0
            for org in org_tree_list:
                index+=1
                org_code=org['org_code']
                org_name=org['org_name']
                org_parent=org['org_parent']
                mdm_company=self.get_mdm_company(org_code)
                if mdm_company:
                    orCode=mdm_company.get('orCode')
                    orName=mdm_company.get('orName')
                    affiliationCompany=mdm_company.get('affiliationCompany')
                    status=mdm_company.get('status')

                    if org_parent != affiliationCompany and status !=0:
                        print(f"店铺：{org_code}  MDM：{affiliationCompany} 支付：{org_parent} 公司匹配失败")
                    # if org_name != orName and status !=0:
                    #     print(f"店铺：{org_code}  MDM：{orName} 支付：{org_name} 名称匹配失败")
                    if org_parent != affiliationCompany and status==0:
                        print(f"【已失效】店铺：{org_code}  MDM：{affiliationCompany} 支付：{org_parent} 公司匹配失败")
                else:
                    print(f"店铺：{org_code}  在MDM查询失败")
            print(index)
        except Exception as e:
            logger.error(e)
            raise e


if __name__ == '__main__':
    StoreCompanyMatch().diff_match()