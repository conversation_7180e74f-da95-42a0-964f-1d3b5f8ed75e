# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/16 17:56
@Auth ： 逗逗的小老鼠
@File ：store_pay_config_match.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.deal_db_mysql import db_mysql_connect
import json


@exception(logger)
class StorePay:
    "门店支付配置比对"
    def __init__(self):
        self.store_code="store_code"

    def get_store_pay_config(self):
        "获取门店支付配置"
        sql = f"""
                SELECT
                    tpicrc.pay_channel_code,
                    tree.org_parent AS pay_company_code,
                    tpicrc.id AS t_pay_info_config_rela_channel_id,
                    tpcc.pay_channel_customer_id AS t_pay_channel_customer_id,
                    tpic.pay_business_name AS merchant_name,
                    tpic.pay_business_code AS merchant_code,
                    tpcc.channel_business_extends AS pay_config_detail,
                    tpicrc.info_status AS info_status,
                    tpcc.config_type AS config_type
                FROM
                    t_organization_tree tree
                    LEFT JOIN t_pay_info_config tpic ON tree.org_code=tpic.pay_business_code
                    LEFT JOIN t_pay_info_config_rela_channel tpicrc ON tpic.id = tpicrc.info_id
                    LEFT JOIN t_pay_channel_customer tpcc ON tpcc.pay_channel_customer_code = tpicrc.pay_channel_customer_code
                WHERE

                    tpic.is_delete = 2
                    AND tpicrc.pay_channel_code='WEIXIN'
                """
        db_mysql = db_mysql_connect("h3_pay_core",sql,environment_flag="prod")
        result = db_mysql.get('data')
        # 输出结果格式：
        # [{
        #     'pay_config_detail': '{"appId": "wx1de43ed1a571ecd8", "mchId": "***********", "cityId": "", "fileVOS": [{"url": "http://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20241014/123789456_1728888497117.p12", "name": "641.jpg"}], "certPath": "http://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20241014/123789456_1728888497117.p12", "appSecret": "1", "publicKey": "", "secretKey": "Yxt1225433002Dfgh1iJKlmnOPUvwxYZ", "privateKey": "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCmND2Evj7HE4aXPBmMTzS6pChCWh", "appletAppId": "wx1de43ed1a571ecd8", "publicAppId": "wxb5d94a25c46be756", "businessMode": 1, "wxChannelCode": "", "medicarePayKey": "", "customerSerialNumber": "123456"}',
        #     'config_type': 2, 'info_status': 1, 'merchant_code': 'H301', 'merchant_name': '一心堂成都营门口路药店',
        #     'pay_channel_code': 'WEIXIN', 'pay_company_code': '1006', 't_pay_channel_customer_id': 176,
        #     't_pay_info_config_rela_channel_id': 152}]
        return result

    def get_company_pay_config(self):
        "获取公司支付配置"
        sql = f"""
                SELECT
                    tree.org_parent AS pay_company_code,
                    tpoc.pay_config_detail AS pay_config_detail
                FROM
                    t_organization_tree tree
                    INNER JOIN t_pay_organization_config tpoc ON tree.org_parent = tpoc.org_code
                GROUP BY
                    tree.org_parent
                """
        db_mysql = db_mysql_connect("h3_pay_core",sql,environment_flag="prod")
        result = db_mysql.get('data')
        # 输出结果格式：
        # {'pay_company_code': '1000',
        #  'pay_config_detail': '{"appId": "wx1de43ed1a571ecd8", "cityId": "", "fileVOS": [{"url": "http://sk-test-centermerchant.oss-cn-chengdu.aliyuncs.com/pay/cert/20241120/1641710730_1732103113286.p12", "name": "apiclient_cert.p12"}], "orgCode": "1000", "appSecret": "3432432", "secretKey": "Yxt1225433002Dfgh1iJKlmnOPUvwxYZ", "privateKey": "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCmND2Evj7HE4aXPBmMTzS6pChCWh+M9qClABXnA6Up4T3ixuI1v9FV4ferQGRI146AJGilm7Bo2h+WcXap0ZI4xyJXAjgx/8vNmR7Qz96cA/SRYLTiYEak3xWOWnGl/Z2FqTmCXeg00X4X7Ajk6tRxKBajGdUBVevslOuemScKRO6imdZAyBNzPrsbJx7nehZwp581aLbpliMBmpPsHxySHqtSvcu1NTCAjdJDeNe7R2vvvQVdyEncI2/PSO+zVKfyEVCyPQVMsTu6O9IgDnkn7N9u63/dspSQBT3EfoCGdE2x/Tu+LtRTmvjmrykyOzTbEUBi6+Pypm/Qgmk87E7XAgMBAAECggEAGLlKsXVvd0PDULoC/ulFacJDruMjMF/7s6/Ykj3rkthL0XFOczpulH0Bz7rLcD6Ily1TV9daHa7c5g9I3KgtPT76L8FHxMBNe+8cM2nIrqKz/fBJ2CcEO0BoP33azBt3FEwBHxCDG2BShpJ5HDyGj7Kr82VDV2iFqPLaPgSAVzl+dYUkbAhWQWHTPJLNE3ES/iesxXdvxk1X4TcUc/cNhwPlhoSGYpxnTswcYX2bCr6VbdtEcO1qZPxvjLB1ehGNWrgo5xjZq5inMxlPCdLie9vPhyK3v0LOLXYinEIWG96PYZ7khmzphpbTAof2xr9EbfFf2ys5cvktn7ARkcJqmQKBgQDUIerqEsmhA1dnVdUxge7AFH34ZFvorbBT4QpvXMxLGkAAMElBriiJcG09Kvn89GusE+bzsNdgrR+dSmLUvFXYSacYg56JabC4MeVF38qQoYT8KVDG6PVs9z6r23Mr14NEm0vo4s4fvODdfnI1kMOswiND0AFNxCFf97bcMGroHQKBgQDIkurmXy7+FyVPCJ8Byym/QlUcxM7NMMK/l+ln/fdRjvC/L4frwqZm2q3TUGAQoAE3dRehVYCDcgGg9wVL+gpoFbfTNYgeBmQKiYGirD6l/b7mehcCiNobYRkgbzXBgJOgEuC+Z+etshslHRyKNUCDoLh50MfbG07SOGLm1U8ogwKBgQC4i4FIfWu3jaaeGUfNOBIyPCGS1Aw5EdFSR3bChuKbe4yuHVO9tftCBfKiwh6U9zYsj2veGLxmOHZGag4ssGOmM/0QEID9XlsFAzNIZHZe+IUuseq6ALRyRW3aKKG7RFPIfam+QoG7jp+5UWWeQABa7NGtDunzc2rtiB0m1WBL6QKBgGwRdM0hNeGjuO9ndrzxqRGq/M7eX4qBHj067YChftML0D1Cr9pga2X5pzS3Frt+aoll5pB/0BmCoMwIrGruUxMXuI7tReTfjqF6y0VJxwuKt4PY/nLYJ6Qe4fnu0ajrzR6vbTZKJ7MXxrZthAYXgSYqv5qq2jP897Aev3MeNWNfAoGBAL5KycvkGk2+IwNHaP/99XgoeGqxcEGvYPmmHXoAKhSERV0qv5GQqZV9bhB9oOAQwSrRp1ts0juP9FM7CAhARfBwOZ04GC4NbUtwF6ozumcpPzxEB6mMR7GZTqf1rP1D1oQVsHPEZizcv+mku97kSXS+odQrlqrmIRdGBhKHmhOW", "publicAppId": "wxb5d94a25c46be756", "wxChannelCode": "", "medicarePayKey": "", "payChannelCode": "WEIXIN", "payCompanyCode": "500001", "payCompanyName": "500001", "payOrgConfigId": "5", "payCustomerCode": "500001", "channelBusinessNo": "1641710730", "customerSerialNumber": "647e282458e6d5620aaafef4853541fee345f633"}'}
        return result


    def compare_pay_configs(self):
        "比对门店支付配置和公司支付配置"
        # 获取门店支付配置
        store_pay_configs = self.get_store_pay_config()
        # 获取公司支付配置
        company_pay_configs = self.get_company_pay_config()

        # 需要比对的字段列表
        fields_to_compare = [
            "appId", "cityId", "fileVOS", "appSecret", "secretKey",
            "privateKey", "publicAppId", "wxChannelCode", "medicarePayKey", "customerSerialNumber"
        ]

        # 存储比对结果
        comparison_results = []

        # 创建公司支付配置的字典，以pay_company_code为键
        company_configs_dict = {}
        for company_config in company_pay_configs:
            pay_company_code = company_config.get('pay_company_code')
            if pay_company_code:
                try:
                    pay_config_detail = json.loads(company_config.get('pay_config_detail', '{}'))
                    company_configs_dict[pay_company_code] = pay_config_detail
                except json.JSONDecodeError as e:
                    logger.error(f"解析公司支付配置JSON失败: {e}")
                    continue

        # 遍历门店支付配置，与对应的公司支付配置进行比对
        for store_config in store_pay_configs:
            store_pay_company_code = store_config.get('pay_company_code')
            if not store_pay_company_code or store_pay_company_code not in company_configs_dict:
                continue

            try:
                # 解析门店支付配置的JSON
                store_pay_config_detail = json.loads(store_config.get('pay_config_detail', '{}'))
                # 获取对应的公司支付配置
                company_pay_config_detail = company_configs_dict[store_pay_company_code]

                # 存储不一致的字段
                mismatched_fields = []

                # 比对指定字段
                for field in fields_to_compare:
                    store_value = store_pay_config_detail.get(field)
                    company_value = company_pay_config_detail.get(field)

                    # 对于fileVOS字段，需要特殊处理，因为它是一个列表
                    if field == "fileVOS":
                        # 如果两者都是列表，则比较列表内容
                        if isinstance(store_value, list) and isinstance(company_value, list):
                            # 简单比较：转换为字符串进行比较
                            if json.dumps(store_value, sort_keys=True) != json.dumps(company_value, sort_keys=True):
                                mismatched_fields.append({
                                    "field": field,
                                    "store_value": store_value,
                                    "company_value": company_value
                                })
                        # 如果其中一个不是列表，则认为不一致
                        elif store_value != company_value:
                            mismatched_fields.append({
                                "field": field,
                                "store_value": store_value,
                                "company_value": company_value
                            })
                    # 对于其他字段，直接比较值
                    elif store_value != company_value:
                        mismatched_fields.append({
                            "field": field,
                            "store_value": store_value,
                            "company_value": company_value
                        })

                # 如果有不一致的字段，则添加到结果中
                if mismatched_fields:
                    comparison_results.append({
                        "pay_company_code": store_pay_company_code,
                        "merchant_code": store_config.get('merchant_code'),
                        "merchant_name": store_config.get('merchant_name'),
                        "mismatched_fields": mismatched_fields
                    })

            except json.JSONDecodeError as e:
                logger.error(f"解析门店支付配置JSON失败: {e}")
                continue

        return comparison_results


if __name__ == '__main__':
    config = StorePay()
    results = config.compare_pay_configs()
    if results:
        print("发现配置不一致的情况:")
        for result in results:
            print(f"\n公司编码: {result['pay_company_code']}")
            print(f"门店编码: {result['merchant_code']}")
            print(f"门店名称: {result['merchant_name']}")
            print("不一致的字段:")
            for field_info in result['mismatched_fields']:
                print(f"  字段: {field_info['field']}")
                print(f"  门店值: {field_info['store_value']}")
                print(f"  公司值: {field_info['company_value']}")
                print("  ---")
    else:
        print("所有配置一致")