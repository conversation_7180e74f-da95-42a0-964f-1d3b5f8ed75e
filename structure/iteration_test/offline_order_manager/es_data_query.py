# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/22 14:01
@Auth ： 逗逗的小老鼠
@File ：es_data_query.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.db_elasticsearch import get_es_info
from lib.get_log import exception,logger


def get_es_offline_order_data(query_body,**kwargs):
    """获取es离线订单数据"""
    try:
        es_index="test_es_offline_order_manage"
        data=get_es_info(es_index,query_body,**kwargs)
        hits=data.get("hits")
        query_total=0
        query_data=[]
        if hits:
            query_total=hits.get("total").get("value",0)
            hits_data=hits.get("hits")
            for hits_item in hits_data:
                source_data=hits_item.get("_source")
                query_data.append(source_data)
        result={
            "query_total":query_total,
            "query_data":query_data
        }
        return result
    except Exception as e:
        raise