# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/22 14:09
@Auth ： 逗逗的小老鼠
@File ：api_data_query.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import requests
import json
import time
from lib.get_log import logger, exception
import threading
import urllib3

# 禁用https警告信息
urllib3.disable_warnings()


def order_manage_query(query_body, **kwargs):
    """获取订单管理数据"""
    try:
        url = "http://order-service.svc.k8s.test.hxyxt.com/1.0/manage/offline/order/list"
        headers = {"Content-Type": "application/json;charset=UTF-8"}
        body = {
            "companyCodeList": ["1006"],
            "currentPage": 0,
            "erpCode": "112470",
            "orderNo": "",
            "pageSize": 0,
            "storeCodeList": ["H812"],
            "thirdOrderNo": "1025041700000003",
            "thirdPlatformCode": "KE_CHUAN",
            "createdEnd": "2025-06-01 00:00:00",
            "createdStart": "2025-01-01 00:00:00"
        }
        pass
    except Exception as e:
        raise
