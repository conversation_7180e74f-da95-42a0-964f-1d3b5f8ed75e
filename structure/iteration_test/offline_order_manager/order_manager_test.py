
# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/22 14:43
@Auth ： 逗逗的小老鼠
@File ：order_manager_test.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

from itertools import permutations
from itertools import chain, combinations


def order_manager_test():
    """订单管理测试"""
    try:
        # 生成所有可能的排列组合
        all_permutations = ["companyCodeList","erpCode","orderNo","storeCodeList","thirdOrderNo","thirdPlatformCode"]
        # 生成所有可能的排列组合
        subsets = all_subsets(all_permutations)
        # 遍历所有子集
        for subset in subsets:
            print(subset)
    except Exception as e:
        raise





def all_subsets(iterable):
    "生成所有子集的迭代器"
    s = list(iterable)
    return chain.from_iterable(combinations(s, r) for r in range(len(s)+1))



if __name__ == '__main__':
    order_manager_test()
