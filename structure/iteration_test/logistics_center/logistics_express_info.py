# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/14 19:55
@Auth ： 逗逗的小老鼠
@File ：logistics_express_info.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect
import json
import requests

def logistics_express_logistics_query_third(logistics_no,**kwargs):
    "三方状态查询"
    try:
        req_url = f"http://logistics-center.svc.k8s.test.hxyxt.com/1.0/express/logistics/queryLogisticOrder?logisticsNo={logistics_no}"
        req_headers = {"Content-Type": "application/json;charset=UTF-8"}
        res = requests.get(url=req_url, headers=req_headers, verify=False)
        res_json = res.json()
        if res.status_code == 200 and res_json.get("code") == "10000":
            result = res_json.get("data")
            return result
        else:
            raise Exception(f"物流单查询失败,物流单号：{logistics_no},返回状态码：{res.json()}")
    except Exception as e:
        raise e

def logistics_express_logistics_cancel(logistics_no,**kwargs):
    "取消/拦截物流单"
    try:
        body = {
            "isDirectCancel": True,
            "logisticsNo": logistics_no
        }
        req_url = "http://logistics-center.svc.k8s.test.hxyxt.com/1.0/express/logistics/cancel"
        req_headers = {"Content-Type": "application/json;charset=UTF-8"}
        res = requests.post(url=req_url, headers=req_headers, data=json.dumps(body), verify=False)
        res_json = res.json()
        if res.status_code == 200 and res_json.get("code") == "10000":
            result = res_json.get("data")
            return result
        else:
            raise Exception(f"物流单取消/拦截失败,物流单号：{logistics_no},返回状态码：{res.json()}")
    except Exception as e:
        raise e

def logistics_express_upaddress(logistics_no,**kwargs):
    "物流单修改收货地址"
    try:
        oaid=kwargs.get("oaid","")
        address=kwargs.get("address","嘉汇馨源南区-6栋 (20栋菜鸟驿站)")
        city=kwargs.get("city","南宁市")
        district=kwargs.get("district","西乡塘区")
        province=kwargs.get("province","广西壮族自治区")
        receiverName=kwargs.get("receiver_name","没BUG(**)")
        receiverTelephone=kwargs.get("receiver_telephone","17780612022")
        receiverMobile=kwargs.get("receiver_mobile","18780612022")
        town=kwargs.get("town","西乡塘街道")
        body = {
            "logisticsNo": logistics_no,
            "receiverAddressInfo": {
                "address": address,
                "city": city,
                "district": district,
                "oaid": oaid,
                "province": province,
                "receiverMobile": receiverMobile,
                "receiverName": receiverName,
                "receiverTelephone": receiverTelephone,
                "town": town
            }
        }
        req_url = "http://logistics-center.svc.k8s.test.hxyxt.com/1.0/express/logistics/upAddress"
        req_headers = {"Content-Type": "application/json;charset=UTF-8"}
        res = requests.post(url=req_url, headers=req_headers, data=json.dumps(body), verify=False)
        res_json = res.json()
        if res.status_code == 200:
            return res_json
        else:
            raise Exception(f"物流单地址修改失败,物流单号：{logistics_no},返回状态码：{res.json()}")
    except Exception as e:
        raise e



def logistics_express_create(third_order_no,express_code,**kwargs):
    "物流单创建"
    try:
        # 查询订单及商品信息
        oms_info_sql=f"""
        SELECT
            oms.oms_order_no AS 'oms_order_no',
            oms.online_store_code AS 'online_store_code',
            detail.commodity_name AS 'commodity_name',
            detail.commodity_count AS 'commodity_count' 
        FROM
            oms_order_info oms
            LEFT JOIN order_detail detail ON oms.oms_order_no = detail.oms_order_no 
        WHERE
            oms.third_order_no = %s 
            AND oms.deleted = 0 
            AND oms.is_post_fee_order =0
        """
        oms_info_query=(third_order_no,)
        oms_query_result = db_mysql_connect("dscloud", oms_info_sql, sql_val=oms_info_query, **kwargs)
        oms_data = oms_query_result['data']
        # 查询收货地址信息
        address_query_sql=f"""
            SELECT
                oms.oms_order_no AS 'oms_order_no',
                address.address AS 'address',
                address.town AS 'town',
                address.city AS 'city',
                address.district AS 'district',
                address.province AS 'province',
                address.receiver_name AS 'receiver_name',
                address.receiver_telephone AS 'receiver_telephone',
                address.receiver_mobile AS 'receiver_mobile',
                address.oaid AS 'oaid'
            FROM
                oms_order_info oms
                LEFT JOIN order_delivery_address address ON oms.oms_order_no = address.oms_order_no 
            WHERE
                oms.third_order_no = %s
                AND oms.deleted = 0 
                AND oms.is_post_fee_order =0
            ORDER BY address.modify_time DESC
            LIMIT 1
        """
        address_query_result=db_mysql_connect("dscloud", address_query_sql, sql_val=oms_info_query, **kwargs)
        address_data = address_query_result['data']
        """
            组装创建物流单报文,报文实例
            {
                "expressCode": "YTO",
                "omsOrderNo": 1816051614037909255,
                "onlineStoreCode": "add9b3de422449ea813834b694d49bf4",
                "orderDetails": [
                    {
                        "commodityCount": 0,
                        "commodityName": ""
                    }
                ],
                "receiverAddressInfo": {
                    "address": "于田县阿热勒乡政府 (测试地址)",
                    "city": "和田地区",
                    "district": "于田县",
                    "oaid": "",
                    "province": "新疆维吾尔自治区",
                    "receiverMobile": "17780659272",
                    "receiverName": "接口测试",
                    "receiverTelephone": "17780659272",
                    "town": "阿热勒乡"
                },
                "thirdOrderNo": "3801343120765544879"
            }
        """
        if oms_data:
            orderDetails=[]
            for oms_item in oms_data:
                omsOrderNo=oms_item.get("oms_order_no",None)
                onlineStoreCode=oms_item.get("online_store_code",None)
                commodityCount=oms_item.get("commodity_count",None)
                commodityName=oms_item.get("commodity_name",None)
                detail_item={"commodityCount": commodityCount, "commodityName": commodityName}
                orderDetails.append(detail_item)
            if address_data:
                for address_item in address_data:
                    address=address_item.get("address",None)
                    town=address_item.get("town",None)
                    city=address_item.get("city",None)
                    district=address_item.get("district",None)
                    province=address_item.get("province",None)
                    receiverMobile=address_item.get("receiver_mobile",None)
                    receiverName=address_item.get("receiver_name",None)
                    receiverTelephone=address_item.get("receiver_telephone",None)
                    oaid=address_item.get("oaid",None)
                    receiverAddressInfo={"address": address, "town": town, "city": city, "district": district,
                                        "province": province, "receiverMobile": receiverMobile,"oaid": oaid,
                                        "receiverName": receiverName, "receiverTelephone": receiverTelephone}
                body={
                "expressCode": express_code,
                "omsOrderNo": omsOrderNo,
                "onlineStoreCode": onlineStoreCode,
                "orderDetails": orderDetails,
                "receiverAddressInfo": receiverAddressInfo,
                "thirdOrderNo": third_order_no
                }
                req_url="http://logistics-center.svc.k8s.test.hxyxt.com/1.0/express/logistics/create"
                req_headers={"Content-Type":"application/json;charset=UTF-8"}
                res=requests.post(url=req_url,headers=req_headers,data=json.dumps(body),verify=False)
                res_json=res.json()
                if res.status_code==200 and res_json.get("code")=="10000":
                    result=res_json.get("data")
                    return result
                else:
                    raise Exception(f"物流单创建失败,订单号：{third_order_no},返回状态码：{res.json()}")
            else:
                raise Exception(f"订单收货信息为空,订单号：{third_order_no}")
        else:
            raise Exception("未查询到对应的订单信息,订单号：{third_order_no}")
    except Exception as e:
        raise e


def logistics_express_current_state(logistics_no,**kwargs):
    "物流单当前状态查询"
    try:
        express_data = logistics_express_data(logistics_no, **kwargs)
        logistics_data = express_data['logistics_data']
        if logistics_data:
            logistics_express_detail=logistics_data[0]
            # 物流单状态
            logistics_express_state=logistics_express_detail.get("logistic_status",None)
            # 物流单关联订单号
            ext_oms_order_no=logistics_express_detail.get("ext_oms_order_no",None)
            # 物流平台编码
            express_code=logistics_express_detail.get("express_code",None)
            # 拦截状态
            intercept_tag=logistics_express_detail.get("intercept_tag",None)
            # 修改地址状态
            up_address_tag=logistics_express_detail.get("up_address_tag",None)
        else:
            logistics_express_state=None
            ext_oms_order_no=None
            express_code=None
            intercept_tag=None
            up_address_tag=None
        result={"express_state":logistics_express_state,"ext_oms_order_no":ext_oms_order_no,"express_code":express_code,"intercept_tag":intercept_tag,"up_address_tag":up_address_tag}
        return result
    except Exception as e:
        raise e

@exception(logger)
def logistics_express_data(logistics_no,**kwargs):
    "物流单信息查询"
    try:
        logistics_value=(logistics_no,)
        # 查询物流单信息
        logistics_sql = f"""
                    SELECT * FROM express_logistics_order WHERE logistics_no=%s;
                """
        query_result = db_mysql_connect("logistics_center", logistics_sql, sql_val=logistics_value, **kwargs)
        logistics_data = query_result['data']

        express_track_value=(logistics_no,)
        # 查询物流轨迹信息
        express_track_sql = f"""
                    SELECT * FROM express_track WHERE logistics_no=%s ORDER BY id desc;
                """
        express_track_result=db_mysql_connect("logistics_center", express_track_sql, sql_val=express_track_value, **kwargs)
        express_track_data=express_track_result['data']
        result={"logistics_data":logistics_data,"express_track_data":express_track_data}
        return result

    except Exception as e:
        raise e

"""
    定义三方快递数据的结构信息
    third_track_data={"express_state":"轨迹状态","operation_action":"轨迹操作描述","express_desc":"物流状态描述","location_province":"快件所在省","location_city":"快件所在市","location_district":"快件所在区","network_name":"网点名称","operation_time":"时间","expected_status":"预期状态"}
"""
@exception(logger)
def express_status_check(logistics_no,third_express_data,**kwargs):
    "校验物流单状态及轨迹信息是否正确"
    try:
        express_data=logistics_express_data(logistics_no,**kwargs)
        logistics_data=express_data['logistics_data']
        express_track_data=express_data['express_track_data']
        # 获取三方平台的快递信息
        third_express_state=third_express_data.get("express_state",None)
        third_operation_action=third_express_data.get("operation_action",None)
        third_express_desc=third_express_data.get("express_desc",None)
        third_location_province=third_express_data.get("location_province",None)
        third_location_city=third_express_data.get("location_city",None)
        third_location_district=third_express_data.get("location_district",None)
        third_network_name=third_express_data.get("network_name",None)
        third_operation_time=third_express_data.get("operation_time",None)
        third_expected_status=third_express_data.get("expected_status",None)
        third_express_code=third_express_data.get("express_code",None)
        # 物流单存在时
        if logistics_data:
            logistics_express_detail=logistics_data[0]
            # 物流单状态
            logistics_express_state=logistics_express_detail.get("logistic_status",None)
            logistics_express_code=logistics_express_detail.get("express_code",None)
            if express_track_data:
                error_detail=[]
                express_track_detail=express_track_data[0]
                track_express_state=express_track_detail.get("express_state",None)
                track_operation_action=express_track_detail.get("operation_action",None)
                track_express_desc=express_track_detail.get("express_desc",None)
                track_location_province=express_track_detail.get("location_province",None)
                track_location_city=express_track_detail.get("location_city",None)
                track_location_district=express_track_detail.get("location_district",None)
                track_network_name=express_track_detail.get("network_name",None)
                track_operation_time=express_track_detail.get("operation_time",None)
                # 如果轨迹时间不为空，则格式化时间
                if track_operation_time != None and track_operation_time != "":
                    track_operation_time=track_operation_time.strftime("%Y-%m-%d %H:%M:%S")
                # 比对物流平台编码
                if diff_value(logistics_express_code,third_express_code):
                    diff_detail={"expect_value":third_express_code,"actual_value":logistics_express_code,"check_value":"物流平台编码","check_key":"express_code"}
                    error_detail.append(diff_detail)
                # 比对物流单状态
                if diff_value(logistics_express_state,third_expected_status):
                    diff_detail={"expect_value":third_expected_status,"actual_value":logistics_express_state,"check_value":"物流单状态","check_key":"logistic_status"}
                    error_detail.append(diff_detail)
                # 比对物流轨迹状态
                if diff_value(track_express_state,third_express_state):
                    diff_detail={"expect_value":third_express_state,"actual_value":track_express_state,"check_value":"物流轨迹状态","check_key":"express_state"}
                    error_detail.append(diff_detail)
                # 比对物流轨迹操作描述
                if diff_value(track_operation_action,third_operation_action):
                    diff_detail={"expect_value":third_operation_action,"actual_value":track_operation_action,"check_value":"物流轨迹操作描述","check_key":"operation_action"}
                    error_detail.append(diff_detail)
                # 比对物流轨迹描述
                if diff_value(track_express_desc,third_express_desc):
                    diff_detail={"expect_value":third_express_desc,"actual_value":track_express_desc,"check_value":"物流轨迹描述","check_key":"express_desc"}
                    error_detail.append(diff_detail)
                # 比对物流轨迹所在省
                if diff_value(track_location_province,third_location_province):
                    diff_detail={"expect_value":third_location_province,"actual_value":track_location_province,"check_value":"物流轨迹所在省","check_key":"location_province"}
                    error_detail.append(diff_detail)
                # # 比对物流轨迹所在市
                if diff_value(track_location_city,third_location_city):
                    diff_detail={"expect_value":third_location_city,"actual_value":track_location_city,"check_value":"物流轨迹所在市","check_key":"location_city"}
                    error_detail.append(diff_detail)
                # # 比对物流轨迹所在区
                if diff_value(track_location_district,third_location_district):
                    diff_detail={"expect_value":third_location_district,"actual_value":track_location_district,"check_value":"物流轨迹所在区","check_key":"location_district"}
                    error_detail.append(diff_detail)
                # 比对物流轨迹网点名称
                if diff_value(track_network_name,third_network_name):
                    diff_detail={"expect_value":third_network_name,"actual_value":track_network_name,"check_value":"物流轨迹网点名称","check_key":"network_name"}
                    error_detail.append(diff_detail)
                # 比对物流轨迹操作时间
                if diff_value(track_operation_time,third_operation_time):
                    diff_detail={"expect_value":third_operation_time,"actual_value":track_operation_time,"check_value":"物流轨迹操作时间","check_key":"operation_time"}
                    error_detail.append(diff_detail)
                if len(error_detail)>0:
                    result={"check_result":"False","error_info":"物流轨迹或物流单状态信息不一致","error_detail":error_detail}
                else:
                    result={"check_result":"True","error_info":"物流轨迹或物流单状态信息一致","error_detail":[]}
            else:
                result={"check_result":"False","error_info":"物流单号没有轨迹信息","error_detail":[]}
        else:
            result={"check_result":"False","error_info":"物流单号不存在","error_detail":[]}
        return result
    except Exception as e:
        raise e


def diff_value(actual_value,expect_value):
    """两个值的差异"""
    try:
        if actual_value == None:
            actual_value=""
        if expect_value == None:
            expect_value=""
        if actual_value != expect_value:
            return True
        else:
            return False
    except Exception as e:
        raise e

if __name__ == '__main__':
    logistics_no = '73100160661843'
    # data=logistics_express_data(logistics_no)
    # print(data)
    third_order_no="3801343120765544879"
    express_code="YTO"
    # create_result=logistics_express_create(third_order_no,express_code)
    upaddress=logistics_express_upaddress(logistics_no)
    print(json.dumps(upaddress))