# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/21 20:14
@Auth ： 逗逗的小老鼠
@File ：express_intercept_mock_yto.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""


import json,requests,time



def zto_express_intercept_mock(logistics_interface):
    "进行拦截消息mock推送"
    try:
        push_url = "https://test-api.hxyxt.com/zeus/third-platform/callback/YTO/500001/intercept"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "data": json.dumps(logistics_interface,ensure_ascii=False),
            "channel": "K9991024989",
            "digest": "JQwXB+NXCHWgL4WlmYhzQw==",
            "digestData": "eyJyZXN1bHQiOiLpnZ7mjIflrprnvZHngrnnrb7mlLbvvIzlrp7nianmi6bmiKrlpLHotKUiLCJyZXN1bHRDb2RlIjoiNSIsIndheWJpbGxObyI6IllURDEwMDAwMDI3NzI0OSJ9",
            "token": ""
        }
        print(data)
        response = requests.post(push_url, data=data, headers=headers)
        result = response.json()
        print(result)
        return result
    except Exception as e:
        raise e


def yto_express_intercept_cancle_mock(logistics_no,**kwargs):
    # 拦截件已取消
    try:
        logistics_data={
                "result": "拦截件已取消",
                "resultCode": "1",
                "waybillNo": logistics_no
            }
        return logistics_data
    except Exception as e:
        raise e


def yto_express_intercept_timeout_mock(logistics_no,**kwargs):
    # 超时拦截失败
    try:
        logistics_data={
                "result": "超时拦截失败",
                "resultCode": "2",
                "waybillNo": logistics_no
            }
        return logistics_data
    except Exception as e:
        raise e


def yto_express_intercept_return_mock(logistics_no,**kwargs):
    # 已做退回/更址扫描操作
    try:
        logistics_data={
                "result": "已做退回/更址扫描操作",
                "resultCode": "3",
                "waybillNo": logistics_no
            }
        return logistics_data
    except Exception as e:
        raise e


def yto_express_intercept_signed_mock(logistics_no,**kwargs):
    # 快件已签收
    try:
        logistics_data={
                "result": "快件已签收",
                "resultCode": "4",
                "waybillNo": logistics_no
            }
        return logistics_data
    except Exception as e:
        raise e


def yto_express_intercept_fail_mock(logistics_no,**kwargs):
    # 非指定网点签收，实物拦截失败
    try:
        logistics_data={
                "result": "非指定网点签收，实物拦截失败",
                "resultCode": "5",
                "waybillNo": logistics_no
            }
        return logistics_data
    except Exception as e:
        raise e


if __name__ == '__main__':
    logistics_no = "YT2819009688626"
    mock_msg=yto_express_intercept_return_mock(logistics_no)
    result = zto_express_intercept_mock(mock_msg)
    print(json.dumps(result,ensure_ascii=False))