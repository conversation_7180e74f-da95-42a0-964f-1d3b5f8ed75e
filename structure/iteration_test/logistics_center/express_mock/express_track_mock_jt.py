# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/26 10:45
@Auth ： 逗逗的小老鼠
@File ：express_track_mock_jt.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json, datetime, requests, time
from lib.get_log import logger, exception
from structure.iteration_test.logistics_center.logistics_express_info import logistics_express_data, \
    logistics_express_current_state, express_status_check, logistics_express_create
from lib.deal_method import dynamically_import_and_call_method


def jt_express_track_normal(**kwargs):
    "正常轨迹信息:快件揽收-->发件扫描-->到件扫描-->出仓扫描-->入库扫描-->代理点收入扫描-->代理点出库扫描-->出库扫描"
    try:
        if "logistics_no" in kwargs:
            logistics_no = kwargs.get("logistics_no")
        elif "third_order_no" in kwargs:
            third_order_no = kwargs.get("third_order_no")
            express_code = "JTSD"
            # 创建物流单
            new_kwargs = {k: v for k, v in kwargs.items() if k != 'third_order_no'}
            logistics_create_data = logistics_express_create(third_order_no, express_code, **new_kwargs)
            logistics_no = logistics_create_data.get("logisticsNo")
        else:
            raise Exception("参数third_order_no或者logistics_no必须二选一")
        mock_workflow = "快件揽收-->发件扫描-->到件扫描-->出仓扫描-->入库扫描-->代理点收入扫描-->代理点出库扫描-->出库扫描"
        mock_list = [
            {"work_action_code": "GOT", "work_action_value": "快件揽收", "work_action_method": "jt_express_track_mock_GOT",
             "mock_workflow": mock_workflow},
            {"work_action_code": "DEPARTURE", "work_action_value": "发件扫描",
             "work_action_method": "jt_express_track_mock_SEND", "mock_workflow": mock_workflow},
            {"work_action_code": "ARRIVAL", "work_action_value": "到件扫描",
             "work_action_method": "jt_express_track_mock_ARRIVAL", "mock_workflow": mock_workflow},
            {"work_action_code": "WITHDRAWN", "work_action_value": "出仓扫描",
             "work_action_method": "jt_express_track_mock_WITHDRAWN", "mock_workflow": mock_workflow},
            {"work_action_code": "STOCK_IN", "work_action_value": "入库扫描",
             "work_action_method": "jt_express_track_mock_STOCK_IN", "mock_workflow": mock_workflow},
            {"work_action_code": "POINT_STOCK_IN", "work_action_value": "代理点收入扫描",
             "work_action_method": "jt_express_track_mock_POINT_STOCK_IN", "mock_workflow": mock_workflow},
            {"work_action_code": "POINT_OUT", "work_action_value": "代理点出库扫描",
             "work_action_method": "jt_express_track_mock_POINT_OUT",
             "mock_workflow": mock_workflow},
            {"work_action_code": "OUT_SCAN", "work_action_value": "出库扫描",
             "work_action_method": "jt_express_track_mock_OUT_SCAN",
             "mock_workflow": mock_workflow}
        ]
        result = jt_express_track_workflow(mock_list, logistics_no, **kwargs)
        return result
    except Exception as e:
        raise e

def jt_express_track_error(**kwargs):
    "正常轨迹信息:快件揽收-->发件扫描-->到件扫描-->出仓扫描-->入库扫描-->问题件扫描-->签收"
    try:
        if "logistics_no" in kwargs:
            logistics_no = kwargs.get("logistics_no")
        elif "third_order_no" in kwargs:
            third_order_no = kwargs.get("third_order_no")
            express_code = "JTSD"
            # 创建物流单
            new_kwargs = {k: v for k, v in kwargs.items() if k != 'third_order_no'}
            logistics_create_data = logistics_express_create(third_order_no, express_code, **new_kwargs)
            logistics_no = logistics_create_data.get("logisticsNo")
        else:
            raise Exception("参数third_order_no或者logistics_no必须二选一")
        mock_workflow = "快件揽收-->发件扫描-->到件扫描-->出仓扫描-->入库扫描-->问题件扫描-->签收"
        mock_list = [
            {"work_action_code": "GOT", "work_action_value": "快件揽收", "work_action_method": "jt_express_track_mock_GOT",
             "mock_workflow": mock_workflow},
            {"work_action_code": "DEPARTURE", "work_action_value": "发件扫描",
             "work_action_method": "jt_express_track_mock_SEND", "mock_workflow": mock_workflow},
            {"work_action_code": "ARRIVAL", "work_action_value": "到件扫描",
             "work_action_method": "jt_express_track_mock_ARRIVAL", "mock_workflow": mock_workflow},
            {"work_action_code": "WITHDRAWN", "work_action_value": "出仓扫描",
             "work_action_method": "jt_express_track_mock_WITHDRAWN", "mock_workflow": mock_workflow},
            {"work_action_code": "STOCK_IN", "work_action_value": "入库扫描",
             "work_action_method": "jt_express_track_mock_STOCK_IN", "mock_workflow": mock_workflow},
            {"work_action_code": "ERROR", "work_action_value": "问题件扫描",
             "work_action_method": "jt_express_track_mock_ERROR", "mock_workflow": mock_workflow},
            {"work_action_code": "SGINED", "work_action_value": "签收",
             "work_action_method": "jt_express_track_mock_SGINED",
             "mock_workflow": mock_workflow}
        ]
        result = jt_express_track_workflow(mock_list, logistics_no, **kwargs)
        return result
    except Exception as e:
        raise e

@exception(logger)
def jt_express_track_workflow(workflow_list, logistics_no, **kwargs):
    "进行轨迹mock推送"
    try:
        # 获取订单初始状态
        express_info = logistics_express_current_state(logistics_no, **kwargs)
        ext_oms_order_no = express_info.get("ext_oms_order_no", None)
        express_state = express_info.get("express_state", None)
        result = []
        if express_state == "WAIT_COLLECT":
            # 遍历需要测试的工作流
            for work_flow_item in workflow_list:
                work_flow_action_code = work_flow_item.get("work_action_code", None)
                work_flow_action_value = work_flow_item.get("work_action_value", None)
                work_flow_action_method = work_flow_item.get("work_action_method", None)
                mock_workflow = work_flow_item.get("mock_workflow", None)
                work_flow_action_moudle = "structure.iteration_test.logistics_center.express_mock.express_track_mock_jt"
                logistics_data = {"logistics_no": logistics_no, "ext_oms_order_no": ext_oms_order_no}
                work_flow_msg = dynamically_import_and_call_method(work_flow_action_moudle, work_flow_action_method,
                                                                   logistics_data)
                third_track_data = work_flow_msg.get("third_track_data")
                time.sleep(5)
                check_got = express_status_check(logistics_no, third_track_data, **kwargs)
                check_data = {"action_code": work_flow_action_code, "action_value": work_flow_action_value,
                              "check_data": check_got, "mock_workflow": mock_workflow}
                result.append(check_data)
        else:
            check_data = {"code": 500, "msg": f"物流单当前状态为{express_state},无法进行轨迹检测", "data": []}
        result.append(check_data)
        return result
    except Exception as e:
        raise e

def jt_express_track_mock(logistics_interface):
    "进行轨迹消息mock推送"
    try:

        push_url = "https://test-api.hxyxt.com/zeus/third-platform/callback/JTSD/500001/track"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "bizContent": json.dumps(logistics_interface, ensure_ascii=False),
            "data_digest": "8eJw2MGl2YAV3VRxxCQkiw==",
            "company_id": "91cc7cb9df51da39a35b6",
            "msg_type": "Traces",
            "token": ""
        }
        # print(data)
        response = requests.post(push_url, data=data, headers=headers)
        result = response.json()
        # print(result)
        return result
    except Exception as e:
        raise e

def jt_track_info_parse(logistics_interface):
    "解析物流信息"
    try:
        interface_detail=logistics_interface.get("details", None)
        detail_data=interface_detail[0]
        billCode = detail_data.get("billCode", None)
        desc = detail_data.get("desc", None)
        scanNetworkArea = detail_data.get("scanNetworkArea", None)
        scanNetworkCity = detail_data.get("scanNetworkCity", None)
        scanNetworkDetailAddress = detail_data.get("scanNetworkDetailAddress", None)
        scanNetworkId = detail_data.get("scanNetworkId", None)
        scanNetworkName = detail_data.get("scanNetworkName", None)
        scanNetworkProvince = detail_data.get("scanNetworkProvince", None)
        scanNetworkTypeName = detail_data.get("scanNetworkTypeName", None)
        scanTime = detail_data.get("scanTime", None)
        scanType = detail_data.get("scanType", None)
        track_data = {"billCode": billCode, "desc": desc, "scanNetworkArea": scanNetworkArea,
                      "scanNetworkCity": scanNetworkCity, "scanNetworkDetailAddress": scanNetworkDetailAddress,
                      "scanNetworkId": scanNetworkId, "scanNetworkName": scanNetworkName,
                      "scanNetworkProvince": scanNetworkProvince, "scanNetworkTypeName": scanNetworkTypeName,
                      "scanTime": scanTime, "scanType": scanType}
        return track_data
    except Exception as e:
        raise e

def jt_express_track_mock_GOT(logistics_data):
    "快件揽收"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
            "billCode": logistics_no,
            "details": [
                {
                    "billCode": logistics_no,
                    "desc": "【北京西城车公庄网点】的胡某生（13100001234）已取件，投诉电话 16623452345 ",
                    "scanNetworkArea": "西城区",
                    "scanNetworkCity": "北京市",
                    "scanNetworkDetailAddress": "中国北京北京市西城区西直门外大街132号京鼎大厦",
                    "scanNetworkId": 17896,
                    "scanNetworkName": "北京西城车公庄网点",
                    "scanNetworkProvince": "北京",
                    "scanNetworkTypeName": "网点",
                    "scanTime": now_time,
                    "scanType": "快件揽收"
                }
            ]
        }
        track_data = jt_track_info_parse(logistics_interface)
        mock_track = jt_express_track_mock(logistics_interface)
        mock_result = mock_track.get("msg")
        if mock_result == '成功':
            third_track_data = {"express_state": "WAIT_DIST", "operation_action": track_data['desc'],
                                "express_desc": "已揽收",
                                "location_province": track_data['scanNetworkProvince'],
                                "location_city": track_data['scanNetworkCity'],
                                "location_district": track_data['scanNetworkArea'],
                                "network_name": track_data['scanNetworkName'], "operation_time": now_time,
                                "expected_status": "WAIT_DIST", "express_code": "JTSD"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e

def jt_express_track_mock_SEND(logistics_data):
    "发件扫描"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
            "billCode": logistics_no,
            "details": [
                {
                    "billCode": logistics_no,
                    "desc": "快件离开【北京西城车公庄网点】已发往【廊坊转运中心】",
                    "nextNetworkAreaName": "三河市",
                    "nextNetworkCityName": "廊坊市",
                    "nextNetworkDetailAddress": "三河市高楼镇",
                    "nextNetworkId": 1002842,
                    "nextNetworkProvinceName": "河北省",
                    "nextStopName": "廊坊转运中心",
                    "scanNetworkArea": "西城区",
                    "scanNetworkCity": "北京市",
                    "scanNetworkDetailAddress": "中国北京北京市西城区西直门外大街132号京鼎大厦",
                    "scanNetworkId": 17896,
                    "scanNetworkName": "北京西城车公庄网点",
                    "scanNetworkTypeName": "网点",
                    "scanTime": now_time,
                    "scanType": "发件扫描"
                }
            ]
        }
        track_data = jt_track_info_parse(logistics_interface)
        mock_track = jt_express_track_mock(logistics_interface)
        mock_result = mock_track.get("msg")
        if mock_result == '成功':
            third_track_data = {"express_state": "IN_TRANSIT", "operation_action": track_data['desc'],
                                "express_desc": "运输中",
                                "location_province": track_data['scanNetworkProvince'],
                                "location_city": track_data['scanNetworkCity'],
                                "location_district": track_data['scanNetworkArea'],
                                "network_name": track_data['scanNetworkName'], "operation_time": now_time,
                                "expected_status": "IN_TRANSIT", "express_code": "JTSD"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e

def jt_express_track_mock_ARRIVAL(logistics_data):
    "到件扫描"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
            "billCode": logistics_no,
            "details": [
                {
                    "billCode": logistics_no,
                    "desc": " 快件到达【廊坊转运中心】",
                    "scanNetworkArea": "三河市",
                    "scanNetworkCity": "廊坊市",
                    "scanNetworkDetailAddress": "三河市高楼镇",
                    "scanNetworkId": 1002842,
                    "scanNetworkName": "廊坊转运中心",
                    "scanNetworkProvince": "河北省",
                    "scanNetworkTypeName": "中心",
                    "scanTime": now_time,
                    "scanType": "到件扫描"
                }
            ]
        }
        track_data = jt_track_info_parse(logistics_interface)
        mock_track = jt_express_track_mock(logistics_interface)
        mock_result = mock_track.get("msg")
        if mock_result == '成功':
            third_track_data = {"express_state": "IN_TRANSIT", "operation_action": track_data['desc'],
                                "express_desc": "运输中",
                                "location_province": track_data['scanNetworkProvince'],
                                "location_city": track_data['scanNetworkCity'],
                                "location_district": track_data['scanNetworkArea'],
                                "network_name": track_data['scanNetworkName'], "operation_time": now_time,
                                "expected_status": "IN_TRANSIT", "express_code": "JTSD"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e

def jt_express_track_mock_WITHDRAWN(logistics_data):
    "出仓扫描"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
            "billCode": logistics_no,
            "details": [
                {
                    "billCode": logistics_no,
                    "desc": "【佳木斯富锦网点】的极兔小哥 金大强 13800010001 正在派件（可放心接听952300专属热线），投诉电话：18511112222。今天的极兔小哥，体温正常，口罩戴好，消毒到位，即将为您派件。",
                    "scanNetworkArea": "富锦市",
                    "scanNetworkCity": "佳木斯市",
                    "scanNetworkDetailAddress": "富锦市汇通物流院内24-25号库",
                    "scanNetworkId": 5379,
                    "scanNetworkName": "佳木斯富锦网点",
                    "scanNetworkProvince": "黑龙江省",
                    "scanNetworkTypeName": "网点",
                    "scanTime": now_time,
                    "scanType": "出仓扫描"
                }
            ]
        }
        track_data = jt_track_info_parse(logistics_interface)
        mock_track = jt_express_track_mock(logistics_interface)
        mock_result = mock_track.get("msg")
        if mock_result == '成功':
            third_track_data = {"express_state": "DISPATCHED", "operation_action": track_data['desc'],
                                "express_desc": "派送中",
                                "location_province": track_data['scanNetworkProvince'],
                                "location_city": track_data['scanNetworkCity'],
                                "location_district": track_data['scanNetworkArea'],
                                "network_name": track_data['scanNetworkName'], "operation_time": now_time,
                                "expected_status": "DISPATCHED", "express_code": "JTSD"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e

def jt_express_track_mock_STOCK_IN(logistics_data):
    "入库扫描"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
            "billCode": logistics_no,
            "details": [
                {
                    "billCode": logistics_no,
                    "desc": "您的包裹已存放至【代收点】，记得早点来【星语心愿C区】取它回家！如有问题请联系金大强：13800010001，投诉电话：18511112222",
                    "scanNetworkArea": "富锦市",
                    "scanNetworkCity": "佳木斯市",
                    "scanNetworkDetailAddress": "富锦市汇通物流院内24-25号库",
                    "scanNetworkId": 5379,
                    "scanNetworkName": "佳木斯富锦网点",
                    "scanNetworkProvince": "黑龙江省",
                    "scanTime": now_time,
                    "scanType": "入库扫描"
                }
            ]
        }
        track_data = jt_track_info_parse(logistics_interface)
        mock_track = jt_express_track_mock(logistics_interface)
        mock_result = mock_track.get("msg")
        if mock_result == '成功':
            third_track_data = {"express_state": "DISPATCHED", "operation_action": track_data['desc'],
                                "express_desc": "派送中",
                                "location_province": track_data['scanNetworkProvince'],
                                "location_city": track_data['scanNetworkCity'],
                                "location_district": track_data['scanNetworkArea'],
                                "network_name": track_data['scanNetworkName'], "operation_time": now_time,
                                "expected_status": "DISPATCHED", "express_code": "JTSD"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e

def jt_express_track_mock_POINT_STOCK_IN(logistics_data):
    "代理点收入扫描"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
            "billCode": logistics_no,
            "details": [
                {
                    "billCode": logistics_no,
                    "desc": "您的包裹已在代理点收入扫描",
                    "scanNetworkArea": "富锦市",
                    "scanNetworkCity": "佳木斯市",
                    "scanNetworkDetailAddress": "富锦市汇通物流院内24-25号库",
                    "scanNetworkId": 5379,
                    "scanNetworkName": "佳木斯富锦网点",
                    "scanNetworkProvince": "黑龙江省",
                    "scanTime": now_time,
                    "scanType": "代理点收入扫描"
                }
            ]
        }
        track_data = jt_track_info_parse(logistics_interface)
        mock_track = jt_express_track_mock(logistics_interface)
        mock_result = mock_track.get("msg")
        if mock_result == '成功':
            third_track_data = {"express_state": "DISPATCHED", "operation_action": track_data['desc'],
                                "express_desc": "派送中",
                                "location_province": track_data['scanNetworkProvince'],
                                "location_city": track_data['scanNetworkCity'],
                                "location_district": track_data['scanNetworkArea'],
                                "network_name": track_data['scanNetworkName'], "operation_time": now_time,
                                "expected_status": "DISPATCHED", "express_code": "JTSD"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e

def jt_express_track_mock_POINT_OUT(logistics_data):
    "快件取出扫描"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
            "billCode": logistics_no,
            "details": [
                {
                    "billCode": logistics_no,
                    "desc": "您的包裹已从【星语心愿C区】重新取出，极兔小哥（金大强13800010001）将继续为您派件，请保持电话畅通  ",
                    "scanNetworkArea": "富锦市",
                    "scanNetworkCity": "佳木斯市",
                    "scanNetworkDetailAddress": "富锦市汇通物流院内24-25号库",
                    "scanNetworkId": 5379,
                    "scanNetworkName": "佳木斯富锦网点",
                    "scanTime": now_time,
                    "scanType": "快件取出扫描"
                }
            ]
        }
        track_data = jt_track_info_parse(logistics_interface)
        mock_track = jt_express_track_mock(logistics_interface)
        mock_result = mock_track.get("msg")
        if mock_result == '成功':
            third_track_data = {"express_state": "DISPATCHED", "operation_action": track_data['desc'],
                                "express_desc": "派送中",
                                "location_province": track_data['scanNetworkProvince'],
                                "location_city": track_data['scanNetworkCity'],
                                "location_district": track_data['scanNetworkArea'],
                                "network_name": track_data['scanNetworkName'], "operation_time": now_time,
                                "expected_status": "DISPATCHED", "express_code": "JTSD"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e

def jt_express_track_mock_OUT_SCAN(logistics_data):
    "出库扫描"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
            "billCode": logistics_no,
            "details": [
                {
                    "billCode": logistics_no,
                    "desc": "包裹已签收！（凭取件码）如有问题请联系金大强：13800010001，投诉电话：18511112222  特殊时期，每天消杀测温不懈怠，极兔防疫正行动。",
                    "scanNetworkArea": "富锦市",
                    "scanNetworkCity": "佳木斯市",
                    "scanNetworkDetailAddress": "富锦市汇通物流院内24-25号库",
                    "scanNetworkId": 5379,
                    "scanNetworkName": "代收点",
                    "scanNetworkProvince": "佳木斯富锦网点",
                    "scanTime": now_time,
                    "scanType": "出库扫描"
                }
            ]
        }
        track_data = jt_track_info_parse(logistics_interface)
        mock_track = jt_express_track_mock(logistics_interface)
        mock_result = mock_track.get("msg")
        if mock_result == '成功':
            third_track_data = {"express_state": "FINISH", "operation_action": track_data['desc'],
                                "express_desc": "已签收",
                                "location_province": track_data['scanNetworkProvince'],
                                "location_city": track_data['scanNetworkCity'],
                                "location_district": track_data['scanNetworkArea'],
                                "network_name": track_data['scanNetworkName'], "operation_time": now_time,
                                "expected_status": "FINISH", "express_code": "JTSD"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e

def jt_express_track_mock_SGINED(logistics_data):
    "快件取出扫描"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
            "billCode": logistics_no,
            "details": [
                {
                    "billCode": logistics_no,
                    "desc": "包裹已签收！签收人是【门卫代收】，如有问题请联系白兵兵：18800001000，投诉电话：18511112222  特殊时期，每天消杀测温不懈怠，极兔防疫正行动。",
                    "scanNetworkArea": "富锦市",
                    "scanNetworkCity": "佳木斯市",
                    "scanNetworkDetailAddress": "富锦市汇通物流院内24-25号库",
                    "scanNetworkId": 5379,
                    "scanNetworkName": "佳木斯富锦网点",
                    "scanNetworkProvince": "黑龙江省",
                    "scanNetworkTypeName": "网点",
                    "scanTime": now_time,
                    "scanType": "快件签收"
                }
            ]
        }
        track_data = jt_track_info_parse(logistics_interface)
        mock_track = jt_express_track_mock(logistics_interface)
        mock_result = mock_track.get("msg")
        if mock_result == '成功':
            third_track_data = {"express_state": "FINISH", "operation_action": track_data['desc'],
                                "express_desc": "已签收",
                                "location_province": track_data['scanNetworkProvince'],
                                "location_city": track_data['scanNetworkCity'],
                                "location_district": track_data['scanNetworkArea'],
                                "network_name": track_data['scanNetworkName'], "operation_time": now_time,
                                "expected_status": "FINISH", "express_code": "JTSD"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e

def jt_express_track_mock_ERROR(logistics_data):
    "问题件扫描"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
            "billCode": logistics_no,
            "details": [
                {
                    "billCode": logistics_no,
                    "desc": "【大理转运中心】已进行问题件扫描，扫描人员是【小安(00120034)】，问题原因【有发未到件】",
                    "scanNetworkArea": "大理市",
                    "scanNetworkCity": "大理白族自治州",
                    "scanNetworkDetailAddress": "云南省大理白族自治州大理市海东新城兴武街以南、横三路以北的海东新区物流园",
                    "scanNetworkId": 7666,
                    "scanNetworkName": "大理转运中心",
                    "scanNetworkTypeName": "中心",
                    "scanTime": now_time,
                    "scanType": "问题件扫描"
                }
            ]
        }
        track_data = jt_track_info_parse(logistics_interface)
        mock_track = jt_express_track_mock(logistics_interface)
        mock_result = mock_track.get("msg")
        if mock_result == '成功':
            third_track_data = {"express_state": "ERROR", "operation_action": track_data['desc'],
                                "express_desc": "异常",
                                "location_province": track_data['scanNetworkProvince'],
                                "location_city": track_data['scanNetworkCity'],
                                "location_district": track_data['scanNetworkArea'],
                                "network_name": track_data['scanNetworkName'], "operation_time": now_time,
                                "expected_status": "ERROR", "express_code": "JTSD"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e



if __name__ == "__main__":
    ext_oms_order_no = "1816044474492271879_1"
    logistics_no = "YT2819009495626"
    third_order_no = "3801354493941858930"
    express_code = "ZTO"
    mock_normal_result=jt_express_track_normal(third_order_no=third_order_no)
    print(json.dumps(mock_normal_result))
    mock_error_result=jt_express_track_error(third_order_no=third_order_no)
    print(json.dumps(mock_error_result))