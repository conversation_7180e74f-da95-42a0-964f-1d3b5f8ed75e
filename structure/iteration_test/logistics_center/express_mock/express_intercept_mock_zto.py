# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/21 11:18
@Auth ： 逗逗的小老鼠
@File ：express_intercept_mock_zto.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json,requests,time



def zto_express_intercept_mock(logistics_interface):
    "进行拦截消息mock推送"
    try:
        push_url = "https://test-api.hxyxt.com/zeus/third-platform/callback/ZTO/500001/intercept"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "data": json.dumps(logistics_interface,ensure_ascii=False),
            "data_digest": "8eJw2MGl2YAV3VRxxCQkiw==",
            "company_id": "91cc7cb9df51da39a35b6",
            "msg_type": "zto.merchant.intercept.push",
            "token": ""
        }
        print(data)
        response = requests.post(push_url, data=data, headers=headers)
        result = response.json()
        print(result)
        return result
    except Exception as e:
        raise e


def zto_express_intercept_mock_upaddress(logistics_no,**kwargs):
    # 修改地址拦截
    try:
        intercept_status=kwargs.get("intercept_status","INTERCEPT_DISCONTINUE")
        if intercept_status=="INTERCEPT_FAIL":
            statusmsg="拦截失败"
        else:
            statusmsg="拦截完结"
        current_timestamp = time.time()
        logistics_data={
                "interceptType": "3",
                "serviceOrderStatusMsg": statusmsg,
                "waybillCode": logistics_no,
                "serviceOrderStatus": intercept_status,
                "channelName": "开放平台",
                "interceptName": "改地址",
                "serviceOrderId": "",
                "operationTime": current_timestamp,
                "channelCode": "OPEN_PLAT"
            }
        return logistics_data
    except Exception as e:
        raise e


def zto_express_intercept_mock_return_net(logistics_no,**kwargs):
    # 退回寄件网点
    try:
        intercept_status = kwargs.get("intercept_status", "INTERCEPT_DISCONTINUE")
        if intercept_status == "INTERCEPT_FAIL":
            statusmsg = "拦截失败"
        else:
            statusmsg = "拦截完结"
        current_timestamp = time.time()
        logistics_data={
                "interceptType": "1",
                "serviceOrderStatusMsg": statusmsg,
                "waybillCode": logistics_no,
                "serviceOrderStatus": intercept_status,
                "channelName": "开放平台",
                "interceptName": "退回发件网点",
                "serviceOrderId": "",
                "operationTime": current_timestamp,
                "channelCode": "OPEN_PLAT"
            }
        return logistics_data
    except Exception as e:
        raise e


def zto_express_intercept_mock_return_person(logistics_no,**kwargs):
    # 退回寄件人
    try:
        intercept_status = kwargs.get("intercept_status", "INTERCEPT_DISCONTINUE")
        if intercept_status == "INTERCEPT_FAIL":
            statusmsg = "拦截失败"
        else:
            statusmsg = "拦截完结"
        current_timestamp = time.time()
        logistics_data={
                "interceptType": "2",
                "serviceOrderStatusMsg": statusmsg,
                "waybillCode": logistics_no,
                "serviceOrderStatus": intercept_status,
                "channelName": "开放平台",
                "interceptName": "退回寄件人",
                "serviceOrderId": "",
                "operationTime": current_timestamp,
                "channelCode": "OPEN_PLAT"
            }
        return logistics_data
    except Exception as e:
        raise e


if __name__=="__main__":
    logistics_no="73100162386556"
    data=zto_express_intercept_mock_upaddress(logistics_no,intercept_status="INTERCEPT_FAIL")
    result=zto_express_intercept_mock(data)
    print(json.dumps(result))