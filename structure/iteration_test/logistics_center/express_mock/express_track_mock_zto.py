# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/19 18:41
@Auth ： 逗逗的小老鼠
@File ：express_track_mock_zto.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json
import time
import requests
from lib.get_log import logger, exception
import datetime
from structure.iteration_test.logistics_center.logistics_express_info import logistics_express_data, \
    logistics_express_current_state, express_status_check,logistics_express_create
from lib.deal_method import dynamically_import_and_call_method


@exception(logger)
def zto_express_track_workflow(workflow_list, logistics_no, **kwargs):
    "进行轨迹mock推送"
    try:
        # 获取订单初始状态
        express_info = logistics_express_current_state(logistics_no, **kwargs)
        ext_oms_order_no = express_info.get("ext_oms_order_no", None)
        express_state = express_info.get("express_state", None)
        result = []
        if express_state == "WAIT_COLLECT":
            # 遍历需要测试的工作流
            for work_flow_item in workflow_list:
                work_flow_action_code = work_flow_item.get("work_action_code", None)
                work_flow_action_value = work_flow_item.get("work_action_value", None)
                work_flow_action_method = work_flow_item.get("work_action_method", None)
                mock_workflow = work_flow_item.get("mock_workflow", None)
                work_flow_action_moudle = "structure.iteration_test.logistics_center.express_mock.express_track_mock_zto"
                logistics_data = {"logistics_no": logistics_no, "ext_oms_order_no": ext_oms_order_no}
                work_flow_msg = dynamically_import_and_call_method(work_flow_action_moudle, work_flow_action_method,
                                                                   logistics_data)
                third_track_data = work_flow_msg.get("third_track_data")
                time.sleep(5)
                check_got = express_status_check(logistics_no, third_track_data, **kwargs)
                check_data = {"action_code": work_flow_action_code, "action_value": work_flow_action_value,
                              "check_data": check_got, "mock_workflow": mock_workflow}
                result.append(check_data)
        else:
            check_data = {"code": 500, "msg": f"物流单当前状态为{express_state},无法进行轨迹检测", "data": []}
        result.append(check_data)
        return result
    except Exception as e:
        raise e

def zto_express_track_normal(**kwargs):
    "正常轨迹信息:收件-->发件-->到件-->派件-->签收"
    try:
        if "logistics_no" in kwargs:
            logistics_no=kwargs.get("logistics_no")
        elif "third_order_no" in kwargs:
            third_order_no = kwargs.get("third_order_no")
            express_code = "ZTO"
            # 创建物流单
            new_kwargs = {k: v for k, v in kwargs.items() if k != 'third_order_no'}
            logistics_create_data = logistics_express_create(third_order_no, express_code, **new_kwargs)
            logistics_no = logistics_create_data.get("logisticsNo")
        else:
            raise Exception("参数third_order_no或者logistics_no必须二选一")
        mock_workflow = "收件-->发件-->到件-->派件-->签收"
        mock_list = [
            {"work_action_code": "GOT", "work_action_value": "揽收成功", "work_action_method": "zto_express_track_mock_GOT",
             "mock_workflow": mock_workflow},
            {"work_action_code": "DEPARTURE", "work_action_value": "发件",
             "work_action_method": "zto_express_track_mock_DEPARTURE", "mock_workflow": mock_workflow},
            {"work_action_code": "ARRIVAL", "work_action_value": "发出",
             "work_action_method": "zto_express_track_mock_ARRIVAL", "mock_workflow": mock_workflow},
            {"work_action_code": "DISPATCH", "work_action_value": "派件",
             "work_action_method": "zto_express_track_mock_DISPATCH", "mock_workflow": mock_workflow},
            {"work_action_code": "SIGNED", "work_action_value": "签收成功",
             "work_action_method": "zto_express_track_mock_SIGNED", "mock_workflow": mock_workflow}
        ]
        result = zto_express_track_workflow(mock_list, logistics_no, **kwargs)
        return result
    except Exception as e:
        raise e


def zto_express_track_third_normal(**kwargs):
    "正常轨迹信息:收件-->发件-->到件-->第三方派件进站-->第三方派件进站签收-->第三方出库签收成功"
    try:
        if "logistics_no" in kwargs:
            logistics_no=kwargs.get("logistics_no")
        elif "third_order_no" in kwargs:
            third_order_no = kwargs.get("third_order_no")
            express_code = "ZTO"
            # 创建物流单
            new_kwargs = {k: v for k, v in kwargs.items() if k != 'third_order_no'}
            logistics_create_data = logistics_express_create(third_order_no, express_code, **new_kwargs)
            logistics_no = logistics_create_data.get("logisticsNo")
        else:
            raise Exception("参数third_order_no或者logistics_no必须二选一")
        mock_workflow = "收件-->发件-->到件-->第三方派件进站-->第三方派件进站签收-->第三方出库签收成功"
        mock_list = [
            {"work_action_code": "GOT", "work_action_value": "揽收成功", "work_action_method": "zto_express_track_mock_GOT",
             "mock_workflow": mock_workflow},
            {"work_action_code": "DEPARTURE", "work_action_value": "发件",
             "work_action_method": "zto_express_track_mock_DEPARTURE", "mock_workflow": mock_workflow},
            {"work_action_code": "ARRIVAL", "work_action_value": "发出",
             "work_action_method": "zto_express_track_mock_ARRIVAL", "mock_workflow": mock_workflow},
            {"work_action_code": "HANDOVERSCAN_SIGNED", "work_action_value": "第三方派件进站",
             "work_action_method": "zto_express_track_mock_HANDOVERSCAN_SIGNED", "mock_workflow": mock_workflow},
            {"work_action_code": "HANDOVERSCAN_INBOUND", "work_action_value": "第三方派件进站签收",
             "work_action_method": "zto_express_track_mock_HANDOVERSCAN_INBOUND", "mock_workflow": mock_workflow},
            {"work_action_code": "HANDOVERSCAN_DEPARTURE_SIGNED", "work_action_value": "第三方出库签收成功",
             "work_action_method": "zto_express_track_mock_HANDOVERSCAN_DEPARTURE_SIGNED", "mock_workflow": mock_workflow}
        ]
        result = zto_express_track_workflow(mock_list, logistics_no, **kwargs)
        return result
    except Exception as e:
        raise e


def zto_express_track_return(**kwargs):
    "正常轨迹信息:收件-->发件-->到件-->第三方派件进站-->第三方派件进站签收-->第三方出库签收成功"
    try:
        if "logistics_no" in kwargs:
            logistics_no=kwargs.get("logistics_no")
        elif "third_order_no" in kwargs:
            third_order_no = kwargs.get("third_order_no")
            express_code = "ZTO"
            # 创建物流单
            new_kwargs = {k: v for k, v in kwargs.items() if k != 'third_order_no'}
            logistics_create_data = logistics_express_create(third_order_no, express_code, **new_kwargs)
            logistics_no = logistics_create_data.get("logisticsNo")
        else:
            raise Exception("参数third_order_no或者logistics_no必须二选一")
        mock_workflow = "收件-->发件-->退回"
        mock_list = [
            {"work_action_code": "GOT", "work_action_value": "揽收成功", "work_action_method": "zto_express_track_mock_GOT",
             "mock_workflow": mock_workflow},
            {"work_action_code": "DEPARTURE", "work_action_value": "发件",
             "work_action_method": "zto_express_track_mock_DEPARTURE", "mock_workflow": mock_workflow},
            {"work_action_code": "RETURN_SCAN", "work_action_value": "退回",
             "work_action_method": "zto_express_track_mock_RETURN_SCAN", "mock_workflow": mock_workflow}
        ]
        result = zto_express_track_workflow(mock_list, logistics_no, **kwargs)
        return result
    except Exception as e:
        raise e

def zto_express_track_mock(logistics_interface):
    "进行轨迹消息mock推送"
    try:

        push_url = "https://test-api.hxyxt.com/zeus/third-platform/callback/ZTO/500001/track"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "data": json.dumps(logistics_interface,ensure_ascii=False),
            "data_digest": "8eJw2MGl2YAV3VRxxCQkiw==",
            "company_id": "91cc7cb9df51da39a35b6",
            "msg_type": "Traces",
            "token": ""
        }
        print(data)
        response = requests.post(push_url, data=data, headers=headers)
        result = response.json()
        print(result)
        return result
    except Exception as e:
        raise e

def zto_track_info_parse(logistics_interface):
    "解析物流信息"
    try:
        facilityCode=logistics_interface.get("facilityCode",None)
        actionTime=logistics_interface.get("actionTime",None)
        city=logistics_interface.get("city",None)
        facilityContactPhone=logistics_interface.get("facilityContactPhone",None)
        dist=logistics_interface.get("dist",None)
        billCode=logistics_interface.get("billCode",None)
        courier=logistics_interface.get("courier",None)
        action=logistics_interface.get("action",None)
        courierPhone=logistics_interface.get("courierPhone",None)
        facilityName=logistics_interface.get("facilityName",None)
        desc=logistics_interface.get("desc",None)

        track_data = {"facilityCode": facilityCode, "actionTime": actionTime, "city": city,
                      "facilityContactPhone": facilityContactPhone, "dist": dist, "billCode": billCode, "courier": courier,
                      "action": action,"courierPhone":courierPhone,"facilityName":facilityName,"desc":desc}
        return track_data
    except Exception as e:
        raise e

def zto_express_track_mock_GOT(logistics_data):
    "揽收成功"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
                "facilityCode": "02191",
                "actionTime": now_time,
                "city": "上海市",
                "facilityContactPhone": "021-60556935",
                "dist": "青浦区",
                "billCode": logistics_no,
                "courier": "梅宇兵",
                "action": "GOT",
                "courierPhone": "90000000000",
                "facilityName": "青浦华新",
                "desc": "【上海市】【青浦华新】(021-60556935)的业务员梅宇兵(90000000000)已揽收"
        }
        track_data = zto_track_info_parse(logistics_interface)
        mock_track = zto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("message")
        if mock_result == "success":
            third_track_data = {"express_state": "WAIT_DIST", "operation_action": track_data['desc'],
                                "express_desc": "已揽收",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['dist'],
                                "network_name": track_data['facilityName'], "operation_time": now_time,
                                "expected_status": "WAIT_DIST", "express_code": "ZTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def zto_express_track_mock_DEPARTURE(logistics_data):
    "发件"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
                "facilityCode": "02191",
                "nextNodeCode": "37131",
                "nextCity": "郑州市",
                "actionTime": now_time,
                "city": "上海市",
                "nextNodeName": "郑州高新区",
                "dist": "青浦区",
                "billCode": logistics_no,
                "action": "DEPARTURE",
                "facilityName": "青浦华新",
                "desc": "【上海市】快件离开【青浦华新】发往郑州高新区"
            }
        track_data = zto_track_info_parse(logistics_interface)
        mock_track = zto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("message")
        if mock_result == "success":
            third_track_data = {"express_state": "IN_TRANSIT", "operation_action": track_data['desc'],
                                "express_desc": "运输中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['dist'],
                                "network_name": track_data['facilityName'], "operation_time": now_time,
                                "expected_status": "IN_TRANSIT", "express_code": "ZTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def zto_express_track_mock_ARRIVAL(logistics_data):
    "到件"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
                "facilityCode": "37131",
                "actionTime": now_time,
                "city": "郑州市",
                "action": "ARRIVAL",
                "dist": "中原区",
                "billCode": logistics_no,
                "facilityName": "郑州高新区",
                "desc": "【郑州市】快件已到达【郑州高新区】"
            }
        track_data = zto_track_info_parse(logistics_interface)
        mock_track = zto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("message")
        if mock_result == "success":
            third_track_data = {"express_state": "IN_TRANSIT", "operation_action": track_data['desc'],
                                "express_desc": "运输中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['dist'],
                                "network_name": track_data['facilityName'], "operation_time": now_time,
                                "expected_status": "IN_TRANSIT", "express_code": "ZTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e



def zto_express_track_mock_HANDOVERSCAN_SIGNED(logistics_data):
    "第三方派件进站"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
                "actionTime": now_time,
                "facilityContactPhone": "027-59767205",
                "city": "黔南布依族苗族自治州",
                "action": "HANDOVERSCAN_SIGNED",
                "dist": "江夏区",
                "billCode": logistics_no,
                "facilityName": "常州景泰家园南门商铺2_10店",
                "desc": "【武汉市】快件已投递【常州景泰家园南门商铺2_10店】，如有问题请电联 (027-59767205)，感谢使用中通快递，期待再次为您服务！"
            }
        track_data = zto_track_info_parse(logistics_interface)
        mock_track = zto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("message")
        if mock_result == "success":
            third_track_data = {"express_state": "DISPATCHED", "operation_action": track_data['desc'],
                                "express_desc": "派送中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['dist'],
                                "network_name": track_data['facilityName'], "operation_time": now_time,
                                "expected_status": "DISPATCHED", "express_code": "ZTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def zto_express_track_mock_HANDOVERSCAN_INBOUND(logistics_data):
    "第三方派件签收"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
                "actionTime": now_time,
                "facilityContactPhone": "027-59767205",
                "city": "黔南布依族苗族自治州",
                "dist": "江夏区",
                "billCode": logistics_no,
                "action": "INBOUND",
                "facilityName": "常州景泰家园南门商铺2_10店",
                "comName": "菜鸟",
                "comCode": "1014",
                "desc": "【武汉市】快件已被【常州景泰家园南门商铺2_10店】代收，如有问题请联系 (027-59767205)，感谢使用中通快递，期待再次为您服务！"
            }
        track_data = zto_track_info_parse(logistics_interface)
        mock_track = zto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("message")
        if mock_result == "success":
            third_track_data = {"express_state": "DISPATCHED", "operation_action": track_data['desc'],
                                "express_desc": "派送中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['dist'],
                                "network_name": track_data['facilityName'], "operation_time": now_time,
                                "expected_status": "DISPATCHED", "express_code": "ZTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def zto_express_track_mock_HANDOVERSCAN_DEPARTURE_SIGNED(logistics_data):
    "第三方派件出站"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
                "actionTime": now_time,
                "facilityContactPhone": "027-59767205",
                "city": "攀枝花市",
                "dist": "江夏区",
                "billCode": logistics_no,
                "courierPhone": "027-59767205",
                "action": "DEPARTURE_SIGNED",
                "facilityName": "渣渣",
                "comName": "快递超市",
                "comCode": "1022",
                "desc": "【武汉市】已签收，签收人凭取货码签收，如有疑问请电联：027-59767205/ 027-59767205，您的快递已经妥投，风里来雨里去，只为客官您满意。上有老下有小，赏个好评好不好？"
            }
        track_data = zto_track_info_parse(logistics_interface)
        mock_track = zto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("message")
        if mock_result == "success":
            third_track_data = {"express_state": "FINISH", "operation_action": track_data['desc'],
                                "express_desc": "已签收",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['dist'],
                                "network_name": track_data['facilityName'], "operation_time": now_time,
                                "expected_status": "FINISH", "express_code": "ZTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def zto_express_track_mock_DISPATCH(logistics_data):
    "派件"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
                "facilityCode": "37131",
                "actionTime": now_time,
                "city": "郑州市",
                "facilityContactPhone": "0371-67584463",
                "dist": "中原区",
                "billCode": logistics_no,
                "courier": "梅宇兵",
                "action": "DISPATCH",
                "courierPhone": "90000000000",
                "facilityName": "郑州高新区",
                "desc": "【郑州市】【郑州高新区】（0371-67584463）的业务员梅宇兵（90000000000）正在派件【95720为中通快递员外呼专属号码，请放心接听】小哥今日体温正常，将佩戴口罩为您投递，也可以联系小哥将您的快递，放到您指定的代收点，祝您身体健康！"
            }
        track_data = zto_track_info_parse(logistics_interface)
        mock_track = zto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("message")
        if mock_result == "success":
            third_track_data = {"express_state": "DISPATCHED", "operation_action": track_data['desc'],
                                "express_desc": "派送中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['dist'],
                                "network_name": track_data['facilityName'], "operation_time": now_time,
                                "expected_status": "DISPATCHED", "express_code": "ZTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e



def zto_express_track_mock_SIGNED(logistics_data):
    "签收"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
                "facilityCode": "37131",
                "actionTime": now_time,
                "city": "郑州市",
                "facilityContactPhone": "0371-67584463",
                "dist": "中原区",
                "billCode": logistics_no,
                "courier": "梅宇兵",
                "action": "SIGNED",
                "courierPhone": "90000000000",
                "facilityName": "郑州高新区",
                "expressSigner": "王五",
                "desc": "【郑州市】快件已由【王五】签收，签收网点：【郑州高新区】。如有疑问请电联：90000000000，投诉电话：0371-67584463，您的快递已经妥投，风里来雨里去，只为客官您满意。上有老下有小，赏个好评好不好？"
            }
        track_data = zto_track_info_parse(logistics_interface)
        mock_track = zto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("message")
        if mock_result == "success":
            third_track_data = {"express_state": "FINISH", "operation_action": track_data['desc'],
                                "express_desc": "已签收",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['dist'],
                                "network_name": track_data['facilityName'], "operation_time": now_time,
                                "expected_status": "FINISH", "express_code": "ZTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def zto_express_track_mock_RETURN_SCAN(logistics_data):
    "退回"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = {
                "facilityCode": "02100",
                "actionTime": now_time,
                "city": "上海市",
                "returnUpdateType": "R-1-1-1",
                "dist": "青浦区",
                "billCode": logistics_no,
                "thirdTypeReason": "商家/发件人要求",
                "action": "RETURN_SCAN",
                "facilityName": "上海",
                "returnType": "1",
                "desc": "【上海市】快件已在 上海 被退回，原因：【由于商家/发件人要求退回发件网点】"
            }
        track_data = zto_track_info_parse(logistics_interface)
        mock_track = zto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("message")
        if mock_result == "success":
            third_track_data = {"express_state": "RETURNED", "operation_action": track_data['desc'],
                                "express_desc": "已退回",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['dist'],
                                "network_name": track_data['facilityName'], "operation_time": now_time,
                                "expected_status": "RETURNED", "express_code": "ZTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e



if __name__ == "__main__":
    ext_oms_order_no = "1816044474492271879_1"
    logistics_no = "YT2819009495626"
    third_order_no = "3801354193941858930"
    express_code = "ZTO"
    mock_normal_result=zto_express_track_normal(third_order_no=third_order_no)
    print(json.dumps(mock_normal_result))
    mock_third_result=zto_express_track_third_normal(third_order_no=third_order_no)
    print(json.dumps(mock_third_result))
    mock_return_result=zto_express_track_return(third_order_no=third_order_no)
    # mock_result=zto_express_track_third_normal(third_order_no=third_order_no)
    print(json.dumps(mock_return_result))