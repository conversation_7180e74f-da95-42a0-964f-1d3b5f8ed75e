# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/14 20:12
@Auth ： 逗逗的小老鼠
@File ：express_track_mock_yto.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：圆通物流单轨迹信息mock
"""
import json
import time

import requests
from lib.get_log import logger, exception
import datetime
from structure.iteration_test.logistics_center.logistics_express_info import logistics_express_data, \
    logistics_express_current_state, express_status_check,logistics_express_create
import xml.etree.ElementTree as ET
from lib.deal_method import dynamically_import_and_call_method

import uuid


@exception(logger)
def yto_express_track_workflow(workflow_list, logistics_no, **kwargs):
    "进行轨迹mock推送"
    try:
        # 获取订单初始状态
        express_info = logistics_express_current_state(logistics_no, **kwargs)
        ext_oms_order_no = express_info.get("ext_oms_order_no", None)
        express_state = express_info.get("express_state", None)
        result = []
        if express_state == "WAIT_COLLECT":
            # 遍历需要测试的工作流
            for work_flow_item in workflow_list:
                work_flow_action_code = work_flow_item.get("work_action_code", None)
                work_flow_action_value = work_flow_item.get("work_action_value", None)
                work_flow_action_method = work_flow_item.get("work_action_method", None)
                mock_workflow = work_flow_item.get("mock_workflow", None)
                work_flow_action_moudle = "structure.iteration_test.logistics_center.express_mock.express_track_mock_yto"
                logistics_data = {"logistics_no": logistics_no, "ext_oms_order_no": ext_oms_order_no}
                work_flow_msg = dynamically_import_and_call_method(work_flow_action_moudle, work_flow_action_method,
                                                                   logistics_data)
                third_track_data = work_flow_msg.get("third_track_data")
                time.sleep(5)
                check_got = express_status_check(logistics_no, third_track_data, **kwargs)
                check_data = {"action_code": work_flow_action_code, "action_value": work_flow_action_value,
                              "check_data": check_got, "mock_workflow": mock_workflow}
                result.append(check_data)
        else:
            check_data = {"code": 500, "msg": f"物流单当前状态为{express_state},无法进行轨迹检测", "data": []}
        result.append(check_data)
        return result
    except Exception as e:
        raise e


def yto_express_track_normal(**kwargs):
    "正常轨迹信息:揽件成功-收入-发出-派件-自提柜入库-签收成功"
    try:
        if "logistics_no" in kwargs:
            logistics_no=kwargs.get("logistics_no")
        elif "third_order_no" in kwargs:
            third_order_no = kwargs.get("third_order_no")
            express_code = "YTO"
            # 创建物流单
            new_kwargs = {k: v for k, v in kwargs.items() if k != 'third_order_no'}
            logistics_create_data = logistics_express_create(third_order_no, express_code, **new_kwargs)
            logistics_no = logistics_create_data.get("logisticsNo")
        else:
            raise Exception("参数third_order_no或者logistics_no必须二选一")
        mock_workflow = "揽件成功-->收入-->发出-->派件-->自提柜入库-->签收成功"
        mock_list = [
            {"work_action_code": "GOT", "work_action_value": "揽收成功", "work_action_method": "yto_express_track_mock_GOT",
             "mock_workflow": mock_workflow},
            {"work_action_code": "ARRIVAL", "work_action_value": "收入",
             "work_action_method": "yto_express_track_mock_ARRIVAL", "mock_workflow": mock_workflow},
            {"work_action_code": "DEPARTURE", "work_action_value": "发出",
             "work_action_method": "yto_express_track_mock_DEPARTURE", "mock_workflow": mock_workflow},
            {"work_action_code": "SENT_SCAN", "work_action_value": "派件",
             "work_action_method": "yto_express_track_mock_SENT_SCAN", "mock_workflow": mock_workflow},
            {"work_action_code": "INBOUND", "work_action_value": "自提柜入库",
             "work_action_method": "yto_express_track_mock_INBOUND", "mock_workflow": mock_workflow},
            {"work_action_code": "SIGNED", "work_action_value": "签收成功",
             "work_action_method": "yto_express_track_mock_SIGNED", "mock_workflow": mock_workflow}
        ]
        result = yto_express_track_workflow(mock_list, logistics_no, **kwargs)
        return result
    except Exception as e:
        raise e


def yto_express_track_failed( **kwargs):
    "修改预约取件时间-->揽收成功-->航空发货-->航空提货-->转寄-->派件-->签收失败"
    try:
        if "logistics_no" in kwargs:
            logistics_no=kwargs.get("logistics_no")
        elif "third_order_no" in kwargs:
            third_order_no = kwargs.get("third_order_no")
            express_code = "YTO"
            # 创建物流单
            new_kwargs = {k: v for k, v in kwargs.items() if k != 'third_order_no'}
            logistics_create_data = logistics_express_create(third_order_no, express_code, **new_kwargs)
            logistics_no = logistics_create_data.get("logisticsNo")
        else:
            raise Exception("参数third_order_no或者logistics_no必须二选一")
        mock_workflow = "修改预约取件时间-->揽收成功-->航空发货-->航空提货-->转寄-->派件-->签收失败"
        mock_list = [
            {"work_action_code": "BOOKING", "work_action_value": "修改预约取件时间",
             "work_action_method": "yto_express_track_mock_ORDER_BOOKING", "mock_workflow": mock_workflow},
            {"work_action_code": "GOT", "work_action_value": "揽收成功", "work_action_method": "yto_express_track_mock_GOT",
             "mock_workflow": mock_workflow},
            {"work_action_code": "AIR_SHIPMENT", "work_action_value": "航空发货",
             "work_action_method": "yto_express_track_mock_AIRSEND", "mock_workflow": mock_workflow},
            {"work_action_code": "AIR_PICKUP", "work_action_value": "航空提货",
             "work_action_method": "yto_express_track_mock_AIRPICK", "mock_workflow": mock_workflow},
            {"work_action_code": "FORWARDING", "work_action_value": "转寄",
             "work_action_method": "yto_express_track_mock_FORWARDING",
             "mock_workflow": mock_workflow},
            {"work_action_code": "SENT_SCAN", "work_action_value": "派件",
             "work_action_method": "yto_express_track_mock_SENT_SCAN", "mock_workflow": mock_workflow},
            {"work_action_code": "FAILED", "work_action_value": "签收失败",
             "work_action_method": "yto_express_track_mock_FAILED", "mock_workflow": mock_workflow}

        ]
        result = yto_express_track_workflow(mock_list, logistics_no, **kwargs)
        return result

    except Exception as e:
        raise e


def yto_express_track_return(**kwargs):
    "转寄轨迹信息:揽件成功-->收入-->发出-->派件-->退回"
    try:
        if "logistics_no" in kwargs:
            logistics_no=kwargs.get("logistics_no")
        elif "third_order_no" in kwargs:
            third_order_no = kwargs.get("third_order_no")
            express_code = "YTO"
            # 创建物流单
            new_kwargs = {k: v for k, v in kwargs.items() if k != 'third_order_no'}
            logistics_create_data = logistics_express_create(third_order_no, express_code, **new_kwargs)
            logistics_no = logistics_create_data.get("logisticsNo")
        else:
            raise Exception("参数third_order_no或者logistics_no必须二选一")
        mock_workflow = "揽件成功-->收入-->发出-->派件-->退回"
        mock_list = [
            {"work_action_code": "GOT", "work_action_value": "揽收成功", "work_action_method": "yto_express_track_mock_GOT",
             "mock_workflow": mock_workflow},
            {"work_action_code": "ARRIVAL", "work_action_value": "收入",
             "work_action_method": "yto_express_track_mock_ARRIVAL", "mock_workflow": mock_workflow},
            {"work_action_code": "DEPARTURE", "work_action_value": "发出",
             "work_action_method": "yto_express_track_mock_DEPARTURE", "mock_workflow": mock_workflow},
            {"work_action_code": "SENT_SCAN", "work_action_value": "派件",
             "work_action_method": "yto_express_track_mock_SENT_SCAN", "mock_workflow": mock_workflow},
            {"work_action_code": "TMS_RETURN", "work_action_value": "退回",
             "work_action_method": "yto_express_track_mock_TMS_RETURN", "mock_workflow": mock_workflow}
        ]
        result = yto_express_track_workflow(mock_list, logistics_no, **kwargs)
        return result
    except Exception as e:
        raise e


def yto_express_track_mock(logistics_interface):
    "进行轨迹消息mock推送"
    try:

        push_url = "https://test-api.hxyxt.com/zeus/third-platform/callback/YTO/500001/track"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "logistics_interface": logistics_interface,
            "data_digest": "8eJw2MGl2YAV3VRxxCQkiw==",
            "type": "online",
            "clientId": "TEST"
        }
        # print(data)
        response = requests.post(push_url, data=data, headers=headers)
        result = response.json()
        return result
    except Exception as e:
        raise e


def yto_track_info_parse(logistics_interface):
    "解析物流信息"
    try:
        root = ET.fromstring(logistics_interface)

        txLogisticID = get_element_text(root, "txLogisticID")
        mailNo = get_element_text(root, "mailNo")
        logisticProviderID = get_element_text(root, "logisticProviderID")
        infoContent = get_element_text(root, "infoContent")
        remark = get_element_text(root, "remark")
        acceptTime = get_element_text(root, "acceptTime")
        city = get_element_text(root, "city")
        district = get_element_text(root, "district")
        track_data = {"txLogisticID": txLogisticID, "mailNo": mailNo, "logisticProviderID": logisticProviderID,
                      "infoContent": infoContent, "remark": remark, "acceptTime": acceptTime, "city": city,
                      "district": district}
        return track_data
    except Exception as e:
        raise e


def yto_express_track_mock_GOT(logistics_data):
    "揽收成功"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>GOT</infoContent><mailNo>{logistics_no}</mailNo><weight>0.5</weight><acceptTime>{now_time}.0 CST</acceptTime><city>测试市</city><district>测试县</district><remark>测试省测试市测试县 公司 揽收成功</remark></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "WAIT_DIST", "operation_action": track_data['remark'],
                                "express_desc": "已揽收",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "WAIT_DIST", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_NOT_SEND(logistics_data):
    "揽收失败"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>NOT_SEND</infoContent><acceptTime>{now_time}.0 CST</acceptTime><remark>测试省测试市测试区测试 公司 揽收失败，原因：托寄物品为禁限寄品</remark><questionCause>托寄物品为禁限寄品</questionCause></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "ERROR", "operation_action": track_data['remark'],
                                "express_desc": "异常",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "ERROR", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_ARRIVAL(logistics_data):
    "已收入"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>ARRIVAL</infoContent><mailNo>{logistics_no}</mailNo><weight>0.0</weight><acceptTime>{now_time}.0 CST</acceptTime><city>测试市</city><district>测试区</district><remark>测试转运中心 已收入</remark></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "IN_TRANSIT", "operation_action": track_data['remark'],
                                "express_desc": "运输中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "IN_TRANSIT", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_DEPARTURE(logistics_data):
    "已发出"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>DEPARTURE</infoContent><mailNo>{logistics_no}</mailNo><acceptTime>{now_time}.0 CST</acceptTime><city>测试市</city><district>测试区</district><remark>测试转运中心 已发出 下一站 测试省测试市</remark></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "IN_TRANSIT", "operation_action": track_data['remark'],
                                "express_desc": "运输中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "IN_TRANSIT", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_SENT_SCAN(logistics_data):
    "派件"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>SENT_SCAN</infoContent><mailNo>{logistics_no}</mailNo><acceptTime>{now_time}.0 CST</acceptTime><deliveryName>张三</deliveryName><city>测试市</city><district>测试镇</district><contactInfo>13888888888</contactInfo><remark>测试省测试市测试区测试 公司 派件人：张三 派件中 派件员电话：13888888888</remark></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "DISPATCHED", "operation_action": track_data['remark'],
                                "express_desc": "派送中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "DISPATCHED", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_INBOUND(logistics_data):
    "自提柜入库"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>INBOUND</infoContent><mailNo>{logistics_no}</mailNo><acceptTime>{now_time}.0 CST</acceptTime><city>测试市</city><district>测试镇</district><remark>快件已被测试【自提柜】代收，请及时取件。有问题请联系派件员 13888888888</remark></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "DISPATCHED", "operation_action": track_data['remark'],
                                "express_desc": "派送中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "DISPATCHED", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_SIGNED(logistics_data):
    "签收成功"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>SIGNED</infoContent><mailNo>{logistics_no}</mailNo><signedName>李四</signedName><acceptTime>{now_time}.0 CST</acceptTime><district>测试镇</district><remark>快件已被签收。有问题请联系派件员 13888888888</remark></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "FINISH", "operation_action": track_data['remark'],
                                "express_desc": "已签收",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "FINISH", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_FAILED(logistics_data):
    "签收失败"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>FAILED</infoContent><mailNo>{logistics_no}</mailNo><acceptTime>{now_time}.0 CST</acceptTime><city>测试市</city><district>测试区</district><remark>测试省测试市测试区测试 公司 投递失败，原因：客户拒收</remark><questionCause>客户拒收</questionCause></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "ERROR", "operation_action": track_data['remark'],
                                "express_desc": "异常",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "ERROR", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_ORDER_BOOKING(logistics_data):
    "修改预约取件时间"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>ORDER_BOOKING</infoContent><mailNo>{logistics_no}</mailNo><acceptTime>{now_time}.0 CST</acceptTime><orgName>测试省测试市测试区测试</orgName><orgCode>123321</orgCode><empName>李四</empName><empCode>11112345</empCode><remark>订单信息修改</remark><bookingStartTime>{now_time}.0 CST</bookingStartTime><bookingEndTime>{now_time}.0 CST</bookingEndTime></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "WAIT_COLLECT", "operation_action": track_data['remark'],
                                "express_desc": "待揽收",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "WAIT_COLLECT", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_AIRSEND(logistics_data):
    "航空发货"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>AIRSEND</infoContent><mailNo>{logistics_no}</mailNo><acceptTime>{now_time}.0 CST</acceptTime><city>测试市</city><district>测试区</district><remark>快件已到达【测试国际机场】，准备飞往【测试转运中心】</remark></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "IN_TRANSIT", "operation_action": track_data['remark'],
                                "express_desc": "运输中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "IN_TRANSIT", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_AIRPICK(logistics_data):
    "航空提货"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>AIRPICK</infoContent><mailNo>{logistics_no}</mailNo><acceptTime>{now_time}.0 CST</acceptTime><city>测试市</city><district>测试区</district><remark>航班已落地，快件发往【测试转运中心】</remark></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "IN_TRANSIT", "operation_action": track_data['remark'],
                                "express_desc": "运输中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "IN_TRANSIT", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_TMS_RETURN(logistics_data):
    "退回"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>TMS_RETURN</infoContent><mailNo>{logistics_no}</mailNo><acceptTime>{now_time}.0 CST</acceptTime><city>测试市</city><district>测试市</district><remark>您的快件已被【测试省测试市测试市】安排退回</remark></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "RETURNED", "operation_action": track_data['remark'],
                                "express_desc": "已退回",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "RETURNED", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


def yto_express_track_mock_FORWARDING(logistics_data):
    "转寄"
    try:
        ext_oms_order_no = logistics_data.get("ext_oms_order_no", "")
        logistics_no = logistics_data.get("logistics_no", "")
        now_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        logistics_interface = f"<UpdateInfo><logisticProviderID>YTO</logisticProviderID><clientID>TEST</clientID><txLogisticID>{ext_oms_order_no}</txLogisticID><infoType>STATUS</infoType><infoContent>FORWARDING</infoContent><mailNo>{logistics_no}</mailNo><acceptTime>{now_time}.0 CST</acceptTime><city>测试市</city><district>测试区</district><remark>您的快件已被【测试转运中心】发往到新地址</remark></UpdateInfo>"
        track_data = yto_track_info_parse(logistics_interface)
        mock_track = yto_express_track_mock(logistics_interface)
        mock_result = mock_track.get("success")
        if mock_result == True:
            third_track_data = {"express_state": "IN_TRANSIT", "operation_action": track_data['remark'],
                                "express_desc": "运输中",
                                "location_province": "", "location_city": track_data['city'],
                                "location_district": track_data['district'],
                                "network_name": "", "operation_time": now_time,
                                "expected_status": "IN_TRANSIT", "express_code": "YTO"}
            result = {"mock_result": mock_track, "track_data": track_data, "third_track_data": third_track_data}
        else:
            raise Exception("轨迹信息mock推送失败")
        return result
    except Exception as e:
        raise e


# 定义一个函数来安全地获取元素文本，如果未找到则返回空字符串
def get_element_text(element, tag, default=''):
    found_element = element.find(tag)
    return found_element.text if found_element is not None else default


if __name__ == "__main__":
    ext_oms_order_no = "1816044474492271879_1"
    logistics_no = "YT2819009495626"
    third_order_no = "3801354493941858930"
    express_code = "YTO"
    # yto_express_track_mock_GOT(ext_oms_order_no, logistics_no)
    mock_normal_result=yto_express_track_normal(third_order_no=third_order_no)
    mock_fail_result=yto_express_track_failed(third_order_no=third_order_no)
    mock_return_result=yto_express_track_return(third_order_no=third_order_no)
    print(json.dumps(mock_normal_result))
    print(json.dumps(mock_fail_result))
    print(json.dumps(mock_return_result))

