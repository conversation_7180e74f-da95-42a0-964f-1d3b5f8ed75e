
# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/26 15:46
@Auth ： 逗逗的小老鼠
@File ：express_intercept_mock_jt.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

import json, datetime, requests, time
from lib.get_log import logger, exception

# {"bizContent":"{\"mailNo\":\"UT0000456908252\",\"interceptResult\":\"processing\"}"}

def jt_express_intercept_mock(logistics_interface):
    "进行轨迹消息mock推送"
    try:

        push_url = "https://test-api.hxyxt.com/zeus/third-platform/callback/JTSD/500001/intercept"
        headers = {"Content-Type": "application/x-www-form-urlencoded"}
        data = {
            "bizContent": json.dumps(logistics_interface, ensure_ascii=False)
        }
        response = requests.post(push_url, data=data, headers=headers)
        result = response.json()
        return result
    except Exception as e:
        raise e


def jt_express_intercept_mock_processing(logistics_no,**kwargs):
    # 修改地址拦截
    try:
        logistics_data={
                "mailNo": logistics_no,
                "interceptResult": "processing"
            }
        return logistics_data
    except Exception as e:
        raise e


def jt_express_intercept_mock_success(logistics_no,**kwargs):
    # 修改地址拦截
    try:
        logistics_data={
                "mailNo": logistics_no,
                "interceptResult": "success",
                "returnMailNo": logistics_no
            }
        return logistics_data
    except Exception as e:
        raise e


def jt_express_intercept_mock_fail(logistics_no,**kwargs):
    # 修改地址拦截
    try:
        logistics_data={
                "mailNo": logistics_no,
                "interceptResult": "fail",
                "errorDesc": "系统中存在待审核/已审核的申请记录，请勿重复申请！"
            }
        return logistics_data
    except Exception as e:
        raise e


if __name__ == '__main__':
    logistics_no = "UT0000456908252"
    logistics_data = jt_express_intercept_mock_fail(logistics_no)
    result = jt_express_intercept_mock(logistics_data)
    print(json.dumps(result))