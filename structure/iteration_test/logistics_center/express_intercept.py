# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/20 20:19
@Auth ： 逗逗的小老鼠
@File ：express_intercept.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import time

from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect
import json
import requests
from structure.iteration_test.logistics_center.logistics_express_info import logistics_express_data, \
    logistics_express_current_state, express_status_check,logistics_express_create,logistics_express_upaddress
from structure.iteration_test.logistics_center.express_mock.express_intercept_mock_zto import zto_express_intercept_mock,zto_express_intercept_mock_upaddress,zto_express_intercept_mock_return_net,zto_express_intercept_mock_return_person

# 快递修改地址处理
def express_address_update(**kwargs):
    try:
        result = []
        if "logistics_no" in kwargs:
            logistics_no = kwargs.get("logistics_no")
        elif "third_order_no" in kwargs:
            third_order_no = kwargs.get("third_order_no")
        else:
            raise Exception("参数third_order_no或者logistics_no必须二选一")
        express_code_list = ["ZTO"]
        # express_code_list = ["ZTO", "TYO", "JTSD", "YZXB", "JD"]
        for express_code in express_code_list:
            for i in range(1, 2):
                # 创建物流单
                new_kwargs = {k: v for k, v in kwargs.items() if k != 'third_order_no'}
                logistics_create_data = logistics_express_create(third_order_no, express_code, **new_kwargs)
                logistics_no = logistics_create_data.get("logisticsNo")
                logistics_create_dict={"express_code":express_code,"logistics_no":logistics_no}
                logistics_updateaddress_check=[]
                # 修改成功
                update_success=logistics_express_upaddress(logistics_no)
                if update_success['code']=="10000":
                    update_success_data = update_success.get("data", None)
                    update_status=update_success_data.get("status",None)
                    express_info = logistics_express_current_state(logistics_no, **kwargs)
                    express_code = express_info.get("express_code", None)
                    up_address_tag = express_info.get("up_address_tag", None)
                    if update_status=="MODIFY_ING":
                        if up_address_tag!="MODIFY_ING":
                            modify_result={"logistics_no":logistics_no,"express_code":express_code, "expect_value":"MODIFYING","actul_value":up_address_tag}
                            logistics_updateaddress_check.append(modify_result)
                        if express_code=="ZTO":
                            time.sleep(5)
                            # 模拟修改成功的消息
                            if i==1:
                                zto_mock_data=zto_express_intercept_mock_upaddress(logistics_no,intercept_status="INTERCEPT_DISCONTINUE")
                                zto_mock_result=zto_express_intercept_mock(zto_mock_data)
                                mock_success=zto_mock_result.get("message")
                                time.sleep(5)
                                if mock_success == "success":
                                    # 获取消息推送后的修改状态
                                    express_info = logistics_express_current_state(logistics_no, **kwargs)
                                    up_address_tag = express_info.get("up_address_tag", None)
                                    if up_address_tag != "MODIFY_SUCCESS":
                                        modify_result = {"logistics_no": logistics_no,"express_code":express_code,  "expect_value": "MODIFY_SUCCESS",
                                                         "actul_value": up_address_tag}
                                        logistics_updateaddress_check.append(modify_result)
                            if i==2:
                                # 模拟一个修改失败的消息
                                zto_mock_data = zto_express_intercept_mock_upaddress(logistics_no,
                                                                                     intercept_status="INTERCEPT_FAIL")
                                zto_mock_result = zto_express_intercept_mock(zto_mock_data)
                                mock_success = zto_mock_result.get("message")
                                time.sleep(5)
                                if mock_success == "success":
                                    # 获取消息推送后的修改状态
                                    express_info = logistics_express_current_state(logistics_no, **kwargs)
                                    up_address_tag = express_info.get("up_address_tag", None)
                                    if up_address_tag != "MODIFY_FAIL":
                                        modify_result = {"logistics_no": logistics_no,"express_code":express_code,  "expect_value": "MODIFY_FAIL",
                                                         "actul_value": up_address_tag}
                                        logistics_updateaddress_check.append(modify_result)
                        if express_code=="YTO":
                            # 模拟修改成功的消息

                            # 获取消息推送后的修改状态
                            express_info = logistics_express_current_state(logistics_no, **kwargs)
                            up_address_tag = express_info.get("up_address_tag", None)
                            if up_address_tag != "MODIFY_SUCCESS":
                                modify_result = {"logistics_no": logistics_no,"express_code":express_code,  "expect_value": "MODIFY_SUCCESS",
                                                 "actul_value": up_address_tag}
                                logistics_updateaddress_check.append(modify_result)

                            # 模拟一个修改失败的消息

                            # 获取消息推送后的修改状态
                            express_info = logistics_express_current_state(logistics_no, **kwargs)
                            up_address_tag = express_info.get("up_address_tag", None)
                            if up_address_tag != "MODIFY_FAIL":
                                modify_result = {"logistics_no": logistics_no,"express_code":express_code, "expect_value": "MODIFY_FAIL",
                                                 "actul_value": up_address_tag}
                                logistics_updateaddress_check.append(modify_result)
                    else:
                        if update_status != up_address_tag:
                            modify_result = {"logistics_no": logistics_no,"express_code":express_code, "expect_value": update_status,
                                             "actul_value": up_address_tag}
                            logistics_updateaddress_check.append(modify_result)
                logistics_create_dict["updateaddress_check"]=logistics_updateaddress_check
                result.append(logistics_create_dict)
            else:
                raise Exception(f"物流单地址修改失败,物流单号：{logistics_no},返回结果：{update_success}")
        return result
    except Exception as e:
        raise e



if __name__ == '__main__':
    third_order_no="3801347544120557738"
    update_result=express_address_update(third_order_no=third_order_no)
    print(json.dumps(update_result))
