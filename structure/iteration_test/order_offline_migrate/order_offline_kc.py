# -*- coding: utf-8 -*-
"""
@Time ： 2024/7/19 15:46
@Auth ： 逗逗的小老鼠
@File ：order_offline_kc.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from lib.db_conf_test import db_yx_test_dsclound_offline_archive
from lib.deal_format_convert import list_to_sql_filter



"""
    科传订单信息查询
"""
@exception(logger)
def offline_kc_order_info(store_list):
    try:
        store_list=list_to_sql_filter(store_list)
        # 订单主信息查询
        order_info_sql=f"""
            SELECT
                sale.XF_DOCNO AS 'XF_DOCNO',
                sale.XF_VOIDDOCNO AS 'XF_VOIDDOCNO',
                sale.XF_CREATETIME AS 'XF_CREATETIME',
                sale.XF_SELLINGAMOUNT AS 'XF_SELLINGAMOUNT',
                CONCAT_WS(
                    " ",
                    sale.XF_TXDATE,
                TIME_FORMAT(( sale.XF_TXTIME ), '%H:%i:%s' )) AS XF_BILLTIME,
                sale.XF_STORECODE AS 'XF_STORECODE',
                store.PLANT_TEXT AS 'PLANT_TEXT',
                store.COMP_CODE AS 'COMP_CODE',
                store.ZC_GSJC AS 'ZC_GSJC',
                store.JMTYPE AS 'JMTYPE',
                sale.XF_TILLID AS 'XF_TILLID',
                sale.XF_CASHIER AS 'XF_CASHIER',
                sale.XF_SALESMAN AS 'XF_SALESMAN',
                sale.XF_CLIENTCODE AS 'XF_CLIENTCODE',
                crm.`姓名` AS 'MER_NAME',
                crm.`移动电话` AS 'MER_TEL' 
            FROM
                xf_transsalestotal_cdhx_users sale
                LEFT JOIN cv_storeinfor store ON sale.XF_STORECODE = store.PLANT
                LEFT JOIN tb_crm_matster_n crm ON sale.XF_CLIENTCODE = crm.`会员卡ID(实物卡号)` 
            WHERE
                sale.XF_STORECODE IN ({store_list}) ;        
        """
        order_info_result=db_yx_test_dsclound_offline_archive(order_info_sql)
        order_info_data=order_info_result['data']
        # 订单支付信息查询
        order_pay_sql=f"""
            SELECT
                sale.XF_DOCNO AS 'XF_DOCNO',
                sale.XF_VOIDDOCNO AS 'XF_VOIDDOCNO',
                sale.XF_CLIENTCODE AS 'XF_CLIENTCODE',
                sale.XF_STORECODE AS 'XF_STORECODE',
                tender.XF_TENDERCODE AS 'XF_TENDERCODE',
                tend.DESCRIPTION AS 'DESCRIPTION',
                tender.XF_PAYAMOUNT AS 'XF_PAYAMOUNT' 
            FROM
                xf_transsalestotal_cdhx_users sale
                LEFT JOIN xf_transsalestender_cdhx_users tender ON tender.XF_DOCNO=sale.XF_DOCNO AND tender.XF_STORECODE=sale.XF_STORECODE
                LEFT JOIN tendtyt tend ON tender.XF_TENDERCODE = tend.TENDERTYPECODE 
            WHERE
                tender.XF_STORECODE IN ({store_list}) 
                AND tend.SPRAS = 1; 
        """
        order_pay_result=db_yx_test_dsclound_offline_archive(order_pay_sql)
        order_pay_data=order_pay_result['data']
        # 订单商品信息查询
        order_detail_sql=f"""
            SELECT
                sale.XF_DOCNO AS 'XF_DOCNO',
                sale.XF_VOIDDOCNO AS 'XF_VOIDDOCNO',
                sale.XF_CLIENTCODE AS 'XF_CLIENTCODE',
                sale.XF_STORECODE AS 'XF_STORECODE',
                item.XF_TXSERIAL AS 'XF_TXSERIAL',
                item.XF_PLU AS 'XF_PLU',
                makt.MAKTX AS 'MAKTX',
                ROUND(item.XF_AMTSOLD/item.XF_QTYSOLD,6) AS 'XF_PRICE',
                item.XF_QTYSOLD AS 'XF_QTYSOLD',
                item.XF_ORGUPRICE AS 'XF_ORGUPRICE' ,
                item.XF_AMTSOLD AS 'XF_AMTSOLD',
                item.XF_MARKDOWNAMT + item.XF_DISCOUNTAMT + item.XF_PROMOTIONAMT AS 'XF_DISCOUNTSHARE',
                item.XF_ITEMLOTNUM AS 'XF_ITEMLOTNUM'
            FROM
                xf_transsalestotal_cdhx_users sale
                LEFT JOIN	xf_transsalesitem_cdhx_users item ON item.XF_DOCNO=sale.XF_DOCNO AND item.XF_STORECODE=sale.XF_STORECODE
                LEFT JOIN makt ON item.XF_PLU = makt.MATNR 
            WHERE
                item.XF_STORECODE IN ({store_list}) ;
        """
        order_detail_result=db_yx_test_dsclound_offline_archive(order_detail_sql)
        order_detail_data=order_detail_result['data']
        result={"order_info":order_info_data,"salePayDetails":order_pay_data,"saleDetails":order_detail_data}
        return result
    except Exception as e:
        raise e


if __name__=="__main__":
    my_list = ['H477', 'H471']
    offline_kc_order_info_data=offline_kc_order_info(my_list)
    print(offline_kc_order_info_data)