# -*- coding: utf-8 -*-
"""
@Time ： 2024/7/19 16:35
@Auth ： 逗逗的小老鼠
@File ：order_offline_kc_compare.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json
from lib.db_conf_test import db_yx_test_dsclound_offline
from lib.get_log import exception,logger
from structure.iteration_test.order_offline_migrate.order_offline_kc import offline_kc_order_info
from structure.iteration_test.order_offline_migrate.order_offline_xy import offline_table_num,offline_kc_order_no
from lib.deal_excel import read_excel,write_excel
from decimal import Decimal,ROUND_HALF_UP
import datetime


"""
    科传订单信息比对
"""
@exception(logger)
def order_kc_compare(store_list):
    try:
        compare_result=[]
        # 获取科传的订单数据
        order_kc_sale_result=offline_kc_order_info(store_list)
        # 获取正向单字段映射关系
        order_sale_field_map=read_excel("order_offline_hydee_sale.xlsx","sale_order_kc")
        # 获取正向单字段枚举值映射
        order_sale_enum_map = read_excel("order_offline_hydee_sale.xlsx", "kc_sale_enum_map")
        # 获取退款单字段映射关系
        refund_field_map=read_excel("order_offline_hydee_sale.xlsx","refund_order_kc")
        sale_order_base_info=order_kc_sale_result['order_info']
        sale_order_pay_info=order_kc_sale_result['salePayDetails']
        sale_order_detail=order_kc_sale_result['saleDetails']
        sale_order_base_result=order_kc_orderinfo_compare(order_sale_enum_map,sale_order_base_info,order_sale_field_map,refund_field_map)
        compare_result.extend(sale_order_base_result)
        sale_order_pay_result=order_kc_orderpay_compare(order_sale_enum_map,sale_order_pay_info,order_sale_field_map,refund_field_map)
        compare_result.extend(sale_order_pay_result)
        sale_order_detail_result=order_kc_orderdetail_compare(order_sale_enum_map,sale_order_detail,order_sale_field_map,refund_field_map)
        compare_result.extend(sale_order_detail_result)
        print(compare_result)
        #将信息比对结果写入Excel
        now_time = datetime.datetime.now()
        now_date = datetime.datetime.strftime(now_time, "%Y-%m-%d %H&%M&%S")
        excel_name = "offline_order_kc_test.xlsx"
        sheet_name = f"路由比对结果_{now_date}"
        column_list = ["分表位置","门店编码","订单号", "DB类名", "DB字段", "原始值", "迁移值", "备注", "订单类型"]
        write_excel(excel_name, sheet_name, column_list, compare_result)
        return compare_result
    except Exception as e:
        raise e


"""
    科传订单主信息比对
"""
@exception(logger)
def order_kc_orderinfo_compare(order_sale_enum_map,sale_order_base_info,order_sale_field_map,refund_field_map):
    try:
        compare_result=[]
        # 遍历科传查询的订单主信息
        for kc_base_item in sale_order_base_info:
            order_type_flag = "正向单"
            # 获取订单的会员号
            mer_no=kc_base_item['XF_CLIENTCODE']
            store_code=kc_base_item['XF_STORECODE']
            sale_no=kc_base_item['XF_DOCNO']
            sale_amount=kc_base_item['XF_SELLINGAMOUNT']
            sale_refund_no=kc_base_item['XF_VOIDDOCNO']
            # 获取分表位置
            position = offline_table_num(mer_no)
            # 查询订单基础信息
            if sale_amount>=0:
                sale_baseinfo_sql = f"""
                                SELECT
                                    order_info.order_no AS 'order_no',
                                    order_info.user_id AS 'user_id',
                                    order_info.third_platform_code AS 'third_platform_code',
                                    order_info.third_order_no AS 'third_order_no',
                                    order_info.day_num AS 'day_num',
                                    order_info.order_state AS 'order_state',
                                    order_info.created AS 'created',
                                    order_info.bill_time AS 'bill_time',
                                    order_info.pay_time AS 'pay_time',
                                    order_info.complete_time AS 'complete_time',
                                    order_info.actual_pay_amount AS 'actual_pay_amount',
                                    order_info.actual_collect_amount AS 'actual_collect_amount',
                                    order_info.coupon_codes AS 'coupon_codes',
                                    order_info.serial_no AS 'serial_no',
                                    order_info.store_code AS 'store_code',
                                    order_cashier.pos_cashier_desk_no AS 'pos_cashier_desk_no',
                                    order_cashier.cashier AS 'cashier',
                                    order_cashier.cashier_name AS 'cashier_name',
                                    order_cashier.picker AS 'picker',
                                    order_cashier.picker_name AS 'picker_name',
                                    order_cashier.shift_id AS 'shift_id',
                                    order_cashier.shift_date AS 'shift_date',
                                    order_organization.store_code AS 'org_store_code',
                                    order_organization.store_name AS 'store_name',
                                    order_organization.company_code AS 'company_code',
                                    order_organization.company_name AS 'company_name',
                                    order_organization.store_direct_join_type AS 'store_direct_join_type',
                                    order_prescription.prescription_type AS 'prescription_type',
                                    order_prescription.prescription_no AS 'prescription_no',
                                    order_user.user_id AS 'u_user_id',
                                    order_user.user_name AS 'user_name',
                                    order_user.user_card_no AS 'user_card_no',
                                    order_user.user_mobile AS 'user_mobile' 
                                FROM
                                    offline_order_{position} order_info
                                    LEFT JOIN offline_order_cashier_desk_{position} order_cashier ON order_info.order_no = order_cashier.order_no
                                    LEFT JOIN offline_order_organization_{position} order_organization ON order_info.order_no = order_organization.order_no
                                    LEFT JOIN offline_order_prescription_{position} order_prescription ON order_info.order_no = order_prescription.order_no
                                    LEFT JOIN offline_order_user_{position} order_user ON order_info.order_no = order_user.order_no 
                                WHERE
                                    order_info.third_order_no = '{sale_no}'  AND order_info.store_code='{store_code}'
                            """
                order_field_map=order_sale_field_map
            else:
                order_type_flag = "逆向单"
                sale_baseinfo_sql=f"""
                    SELECT * FROM offline_refund_order_{position} WHERE third_refund_no='{sale_no}' AND store_code='{store_code}'
                """
                order_field_map=refund_field_map
            sale_baseinfo_result = db_yx_test_dsclound_offline(sale_baseinfo_sql)
            sale_baseinfo_data = sale_baseinfo_result['data']
            # 遍历订单查询结果信息
            for sale_base_item in sale_baseinfo_data:
                # 遍历订单比对字典信息
                for sale_map_item in order_field_map:
                    xy_key = sale_map_item['xy_key']
                    xy_param = sale_map_item['xy_param']
                    kc_key = sale_map_item['kc_key']
                    kc_param = sale_map_item['kc_param']
                    kc_default = sale_map_item['kc_default']
                    # 迁移key为order_info时，进入判断
                    if xy_key=="order_info":
                        # 需要科传参数值或科传默认值不为空时，进入判断
                        if kc_param != "" or kc_default != "":
                            if kc_param in kc_base_item.keys():
                                kc_value=kc_base_item[kc_param]
                                # 逆向单判断，取绝对值
                                if sale_amount < 0:
                                    if isinstance(kc_value,Decimal) or isinstance(kc_value,int):
                                        kc_value=abs(kc_value)
                            # 默认值若不为空，则取默认值
                            if kc_default != "":
                                kc_value = kc_default
                            order_value = sale_base_item[xy_param]
                            # 值比对
                            comapa = kc_value_compare(order_sale_enum_map, xy_param, kc_param, kc_value,
                                                      order_value)
                            if comapa:
                                pass
                            else:
                                order_info_com = [position,store_code,sale_no, xy_key, xy_param, kc_value, order_value, '', order_type_flag]
                                compare_result.append(order_info_com)
            # 未查询到订单信息
            if len(sale_baseinfo_data)==0:
                order_info_com = [position, store_code, sale_no, "", "", "", "", "订单未完成同步或分表位置错误，预期分期位置为{position}", order_type_flag]
                compare_result.append(order_info_com)
            # 查询到订单信息大于1
            if len(sale_baseinfo_data)>1:
                order_info_com = [position, store_code, sale_no, "", "", "", "", "订单迁移存在重复数量",
                                  order_type_flag]
                compare_result.append(order_info_com)
        return compare_result
    except Exception as e:
        raise e

"""
    科传订单详情信息比对
"""
@exception(logger)
def order_kc_orderdetail_compare(order_sale_enum_map,sale_order_detail_info,order_sale_field_map,refund_field_map):
    try:
        compare_result=[]
        for kc_detail_item in sale_order_detail_info:
            order_type_flag = "正向单"
            # 获取订单的会员号
            mer_no=kc_detail_item['XF_CLIENTCODE']
            store_code=kc_detail_item['XF_STORECODE']
            sale_no=kc_detail_item['XF_DOCNO']
            detail_num = kc_detail_item['XF_QTYSOLD']
            sale_refund_no = kc_detail_item['XF_VOIDDOCNO']
            # 商品行号
            kc_row_no=kc_detail_item['XF_TXSERIAL']
            # 获取分表位置
            position = offline_table_num(mer_no)
            # 获取订单号
            order_no=offline_kc_order_no(sale_no,store_code,position)
            if detail_num>=0:
                sale_detail_sql=f"""
                SELECT
                    order_detail.order_no AS 'order_no',
                    order_detail.order_detail_no AS 'order_detail_no',
                    order_detail.row_no AS 'row_no',
                    order_detail.platform_sku_id AS 'platform_sku_id',
                    order_detail.erp_code AS 'erp_code',
                    order_detail.erp_name AS 'erp_name',
                    order_detail.commodity_count AS 'commodity_count',
                    order_detail.STATUS AS 'status',
                    order_detail.gift_type AS 'gift_type',
                    order_detail.original_price AS 'original_price',
                    order_detail.price AS 'price',
                    order_detail.total_amount AS 'total_amount',
                    order_detail.discount_share AS 'discount_share',
                    order_detail.discount_amount AS 'discount_amount',
                    order_detail.bill_price AS 'bill_price',
                    order_detail.bill_amount AS 'bill_amount',
                    order_detail.commodity_cost_price AS 'commodity_cost_price',
                    detail_pick.make_no AS 'make_no',
                    detail_pick.count AS 'count',
                    detail_pick.make_no AS 'make_no' 
                FROM
                    offline_order_detail_{position} order_detail
                    LEFT JOIN offline_order_detail_pick_{position} detail_pick ON order_detail.order_detail_no = detail_pick.order_detail_no 
                WHERE
                    order_detail.order_no = '{order_no}' ;            
            """
                order_field_map = order_sale_field_map
            else:
                order_type_flag = "逆向单"
                sale_detail_sql=f"""
                    SELECT
                        detail.* 
                    FROM
                        offline_refund_order_{position} refund
                        LEFT JOIN offline_refund_order_detail_{position} detail ON refund.refund_no = detail.refund_no 
                    WHERE
                        refund.third_refund_no = '{sale_no}' 
                        AND refund.store_code = '{store_code}';
                """
                order_field_map = refund_field_map
            sale_detail_result=db_yx_test_dsclound_offline(sale_detail_sql)
            sale_detail_data=sale_detail_result['data']
            # 遍历订单详情查询结果信息
            for sale_detail_item in sale_detail_data:
                # 商品详情行号
                sale_row_no=sale_detail_item['row_no']
                # 如果两个行号相等，则进入判断
                if kc_row_no==sale_row_no:
                    # 遍历订单比对字典信息
                    for sale_map_item in order_field_map:
                        xy_key = sale_map_item['xy_key']
                        xy_param = sale_map_item['xy_param']
                        kc_key = sale_map_item['kc_key']
                        kc_param = sale_map_item['kc_param']
                        kc_default = sale_map_item['kc_default']
                        # 迁移key为order_detail时，进入判断
                        if xy_key == "order_detail":
                            # 需要科传参数值或科传默认值不为空时，进入判断
                            if kc_param != "" or kc_default != "":
                                if kc_param in kc_detail_item.keys():
                                    kc_value = kc_detail_item[kc_param]
                                    # 逆向单判断，取绝对值
                                    if detail_num < 0:
                                        if isinstance(kc_value, Decimal) or isinstance(kc_value, int):
                                            kc_value = abs(kc_value)
                                # 默认值若不为空，则取默认值
                                if kc_default != "":
                                    kc_value = kc_default
                                order_value = kc_detail_item[xy_param]
                                # 值比对
                                comapa = kc_value_compare(order_sale_enum_map, xy_param, kc_param, kc_value,
                                                          order_value)
                                if comapa:
                                    pass
                                else:
                                    order_detail_com = [position, store_code, sale_no, xy_key, xy_param, kc_value,
                                                      order_value, '', order_type_flag]
                                    compare_result.append(order_detail_com)
            # 未查询到订单详情信息
            if len(sale_detail_data) == 0:
                order_detail_com = [position, store_code, sale_no, "", "", "", "",
                                  "订单详情未完成同步或分表位置错误，预期分期位置为{position}", order_type_flag]
                compare_result.append(order_detail_com)
        return compare_result
    except Exception as e:
        raise e


"""
    科传订单支付信息比对
"""
@exception(logger)
def order_kc_orderpay_compare(order_sale_enum_map,sale_order_pay_info,order_sale_field_map,refund_field_map):
    try:
        compare_result = []
        for kc_pay_item in sale_order_pay_info:
            order_type_flag = "正向单"
            # 获取订单的会员号
            mer_no = kc_pay_item['XF_CLIENTCODE']
            store_code = kc_pay_item['XF_STORECODE']
            sale_no = kc_pay_item['XF_DOCNO']
            sale_refund_no = kc_pay_item['XF_VOIDDOCNO']
            sale_pay_amount=kc_pay_item['XF_PAYAMOUNT']
            # 支付类型
            kc_pay_type = kc_pay_item['XF_TENDERCODE']
            # 获取分表位置
            position = offline_table_num(mer_no)
            # 获取订单号
            order_no = offline_kc_order_no(sale_no, store_code, position)
            if sale_pay_amount>=0:
                sale_pay_sql = f"""
                    SELECT
                        order_info.order_no AS 'order_no',
                        order_pay.pay_type AS 'pay_type',
                        order_pay.pay_name AS 'pay_name',
                        order_pay.pay_amount AS 'pay_amount' 
                    FROM
                        offline_order_{position} order_info
                        INNER JOIN offline_order_pay_{position} order_pay ON order_info.order_no = order_pay.order_no 
                    WHERE
                        order_info.order_no = '{order_no}' ;            
                    """
                order_field_map = order_sale_field_map
            else:
                order_type_flag = "逆向单"
                sale_pay_sql=f"""
                    SELECT
                        pay.* 
                    FROM
                        offline_refund_order_{position} refund
                        LEFT JOIN offline_refund_order_pay_{position} pay ON refund.refund_no = pay.refund_no 
                    WHERE
                        refund.third_refund_no = '{sale_no}' 
                        AND refund.store_code = '{store_code}';
                """
                order_field_map = refund_field_map
            sale_pay_result = db_yx_test_dsclound_offline(sale_pay_sql)
            sale_pay_data = sale_pay_result['data']
            # 遍历订单详情查询结果信息
            for sale_pay_item in sale_pay_data:
                # 支付类型编码
                if sale_pay_amount>=0:
                    sale_pay_type = sale_pay_item['pay_type']
                else:
                    sale_pay_type = sale_pay_item['refund_pay_type']
                # 如果两个支付类型相等，则进入判断
                if kc_pay_type == sale_pay_type:
                    # 遍历订单比对字典信息
                    for sale_map_item in order_field_map:
                        xy_key = sale_map_item['xy_key']
                        xy_param = sale_map_item['xy_param']
                        kc_key = sale_map_item['kc_key']
                        kc_param = sale_map_item['kc_param']
                        kc_default = sale_map_item['kc_default']
                        # 迁移key为order_detail时，进入判断
                        if xy_key == "order_pay":
                            # 需要科传参数值或科传默认值不为空时，进入判断
                            if kc_param != "" or kc_default != "":
                                if kc_param in kc_pay_item.keys():
                                    kc_value = kc_pay_item[kc_param]
                                    # 逆向单判断，取绝对值
                                    if sale_pay_amount < 0:
                                        if isinstance(kc_value, Decimal) or isinstance(kc_value, int):
                                            kc_value = abs(kc_value)
                                # 默认值若不为空，则取默认值
                                if kc_default != "":
                                    kc_value = kc_default
                                order_value = sale_pay_item[xy_param]
                                # 值比对
                                comapa = kc_value_compare(order_sale_enum_map, xy_param, kc_param, kc_value,
                                                          order_value)
                                if comapa:
                                    pass
                                else:
                                    order_detail_com = [position, store_code, sale_no, xy_key, xy_param, kc_value,
                                                        order_value, '', order_type_flag]
                                    compare_result.append(order_detail_com)
            # 未查询到订单详情信息
            if len(sale_pay_data) == 0:
                order_detail_com = [position, store_code, sale_no, "", "", "", "",
                                    "订单支付未完成同步或分表位置错误，预期分期位置为{position}", order_type_flag]
                compare_result.append(order_detail_com)
        return compare_result
    except Exception as e:
        raise e


"""
    科传字段值比较
"""
@exception(logger)
def kc_value_compare(enum_map,xy_param,kc_param,kc_value,order_value):
    try:
        order_type=type(order_value)
        kc_type=type(kc_value)
        # 处理None
        if kc_value is None:
            kc_value=""
        if order_value is None:
            order_value=""
        # 判断是否存在枚举映射关系
        for enum_item in enum_map:
            enum_xy_param=enum_item['xy_param']
            enum_xy_map_value=enum_item['xy_map_value']
            enum_kc_param=enum_item['kc_param']
            enum_kc_map_value=enum_item['kc_map_value']
            if enum_xy_param==xy_param and enum_kc_param==kc_param:
                if str(enum_xy_map_value)==str(order_value):
                    order_value=enum_kc_map_value
        if isinstance(order_value, datetime.datetime):
            # 将字符串转换为datetime类型，注意这里包含了微秒部分
            dt = datetime.datetime.strptime(kc_value, '%Y-%m-%d %H:%M:%S')
            # 输出转换后的日期时间字符串，不包含微秒
            kc_value = dt.strftime('%Y-%m-%d %H:%M:%S')
            order_value=order_value.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(order_value,Decimal):
            # 将字符串转换为Decimal并保留6位小数
            kc_value = Decimal(kc_value).quantize(Decimal('1.000000'), rounding=ROUND_HALF_UP)
            kc_value=abs(kc_value)
        elif isinstance(order_value,int):
            order_value=str(order_value)
            kc_value = str(kc_value)
        else:
            kc_value=str(kc_value)
        if kc_value==order_value:
            return True
        else:
            return False
    except Exception as e:
        raise e



if __name__=="__main__":
    store_list=["H525","H520","H494","H364","HA48"]
    order_kc_compare(store_list)