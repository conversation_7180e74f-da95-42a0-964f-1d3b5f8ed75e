# -*- coding: utf-8 -*-
"""
@Time ： 2024/6/18 18:24
@Auth ： 逗逗的小老鼠
@File ：order_offline_xy.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json

from lib.get_log import exception, logger
import requests, datetime
import urllib3
from lib.db_conf_test import db_yx_test_dsclound_offline

# 禁用https警告信息
urllib3.disable_warnings()

"""
    根据订单号查询分表
"""
@exception(logger)
def offline_table_num(member_card):
    try:
        now = datetime.datetime.now()
        position = now.strftime("%Y%m")
        if member_card != "":
            # 根据会员卡查询userid
            member_url = f"http://hydee-middle-member.svc.k8s.test.hxyxt.com/member/info/getByCardNo?cardNo={member_card}"

            member_result = requests.post(url=member_url)
            if member_result.status_code == 200:
                if member_result.text == '':
                    pass
                else:
                    member_data = member_result.json()
                    if 'userId' in member_data.keys():
                        userId = member_data['userId']
                        if userId != "" and userId != None:
                            sharing_url = f"http://order-atom-service.svc.k8s.test.hxyxt.com/1.0/sharding-value"
                            sharing_body = {"userId": str(userId)}
                            sharing_result = requests.post(url=sharing_url, data=json.dumps(sharing_body),
                                                           headers={"Content-Type": "application/json;charset=UTF-8"})
                            if sharing_result.status_code == 200:
                                sharing_data = sharing_result.json()
                                code = sharing_data['code']
                                if code == "10000":
                                    position = sharing_data['data']['position']
                        else:
                            print(f"member_card:{member_card},member_url:{member_url},member_data:{member_data}")
                    else:
                        print(f"member_card:{member_card},member_url:{member_url},member_data:{member_data}")
        return position
    except Exception as e:
        raise e



"""
    根据销售号及门店查询系统生成的订单号
"""
@exception(logger)
def offline_kc_order_no(sale_no,store_code,position):
    try:
        order_no_sql=f"""
            SELECT
                order_no 
            FROM
                offline_order_{position} 
            WHERE
                store_code = '{store_code}' 
                AND third_order_no = '{sale_no}'
        """
        order_no_result=db_yx_test_dsclound_offline(order_no_sql)
        order_no_data=order_no_result['data']
        if len(order_no_data)>0:
            order_no=order_no_data[0]['order_no']
        # 查询数量存在异常
        else:
            order_no=len(order_no_data)
        return order_no
    except Exception as e:
        raise e



if __name__ == "__main__":
    member_card = "900043786744"
    table_num = offline_table_num(member_card)
    print(table_num)
