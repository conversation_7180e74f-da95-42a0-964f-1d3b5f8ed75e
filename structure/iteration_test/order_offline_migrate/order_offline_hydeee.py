# -*- coding: utf-8 -*-
"""
@Time ： 2024/6/18 17:50
@Auth ： 逗逗的小老鼠
@File ：order_offline_hydeee.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.mongdb_test_conf import mongdb_test_ds_aurora
from lib.get_log import exception,logger



"""
    海典线下单数据查询
"""
@exception(logger)
def order_hydee_info(order_no):
    "海典线下单数据查询"
    try:
        collect_name="ORDER_SYNC_TP_ORDER_OFFLINE_SYNC-HD"
        if order_no=="" or order_no is None:
            filter_dict=None
        else:
            filter_dict={"message":{'$regex': order_no}}
        hydee_result=mongdb_test_ds_aurora(collect_name,filter_dict)
        # print(hydee_result)
        return hydee_result
    except Exception as e:
        raise e


if __name__=="__main__":
    order_hydee_info()