# -*- coding: utf-8 -*-
"""
@Time ： 2024/6/18 18:02
@Auth ： 逗逗的小老鼠
@File ：order_offline_hydee_compare.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json
from lib.db_conf_test import db_yx_test_dsclound_offline
from lib.get_log import exception,logger
from structure.iteration_test.order_offline_migrate.order_offline_hydeee import order_hydee_info
from structure.iteration_test.order_offline_migrate.order_offline_xy import offline_table_num
from lib.deal_excel import read_excel,write_excel
from decimal import Decimal,ROUND_HALF_UP
import datetime

@exception(logger)
def order_hydee_compare(order_no):
    try:
        # 获取海典的订单数据
        order_hydee_result=order_hydee_info(order_no)
        # 获取正向单字段映射关系
        order_sale_field_map=read_excel("order_offline_hydee_sale.xlsx","sale_order")
        # 获取正向单字段枚举值映射
        order_sale_enum_map = read_excel("order_offline_hydee_sale.xlsx", "sale_enum_map")
        # 获取退款单字段映射关系
        refund_field_map=read_excel("order_offline_hydee_sale.xlsx","refund_order")
        compare_result=[]
        print(len(order_hydee_result))
        for order_hydee_item in order_hydee_result:
            order_hydee_message=order_hydee_item['message']
            order_hydee_data=json.loads(order_hydee_message)
            # 获取订单实付金额
            netsum=order_hydee_data['netsum']
            # 获取订单号
            saleno=order_hydee_data['saleno']
            # 获取分表位置
            membercardno=order_hydee_data['membercardno']
            position=offline_table_num(membercardno)
            # netsum>=0 判断为正向单
            if netsum>=0:
                # 查询订单基础信息
                sale_baseinfo_sql=f"""
                    SELECT
                        order_info.order_no AS 'order_no',
                        order_info.user_id AS 'user_id',
                        order_info.third_platform_code AS 'third_platform_code',
                        order_info.third_order_no AS 'third_order_no',
                        order_info.day_num AS 'day_num',
                        order_info.order_state AS 'order_state',
                        order_info.created AS 'created',
                        order_info.bill_time AS 'bill_time',
                        order_info.complete_time AS 'complete_time',
                        order_info.actual_pay_amount AS 'actual_pay_amount',
                        order_info.actual_collect_amount AS 'actual_collect_amount',
                        order_info.coupon_codes AS 'coupon_codes',
                        order_info.serial_no AS 'serial_no',
                        order_info.store_code AS 'store_code',
                        order_cashier.pos_cashier_desk_no AS 'pos_cashier_desk_no',
                        order_cashier.cashier AS 'cashier',
                        order_cashier.cashier_name AS 'cashier_name',
                        order_cashier.picker AS 'picker',
                        order_cashier.picker_name AS 'picker_name',
                        order_cashier.shift_id AS 'shift_id',
                        order_cashier.shift_date AS 'shift_date',
                        order_organization.store_code AS 'org_store_code',
                        order_organization.store_name AS 'store_name',
                        order_organization.company_code AS 'company_code',
                        order_organization.company_name AS 'company_name',
                        order_organization.store_direct_join_type AS 'store_direct_join_type',
                        order_prescription.prescription_type AS 'prescription_type',
                        order_prescription.prescription_no AS 'prescription_no',
                        order_user.user_id AS 'u_user_id',
                        order_user.user_name AS 'user_name',
                        order_user.user_card_no AS 'user_card_no',
                        order_user.user_mobile AS 'user_mobile' 
                    FROM
                        offline_order_{position} order_info
                        LEFT JOIN offline_order_cashier_desk_{position} order_cashier ON order_info.order_no = order_cashier.order_no
                        LEFT JOIN offline_order_organization_{position} order_organization ON order_info.order_no = order_organization.order_no
                        LEFT JOIN offline_order_prescription_{position} order_prescription ON order_info.order_no = order_prescription.order_no
                        LEFT JOIN offline_order_user_{position} order_user ON order_info.order_no = order_user.order_no 
                    WHERE
                        order_info.third_order_no = '{saleno}' 
                """
                sale_baseinfo_result=db_yx_test_dsclound_offline(sale_baseinfo_sql)
                sale_baseinfo_data=sale_baseinfo_result['data']
                for sale_baseinfo_item in sale_baseinfo_data:
                    order_no=sale_baseinfo_item['order_no']
                    # 订单详情及拣货信息
                    sale_order_detail_sql=f"""
                        SELECT
                            order_detail.order_no AS 'order_no',
                            order_detail.order_detail_no AS 'order_detail_no',
                            order_detail.row_no AS 'row_no',
                            order_detail.platform_sku_id AS 'platform_sku_id',
                            order_detail.erp_code AS 'erp_code',
                            order_detail.erp_name AS 'erp_name',
                            order_detail.commodity_count AS 'commodity_count',
                            order_detail.STATUS AS 'status',
                            order_detail.gift_type AS 'gift_type',
                            order_detail.original_price AS 'original_price',
                            order_detail.price AS 'price',
                            order_detail.total_amount AS 'total_amount',
                            order_detail.discount_share AS 'discount_share',
                            order_detail.discount_amount AS 'discount_amount',
                            order_detail.bill_price AS 'bill_price',
                            order_detail.bill_amount AS 'bill_amount',
                            order_detail.commodity_cost_price AS 'commodity_cost_price',
                            detail_pick.make_no AS 'make_no',
                            detail_pick.count AS 'count',
                            detail_pick.make_no AS 'make_no' 
                        FROM
                            offline_order_detail_{position} order_detail
                            LEFT JOIN offline_order_detail_pick_{position} detail_pick ON order_detail.order_detail_no = detail_pick.order_detail_no 
                        WHERE
                            order_detail.order_no = '{order_no}' 
                    """
                    sale_order_detail_result=db_yx_test_dsclound_offline(sale_order_detail_sql)
                    sale_order_detail_data=sale_order_detail_result['data']
                    # 订单医保信息查询
                    sale_order_med_sql=f"""
                        SELECT
	                        order_info.order_no AS 'order_no',
                            med_ins.serial_no AS 'serial_no',
                            med_ins.third_order_no AS 'third_order_no',
                            med_ins.invoice_no AS 'invoice_no',
                            med_ins.hospital_name AS 'hospital_name',
                            med_ins.NAME AS 'name',
                            med_ins.person_type AS 'person_type',
                            med_ins.person_type_name AS 'person_type_name',
                            med_ins.person_no AS 'person_no',
                            med_ins.prescription_no AS 'prescription_no',
                            med_ins.ic_card_no AS 'ic_card_no',
                            med_ins.acct_pay AS 'acct_pay',
                            med_ins.fund_pay AS 'fund_pay',
                            med_ins.cash_pay AS 'cash_pay',
                            med_ins.med_type AS 'med_type',
                            med_ins.med_type_name AS 'med_type_name',
                            med_ins.transaction_type AS 'transaction_type',
                            med_ins.transaction_code AS 'transaction_code',
                            med_ins.hospital_code AS 'hospital_code',
                            med_ins.setl_time AS 'setl_time',
                            med_ins.clearing_type AS 'clearing_type',
                            med_ins.clearing_type_name AS 'clearing_type_name',
                            med_ins.refund_time AS 'refund_time',
                            med_ins.is_refunded AS 'is_refunded',
                            med_ins.orig_serial_no AS 'orig_serial_no',
                            med_ins.bill_time AS 'bill_time' 
                        FROM
                            offline_order_{position} order_info
                            INNER JOIN offline_order_med_ins_settle_{position} med_ins ON order_info.order_no = med_ins.order_no 
                        WHERE
                            order_info.order_no = '{order_no}' 
                    
                    """
                    sale_order_med_result=db_yx_test_dsclound_offline(sale_order_med_sql)
                    sale_order_med_data=sale_order_med_result['data']
                    # 订单支付信息查询
                    sale_order_pay_sql=f"""
                        SELECT
                            order_info.order_no AS 'order_no',
                            order_pay.pay_type AS 'pay_type',
                            order_pay.pay_name AS 'pay_name',
                            order_pay.pay_amount AS 'pay_amount' 
                        FROM
                            offline_order_{position} order_info
                            INNER JOIN offline_order_pay_{position} order_pay ON order_info.order_no = order_pay.order_no 
                        WHERE
                            order_info.order_no = '{order_no}'
                    """
                    sale_order_pay_result=db_yx_test_dsclound_offline(sale_order_pay_sql)
                    sale_order_pay_data=sale_order_pay_result['data']
                    sale_order_info={}
                    sale_order_info['order_info']=sale_baseinfo_item
                    sale_order_info['order_detail']=sale_order_detail_data
                    sale_order_info['med_ins']=sale_order_med_data
                    sale_order_info['order_pay']=sale_order_pay_data
                    # 正向单比对
                    for sale_map_item in order_sale_field_map:
                        xy_key=sale_map_item['xy_key']
                        xy_param=sale_map_item['xy_param']
                        hydee_key=sale_map_item['hydee_key']
                        hydee_param=sale_map_item['hydee_param']
                        hydee_default=sale_map_item['hydee_default']

                        # 订单详情数据比对
                        if xy_key=='order_detail':
                            hydee_detail= order_hydee_data['saleDetails']
                            if len(hydee_detail)==len(sale_order_detail_data):
                                for hydee_detail_item in hydee_detail:
                                    hydee_rowno=hydee_detail_item['rowno']
                                    for order_detail_item in sale_order_detail_data:
                                        order_row_no=order_detail_item['row_no']
                                        if str(hydee_rowno)==str(order_row_no):
                                            if hydee_param != "" or hydee_default != "":
                                                if hydee_param != '':
                                                    hydee_value = hydee_detail_item[hydee_param]
                                                # 默认值若不为空，则取默认值
                                                if hydee_default != "":
                                                    hydee_value = hydee_default
                                                order_value = order_detail_item[xy_param]
                                                comapa = hy_value_compare(order_sale_enum_map,xy_param,hydee_param,hydee_value, order_value)
                                                if comapa:
                                                    pass
                                                else:
                                                    order_detail_com=[saleno,xy_key,xy_param,hydee_value,order_value,hydee_rowno,"正向单"]
                                                    compare_result.append(order_detail_com)
                            else:
                                order_detail_com = [saleno, "", "", len(hydee_detail), len(sale_order_detail_data), "商品详情数量不一致","正向单"]
                                compare_result.append(order_detail_com)
                        # 订单支付信息比对
                        elif xy_key=='order_pay':
                            hydee_pay=order_hydee_data['salePayDetails']
                            if len(hydee_pay)==len(sale_order_pay_data):
                                for hydee_pay_item in hydee_pay:
                                    hydee_paytype=hydee_pay_item['paytype']
                                    for order_pay_item in sale_order_pay_data:
                                        order_pay_type=order_pay_item['pay_type']
                                        if str(hydee_paytype)==str(order_pay_type):
                                            if hydee_param != "" or hydee_default != "":
                                                if hydee_param != '':
                                                    hydee_value = hydee_pay_item[hydee_param]
                                                # 默认值若不为空，则取默认值
                                                if hydee_default != "":
                                                    hydee_value = hydee_default
                                                order_value = order_pay_item[xy_param]
                                                comapa = hy_value_compare(order_sale_enum_map,xy_param,hydee_param,hydee_value, order_value)
                                                if comapa:
                                                    pass
                                                else:
                                                    order_pay_com = [saleno, xy_key, xy_param, hydee_value, order_value,hydee_paytype,"正向单"]
                                                    compare_result.append(order_pay_com)
                            else:
                                order_detail_com = [saleno, "", "", len(hydee_pay), len(sale_order_pay_data),"支付信息数量不一致", "正向单"]
                                compare_result.append(order_detail_com)
                        elif xy_key=='med_ins':
                            if 'medicalInfo' in order_hydee_data.keys():
                                hydee_med=order_hydee_data['medicalInfo']
                                if hydee_med=="":
                                    if len(sale_order_med_data)>0:
                                        order_info_com = [saleno, "", "", "", "","该订单不应存在医保信息"]
                                        compare_result.append(order_info_com)
                                else:
                                    if len(sale_order_med_data) == 0:
                                        order_info_com = [saleno, "", "", "", "", "该订单缺失医保信息"]
                                        compare_result.append(order_info_com)
                                    if len(sale_order_med_data) > 1:
                                        order_info_com = [saleno, "", "", "", "", "该订单存在多条医保信息"]
                                        compare_result.append(order_info_com)
                                    if len(sale_order_med_data) == 1:
                                        if hydee_param != "" or hydee_default != "":
                                            if hydee_param != "":
                                                hydee_value = hydee_med[hydee_param]
                                            # 默认值若不为空，则取默认值
                                            if hydee_default != "":
                                                hydee_value = hydee_default
                                            order_value = sale_order_med_data[0][xy_param]
                                            comapa = hy_value_compare(order_sale_enum_map,xy_param,hydee_param,hydee_value, order_value)
                                            if comapa:
                                                pass
                                            else:
                                                order_info_com = [saleno, xy_key, xy_param, hydee_value, order_value, '',"正向单"]
                                                compare_result.append(order_info_com)
                        else:
                            if hydee_param != "" or hydee_default != "":
                                if hydee_key == "saleDetails" and hydee_key != "":
                                    hydee_value = order_hydee_data[hydee_key][0][hydee_param]
                                else:
                                    if hydee_param in order_hydee_data.keys():
                                        if hydee_param != "":
                                            hydee_value = order_hydee_data[hydee_param]
                                # 默认值若不为空，则取默认值
                                if hydee_default != "":
                                    hydee_value = hydee_default
                                order_value = sale_baseinfo_item[xy_param]
                                comapa = hy_value_compare(order_sale_enum_map, xy_param, hydee_param, hydee_value,
                                                          order_value)
                                if comapa:
                                    pass
                                else:
                                    order_info_com = [saleno, xy_key, xy_param, hydee_value, order_value,'',"正向单"]
                                    compare_result.append(order_info_com)


                if len(sale_baseinfo_data)==0:
                    order_info_com = [saleno, "", "", "", "",f"订单未完成同步或分表位置错误，预期分期位置为{position}","正向单"]
                    compare_result.append(order_info_com)
                if len(sale_baseinfo_data)>1:
                    order_info_com = [saleno, "", "", "", "","订单存在多条迁移数据","正向单"]
                    compare_result.append(order_info_com)
            # 退款单
            else:
                # 查询退款单基础信息
                refund_baseinfo_sql=f"""SELECT * FROM offline_refund_order_{position} WHERE third_refund_no='{saleno}';"""
                refund_baseinfo_result=db_yx_test_dsclound_offline(refund_baseinfo_sql)
                refund_baseinfo_data=refund_baseinfo_result['data']
                for refund_baseinfo_item in refund_baseinfo_data:
                    refund_no=refund_baseinfo_item['refund_no']
                    # 查询退款单详情信息
                    refund_datail_sql=f"""SELECT * FROM offline_refund_order_detail_{position} WHERE refund_no='{refund_no}';"""
                    refund_datail_result=db_yx_test_dsclound_offline(refund_datail_sql)
                    refund_datail_data=refund_datail_result['data']
                    # 查询退款单医保信息
                    refund_med_sql=f"""SELECT * FROM offline_refund_order_med_ins_settle_{position} WHERE refund_no='{refund_no}';"""
                    refund_med_result=db_yx_test_dsclound_offline(refund_med_sql)
                    refund_med_data=refund_med_result['data']
                    # 查询退款单支付信息
                    refund_pay_sql=f"""SELECT * FROM offline_refund_order_pay_{position} WHERE refund_no='{refund_no}';"""
                    refund_pay_result=db_yx_test_dsclound_offline(refund_pay_sql)
                    refund_pay_data=refund_pay_result['data']
                    for refund_field_item in refund_field_map:
                        xy_key = refund_field_item['xy_key']
                        xy_param = refund_field_item['xy_param']
                        hydee_key = refund_field_item['hydee_key']
                        hydee_param = refund_field_item['hydee_param']
                        hydee_default = refund_field_item['hydee_default']

                        # 订单详情数据比对
                        if xy_key == 'refund_detail':
                            hydee_detail = order_hydee_data['saleDetails']
                            if len(hydee_detail) == len(refund_datail_data):
                                for hydee_detail_item in hydee_detail:
                                    hydee_rowno = hydee_detail_item['rowno']
                                    for order_detail_item in refund_datail_data:
                                        order_row_no = order_detail_item['row_no']
                                        if str(hydee_rowno) == str(order_row_no):
                                            if hydee_param != "" or hydee_default != "":
                                                if hydee_param != '':
                                                    hydee_value = hydee_detail_item[hydee_param]
                                                # 默认值若不为空，则取默认值
                                                if hydee_default !="":
                                                    hydee_value=hydee_default
                                                order_value = order_detail_item[xy_param]
                                                comapa = hy_value_compare(order_sale_enum_map, xy_param, hydee_param,
                                                                          hydee_value, order_value)
                                                if comapa:
                                                    pass
                                                else:
                                                    order_detail_com = [saleno, xy_key, xy_param, hydee_value,
                                                                        order_value, hydee_rowno,"退款单"]
                                                    compare_result.append(order_detail_com)
                            else:
                                order_detail_com = [saleno, "", "", len(hydee_detail), len(refund_datail_data),
                                                    "商品详情数量不一致", "退款单"]
                                compare_result.append(order_detail_com)
                        # 订单支付信息比对
                        elif xy_key == 'refund_pay':
                            hydee_pay = order_hydee_data['salePayDetails']
                            if len(hydee_pay) == len(refund_pay_data):
                                for hydee_pay_item in hydee_pay:
                                    hydee_paytype = hydee_pay_item['paytype']
                                    for order_pay_item in refund_pay_data:
                                        order_pay_type = order_pay_item['refund_pay_type']
                                        if str(hydee_paytype) == str(order_pay_type):
                                            if hydee_param != "" or hydee_default != "":
                                                if hydee_param != '':
                                                    hydee_value = hydee_pay_item[hydee_param]
                                                # 默认值若不为空，则取默认值
                                                if hydee_default != "":
                                                    hydee_value = hydee_default
                                                order_value = order_pay_item[xy_param]
                                                comapa = hy_value_compare(order_sale_enum_map, xy_param, hydee_param,hydee_value, order_value)
                                                if comapa:
                                                    pass
                                                else:
                                                    order_pay_com = [saleno, xy_key, xy_param, hydee_value, order_value,
                                                                     hydee_paytype, "退款单"]
                                                    compare_result.append(order_pay_com)
                            else:
                                order_detail_com = [saleno, "", "", len(hydee_pay), len(refund_pay_data),
                                                    "支付信息数量不一致", "退款单"]
                                compare_result.append(order_detail_com)
                        # 订单医保信息比对
                        elif xy_key == 'refund_med_ins':
                            if 'medicalInfo' in order_hydee_data.keys():
                                hydee_med = order_hydee_data['medicalInfo']
                                if hydee_med == "":
                                    if len(refund_med_data) > 0:
                                        order_info_com = [saleno, "", "", "", "", "该订单不应存在医保信息", "退款单"]
                                        compare_result.append(order_info_com)
                                else:
                                    if len(refund_med_data) == 0:
                                        order_info_com = [saleno, "", "", "", "", "该订单缺失医保信息", "退款单"]
                                        compare_result.append(order_info_com)
                                    if len(refund_med_data) > 1:
                                        order_info_com = [saleno, "", "", "", "", "该订单存在多条医保信息", "退款单"]
                                        compare_result.append(order_info_com)
                                    if len(refund_med_data) == 1:
                                        if hydee_param != "" or hydee_default != "":
                                            if hydee_param != "":
                                                hydee_value = hydee_med[hydee_param]
                                            # 默认值若不为空，则取默认值
                                            if hydee_default != "":
                                                hydee_value = hydee_default
                                            order_value = refund_med_data[0][xy_param]
                                            comapa = hy_value_compare(order_sale_enum_map, xy_param, hydee_param,hydee_value, order_value)
                                            if comapa:
                                                pass
                                            else:
                                                order_info_com = [saleno, xy_key, xy_param, hydee_value, order_value, '',
                                                                  "退款单"]
                                                compare_result.append(order_info_com)
                        else:
                            if hydee_param !="" or hydee_default != "":
                                if hydee_key == "saleDetails" and hydee_key != "":
                                    hydee_value = order_hydee_data[hydee_key][0][hydee_param]
                                else:
                                    if hydee_param in order_hydee_data.keys():
                                        if hydee_param !="":
                                            hydee_value = order_hydee_data[hydee_param]
                                # 默认值若不为空，则取默认值
                                if hydee_default != "":
                                    hydee_value = hydee_default
                                order_value = refund_baseinfo_item[xy_param]
                                comapa = hy_value_compare(order_sale_enum_map, xy_param, hydee_param, hydee_value,
                                                          order_value)
                                if comapa:
                                    pass
                                else:
                                    order_info_com = [saleno, xy_key, xy_param, hydee_value, order_value, '', "退款单"]
                                    compare_result.append(order_info_com)


                if len(refund_baseinfo_data)==0:
                    order_info_com = [saleno, "", "", "", "","订单未完成同步或分表位置错误","退款单"]
                    compare_result.append(order_info_com)

        print(compare_result)
        now_time = datetime.datetime.now()
        now_date = datetime.datetime.strftime(now_time, "%Y-%m-%d %H&%M&%S")
        excel_name = "offline_order_hydee_test.xlsx"
        sheet_name = f"路由比对结果_{now_date}"
        column_list = ["订单号", "DB类名", "DB字段", "原始值", "迁移值", "备注","订单类型"]
        write_excel(excel_name, sheet_name, column_list, compare_result)
        return compare_result
    except Exception as e:
        raise e
"""
    海典字段值比较
"""
@exception(logger)
def hy_value_compare(enum_map,xy_param,hydee_param,hydee_value,order_value):
    try:
        order_type=type(order_value)
        hydee_type=type(hydee_value)
        # 处理None
        if hydee_value is None:
            hydee_value=""
        if order_value is None:
            order_value=""
        # 判断是否存在枚举映射关系
        for enum_item in enum_map:
            enum_xy_param=enum_item['xy_param']
            enum_xy_map_value=enum_item['xy_map_value']
            enum_hydee_param=enum_item['hydee_param']
            enum_hydee_map_value=enum_item['hydee_map_value']
            if enum_xy_param==xy_param and enum_hydee_param==hydee_param:
                if str(enum_xy_map_value)==str(order_value):
                    order_value=enum_hydee_map_value

        if isinstance(order_value, datetime.datetime):
            # 将字符串转换为datetime类型，注意这里包含了微秒部分
            dt = datetime.datetime.strptime(hydee_value, '%Y-%m-%d %H:%M:%S.%f')
            # 输出转换后的日期时间字符串，不包含微秒
            hydee_value = dt.strftime('%Y-%m-%d %H:%M:%S')
            order_value=order_value.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(order_value,Decimal):
            # 将字符串转换为Decimal并保留6位小数
            hydee_value = Decimal(hydee_value).quantize(Decimal('1.000000'), rounding=ROUND_HALF_UP)
            hydee_value=abs(hydee_value)
        elif isinstance(order_value,int):
            order_value=str(order_value)
            hydee_value = str(hydee_value)
        else:
            hydee_value=str(hydee_value)
        if hydee_value==order_value:
            return True
        else:
            return False

    except Exception as e:
        raise e


if __name__=="__main__":
    order_no="1124030500015255"
    com_hydee=order_hydee_compare('')