# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/28 15:07
@Auth ： 逗逗的小老鼠
@File ：get_offline_data.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from lib.deal_db_mysql import db_mysql_connect

class OfflineData:
    def __init__(self):
        self.order_no=""

    @exception(logger)
    def get_offline_order_migration_data(self):
        """获取迁移后的订单数据 - 查询所有分表(0-255)和指定月份的数据"""
        try:
            summary = {}
            # 生成0-255的表索引
            table_indices = list(range(0, 256))

            # 指定的月份条件
            target_months = ['202404', '202405', '202406', '202407']
            table_indices.extend(target_months)

            offline_order_sql=[]
            offline_refund_order_sql=[]

            # 遍历每个表索引
            for table_index in table_indices:
                table_name = f"offline_order_{table_index}"
                # 构建基础SQL
                base_sql = f"SELECT store_code,COUNT(*) AS 'store_count' FROM {table_name}  GROUP BY  store_code"
                offline_order_sql.append(base_sql)

            # 遍历每个表索引
            for table_index in table_indices:
                table_name = f"offline_refund_order_{table_index}"
                # 构建基础SQL
                base_sql = f"SELECT store_code,COUNT(*) AS 'store_count' FROM {table_name}  GROUP BY  store_code"
                offline_refund_order_sql.append(base_sql)

            for order_sql in offline_order_sql:
                order_result = db_mysql_connect(order_sql)
                order_data=order_result['data']
                if order_data:
                    for row in order_result:
                        store_code = row['store_code']
                        store_count = row['store_count']
                        if store_code in summary:
                            if "offline_order" in summary[store_code]:
                                summary[store_code]["offline_order"] += store_count
                            else:
                                summary[store_code]["offline_order"] = store_count
                        else:
                            summary[store_code] = {"offline_order": store_count}
            # 处理退款单据
            for refund_sql in offline_refund_order_sql:
                refund_result = db_mysql_connect(refund_sql)
                refund_data=refund_result['data']
                if refund_data:
                    for row in refund_result:
                        store_code = row['store_code']
                        store_count = row['store_count']
                        if store_code in summary:
                            if "offline_refund_order" in summary[store_code]:
                                summary[store_code]["offline_refund_order"] += store_count
                            else:
                                summary[store_code]["offline_refund_order"] = store_count
                        else:
                            summary[store_code] = {"offline_refund_order": store_count}
            return summary

        except Exception as e:
            logger.error(f"获取离线订单迁移数据时出错: {str(e)}")
            raise e

