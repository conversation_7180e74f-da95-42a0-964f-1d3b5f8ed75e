# -*- coding: utf-8 -*-
"""
@Time ： 2024/10/31 15:46
@Auth ： 逗逗的小老鼠
@File ：im_message.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import requests
import json
import time
from lib.get_log import logger, exception
import threading
import urllib3
# 禁用https警告信息
urllib3.disable_warnings()


@exception(logger)
def mocka_message(num):
    "生成发送消息"
    try:
        success_num = 0
        fail_num = 0
        url = "https://test-api.hxyxt.com/zeus/third-platform/callback/ics/reception_msg/500001/48775e71e85c48a8b971ac8b3cff1dcc"
        headers = {"Content-Type": "application/json;charset=UTF-8"}
        # 生成open_user_id
        open_user_id = 12844050000 + num
        for i in range(100):
            time.sleep(1)
            timestamp = int(time.time())
            msg_content=f"{open_user_id}_{i}"
            push_content = {
                "app_poi_code": "123614_21615812",
                "cts": timestamp,
                "msg_source": 2,
                "group_id": 0,
                "msg_content": msg_content,
                "msg_type": 1,
                "open_user_id": open_user_id,
                "msg_id": 1546025498873597952,
                "app_id": 123614,
                "order_id": 0
            }
            data={"sig":"b332283ff209c1c0dc4e1bfb22657a4a","biz_type":1,"push_content":json.dumps(push_content),"app_id":"123614","timestamp":timestamp}
            body = {"channelType": 27,
                    "data": json.dumps(data)}
            response = requests.post(url=url, data=json.dumps(body), headers=headers, verify=False)
            response_text = json.loads(response.text)
            if response_text["data"] == "ok":
                success_num=success_num+1
            else:
                fail_num=fail_num+1
        result={"success_num":success_num,"fail_num":fail_num}
        print(result)
        return result
    except Exception as e:
        raise e


def main():
    """主程序，创建并启动线程"""
    threads = []
    for i in range(30):  # 创建并启动3个线程
        t = threading.Thread(target=mocka_message, args=(i,))
        threads.append(t)
        t.start()

    for t in threads:
        t.join()  # 等待所有线程完成

if __name__ == "__main__":
    # mocka_message(2)
    main()
