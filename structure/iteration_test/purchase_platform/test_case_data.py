# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/9 13:47
@Auth ： 逗逗的小老鼠
@File ：test_case_data.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception, logger
from structure.iteration_test.purchase_platform.get_api_data import ApiData
from lib.deal_excel import read_excel, write_excel
import random
from typing import List, Dict, Any, Union

"""
    测试用例数据
"""


@exception(logger)
class TestCaseData:
    def __init__(self):
        self.api_data = ApiData()

    def test_case_data_create(self):
        "测试用例数据"
        try:
            pass
        except Exception as e:
            raise e

    def test_case_data(self):
        "测试用例数据"
        try:
            case_file = "ProcurementPlatform_Automated_TestCase.xlsx"
            sheet_name = "import_procurement_data"
            case_data = read_excel(case_file, sheet_name)
            case_list = []
            case_comment = {}
            for index, data in enumerate(case_data):
                if index == 0:
                    case_comment = data
                else:
                    case_list.append(data)
            return case_list, case_comment
        except Exception as e:
            raise e

    def test_case_value_create(self, **kwargs) -> list[dict[str, list[dict[str, Any]] | None | Any]] | None:
        "生成测试用例中的值"
        try:
            test_case_no = kwargs.get("test_case_no")
            test_case_list,case_comment = self.test_case_data()
            case_question_data_list = []
            for case_item in test_case_list:
                # 获取测试用例编号
                case_no = case_item.get("case_no")
                logger.info(f"测试用例编号：{case_no}")
                if test_case_no:
                    if case_no == test_case_no:
                        case_question_data = self.test_case_quest_data(case_item, **kwargs)
                        case_question_data["case_no"]=case_no
                        case_question_data_list.append(case_question_data)
                else:
                    case_question_data = self.test_case_quest_data(case_item, **kwargs)
                    case_question_data["case_no"] = case_no
                    case_question_data_list.append(case_question_data)
            return case_question_data_list
            # 获取测试用例中的值
        except Exception as e:
            logger.error(f"生成测试用例中的值失败：{e}")

    def test_case_quest_data(self, case_item: Dict, **kwargs):
        "测试用例请求数据"
        try:
            test_store_data = self.test_case_data_create_store(case_item, **kwargs)
            case_data = []
            for store_item in test_store_data:
                store_code = store_item.get("store_code")
                store_status = store_item.get("store_status")
                store_good_type = []
                store_good_status = "normal"
                # 门店正常的情况下，获取商品数据
                if store_status == "normal":
                    # 获取正常的商品数据
                    store_goods_data = self.test_case_data_create_goods(case_item, store_code, **kwargs)
                    for goods_item in store_goods_data:
                        goods_erp_code = goods_item.get("erpCode")
                        goods_qty = goods_item.get("qty")
                        goods_good_status = goods_item.get("good_status")
                        goods_good_type = goods_item.get("good_type")
                        if goods_good_type not in store_good_type:
                            store_good_type.append(goods_good_type)
                        if goods_good_status != "normal":
                            store_good_status = "error"
                        case_data_item = {"erpCode": goods_erp_code, "organizationCode": store_code, "qty": goods_qty,
                                          "goods_data": goods_item, "store_status": store_status}
                        case_data.append(case_data_item)
                # 门店异常的情况，写入固定的商品数据
                else:
                    case_data_item = {"erpCode": "100010", "organizationCode": store_code, "qty": "1", "goods_data": {
                        "erpCode": "100010",
                        "good_type": "nomal",
                        "qty": 1,
                        "purchasePrice": 1.00,
                        "good_status": "normal"
                    },
                                      "store_status": store_status}
                    case_data.append(case_data_item)
            test_payment_data = self.test_case_data_create_payment(case_item, **kwargs)
            case_question_data = {
                "autoConfirm": test_payment_data.get("autoConfirm"),
                "overduePaymentTime": test_payment_data.get('overtime_days'),
                "isAutoPayment": test_payment_data.get('is_pay_automatic'),
                "items": case_data,
                "payment_status": test_payment_data.get('payment_status')
            }
            return case_question_data
        except Exception as e:
            raise e

    def test_case_data_create_store(self, test_case: Dict, **kwargs) -> List:
        "生成测试用例中的店铺数据"
        try:
            store_list=self.api_data.get_store_list(**kwargs)
            # 获取用例编号
            case_no = test_case.get("case_no", "")
            # 获取正常的店铺数量
            store_effective_num = test_case.get("store_effective_num", 0)
            # 获取无权限店铺数量
            store_error_permission_num = test_case.get("store_error_permission_num", 0)
            # 获取不存在门店数量
            store_error_exist_num = test_case.get("store_error_exist_num", 0)
            # 获取非加盟门店数量
            store_non_franchised_num = test_case.get("store_non_franchised_num", 0)
            # 测试门店列表
            case_data_store_list = []
            if store_effective_num > 0:
                # 获取正常的店铺数据
                store_effective_data = store_list.get("store_effective_list", [])
                effective_num = random.randint(1, 3)
                # 选择可以重复的店铺数据
                effective_data = self.random_select_with_repeat(store_effective_data, effective_num, is_repeat=1)
                for item in effective_data:
                    store_data = {"store_code": item, "store_status": "normal"}
                    case_data_store_list.append(store_data)
                logger.info(f"测试用例编号：{case_no},有效门店列表：{effective_data}")
            if store_error_permission_num > 0:
                # 获取无权限的店铺数据
                store_error_permission_data = store_list.get("store_error_permission_list", [])
                # 选择1个无权限的店铺数据
                error_permission_data = self.random_select_with_repeat(store_error_permission_data, 1, is_repeat=0)
                for item in error_permission_data:
                    store_data = {"store_code": item, "store_status": "error_permission"}
                    case_data_store_list.append(store_data)
            if store_error_exist_num > 0:
                # 获取不存在的店铺数据
                store_error_exist_data = store_list.get("store_error_exist_list", [])
                # 选择1个不存在的店铺数据
                error_exist_data = self.random_select_with_repeat(store_error_exist_data, 1, is_repeat=0)
                for item in error_exist_data:
                    store_data = {"store_code": item, "store_status": "error_exist"}
                    case_data_store_list.append(store_data)
            if store_non_franchised_num > 0:
                # 获取非加盟的店铺数据
                store_non_franchised_data = store_list.get("store_non_franchised_list", [])
                # 选择1个非加盟的店铺数据
                non_franchised_data = self.random_select_with_repeat(store_non_franchised_data, 1, is_repeat=0)
                for item in non_franchised_data:
                    store_data = {"store_code": item, "store_status": "non_franchised"}
                    case_data_store_list.append(store_data)
                logger.info(f"测试用例编号：{case_no},非加盟门店列表：{non_franchised_data}")
            return case_data_store_list
        except Exception as e:
            logger.error(f"生成测试用例中的店铺数据失败：{e}")
            raise

    def test_case_data_create_goods(self, test_case: Dict, store_code: str, **kwargs) -> List:
        "生成测试用例中的商品数据"
        try:
            goods_data = self.api_data.get_commodity_list(store_code, **kwargs)
            # 普通商品列表
            normal_goods_erp_code_list = goods_data.get("normal_goods_erp_code_list", [])
            # 冷藏商品列表
            cold_goods_erp_code_list = goods_data.get("cold_goods_erp_code_list", [])
            # 门店不存在的商品列表
            goods_not_exist_erp_code_list = goods_data.get("goods_not_exist_erp_code_list", [])
            # 不在门店经营范围的商品列表
            goods_not_in_scope_erp_code_list = goods_data.get("goods_not_in_scope_erp_code_list", [])
            # 采购价为空的商品列表
            purchase_price_null_erp_code_list = goods_data.get("purchase_price_null_erp_code_list", [])
            # 商品不可采列表
            goods_purchase_erp_code_list = goods_data.get("goods_purchase_erp_code_list", [])
            # 办公品列表
            office_goods_erp_code_list = goods_data.get("office_goods_erp_code_list", [])
            # 未上架商品列表
            unshelved_erp_code_list = goods_data.get("unshelved_erp_code_list", [])
            # 获取用例编号
            case_no = test_case.get("case_no", "")
            # 获取商品分类
            goods_type = test_case.get("goods_type", "普通")
            # 获取正常的商品数量
            good_effiective_num = test_case.get("good_effiective_num", 0)
            # 获取超过最大购买限制数量
            good_limit_maximum_num = test_case.get("good_limit_maximum_num", 0)
            # 获取不符合购买单位数量
            good_limit_unit_num = test_case.get("good_limit_unit_num", 0)
            # 获取超过最大库存数量
            good_limit_stock_num = test_case.get("good_limit_stock_num", 0)
            # 获取商品不存在数量
            goods_error_exist_num = test_case.get("goods_error_exist_num", 0)
            # 获取商品购进价为空数量
            good_error_price_num = test_case.get("good_error_price_num", 0)
            # 获取商品不可采数量
            good_unbuyable_num = test_case.get("good_unbuyable_num", 0)
            # 获取未上架商品数量
            good_unshelved_num = test_case.get("good_unshelved_num", 0)
            # 获取不在经营范围的商品数量
            goods_not_in_scope_num = test_case.get("goods_not_in_scope_num", 0)

            # 定义一个空列表，用于存储选择的erp_code
            erp_code_list = []
            if good_effiective_num > 0:
                if "办公" in goods_type:
                    effiective_num = random.randint(1, 5)
                    office_erp_code_list = self.random_select_with_repeat(office_goods_erp_code_list, effiective_num,
                                                                          is_repeat=1)
                    erp_code_list.extend(
                        [{"erp_code": erp_code, "type": "office"} for erp_code in office_erp_code_list])
                if "冷链" in goods_type:
                    effiective_num = random.randint(1, 5)
                    cold_erp_code_list = self.random_select_with_repeat(cold_goods_erp_code_list, effiective_num,
                                                                        is_repeat=1)
                    erp_code_list.extend([{"erp_code": erp_code, "type": "cold"} for erp_code in cold_erp_code_list])
                if "普通" in goods_type:
                    effiective_num = random.randint(1, 5)
                    normal_erp_code_list = self.random_select_with_repeat(normal_goods_erp_code_list, effiective_num,
                                                                          is_repeat=1)
                    erp_code_list.extend([{"erp_code": erp_code, "type": "nomal"} for erp_code in normal_erp_code_list])

            if good_limit_maximum_num > 0:
                good_limit_maximum_num = random.randint(1, 5)
            if good_limit_unit_num > 0:
                good_limit_unit_num = random.randint(1, 5)
            if good_limit_stock_num > 0:
                good_limit_stock_num = random.randint(1, 5)

            actual_limit_maximum_num = 0
            actual_limit_unit_num = 0
            actual_limit_stock_num = 0
            purchase_list = []
            for item in erp_code_list:
                good_status = "normal"
                qty = 0
                erp_code = item.get("erp_code", "")
                goods_info = self.api_data.get_commodity_info(store_code, erp_code)
                # 采购数量上限
                purchaseUpperLimit = goods_info.get("purchaseUpperLimit", 0) or 0
                # 采购价格
                purchasePrice = goods_info.get("purchasePrice", 0) or 0
                # 仓库库存
                wareStock = goods_info.get("wareStock", 0) or 0
                # 采购单位限制
                purchaseBundleSize = goods_info.get("purchaseBundleSize", 0) or 0
                # 生产厂商
                manufacture = goods_info.get("manufacture", "") or ""
                # 商品名称
                erpName = goods_info.get("name", "") or ""
                # 商品规格
                commoditySpec = goods_info.get("specValue", "") or ""
                if actual_limit_maximum_num < good_limit_maximum_num:
                    if purchaseUpperLimit > 0:
                        qty = purchaseUpperLimit + 1
                        actual_limit_maximum_num += 1
                        # 商品超出最大采购数量
                        good_status = "err_UpperLimit"
                if actual_limit_unit_num < good_limit_unit_num:
                    if purchaseBundleSize > 0:
                        qty = round(purchaseBundleSize * 2 / 3, 2)
                        actual_limit_unit_num += 1
                        # 商品采购单位不匹配
                        good_status = "err_BundleSize"
                if actual_limit_stock_num < good_limit_stock_num:
                    if wareStock > 0:
                        qty = wareStock + 1
                        actual_limit_stock_num += 1
                        # 商品采购数量超出库存
                        good_status = "err_wareStock"
                if qty == 0:
                    available_gap = min(wareStock, purchaseUpperLimit)
                    if purchaseBundleSize > 0:
                        multiple = (available_gap // purchaseBundleSize)
                        qty = (multiple // 2) * purchaseBundleSize
                    else:
                        qty = 2
                purchase_item = {
                    "erpCode": erp_code,
                    "good_type": item.get("type", "nomal"),
                    "qty": qty or 1,
                    "purchasePrice": purchasePrice,
                    "good_status": good_status,
                    "manufacture": manufacture,
                    "erpName": erpName,
                    "commoditySpec": commoditySpec
                }
                purchase_list.append(purchase_item)

            # 商品不存在
            if goods_error_exist_num > 0:
                goods_error_exist_num = random.randint(1, 5)
                goods_error_exist_erp_code_list = self.random_select_with_repeat(goods_not_exist_erp_code_list,
                                                                                 goods_error_exist_num, is_repeat=0)
                for erp_code in goods_error_exist_erp_code_list:
                    purchase_item = {
                        "erpCode": erp_code,
                        "good_type": "nomal",
                        "qty": 1,
                        "purchasePrice": 1,
                        "manufacture": "",
                        "erpName": "",
                        "commoditySpec": "",
                        "good_status": "err_exist"
                    }
                    purchase_list.append(purchase_item)
            # 商品购进价为空
            if good_error_price_num > 0:
                good_error_price_num = random.randint(1, 5)
                good_error_price_erp_code_list = self.random_select_with_repeat(purchase_price_null_erp_code_list,
                                                                                good_error_price_num, is_repeat=0)
                for erp_code in good_error_price_erp_code_list:
                    goods_info = self.api_data.get_commodity_base_info(erp_code)
                    # 生产厂商
                    manufacture = goods_info.get("manufacture", "") or ""
                    # 商品名称
                    erpName = goods_info.get("name", "") or ""
                    # 商品规格
                    commoditySpec = goods_info.get("commoditySpecValue", "") or ""
                    purchase_item = {
                        "erpCode": erp_code,
                        "qty": 1,
                        "purchasePrice": 1,
                        "good_status": "err_price",
                        "manufacture": manufacture,
                        "erpName": erpName,
                        "commoditySpec": commoditySpec
                    }
                    purchase_list.append(purchase_item)
            if good_unbuyable_num > 0:
                good_unbuyable_num = random.randint(1, 5)
                good_unbuyable_erp_code_list = self.random_select_with_repeat(goods_purchase_erp_code_list,
                                                                              good_unbuyable_num, is_repeat=0)
                for erp_code in good_unbuyable_erp_code_list:
                    goods_info=self.api_data.get_commodity_base_info(erp_code)
                    # 生产厂商
                    manufacture = goods_info.get("manufacture", "") or ""
                    # 商品名称
                    erpName = goods_info.get("name", "") or ""
                    # 商品规格
                    commoditySpec = goods_info.get("commoditySpecValue", "") or ""
                    purchase_item = {
                        "erpCode": erp_code,
                        "qty": 1,
                        "good_type": "nomal",
                        "purchasePrice": 1,
                        "good_status": "err_unbuyable",
                        "manufacture": manufacture,
                        "erpName": erpName,
                        "commoditySpec": commoditySpec
                    }
                    purchase_list.append(purchase_item)
            # 商品未上架
            if good_unshelved_num > 0:
                good_unshelved_num = random.randint(1, 5)
                good_unshelved_erp_code_list = self.random_select_with_repeat(unshelved_erp_code_list,
                                                                              good_unshelved_num, is_repeat=0)
                for erp_code in good_unshelved_erp_code_list:
                    goods_info = self.api_data.get_commodity_base_info(erp_code)
                    # 生产厂商
                    manufacture = goods_info.get("manufacture", "") or ""
                    # 商品名称
                    erpName = goods_info.get("name", "") or ""
                    # 商品规格
                    commoditySpec = goods_info.get("commoditySpecValue", "") or ""
                    purchase_item = {
                        "erpCode": erp_code,
                        "qty": 1,
                        "good_type": "nomal",
                        "purchasePrice": 1,
                        "good_status": "err_unshelved",
                        "manufacture": manufacture,
                        "erpName": erpName,
                        "commoditySpec": commoditySpec
                    }
                    purchase_list.append(purchase_item)
            # 商品不在经营范围
            if goods_not_in_scope_num > 0:
                goods_not_in_scope_num = random.randint(1, 5)
                goods_not_in_scope_erp_code_list = self.random_select_with_repeat(goods_not_in_scope_erp_code_list,
                                                                                  goods_not_in_scope_num, is_repeat=0)
                for erp_code in goods_not_in_scope_erp_code_list:
                    goods_info = self.api_data.get_commodity_base_info(erp_code)
                    # 生产厂商
                    manufacture = goods_info.get("manufacture", "") or ""
                    # 商品名称
                    erpName = goods_info.get("name", "") or ""
                    # 商品规格
                    commoditySpec = goods_info.get("commoditySpecValue", "") or ""
                    purchase_item = {
                        "erpCode": erp_code,
                        "qty": 1,
                        "good_type": "nomal",
                        "purchasePrice": 1,
                        "good_status": "err_not_in_scope",
                        "manufacture": manufacture,
                        "erpName": erpName,
                        "commoditySpec": commoditySpec
                    }
                    purchase_list.append(purchase_item)
            return purchase_list
        except Exception as e:
            logger.error(f"生成测试用例中的商品数据失败：{e},数据：{store_code}")
            raise

    def test_case_data_create_payment(self, test_case: Dict, **kwargs) -> Dict:
        "生成测试用例中的支付数据"
        try:
            payment_status = "normal"
            auto_confirm=False
            # 是否自动扣款
            is_pay_automatic = test_case.get("is_pay_automatic", "否")
            # 超时天数
            overtime_days = test_case.get("overtime_day", 0)
            if is_pay_automatic == "是":
                is_pay_automatic = True
            else:
                is_pay_automatic = False
            if is_pay_automatic and overtime_days <= 0:
                payment_status = "error"
            result = {"is_pay_automatic": is_pay_automatic, "overtime_days": overtime_days,
                      "payment_status": payment_status,"autoConfirm":auto_confirm}
            return result
        except Exception as e:
            raise e

    def random_select_with_repeat(self, values: List, count: int, is_repeat=0) -> List:
        """
        从列表中随机选择指定数量的值（允许重复），若元素不足则自动重复选取

        参数:
            values: 待选择的原始列表（可以包含重复元素）
            count: 希望选择的数量（非负整数）

        返回:
            随机选择的结果列表（允许重复）
        """
        if count < 0:
            raise ValueError("count 必须为非负整数")
        if not values:  # 原始列表为空时直接返回空列表
            return []
        if is_repeat == 1:
            # 直接使用 random.choices 允许重复选择（有放回抽样）
            return random.choices(values, k=count)
        else:
            # 随机选择不重复的元素
            return random.sample(values, min(count, len(values)))


if __name__ == "__main__":
    test_case_data = TestCaseData()
    result = test_case_data.test_case_data()
    print(result)
