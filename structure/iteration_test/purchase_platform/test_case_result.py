# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/15 15:21
@Auth ： 逗逗的小老鼠
@File ：test_case_result.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json
from typing import Dict, List
from lib.get_log import logger, exception
from structure.iteration_test.purchase_platform.get_api_data import ApiData
from decimal import Decimal


@exception(logger)
class TestCaseResult:
    "测试用例结果"

    def __init__(self):
        self.api_data = ApiData()


    def test_case_expected_result(self, case_data: List[Dict]):
        """
        测试用例预期结果
        :param case_data: 测试用例数据
        :param response_data: 响应数据
        :return: 预期的采购单数据，以case_no为外层维度
        """
        try:
            # 预期结果字典，以case_no为键
            expected_result = {}

            for case_data_item in case_data:
                # 获取基本信息
                case_no = case_data_item.get("case_no")
                payment_status = case_data_item.get("payment_status")
                autoConfirm = case_data_item.get("autoConfirm")
                overduePaymentTime = case_data_item.get("overduePaymentTime")
                isAutoPayment = case_data_item.get("isAutoPayment")
                items = case_data_item.get("items", [])

                # 如果支付状态不正常，则跳过此项
                if payment_status != "normal":
                    continue

                # 初始化当前测试用例的采购单数据结构
                if case_no not in expected_result:
                    expected_result[case_no] = {
                        "case_no": case_no,
                        "purchase_orders": []
                    }

                # 按门店和商品类型组织的数据结构
                # 格式: {organizationCode: {good_type: {erpCode: {details...}}}}
                purchase_orders_map = {}

                # 处理每个商品数据
                for item in items:
                    # 获取门店信息
                    store_status = item.get("store_status")
                    organizationCode = item.get("organizationCode")

                    # 获取商品信息
                    goods_data = item.get("goods_data")
                    if not goods_data:
                        continue

                    # 处理商品数据
                    erpCode = goods_data.get("erpCode")
                    good_type = goods_data.get("good_type")
                    qty = goods_data.get("qty", 0)
                    purchasePrice = goods_data.get("purchasePrice")
                    good_status = goods_data.get("good_status")
                    manufacture = goods_data.get("manufacture", "")
                    erpName = goods_data.get("erpName", "")
                    commoditySpec = goods_data.get("commoditySpec", "")

                    # 根据商品类型进行标签处理
                    if good_type=="cold":
                        purchaseOrder_Label="MULTIPLE_STORE,COLD_LINK"
                    elif good_type=="office":
                        purchaseOrder_Label="MULTIPLE_STORE,CONSUMABLES"
                    else:
                        purchaseOrder_Label="MULTIPLE_STORE"


                    # 初始化门店数据结构
                    if organizationCode not in purchase_orders_map:
                        purchase_orders_map[organizationCode] = {}

                    # 初始化商品类型数据结构
                    if good_type not in purchase_orders_map[organizationCode]:
                        purchase_orders_map[organizationCode][good_type] = {
                            "isAutoPayment": isAutoPayment,
                            "overduePaymentTime": overduePaymentTime,
                            "organizationCode": organizationCode,
                            "purchaseOrderLabel": purchaseOrder_Label,
                            "payment_status": payment_status,
                            "store_status": store_status,
                            "purchase_order_detail": {}
                        }

                    # 聚合相同erpCode的商品数量
                    details = purchase_orders_map[organizationCode][good_type]["purchase_order_detail"]
                    if erpCode in details:
                        details[erpCode]["commodityCount"] += qty
                    else:
                        details[erpCode] = {
                            "commodityCount": qty,
                            "erpCode": erpCode,
                            "purchasePrice": purchasePrice,
                            "manufacture": manufacture,
                            "erpName": erpName,
                            "commoditySpec": commoditySpec,
                            "good_status": good_status
                        }

                # 将嵌套字典转换为采购单列表
                purchase_orders = []
                for org_code, org_data in purchase_orders_map.items():
                    for good_type, type_data in org_data.items():
                        # 将商品详情字典转换为列表
                        details_list = list(type_data["purchase_order_detail"].values())
                        type_data["purchase_order_detail"] = details_list
                        purchase_orders.append(type_data)

                # 将采购单列表添加到当前测试用例的结果中
                expected_result[case_no]["purchase_orders"] = purchase_orders

            # 将字典转换为列表格式返回
            result_list = list(expected_result.values())

            logger.info(f"生成预期采购单数据: {len(result_list)}个测试用例")
            return result_list

        except Exception as e:
            logger.error(f"生成预期采购单数据时发生错误: {str(e)}")
            raise

    def test_case_run_result(self, case_data: List[Dict]):
        "测试数据执行结果"
        try:
            # 获取预期结果
            case_expected_result = self.test_case_expected_result(case_data)
            # 初始化实际结果列表
            case_actual_result = []
            # 初始化验证结果列表
            validation_results = []

            # 执行测试用例并收集实际结果
            for case_data_item in case_data:
                # 获取基本信息
                case_no = case_data_item.get("case_no")
                print(f"执行测试用例: {case_no}，执行数据：{json.dumps(case_data_item)}")
                # 调用API添加采购单
                case_run_result = self.api_data.add_purchase_order(case_data_item)
                # 添加到实际结果列表
                case_actual_data = {"case_no": case_no, "case_run_result": case_run_result}
                case_actual_result.append(case_actual_data)

            # 1. 根据case_no将case_expected_result和case_actual_result进行分组
            expected_by_case_no = {}
            actual_by_case_no = {}

            # 将预期结果按case_no分组
            for expected_item in case_expected_result:
                case_no = expected_item.get("case_no")
                expected_by_case_no[case_no] = expected_item

            # 将实际结果按case_no分组
            for actual_item in case_actual_result:
                case_no = actual_item.get("case_no")
                actual_by_case_no[case_no] = actual_item

            # 验证每个测试用例
            for case_no, expected_data in expected_by_case_no.items():
                # 获取实际结果
                if case_no not in actual_by_case_no:
                    validation_results.append({
                        "case_no": case_no,
                        "result": False,
                        "reason": f"没有找到case_no={case_no}的实际结果"
                    })
                    continue

                actual_data = actual_by_case_no[case_no]
                actual_response = actual_data.get("case_run_result", {})
                purchase_orders = expected_data.get("purchase_orders", [])

                # 3. 检查是否有非normal的payment_status或store_status
                has_abnormal = False
                abnormal_payment_status = None
                abnormal_store_status = None

                for order in purchase_orders:
                    payment_status = order.get("payment_status")
                    store_status = order.get("store_status")

                    if payment_status != "normal":
                        has_abnormal = True
                        abnormal_payment_status = payment_status

                    if store_status != "normal":
                        has_abnormal = True
                        abnormal_store_status = store_status

                # 2. 根据map_purchase_order的映射关系进行验证
                validation_result = True
                validation_reason = ""
                # 采购单信息比对
                purchase_order_diff_num = 0
                purchase_orders_confirm_result=0

                if has_abnormal:
                    # 如果有异常状态，使用异常状态进行验证
                    payment_status_to_check = abnormal_payment_status or "normal"
                    store_status_to_check = abnormal_store_status or "normal"

                    # 使用map_purchase_order验证响应
                    validation_result = self.map_purchase_order(payment_status_to_check, store_status_to_check,
                                                                actual_response)

                    if not validation_result:
                        validation_reason = f"异常状态验证失败: payment_status={payment_status_to_check}, store_status={store_status_to_check}"
                else:
                    # 如果所有状态都正常，使用normal状态验证
                    validation_result = self.map_purchase_order("normal", "normal", actual_response)

                    if not validation_result:
                        validation_reason = "正常状态验证失败"
                    else:
                        purchase_order_diff_num = self.verify_purchase_order(purchase_orders, actual_response)
                    # 进行采购单信息确认
                    if purchase_order_diff_num == 0:
                        purchase_orders_confirm_result = self.verify_b2b_order(actual_response)

                # 5. 添加验证结果
                validation_results.append({
                    "case_no": case_no,
                    "purchase_order_add_result": validation_result,
                    "reason": validation_reason,
                    "purchase_order_diff_num": purchase_order_diff_num,
                    "purchase_orders_confirm_result": purchase_orders_confirm_result
                })

            # 返回验证结果
            logger.info(f"测试用例验证结果: {len(validation_results)}个")
            return validation_results

        except Exception as e:
            logger.error(f"测试数据执行结果时发生错误: {str(e)}")
            raise e

    def map_purchase_order(self, payment_status: str, store_status: str, response_data: Dict) -> bool:
        "映射采购单数据"
        try:
            # 采购单新增结果映射数据
            purchase_map = [
                {"payment_status": "normal", "store_status": "normal", "response_code": "10000",
                 "response_msg": "操作成功"},
                {"payment_status": "error", "store_status": "normal", "response_code": "400",
                 "response_msg": "支付失败"},
                {"payment_status": "normal", "store_status": "error_permission", "response_code": "50000",
                 "response_msg": "权限不足"},
                {"payment_status": "normal", "store_status": "error_exist", "response_code": "409",
                 "response_msg": "采购单已存在"},
                {"payment_status": "normal", "store_status": "non_franchised", "response_code": "422",
                 "response_msg": "非加盟店铺"}
            ]

            # 根据payment_status和store_status，判断response_data中code和msg是否符合预期
            expected_response = None
            for item in purchase_map:
                if item["payment_status"] == payment_status and item["store_status"] == store_status:
                    expected_response = item
                    break

            if expected_response is None:
                logger.error(f"未找到匹配的预期响应: payment_status={payment_status}, store_status={store_status}")
                return False

            actual_code = response_data.get("code", "")
            actual_msg = response_data.get("msg", "")
            expected_code = expected_response["response_code"]
            expected_msg = expected_response["response_msg"]

            if expected_code and actual_code != expected_code:
                logger.error(f"响应码不匹配: 预期={expected_code}, 实际={actual_code}")
                return False

            if expected_msg and actual_msg != expected_msg:
                logger.error(f"响应消息不匹配: 预期={expected_msg}, 实际={actual_msg}")
                return False

            return True
        except Exception as e:
            logger.error(f"验证采购单响应时发生错误: {str(e)}")
            raise e

    def map_purchase_goods(self, expect_type: str, actul_status: str,actul_msg:str, **kwargs) -> bool:
        "映射采购单商品数据"
        try:
            # 采购单新增结果映射数据
            purchase_map = [
                {"goods_status": "normal", "good_type": ["normal", "cold"], "purchase_goods_status": "0",
                 "purchase_goods_exmsg": "", "response_code": "200", "response_msg": "成功"},
                {"goods_status": "normal", "good_type": ["office"], "purchase_goods_status": 0,
                 "purchase_goods_exmsg": "", "response_code": "200", "response_msg": "成功"},
                {"goods_status": "err_UpperLimit", "good_type": ["normal", "cold"], "purchase_goods_status": -1,
                 "purchase_goods_exmsg": "", "response_code": "200", "response_msg": "成功"},
                {"goods_status": "err_BundleSize", "good_type": ["normal", "cold"], "purchase_goods_status": -1,
                 "purchase_goods_exmsg": "", "response_code": "200", "response_msg": "成功"},
                {"goods_status": "err_wareStock", "good_type": ["normal", "cold"], "purchase_goods_status": -1,
                 "purchase_goods_exmsg": "商品库存不足", "response_code": "200", "response_msg": "成功"},
                {"goods_status": "err_exist", "good_type": ["normal", "cold"], "purchase_goods_status": -1,
                 "purchase_goods_exmsg": "", "response_code": "200", "response_msg": "成功"},
                {"goods_status": "err_price", "good_type": ["normal", "cold"], "purchase_goods_status": -1,
                 "purchase_goods_exmsg": "", "response_code": "200", "response_msg": "成功"},
                {"goods_status": "err_unshelved", "good_type": ["normal", "cold"], "purchase_goods_status": -1,
                 "purchase_goods_exmsg": "", "response_code": "200", "response_msg": "成功"},
                {"goods_status": "err_not_in_scope", "good_type": ["normal", "cold"], "purchase_goods_status": -1,
                 "purchase_goods_exmsg": "", "response_code": "200", "response_msg": "成功"}
            ]


            result=False
            for item in purchase_map:
                if item["goods_status"] == expect_type and actul_status == item["purchase_goods_status"] and actul_msg==item["purchase_goods_exmsg"]:
                    result = True
                    break
            return result
        except Exception as e:
            logger.error(f"验证采购单商品响应时发生错误: {str(e)}")
            raise e

    def verify_purchase_order(self, expected_data: Dict, actual_response: Dict) -> int:
        "验证采购单数据"
        try:
            diff_num=0
            if actual_response["code"] != "10000":
                logger.info(f"创建失败，不进行采购单数据校验")
            else:
                res_data = actual_response.get("data")
                if res_data:
                    purchaseOrderIds = res_data.get("purchaseOrderIds")
                    if purchaseOrderIds:
                        if len(purchaseOrderIds)!= len(expected_data):
                            logger.error(f"采购单数量不匹配: 预期={len(expected_data)}, 实际={len(purchaseOrderIds)}")
                            diff_num+=1
                        for purchaseOrderId in purchaseOrderIds:
                            purchase_order_info = self.api_data.get_purchase_info(purchaseOrderId)
                            purchase_order_detail = self.api_data.get_purchase_detail(purchaseOrderId)
                            if purchase_order_info.get("code") == "10000" and purchase_order_detail.get(
                                    "code") == "10000":
                                purchase_order_data = purchase_order_info.get("data").get("data")[0]
                                purchase_order_goods_data = purchase_order_detail.get("data").get("data")
                                organizationCode = purchase_order_data.get("organizationCode")
                                for expected_item in expected_data:
                                    if organizationCode == expected_item.get("organizationCode"):
                                        if expected_item.get("purchaseOrderLabel") != purchase_order_data.get("purchaseOrderLabel"):
                                            logger.error(f"采购单【{purchaseOrderId}】标签不匹配: 预期={expected_item.get('purchaseOrderLabel')}, 实际={purchase_order_data.get('purchaseOrderLabel')}")
                                            diff_num += 1
                                        if expected_item.get("overduePaymentTime") != purchase_order_data.get(
                                                "overduePaymentTime"):
                                            logger.error(
                                                f"采购单【{purchaseOrderId}】自动支付时间: 预期={expected_item.get('overduePaymentTime')}, 实际={purchase_order_data.get('overduePaymentTime')}")
                                            diff_num += 1
                                        if expected_item.get("isAutoPayment")!= purchase_order_data.get("autoPayment"):
                                            logger.error(
                                                f"采购单【{purchaseOrderId}】是否自动支付: 预期={expected_item.get('isAutoPayment')}, 实际={purchase_order_data.get('autoPayment')}")
                                            diff_num += 1
                                        expect_goods_data= expected_item.get("purchase_order_detail")
                                        if len(expect_goods_data)!= len(purchase_order_goods_data):
                                            logger.error(f"采购单【{purchaseOrderId}】商品数量不匹配: 预期={len(expect_goods_data)}, 实际={len(purchase_order_goods_data)}")
                                            diff_num+=1
                                        for expect_goods in expect_goods_data:
                                            for actual_goods in purchase_order_goods_data:
                                                good_erp_code=expect_goods.get("erpCode")
                                                if good_erp_code == actual_goods.get("erpCode"):
                                                    # 比对商品信息
                                                    if expect_goods.get("commodityCount") != actual_goods.get("commodityCount"):
                                                        logger.error(
                                                            f"采购单【{purchaseOrderId}】商品【{good_erp_code}】数量不一致: 预期={expect_goods.get('commodityCount')}, 实际={actual_goods.get('commodityCount')}")
                                                        diff_num += 1
                                                    if expect_goods.get("erpName")!= actual_goods.get("erpName"):
                                                        logger.error(
                                                            f"采购单【{purchaseOrderId}】商品【{good_erp_code}】名称不一致: 预期={expect_goods.get('erpName')}, 实际={actual_goods.get('erpName')}")
                                                        diff_num += 1
                                                    if expect_goods.get("manufacture")!= actual_goods.get("manufacture"):
                                                        logger.error(
                                                            f"采购单【{purchaseOrderId}】商品【{good_erp_code}】生成厂商不一致: 预期={expect_goods.get('manufacture')}, 实际={actual_goods.get('manufacture')}")
                                                        diff_num += 1
                                                    if expect_goods.get("commoditySpec")!= actual_goods.get("commoditySpec"):
                                                        logger.error(
                                                            f"采购单【{purchaseOrderId}】商品【{good_erp_code}】规格不一致: 预期={expect_goods.get('commoditySpec')}, 实际={actual_goods.get('commoditySpec')}")
                                                    if not (self.map_purchase_goods(expect_goods.get("good_status"),actual_goods.get("status"),actual_goods.get("exMsg"))):
                                                        logger.error(f"采购单【{purchaseOrderId}】商品【{good_erp_code}】状态不一致: 预期={expect_goods.get('good_status')}, 实际={actual_goods.get('status')}, 实际exMsg={actual_goods.get('exMsg')}")
                                                        diff_num += 1
                            else:
                                logger.error(f"采购单信息/详情查询失败，采购单号为：{purchaseOrderIds}")
                                diff_num += 1
                    else:
                        logger.error(f"采购单新增响应中未找到purchaseOrderIds数据，消息为：{actual_response}")
                        diff_num += 1
                else:
                    logger.error(f"采购单新增响应中未找到data数据，消息为：{actual_response}")
                    diff_num += 1
            return diff_num
        except Exception as e:
            logger.error(f"验证采购单响应时发生错误: {str(e)}")
            raise e


    def verify_b2b_order(self, actual_response: Dict) -> int:
        "验证B2B订单数据"
        try:
            diff_num=0
            if actual_response["code"] != "10000":
                logger.info(f"创建失败，不进行采购单数据校验")
            else:
                res_data = actual_response.get("data")
                if res_data:
                    purchaseOrderIds = res_data.get("purchaseOrderIds")
                    if purchaseOrderIds:
                        for purchaseOrderId in purchaseOrderIds:
                            confirm_result = self.api_data.confirm_purchase(purchaseOrderId)
                            if confirm_result.get("code") == "10000" and confirm_result.get("data"):
                                purchase_order_info = self.api_data.get_purchase_info(purchaseOrderId)
                                purchase_order_detail = self.api_data.get_purchase_detail(purchaseOrderId)
                                if purchase_order_info.get("code") == "10000":
                                    # 获取采购单基础信息
                                    purchase_order_data = purchase_order_info.get("data").get("data")[0]
                                    # 获取采购单商品信息
                                    purchase_order_goods_data = purchase_order_detail.get("data").get("data")
                                    organizationCode = purchase_order_data.get("organizationCode")
                                    purchaseOrderLabel = purchase_order_data.get("purchaseOrderLabel")
                                    # 关联订单号
                                    relatedOrderNo=purchase_order_data.get("relatedOrderNo")
                                    # 采购单申请人
                                    createdUserId=purchase_order_data.get("createdUserId")
                                    # 获取B2B订单信息
                                    order_info=self.api_data.get_B2B_order_info(relatedOrderNo)
                                    if order_info.get("code") == "10000":
                                        order_data = order_info.get("data").get("data")[0]
                                        order_org_code=order_data.get("orderInfo").get("launchOrganizationCode")
                                        orderTags=order_data.get("orderInfo").get("orderTags")
                                        orderDetailList=order_data.get("orderDetailList")
                                        # 进行门店信息比对
                                        if organizationCode != order_data.get("organizationCode"):
                                            logger.error(f"采购单：{purchaseOrderId}，与生成订单机构编码不一致: 采购单={organizationCode}, 订单={order_org_code}")
                                            diff_num += 1
                                        if createdUserId!= order_data.get("launchUserId"):
                                            logger.error(f"采购单：{purchaseOrderId}，与生成订单创建人不一致: 采购单={createdUserId}, 订单={order_data.get('launchUserId')}")
                                            diff_num += 1

                                        if len(orderDetailList) != len(purchase_order_goods_data):
                                            logger.error(
                                                f"采购单【{purchaseOrderId}】与生成订单商品数量不匹配: 采购单={len(purchase_order_goods_data)}, 订单={len(orderDetailList)}")
                                            diff_num += 1
                                            break

                                        for order_item in orderDetailList:
                                            for purchase_item in purchase_order_goods_data:
                                                # 按照erpCode对商品信息进行比对
                                                if order_item.get("erpCode") == purchase_item.get("erpCode"):
                                                    if float(order_item.get("commodityCount", 0)) - float(purchase_item.get("commodityCount", 0)) != 0:
                                                        logger.error(f"采购单【{purchaseOrderId}】商品【{order_item.get('erpCode')}】数量不一致: 采购单={purchase_item.get('commodityCount')}, 订单={order_item.get('commodityCount')}")
                                                        diff_num += 1
                                                    if order_item.get("erpName") != purchase_item.get("erpName"):
                                                        logger.error(f"采购单【{purchaseOrderId}】商品【{order_item.get('erpCode')}】名称不一致: 采购单={purchase_item.get('erpName')}, 订单={order_item.get('erpName')}")
                                                        diff_num += 1
                                                    if order_item.get("manufacture")!= purchase_item.get("manufacture"):
                                                        logger.error(f"采购单【{purchaseOrderId}】商品【{order_item.get('erpCode')}】生成厂商不一致: 采购单={purchase_item.get('manufacture')}, 订单={order_item.get('manufacture')}")
                                                        diff_num += 1
                                                    if order_item.get("commoditySpec")!= purchase_item.get("commoditySpec"):
                                                        logger.error(f"采购单【{purchaseOrderId}】商品【{order_item.get('erpCode')}】规格不一致: 采购单={purchase_item.get('commoditySpec')}, 订单={order_item.get('commoditySpec')}")
                                                        diff_num += 1
                                    else:
                                        logger.error(f"订单信息查询失败，订单号为：{relatedOrderNo}")
                                        diff_num += 1

                                else:
                                    logger.error(f"采购单【{purchaseOrderId}】确认失败，消息为：{confirm_result}")
                                    diff_num += 1
                            else:
                                logger.error(f"采购单【{purchaseOrderId}】确认失败，消息为：{confirm_result}")
                                diff_num += 1
                    else:
                        logger.error(f"采购单新增响应中未找到purchaseOrderIds数据，消息为：{actual_response}")
                        diff_num += 1
                else:
                    logger.error(f"采购单新增响应中未找到data数据，消息为：{actual_response}")
                    diff_num += 1
            return diff_num
        except Exception as e:
            logger.error(f"验证B2B订单响应时发生错误: {str(e)}")
            raise e


if __name__ == '__main__':
    test = TestCaseResult()
    result = test.test_case_expected_result()
    print(result)
