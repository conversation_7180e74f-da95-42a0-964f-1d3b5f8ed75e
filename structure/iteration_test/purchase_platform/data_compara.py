# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/18 08:59
@Auth ： 逗逗的小老鼠
@File ：data_compara.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：数据比对工具，支持多数据源、数据锚点、类型自动处理和枚举值映射
"""
from lib.get_log import logger, exception
import json
import jsonpath
from decimal import Decimal
from datetime import datetime
from difflib import SequenceMatcher
from typing import List, Dict, Any, Union, Optional
from decimal import InvalidOperation  # 添加缺失的导入


class DataCompara:
    @exception(logger)
    def __init__(self, data1, data2, field_mappings=None, anchor_fields=None, enum_mappings=None, config=None):
        """
        初始化数据比对工具
        :param data1: 第一个数据源（参照数据）
        :param data2: 第二个数据源（比对数据）
        :param field_mappings: 字段映射关系，格式为列表，每个元素为字典，包含source1_field、source2_field、field_type、field_desc等字段
        :param anchor_fields: 数据锚点字段，用于在比对列表数据时确定匹配项
        :param enum_mappings: 枚举值映射，格式为字典，key为字段名，value为映射关系字典
        :param config: 配置信息，包括是否忽略类型差异、是否忽略大小写等
        """
        self.data1 = data1
        self.data2 = data2
        self.field_mappings = field_mappings or []
        self.anchor_fields = anchor_fields or []
        self.enum_mappings = enum_mappings or {}
        self.config = config or {
            "ignore_data_type_changes": True,  # 是否忽略数据类型差异
            "ignore_numeric_type_changes": True,  # 是否忽略数值类型差异
            "ignore_string_type_changes": True,  # 是否忽略字符串类型差异
            "ignore_string_case": False,  # 是否忽略字符串大小写
            "ignore_sort": False  # 是否忽略排序差异
        }
        self.comparison_results = []

    @exception(logger)
    def data_compara(self):
        """
        比较两个数据是否相同
        :return: 比对结果，包含每个字段的比对情况
        """
        # 如果没有提供字段映射，则直接比较整个数据
        if not self.field_mappings:
            return self._compare_values(self.data1, self.data2)

        # 使用字段映射进行比对
        for mapping in self.field_mappings:
            source1_field = mapping.get("source1_field")
            source2_field = mapping.get("source2_field")
            field_type = mapping.get("field_type", "")
            field_desc = mapping.get("field_desc", "")

            # 获取两个数据源中的值
            value1 = self._get_field_value(self.data1, source1_field)
            value2 = self._get_field_value(self.data2, source2_field)

            # 应用枚举值映射
            if source1_field in self.enum_mappings:
                value1 = self._apply_enum_mapping(value1, source1_field, "source1")
            if source2_field in self.enum_mappings:
                value2 = self._apply_enum_mapping(value2, source2_field, "source2")

            # 处理类型转换
            value1 = self._convert_type(value1, field_type)
            value2 = self._convert_type(value2, field_type)

            # 比较值
            if isinstance(value1, list) and isinstance(value2, list):
                # 如果是列表类型，使用数据锚点进行比对
                result = self._compare_lists(value1, value2, field_desc)
            else:
                # 否则直接比对值
                result = self._compare_values(value1, value2, field_desc)

            self.comparison_results.append({
                "field_desc": field_desc,
                "source1_field": source1_field,
                "source2_field": source2_field,
                "source1_value": value1,
                "source2_value": value2,
                "is_equal": result["is_equal"],
                "similarity": result.get("similarity", 1.0 if result["is_equal"] else 0.0),
                "message": result.get("message", "")
            })

        return self.comparison_results

    @exception(logger)
    def _get_field_value(self, data, field_path):
        """
        根据字段路径获取数据中的值，支持JSONPath表达式
        :param data: 数据源
        :param field_path: 字段路径，可以是JSONPath表达式
        :return: 字段值
        """
        try:
            # 尝试使用JSONPath获取值
            if field_path.startswith("$"):
                result = jsonpath.jsonpath(data, field_path)
                if result:
                    # JSONPath返回的是列表，如果只有一个元素，则返回该元素
                    return result[0] if len(result) == 1 else result
                return None

            # 如果不是JSONPath，则按照点号分隔的路径获取值
            parts = field_path.split(".")
            value = data
            for part in parts:
                # 处理数组索引，如 items[0]
                if "[" in part and "]" in part:
                    array_name, index_str = part.split("[", 1)
                    index = int(index_str.rstrip("]"))
                    value = value.get(array_name, [])[index] if array_name in value else None
                else:
                    value = value.get(part) if isinstance(value, dict) else None

                if value is None:
                    break

            return value
        except Exception as e:
            logger.error(f"获取字段值失败: {field_path}, 错误: {str(e)}")
            return None

    @exception(logger)
    def _apply_enum_mapping(self, value, field_name, source):
        """
        应用枚举值映射
        :param value: 原始值
        :param field_name: 字段名
        :param source: 数据源标识（source1或source2）
        :return: 映射后的值
        """
        if value is None:
            return None

        mapping = self.enum_mappings.get(field_name, {})
        if not mapping:
            return value

        # 获取当前数据源的映射配置
        source_mapping = mapping.get(source, {})

        # 将值转换为字符串进行匹配
        str_value = str(value)
        if str_value in source_mapping:
            return source_mapping[str_value]

        return value

    @exception(logger)
    def _convert_type(self, value, target_type):
        """
        将值转换为指定类型
        :param value: 原始值
        :param target_type: 目标类型
        :return: 转换后的值
        """
        if value is None or target_type == "":
            return value

        try:
            if target_type == "int":
                # 处理整数类型
                if isinstance(value, str) and value.strip() == "":
                    return 0
                return int(value)
            elif target_type == "float" or target_type == "decimal":
                # 处理浮点数类型，使用Decimal确保精度
                if isinstance(value, str) and value.strip() == "":
                    return 0.0
                return float(Decimal(str(value)))
            elif target_type == "string" or target_type == "str":
                # 处理字符串类型
                return str(value)
            elif target_type == "datetime":
                # 处理日期时间类型
                if isinstance(value, int):
                    # 时间戳转日期时间
                    return datetime.fromtimestamp(value).strftime("%Y-%m-%d %H:%M:%S")
                elif isinstance(value, datetime):
                    # 日期时间对象转字符串
                    return value.strftime("%Y-%m-%d %H:%M:%S")
                elif isinstance(value, str):
                    # 尝试解析日期时间字符串
                    try:
                        dt = datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                        return dt.strftime("%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        return value
                return value
            elif target_type == "boolean" or target_type == "bool":
                # 处理布尔类型
                if isinstance(value, str):
                    return value.lower() in ["true", "1", "yes", "y"]
                return bool(value)
            elif target_type == "list":
                # 处理列表类型
                if isinstance(value, str):
                    try:
                        return json.loads(value)
                    except json.JSONDecodeError:
                        return [value]
                elif not isinstance(value, list):
                    return [value]
                return value
            elif target_type == "dict" or target_type == "json":
                # 处理字典/JSON类型
                if isinstance(value, str):
                    try:
                        return json.loads(value)
                    except json.JSONDecodeError:
                        return {"value": value}
                elif not isinstance(value, dict):
                    return {"value": value}
                return value
            else:
                # 未知类型，返回原值
                return value
        except Exception as e:
            logger.warning(f"类型转换失败: {value} -> {target_type}, 错误: {str(e)}")
            return value

    @exception(logger)
    def _compare_lists(self, list1, list2, field_desc=""):
        """
        比较两个列表，使用数据锚点进行匹配
        :param list1: 第一个列表
        :param list2: 第二个列表
        :param field_desc: 字段描述
        :return: 比对结果
        """
        if not self.anchor_fields:
            # 如果没有指定数据锚点，则直接比较列表
            return self._compare_values(list1, list2, field_desc)

        # 结果统计
        matched_count = 0
        total_items = max(len(list1), len(list2))
        mismatched_items = []

        # 将列表1按照锚点字段组织成字典，便于查找
        list1_dict = {}
        for item in list1:
            # 生成锚点键
            anchor_key = self._generate_anchor_key(item)
            if anchor_key:
                list1_dict[anchor_key] = item

        # 遍历列表2，查找匹配项并比较
        for item2 in list2:
            anchor_key = self._generate_anchor_key(item2)
            if not anchor_key:
                mismatched_items.append({
                    "item": item2,
                    "reason": "无法生成锚点键"
                })
                continue

            if anchor_key in list1_dict:
                # 找到匹配项，比较详细内容
                item1 = list1_dict[anchor_key]
                item_result = self._compare_values(item1, item2)
                if item_result["is_equal"]:
                    matched_count += 1
                else:
                    mismatched_items.append({
                        "anchor_key": anchor_key,
                        "item1": item1,
                        "item2": item2,
                        "reason": item_result.get("message", "值不相等")
                    })
            else:
                # 未找到匹配项
                mismatched_items.append({
                    "anchor_key": anchor_key,
                    "item": item2,
                    "reason": "在源数据中未找到匹配项"
                })

        # 计算相似度
        similarity = matched_count / total_items if total_items > 0 else 1.0

        return {
            "is_equal": len(mismatched_items) == 0,
            "similarity": similarity,
            "matched_count": matched_count,
            "total_items": total_items,
            "mismatched_items": mismatched_items,
            "message": f"匹配 {matched_count}/{total_items} 项，相似度 {similarity:.2%}"
        }

    @exception(logger)
    def _generate_anchor_key(self, item):
        """
        根据锚点字段生成唯一键
        :param item: 列表项
        :return: 锚点键
        """
        if not isinstance(item, dict):
            return None

        key_parts = []
        for field in self.anchor_fields:
            if field in item:
                key_parts.append(str(item[field]))
            else:
                # 如果缺少锚点字段，则无法生成唯一键
                return None

        return "|".join(key_parts) if key_parts else None

    @exception(logger)
    def _compare_values(self, value1, value2, field_desc=""):
        """
        比较两个值是否相等
        :param value1: 第一个值
        :param value2: 第二个值
        :param field_desc: 字段描述
        :return: 比对结果
        """
        # 处理None值
        if value1 is None and value2 is None:
            return {"is_equal": True, "similarity": 1.0, "message": "两个值均为None"}

        if value1 is None or value2 is None:
            return {
                "is_equal": False,
                "similarity": 0.0,
                "message": f"值不相等: {value1} != {value2} (一个为None)"
            }

        # 处理类型不一致的情况
        if type(value1) != type(value2):
            if self.config.get("ignore_data_type_changes", False):
                # 如果忽略类型差异，尝试转换为字符串比较
                str_value1 = str(value1)
                str_value2 = str(value2)

                # 处理数值类型
                if self.config.get("ignore_numeric_type_changes", False):
                    try:
                        if isinstance(value1, (int, float, Decimal)) or isinstance(value2, (int, float, Decimal)):
                            # 转换为Decimal比较，确保精度
                            dec_value1 = Decimal(str_value1)
                            dec_value2 = Decimal(str_value2)
                            return {
                                "is_equal": dec_value1 == dec_value2,
                                "similarity": 1.0 if dec_value1 == dec_value2 else 0.9,
                                "message": f"数值比较: {dec_value1} {'==' if dec_value1 == dec_value2 else '!='} {dec_value2}"
                            }
                    except (ValueError, TypeError, InvalidOperation):
                        # 转换失败，继续使用字符串比较
                        pass

                # 处理字符串大小写
                if self.config.get("ignore_string_case", False):
                    str_value1 = str_value1.lower()
                    str_value2 = str_value2.lower()

                # 计算字符串相似度
                similarity = SequenceMatcher(None, str_value1, str_value2).ratio()

                return {
                    "is_equal": str_value1 == str_value2,
                    "similarity": similarity,
                    "message": f"字符串比较: {str_value1} {'==' if str_value1 == str_value2 else '!='} {str_value2}, 相似度: {similarity:.2%}"
                }
            else:
                # 不忽略类型差异，直接返回不相等
                return {
                    "is_equal": False,
                    "similarity": 0.0,
                    "message": f"类型不一致: {type(value1).__name__} != {type(value2).__name__}"
                }

        # 处理列表类型
        if isinstance(value1, list):
            if self.config.get("ignore_sort", False):
                # 忽略排序，对列表进行排序后比较
                try:
                    sorted_value1 = sorted(value1)
                    sorted_value2 = sorted(value2)
                    return {
                        "is_equal": sorted_value1 == sorted_value2,
                        "message": f"排序后列表比较: {sorted_value1} {'==' if sorted_value1 == sorted_value2 else '!='} {sorted_value2}"
                    }
                except TypeError:
                    # 无法排序，使用原始比较
                    pass

            # 计算列表相似度
            if len(value1) == 0 and len(value2) == 0:
                return {"is_equal": True, "message": "两个列表均为空"}

            common_items = sum(1 for item in value1 if item in value2)
            similarity = common_items / max(len(value1), len(value2))

            return {
                "is_equal": value1 == value2,
                "similarity": similarity,
                "message": f"列表比较: 共有{common_items}项相同，相似度: {similarity:.2%}"
            }

        # 处理字典类型
        if isinstance(value1, dict):
            # 计算字典相似度
            all_keys = set(value1.keys()) | set(value2.keys())
            common_keys = set(value1.keys()) & set(value2.keys())

            if not all_keys:
                return {"is_equal": True, "message": "两个字典均为空"}

            # 计算共有键的值相等的比例
            equal_values = sum(1 for k in common_keys if value1[k] == value2[k])
            similarity = equal_values / len(all_keys) if all_keys else 1.0

            return {
                "is_equal": value1 == value2,
                "similarity": similarity,
                "message": f"字典比较: 共有{len(common_keys)}个键，其中{equal_values}个值相等，相似度: {similarity:.2%}"
            }

        # 其他类型直接比较
        # 如果是字符串，计算相似度
        if isinstance(value1, str) and isinstance(value2, str):
            similarity = SequenceMatcher(None, value1, value2).ratio()
            return {
                "is_equal": value1 == value2,
                "similarity": similarity,
                "message": f"字符串比较: {value1} {'==' if value1 == value2 else '!='} {value2}, 相似度: {similarity:.2%}"
            }

        # 其他类型
        return {
            "is_equal": value1 == value2,
            "similarity": 1.0 if value1 == value2 else 0.0,
            "message": f"值比较: {value1} {'==' if value1 == value2 else '!='} {value2}"
        }

    @exception(logger)
    def get_summary(self):
        """
        获取比对结果摘要
        :return: 比对结果摘要
        """
        if not self.comparison_results:
            return {"message": "未执行比对或比对结果为空"}

        total_fields = len(self.comparison_results)
        equal_fields = sum(1 for result in self.comparison_results if result["is_equal"])
        similarity = sum(result.get("similarity", 0) for result in self.comparison_results) / total_fields if total_fields > 0 else 0

        return {
            "total_fields": total_fields,
            "equal_fields": equal_fields,
            "different_fields": total_fields - equal_fields,
            "similarity": similarity,
            "is_equal": equal_fields == total_fields,
            "message": f"总计{total_fields}个字段，相同{equal_fields}个，不同{total_fields - equal_fields}个，整体相似度: {similarity:.2%}"
        }


