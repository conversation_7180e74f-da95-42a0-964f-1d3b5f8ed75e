# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/9 11:49
@Auth ： 逗逗的小老鼠
@File ：get_api_data.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
import requests
import json
from lib.deal_config_json import read_json_file
import os
import time
from typing import List, Dict, Any, Union


@exception(logger)
class ApiData:
    def __init__(self):
        self.user_id = "4086467781451892826"
        self.headers = {"Content-Type": "application/json;charset=UTF-8", "user_id": self.user_id}
        self.evn_flag = "test"

    def get_commodity_list(self, store_code: str, **kwargs):
        "获取商品列表"
        try:
            # 冷链商品列表
            cold_goods_erp_code_list = []
            # 普通商品列表
            normal_goods_erp_code_list = []
            # 加盟商商品列表
            b2b_goods_erp_code_list = []
            # 加盟门店商品列表
            store_goods_erp_code_list = []
            # 办公用品列表
            office_goods_erp_code_list = []
            # 未上架商品列表
            unshelved_erp_code_list = []
            # 采购价为null的商品列表
            purchase_price_null_erp_code_list = []
            # 商品不可采列表
            goods_unpurchase_erp_code_list = []
            # 商品不存在列表
            goods_not_exist_erp_code_list = ["T_00001", "T_00002", "T_00003", "T_00004", "T_00005", "T_00006",
                                             "T_00007", "T_00008", "T_00009", "T_00010"]

            # 门店正常售卖商品列表
            goods_effective_erp_code_list = []
            # 获取B2B商品列表
            b2b_goods_url = "http://hydee-middle-merchandise.svc.k8s.test.hxyxt.com/api/commodityB2b/queryByCondition"
            b2b_goods_data = {
                "currentPage": 1,
                "pageSize": 30000
            }
            b2b_goods_res = requests.post(url=b2b_goods_url, data=json.dumps(b2b_goods_data), headers=self.headers,
                                          verify=False)
            b2b_goods_res_json = b2b_goods_res.json()
            if b2b_goods_res_json['code'] == '10000':
                b2b_goods_data = b2b_goods_res_json['data']['records']
                erp_code_list = [item.get("erpCode") for item in b2b_goods_data]
                b2b_goods_erp_code_list.extend(erp_code_list)
            # 获取门店B2B商品列表
            store_goods_url = "http://hydee-middle-merchandise.svc.k8s.test.hxyxt.com/api/commodityB2b/storeSpec/queryByCondition"
            store_goods_data = {
                "currentPage": 1,
                "pageSize": 30000,
                "storeCodes": [store_code]
            }
            store_goods_res = requests.post(url=store_goods_url, data=json.dumps(store_goods_data),
                                            headers=self.headers, verify=False)
            store_goods_res_json = store_goods_res.json()
            # 获取门店办公用品列表
            office_goods_data = {
                "currentPage": 1,
                "pageSize": 30000,
                "typeCode": "I01",
                "storeCodes": [
                    store_code
                ]
            }
            office_goods_res = requests.post(url=store_goods_url, data=json.dumps(office_goods_data),
                                             headers=self.headers, verify=False)
            office_goods_res_json = office_goods_res.json()

            if store_goods_res_json['code'] == '10000' and office_goods_res_json['code'] == '10000':
                store_goods_data = store_goods_res_json['data']['records']
                office_goods_data = office_goods_res_json['data']['records']
                office_erp_code_list = [item.get("erpCode") for item in office_goods_data]

                for store_item in store_goods_data:
                    goods_erp_code = store_item.get("erpCode")
                    # 商品是否可采
                    goods_is_purchase = store_item.get("isPurchase")
                    # 商品采购价
                    goods_purchasePrice = store_item.get("purchasePrice")
                    # 商品状态：0：下架，1：上架
                    goods_status = store_item.get("status")
                    store_goods_erp_code_list.append(goods_erp_code)
                    if goods_is_purchase == 0:
                        goods_unpurchase_erp_code_list.append(goods_erp_code)
                    if not goods_purchasePrice:
                        purchase_price_null_erp_code_list.append(goods_erp_code)
                    if goods_status == 0:
                        unshelved_erp_code_list.append(goods_erp_code)
                    # 商品正常售卖
                    if goods_is_purchase == 1 and goods_purchasePrice and goods_status == 1:
                        # 商品是办公用品
                        if goods_erp_code in office_erp_code_list:
                            office_goods_erp_code_list.append(goods_erp_code)
                        else:
                            goods_effective_erp_code_list.append(goods_erp_code)
            b2b_goods_erp_code_set = set(b2b_goods_erp_code_list)
            store_goods_erp_code_set = set(store_goods_erp_code_list)
            # 找出门店无权限的商品数据
            store_diff_data = b2b_goods_erp_code_set.difference(store_goods_erp_code_set)
            # 商品不在门店经营范围
            goods_not_in_scope_erp_code_list = list(store_diff_data)

            # 获取门店B2B商品详情
            store_goods_info_url = "http://yxt-merchandise-search.svc.k8s.test.hxyxt.com/api/commodityB2b/storeSpec/search"
            store_goods_info_data = {
                "currentPage": 1,
                "pageSize": 3000,
                "commodityScope": 0,
                "sortTypes": [
                    0
                ],
                "storeCode": store_code
            }
            store_goods_info_res = requests.post(url=store_goods_info_url, data=json.dumps(store_goods_info_data),
                                                 headers=self.headers, verify=False)
            store_goods_info_res_json = store_goods_info_res.json()
            if store_goods_info_res_json['code'] == '10000':
                store_goods_info_data = store_goods_info_res_json.get('data').get('data', [])
                if store_goods_info_data:
                    for info_item in store_goods_info_data:
                        info_erp_code = info_item.get("erpCode")
                        isColdChain = info_item.get("isColdChain")
                        if info_erp_code in goods_effective_erp_code_list:
                            # 判断是否冷链商品
                            if isColdChain == False or isColdChain == "false":
                                normal_goods_erp_code_list.append(info_erp_code)
                            else:
                                cold_goods_erp_code_list.append(info_erp_code)

            result = {"normal_goods_erp_code_list": normal_goods_erp_code_list,
                      "cold_goods_erp_code_list": cold_goods_erp_code_list,
                      "goods_not_exist_erp_code_list": goods_not_exist_erp_code_list,
                      "goods_not_in_scope_erp_code_list": goods_not_in_scope_erp_code_list,
                      "purchase_price_null_erp_code_list": purchase_price_null_erp_code_list,
                      "goods_purchase_erp_code_list": goods_unpurchase_erp_code_list,
                      "office_goods_erp_code_list": office_goods_erp_code_list,
                      "unshelved_erp_code_list": unshelved_erp_code_list}
            return result
        except Exception as e:
            raise e

    def get_commodity_base_info(self, erp_code: str, **kwargs):
        "获取商品详情"
        try:
            url = "http://hydee-middle-merchandise.svc.k8s.test.hxyxt.com/api/commodityB2b/queryByCondition"
            data = {
                "currentPage": 1,
                "erpCodeList": [erp_code],
                "pageSize": 10
            }
            res = requests.post(url=url, data=json.dumps(data), headers=self.headers, verify=False)
            res_json = res.json()
            if res_json['code'] == '10000':
                res_data = res_json['data']['records']
                if len(res_data) > 0:
                    for item in res_data:
                        erpCode = item.get("erpCode")
                        if erpCode == erp_code:
                            return item
                else:
                    return {}
            else:
                return {}
        except Exception as e:
            logger.error(f"获取商品详情失败:{e}")
            raise e

    def get_commodity_info(self, store_code: str, erp_code: str, **kwargs):
        "获取商品详情"
        try:
            url = "http://yxt-merchandise-search.svc.k8s.test.hxyxt.com/api/commodityB2b/storeSpec/search"
            data = {
                "currentPage": 1,
                "pageSize": 9999,
                "commodityScope": 0,
                "sortTypes": [
                    0
                ],
                "keyWord": erp_code,
                "storeCode": store_code
            }
            res = requests.post(url=url, data=json.dumps(data), headers=self.headers, verify=False)
            res_json = res.json()
            if res_json['code'] == '10000':
                res_data = res_json['data']['data']
                if len(res_data) > 0:
                    for item in res_data:
                        erpCode = item.get("erpCode")
                        if erpCode == erp_code:
                            return item
                else:
                    return {}
            else:
                return {}
        except Exception as e:
            logger.error(f"获取商品详情失败:{e}")
            raise e

    def get_store_list(self, **kwargs):
        "获取门店列表"
        try:
            # 有效门店列表
            store_effective_list = []
            # 无权限门店列表
            store_error_permission_list = []
            # 不存在门店列表
            store_error_exist_list = ["TEST_0001", "TEST_0002", "TEST_0003", "TEST_0004", "TEST_0005", "TEST_0006",
                                      "TEST_0007", "TEST_0008", "TEST_0009", "TEST_0010"]
            # 非加盟门店列表
            store_non_franchised_list = []
            url = "http://hydee-middle-baseinfo.svc.k8s.test.hxyxt.com/1.0/api/user/organization/query/multiLayerOrgInfo"
            # 非管理员id:4086467745804817498
            userid = kwargs.get("emp_userid", self.user_id)
            if not userid:
                userid = "4086467781451892826"

            emp_data = {
                "currentPage": 1,
                "layerList": ["4"],
                "merCode": "500001",
                "pageSize": 99999,
                "userId": userid
            }
            # admin_id:4086467943092183130
            admin_data = {
                "currentPage": 1,
                "layerList": ["4"],
                "merCode": "500001",
                "pageSize": 99999,
                "userId": "4086467943092183130"
            }
            admin_res = requests.post(url=url, data=json.dumps(admin_data), headers=self.headers, verify=False)
            emp_res = requests.post(url=url, data=json.dumps(emp_data), headers=self.headers, verify=False)
            emp_res_json = emp_res.json()
            admin_res_json = admin_res.json()
            if admin_res_json['code'] == '10000' and emp_res_json['code'] == '10000':
                emp_data = emp_res_json['data']['data']
                admin_data = admin_res_json['data']['data']
                emp_store_list = []
                # 获取员工有权限的门店数据
                for emp_item in emp_data:
                    emp_store_code = emp_item.get('orgCode')
                    emp_st_class = emp_item.get('orClass')
                    # 门店类型为6，则是加盟店，为有效门店
                    if emp_st_class == 6:
                        store_effective_list.append(emp_store_code)
                    # 门店类型为7，则是直营店，为有效门店
                    if emp_st_class == 7:
                        store_non_franchised_list.append(emp_store_code)
                    emp_store_list.append(emp_store_code)
                admin_franchised_store_list = []
                for admin_item in admin_data:
                    admin_store_code = admin_item.get('orgCode')
                    admin_st_class = admin_item.get('orClass')
                    # 门店类型为6，则是加盟店，为有效门店
                    if admin_st_class == 6:
                        admin_franchised_store_list.append(admin_store_code)
                admin_store_data = set(admin_franchised_store_list)
                emp_store_data = set(store_effective_list)
                # 找出仅admin有权限的加盟门店数据
                store_error_permission_list = admin_store_data.difference(emp_store_data)
            result = {"store_effective_list": store_effective_list,
                      "store_error_permission_list": list(store_error_permission_list),
                      "store_error_exist_list": store_error_exist_list,
                      "store_non_franchised_list": store_non_franchised_list}
            return result
        except Exception as e:
            logger.error(f"获取门店列表失败:{e}")
            raise e

    def add_purchase_order(self, case_data: Dict, **kwargs):
        "新增采购单"
        try:
            url = "http://purchase-order-center.svc.k8s.test.hxyxt.com/1.0/purchase-order/create"
            requst_data = json.dumps(case_data)
            heards = {"Content-Type": "application/json;charset=UTF-8", "userId": self.user_id}
            res = requests.post(url=url, data=json.dumps(case_data), headers=heards, verify=False)
            if res.status_code == 200:
                res_json = res.json()
                return res_json
            else:
                logger.error(f"新增采购单失败,返回状态码：{res.status_code}")
                return {}
        except Exception as e:
            logger.error(f"新增采购单失败:{e}")
            raise e

    def get_purchase_info(self, purchaseOrderNo: str, **kwargs):
        "获取采购单信息"
        try:
            url = "http://purchase-order-center.svc.k8s.test.hxyxt.com/1.0/purchase-order/search"
            requst_data = {"purchaseOrderNo": purchaseOrderNo}
            heards = {"Content-Type": "application/json;charset=UTF-8", "userId": self.user_id}
            res = requests.post(url=url, data=json.dumps(requst_data), headers=heards, verify=False)
            if res.status_code == 200:
                res_json = res.json()
                return res_json
            else:
                logger.error(f"新增采购单失败,返回状态码：{res.status_code}")
                return {}
        except Exception as e:
            logger.error(f"新增采购单失败:{e}")
            raise e

    def get_purchase_detail(self, purchaseOrderNo: str, **kwargs):
        "获取采购单信息"
        try:
            url = "http://purchase-order-center.svc.k8s.test.hxyxt.com/1.0/purchase-order/detail/search"
            requst_data = {"purchaseOrderNo": purchaseOrderNo}
            heards = {"Content-Type": "application/json;charset=UTF-8", "userId": self.user_id}
            res = requests.post(url=url, data=json.dumps(requst_data), headers=heards, verify=False)
            if res.status_code == 200:
                res_json = res.json()
                return res_json
            else:
                logger.error(f"新增采购单失败,返回状态码：{res.status_code}")
                return {}
        except Exception as e:
            logger.error(f"新增采购单失败:{e}")
            raise e

    def confirm_purchase(self, purchaseOrderNo: str, **kwargs):
        "确认采购单信息"
        try:
            url = "http://purchase-order-center.svc.k8s.test.hxyxt.com/1.0/purchase-order/batch-confirm"
            requst_data ={
                          "purchaseOrderNo": purchaseOrderNo
                        }
            heards = {"Content-Type": "application/json;charset=UTF-8", "userId": self.user_id}
            res = requests.post(url=url, data=json.dumps(requst_data), headers=heards, verify=False)
            if res.status_code == 200:
                res_json = res.json()
                return res_json
            else:
                logger.error(f"确认采购单失败,返回状态码：{res.status_code}")
                return {}
        except Exception as e:
            logger.error(f"确认采购单失败:{e}")
            raise e

    def get_B2B_order_info(self, order_no: str, **kwargs):
        "获取订单信息"
        try:
            url = "http://order-service.svc.k8s.test.hxyxt.com/1.0/order/page/query"
            requst_data = {
                  "pageSize": 0,
                  "searchConditionList": [
                    {
                      "searchType": "BUSINESS_TYPE",
                      "searchData": "JOIN_B2B"
                    },
                    {
                      "searchData": order_no,
                      "searchType": "THIRD_ORDER_NO"
                    }
                  ],
                  "queryScaleList": [
                    "MAIN","DETAIL"
                  ]
                }
            heards = {"Content-Type": "application/json;charset=UTF-8", "userId": self.user_id}
            res = requests.post(url=url, data=json.dumps(requst_data), headers=heards, verify=False)
            if res.status_code == 200:
                res_json = res.json()
                return res_json
            else:
                logger.error(f"获取订单{order_no}信息失败,返回状态码：{res.status_code}")
                return {}
        except Exception as e:
            logger.error(f"获取订单{order_no}信息异常:{e}")
            raise e


if __name__ == "__main__":
    api = ApiData()
    result = api.get_commodity_list("JM0067")
    print(json.dumps(result))
