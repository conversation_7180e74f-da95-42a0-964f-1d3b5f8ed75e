# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/24 10:47
@Auth ： 逗逗的小老鼠
@File ：main.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from structure.iteration_test.purchase_platform.test_case_data import TestCaseData
from structure.iteration_test.purchase_platform.test_case_result import TestCaseResult
if __name__ == "__main__":
    test_case_data = TestCaseData()
    case_list  = test_case_data.test_case_value_create(test_case_no=1)
    test = TestCaseResult()
    result = test.test_case_run_result(case_list)
    print(result)
