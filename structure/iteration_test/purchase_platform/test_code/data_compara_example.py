# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/18 09:30
@Auth ： 逗逗的小老鼠
@File ：data_compara_example.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：数据比对工具使用示例
"""
from lib.get_log import logger, exception
from structure.iteration_test.purchase_platform.data_compara import DataCompara
import json


@exception(logger)
def example_simple_comparison():
    """
    简单数据比对示例
    """
    # 两个简单的数据源
    data1 = {"name": "张三", "age": 30, "score": 85.5}
    data2 = {"name": "张三", "age": "30", "score": 85.50}

    # 创建比对工具实例，默认配置会自动处理类型差异
    compara = DataCompara(data1, data2)

    # 执行比对
    result = compara.data_compara()

    # 获取比对结果摘要
    summary = compara.get_summary()

    print("简单比对结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print("\n比对摘要:")
    print(json.dumps(summary, ensure_ascii=False, indent=2))


@exception(logger)
def example_field_mapping_comparison():
    """
    使用字段映射的数据比对示例
    """
    # 两个不同结构的数据源
    data1 = {
        "user": {
            "userName": "张三",
            "userAge": 30,
            "userScore": 85.5
        }
    }

    data2 = {
        "customer": {
            "name": "张三",
            "age": "30",
            "points": 85.50
        }
    }

    # 定义字段映射关系
    field_mappings = [
        {
            "source1_field": "user.userName",
            "source2_field": "customer.name",
            "field_type": "string",
            "field_desc": "用户姓名"
        },
        {
            "source1_field": "user.userAge",
            "source2_field": "customer.age",
            "field_type": "int",
            "field_desc": "用户年龄"
        },
        {
            "source1_field": "user.userScore",
            "source2_field": "customer.points",
            "field_type": "float",
            "field_desc": "用户积分"
        }
    ]

    # 创建比对工具实例，使用字段映射
    compara = DataCompara(data1, data2, field_mappings=field_mappings)

    # 执行比对
    result = compara.data_compara()

    # 获取比对结果摘要
    summary = compara.get_summary()

    print("\n字段映射比对结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print("\n比对摘要:")
    print(json.dumps(summary, ensure_ascii=False, indent=2))


@exception(logger)
def example_list_comparison_with_anchor():
    """
    使用数据锚点比对列表数据示例
    """
    # 两个包含列表的数据源
    data1 = {
        "order": {
            "orderNo": "ORD001",
            "items": [
                {"id": "1", "name": "商品A", "price": 100.0, "quantity": 2},
                {"id": "2", "name": "商品B", "price": 50.0, "quantity": 1},
                {"id": "3", "name": "商品C", "price": 200.0, "quantity": 3}
            ]
        }
    }

    data2 = {
        "order": {
            "orderNo": "ORD001",
            "items": [
                {"id": "2", "name": "商品B", "price": "50", "quantity": 1},
                {"id": "1", "name": "商品A", "price": "100", "quantity": 2},
                {"id": "3", "name": "商品C", "price": "200", "quantity": 4}  # 数量不同
            ]
        }
    }

    # 定义字段映射关系
    field_mappings = [
        {
            "source1_field": "order.orderNo",
            "source2_field": "order.orderNo",
            "field_type": "string",
            "field_desc": "订单号"
        },
        {
            "source1_field": "order.items",
            "source2_field": "order.items",
            "field_type": "list",
            "field_desc": "订单商品列表"
        }
    ]

    # 定义数据锚点字段，用于匹配列表项
    anchor_fields = ["id"]

    # 创建比对工具实例，使用字段映射和数据锚点
    compara = DataCompara(data1, data2, field_mappings=field_mappings, anchor_fields=anchor_fields)

    # 执行比对
    result = compara.data_compara()

    # 获取比对结果摘要
    summary = compara.get_summary()

    print("\n列表数据锚点比对结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print("\n比对摘要:")
    print(json.dumps(summary, ensure_ascii=False, indent=2))


@exception(logger)
def example_enum_mapping():
    """
    使用枚举值映射的数据比对示例
    """
    # 两个使用不同枚举值的数据源
    data1 = {
        "order": {
            "orderNo": "ORD001",
            "status": "1",  # 1表示已支付
            "paymentMethod": "WECHAT"
        }
    }

    data2 = {
        "order": {
            "orderNo": "ORD001",
            "status": "PAID",  # PAID表示已支付
            "paymentMethod": "WX"
        }
    }

    # 定义字段映射关系
    field_mappings = [
        {
            "source1_field": "order.orderNo",
            "source2_field": "order.orderNo",
            "field_type": "string",
            "field_desc": "订单号"
        },
        {
            "source1_field": "order.status",
            "source2_field": "order.status",
            "field_type": "string",
            "field_desc": "订单状态"
        },
        {
            "source1_field": "order.paymentMethod",
            "source2_field": "order.paymentMethod",
            "field_type": "string",
            "field_desc": "支付方式"
        }
    ]

    # 定义枚举值映射
    enum_mappings = {
        "order.status": {
            "source1": {
                "1": "PAID",  # 将源1中的"1"映射为"PAID"
                "2": "SHIPPED",
                "3": "COMPLETED"
            },
            "source2": {
                "PAID": "PAID",  # 源2中的值保持不变
                "SHIPPED": "SHIPPED",
                "COMPLETED": "COMPLETED"
            }
        },
        "order.paymentMethod": {
            "source1": {
                "WECHAT": "WX",  # 将源1中的"WECHAT"映射为"WX"
                "ALIPAY": "ALI"
            },
            "source2": {
                "WX": "WX",  # 源2中的值保持不变
                "ALI": "ALI"
            }
        }
    }

    # 创建比对工具实例，使用字段映射和枚举值映射
    compara = DataCompara(data1, data2, field_mappings=field_mappings, enum_mappings=enum_mappings)

    # 执行比对
    result = compara.data_compara()

    # 获取比对结果摘要
    summary = compara.get_summary()

    print("\n枚举值映射比对结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print("\n比对摘要:")
    print(json.dumps(summary, ensure_ascii=False, indent=2))


@exception(logger)
def example_jsonpath_comparison():
    """
    使用JSONPath表达式的数据比对示例
    """
    # 两个复杂嵌套的数据源
    data1 = {
        "orders": [
            {
                "id": "1",
                "customer": {"name": "张三", "level": "VIP"},
                "items": [
                    {"productId": "A1", "quantity": 2, "price": 100.0},
                    {"productId": "B1", "quantity": 1, "price": 50.0}
                ]
            },
            {
                "id": "2",
                "customer": {"name": "李四", "level": "NORMAL"},
                "items": [
                    {"productId": "C1", "quantity": 3, "price": 200.0}
                ]
            }
        ]
    }

    data2 = {
        "orderList": [
            {
                "orderId": "1",
                "user": {"userName": "张三", "userLevel": "VIP"},
                "products": [
                    {"id": "A1", "qty": 2, "unitPrice": 100.0},
                    {"id": "B1", "qty": 1, "unitPrice": 50.0}
                ]
            },
            {
                "orderId": "2",
                "user": {"userName": "李四", "userLevel": "NORMAL"},
                "products": [
                    {"id": "C1", "qty": 3, "unitPrice": 200.0}
                ]
            }
        ]
    }

    # 定义字段映射关系，使用JSONPath表达式
    field_mappings = [
        {
            "source1_field": "$.orders[?(@.id=='1')].customer.name",
            "source2_field": "$.orderList[?(@.orderId=='1')].user.userName",
            "field_type": "string",
            "field_desc": "订单1的客户名称"
        },
        {
            "source1_field": "$.orders[?(@.id=='1')].customer.level",
            "source2_field": "$.orderList[?(@.orderId=='1')].user.userLevel",
            "field_type": "string",
            "field_desc": "订单1的客户等级"
        },
        {
            "source1_field": "$.orders[?(@.id=='1')].items[?(@.productId=='A1')].price",
            "source2_field": "$.orderList[?(@.orderId=='1')].products[?(@.id=='A1')].unitPrice",
            "field_type": "float",
            "field_desc": "订单1中商品A1的价格"
        }
    ]

    # 创建比对工具实例，使用JSONPath表达式的字段映射
    compara = DataCompara(data1, data2, field_mappings=field_mappings)

    # 执行比对
    result = compara.data_compara()

    # 获取比对结果摘要
    summary = compara.get_summary()

    print("\nJSONPath表达式比对结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    print("\n比对摘要:")
    print(json.dumps(summary, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    # 运行示例
    example_simple_comparison()
    example_field_mapping_comparison()
    example_list_comparison_with_anchor()
    example_enum_mapping()
    example_jsonpath_comparison()
