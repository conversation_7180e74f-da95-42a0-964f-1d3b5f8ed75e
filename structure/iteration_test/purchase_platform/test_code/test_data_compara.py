# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/18 10:00
@Auth ： 逗逗的小老鼠
@File ：test_data_compara.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：数据比对工具测试
"""
import unittest
from structure.iteration_test.purchase_platform.data_compara import DataCompara


class TestDataCompara(unittest.TestCase):
    """
    数据比对工具测试类
    """

    def test_simple_comparison(self):
        """测试简单数据比对"""
        data1 = {"name": "张三", "age": 30, "score": 85.5}
        data2 = {"name": "张三", "age": 30, "score": 85.5}

        compara = DataCompara(data1, data2)
        result = compara._compare_values(data1, data2)

        self.assertTrue(result["is_equal"])

    def test_type_conversion(self):
        """测试类型转换"""
        data1 = {"name": "张三", "age": 30, "score": 85.5}
        data2 = {"name": "张三", "age": "30", "score": "85.5"}

        # 测试单个值的类型转换
        compara = DataCompara({}, {}, config={"ignore_data_type_changes": True, "ignore_numeric_type_changes": True})

        # 测试数值类型转换
        result1 = compara._compare_values(30, "30")
        self.assertTrue(result1["is_equal"], "整数和字符串整数应该相等")

        result2 = compara._compare_values(85.5, "85.5")
        self.assertTrue(result2["is_equal"], "浮点数和字符串浮点数应该相等")

    def test_field_mapping(self):
        """测试字段映射"""
        data1 = {"user": {"name": "张三", "age": 30}}
        data2 = {"customer": {"fullName": "张三", "years": 30}}

        field_mappings = [
            {
                "source1_field": "user.name",
                "source2_field": "customer.fullName",
                "field_type": "string",
                "field_desc": "用户名"
            },
            {
                "source1_field": "user.age",
                "source2_field": "customer.years",
                "field_type": "int",
                "field_desc": "年龄"
            }
        ]

        compara = DataCompara(data1, data2, field_mappings=field_mappings)
        results = compara.data_compara()
        summary = compara.get_summary()

        self.assertEqual(len(results), 2)
        self.assertTrue(summary["is_equal"])

    def test_list_comparison_with_anchor(self):
        """测试带锚点的列表比对"""
        data1 = {
            "items": [
                {"id": "1", "name": "商品A", "price": 100},
                {"id": "2", "name": "商品B", "price": 200}
            ]
        }

        data2 = {
            "items": [
                {"id": "2", "name": "商品B", "price": 200},
                {"id": "1", "name": "商品A", "price": 100}
            ]
        }

        field_mappings = [
            {
                "source1_field": "items",
                "source2_field": "items",
                "field_type": "list",
                "field_desc": "商品列表"
            }
        ]

        # 使用id作为锚点字段
        anchor_fields = ["id"]

        compara = DataCompara(data1, data2, field_mappings=field_mappings, anchor_fields=anchor_fields)
        results = compara.data_compara()
        summary = compara.get_summary()

        self.assertTrue(summary["is_equal"])

    def test_enum_mapping(self):
        """测试枚举值映射"""
        data1 = {"status": "1"}  # 1表示已支付
        data2 = {"status": "PAID"}  # PAID表示已支付

        field_mappings = [
            {
                "source1_field": "status",
                "source2_field": "status",
                "field_type": "string",
                "field_desc": "状态"
            }
        ]

        enum_mappings = {
            "status": {
                "source1": {
                    "1": "PAID",
                    "2": "SHIPPED"
                },
                "source2": {
                    "PAID": "PAID",
                    "SHIPPED": "SHIPPED"
                }
            }
        }

        compara = DataCompara(data1, data2, field_mappings=field_mappings, enum_mappings=enum_mappings)
        results = compara.data_compara()
        summary = compara.get_summary()

        self.assertTrue(summary["is_equal"])

    def test_jsonpath_expression(self):
        """测试JSONPath表达式"""
        data1 = {
            "orders": [
                {"id": "1", "total": 100},
                {"id": "2", "total": 200}
            ]
        }

        data2 = {
            "orderList": [
                {"orderId": "1", "amount": 100},
                {"orderId": "2", "amount": 200}
            ]
        }

        field_mappings = [
            {
                "source1_field": "$.orders[?(@.id=='1')].total",
                "source2_field": "$.orderList[?(@.orderId=='1')].amount",
                "field_type": "int",
                "field_desc": "订单1金额"
            }
        ]

        compara = DataCompara(data1, data2, field_mappings=field_mappings)
        results = compara.data_compara()
        summary = compara.get_summary()

        self.assertTrue(summary["is_equal"])

    def test_numeric_type_conversion(self):
        """测试数值类型转换"""
        compara = DataCompara({}, {})

        # 整数转换
        self.assertEqual(compara._convert_type("123", "int"), 123)
        self.assertEqual(compara._convert_type(123.5, "int"), 123)

        # 浮点数转换
        self.assertEqual(compara._convert_type("123.45", "float"), 123.45)
        self.assertEqual(compara._convert_type(123, "float"), 123.0)

        # 空字符串转换
        self.assertEqual(compara._convert_type("", "int"), 0)
        self.assertEqual(compara._convert_type("", "float"), 0.0)

    def test_similarity_calculation(self):
        """测试相似度计算"""
        # 字符串相似度
        compara = DataCompara({}, {}, config={"ignore_data_type_changes": True})

        # 使用字符串比较模式
        result = compara._compare_values("hello world", "hello word")
        self.assertFalse(result["is_equal"])
        # 字符串相似度应该很高，但不相等
        self.assertGreater(result["similarity"], 0.9)

        # 列表相似度
        result = compara._compare_values([1, 2, 3, 4], [1, 2, 3, 5])
        self.assertFalse(result["is_equal"])
        self.assertEqual(result["similarity"], 0.75)  # 3/4 = 0.75

        # 字典相似度
        result = compara._compare_values({"a": 1, "b": 2, "c": 3}, {"a": 1, "b": 2, "d": 4})
        self.assertFalse(result["is_equal"])
        # 共有键a和b，值相等的有a和b，总共有4个不同的键(a,b,c,d)，所以相似度是2/4=0.5
        self.assertEqual(result["similarity"], 0.5)


if __name__ == "__main__":
    unittest.main()
