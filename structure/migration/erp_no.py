# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/12 10:04
@Auth ： 逗逗的小老鼠
@File ：erp_no.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from lib.db_conf import db_yx_dscloud
from decimal import Decimal
from lib.deal_text import text_find,text_write



def test_erp_np():
    try:
        result=[]
        sql=f"""SELECT third_order_no,third_platform_code FROM order_info WHERE bill_time>="2024-01-10 00:00:00" AND bill_time<="2024-01-12 00:00:00" AND erp_state=100 AND third_platform_code !=43 AND migration_order_no IS NULL"""
        db_result=db_yx_dscloud(sql)
        db_data=db_result['data']
        i=0
        for item in db_data:
            third_order_no=item['third_order_no']
            third_platform_code=item['third_platform_code']
            find_count=text_find("erp_list.txt",third_order_no)
            i=i+1
            if find_count>0:
                print(i)
            else:
                print(item)
                text_write("no_bill_list.txt",item)
                result.append(item)
        return result
    except Exception as e:
        raise e
