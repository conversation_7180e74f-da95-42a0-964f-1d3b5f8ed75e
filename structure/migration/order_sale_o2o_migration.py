# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/3 15:29
@Auth ： 逗逗的小老鼠
@File ：order_sale_o2o_migration.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

from lib.get_log import logger,exception
from lib.db_conf import db_yx_middle_order,db_yx_dscloud,db_yn_oms,db_yx_dev_dsclound
from decimal import Decimal
"""
    迁移订单基本信息比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_o2o_base_info(body):
    "迁移订单基本信息比对"
    try:
        result=[]
        start_time=body['start_time']
        end_time=body['end_time']
        store_list=body['store_list']
        # com_num=body['com_num']
        # 雨诺基本信息查询
        yn_base_info_sql=f"""
            SELECT
	            ORDERCODE,ORDERINDEX,ORDERFROM,STORECODE,ISACCOUNTS,MEMBERSHIPCODE,BUYERNAME,ORDERTYPE,OTCPIC,ORDERSTATUS,TRANTYPE,RECEIVEADDRESS,RECEIVERNAME,RECEIVEPHONE,RECEIVETEL,TAKEORDERTIME,CREATETIME,ACCOUNTTIME,PICKINGTIME,SENDTIME,ISOTC,COMMITTIME 
            FROM
                order_std 
            WHERE
                INNERSTATUS = 1 
                AND CREATETIME >= "{start_time}" 
                AND CREATETIME <= "{end_time}" 
                AND STORECODE IN ({store_list})
                AND ORDERFROM != '104'
                AND ORDERFROM != '108'
                AND ORDERFROM != '12'
                AND ORDERFROM != '4'
                AND ORDERFROM != '111' 
        """
        yn_base_info_result=db_yn_oms(yn_base_info_sql)
        # 雨诺订单信息结果
        yn_base_info_data=yn_base_info_result['data']
        # 雨诺订单信息数量
        yn_base_info_count=len(yn_base_info_data)
        # 心云订单SQL
        yx_base_info_sql=f"""
            SELECT
                info.third_order_no AS 'third_order_no',
                info.third_platform_code AS 'third_platform_code',
                info.organization_code AS 'organization_code',
                info.order_state AS 'order_state',
                info.erp_state AS 'erp_state',
                info.accept_time AS 'accept_time',
                info.pick_time AS 'pick_time',
                info.receiver_lat AS 'receiver_lat',
                info.receiver_lng AS 'receiver_lng',
                info.complete_time AS 'complete_time',
                info.bill_time AS 'bill_time',
                info.created AS 'created_time',
                info.order_type AS 'order_type',
                info.day_num AS 'day_num',
                info.member_no AS 'member_no',
                info.migration_order_no AS 'migration_order_no',
                address.receiver_name AS 'receiver_name',
                address.receiver_telephone AS 'receiver_telephone',
                address.address AS 'address',
                address.province AS 'province',
                address.city AS 'city',
                address.district AS 'district' 
            FROM
                order_info info
                LEFT JOIN order_delivery_address address ON info.order_no = address.order_no 
            WHERE
                info.created >= "{start_time}" 
                AND info.created <= "{end_time}" 
                AND info.organization_code IN ( {store_list} ) 
                AND info.service_mode = 'o2o'
                AND info.third_platform_code !='43'
        """
        yx_base_info_result=db_yx_dev_dsclound(yx_base_info_sql)
        # 心云订单信息结果
        yx_base_info_data=yx_base_info_result['data']
        # 心云订单信息数量
        yx_base_info_count=len(yx_base_info_data)
        if yx_base_info_count==yn_base_info_count:
            for yn_item in yn_base_info_data:
                # 订单号
                ORDERCODE=yn_item['ORDERCODE']
                # 订单流水号
                ORDERINDEX=yn_item['ORDERINDEX']
                # 订单来源
                ORDERFROM=yn_item['ORDERFROM']
                # 门店编码
                STORECODE=yn_item['STORECODE']
                # 购买人名称
                BUYERNAME=yn_item['BUYERNAME']
                # 下单时间
                CREATETIME=yn_item['CREATETIME']
                # 会员号
                MEMBERSHIPCODE=yn_item['MEMBERSHIPCODE']
                # 订单类型
                ORDERTYPE=yn_item['ORDERTYPE']
                # 订单状态
                ORDERSTATUS=yn_item['ORDERSTATUS']
                # 配送方式
                TRANTYPE=yn_item['TRANTYPE']
                # 收货人地址
                RECEIVEADDRESS=yn_item['RECEIVEADDRESS']
                # 收货人姓名
                RECEIVERNAME=yn_item['RECEIVERNAME']
                # 收货人电话
                RECEIVEPHONE=yn_item['RECEIVEPHONE']
                # 门店接单时间
                TAKEORDERTIME=yn_item['TAKEORDERTIME']
                # 下账时间
                ACCOUNTTIME=yn_item['ACCOUNTTIME']
                # 拣货时间
                PICKINGTIME=yn_item['PICKINGTIME']
                # 发货时间
                SENDTIME=yn_item['SENDTIME']
                # 交易完成时间
                COMMITTIME=yn_item['COMMITTIME']
                # 下账状态
                ISACCOUNT=yn_item['ISACCOUNTS']
                # 是否处方单
                ISOTC=yn_item['ISOTC']
                # 处方单地址
                OTCPIC=yn_item['OTCPIC']
                for xy_item in yx_base_info_data:
                    # 平台订单号
                    third_order_no=xy_item['third_order_no']
                    if ORDERCODE==third_order_no:
                        # 平台订单号
                        third_platform_code=xy_item['third_platform_code']
                        # 所属机构
                        organization_code=xy_item['organization_code']
                        # 订单状态
                        order_state=xy_item['order_state']
                        # 下账状态
                        erp_state=xy_item['erp_state']
                        # 接单时间
                        accept_time=xy_item['accept_time']
                        # 下单时间
                        created_time=xy_item['created_time']
                        # 拣货时间
                        pick_time=xy_item['pick_time']
                        # 完成时间
                        complete_time=xy_item['complete_time']
                        # 下账时间
                        bill_time=xy_item['bill_time']
                        # 会员号
                        member_no=xy_item['member_no']
                        # 订单类型
                        order_type=xy_item['order_type']
                        # 每日流水号
                        day_num=xy_item['day_num']
                        # 迁移订单号
                        migration_order_no=xy_item['migration_order_no']
                        # 收货人
                        receiver_name=xy_item['receiver_name']
                        # 收货人电话
                        receiver_telephone=xy_item['receiver_telephone']
                        # 收货人地址
                        address=xy_item['address']
                        if STORECODE!=organization_code:
                            err_msg={"order_code":ORDERCODE,"error_msg":f"门店编码不一致，心云:{organization_code}，雨诺：{STORECODE}"}
                            result.append(err_msg)
                        if ORDERINDEX!=day_num:
                            err_msg = {"order_code":ORDERCODE,"error_msg":f"每日流水号不一致，心云:{day_num}，雨诺：{ORDERINDEX}"}
                            result.append(err_msg)
                        if migration_order_no!=ORDERCODE:
                            err_msg = {"order_code":ORDERCODE,"error_msg":f"迁移订单号不一致，心云:{migration_order_no}，雨诺：{ORDERCODE}"}
                            result.append(err_msg)
                        if str(created_time) !=str(CREATETIME):
                            err_msg = {"order_code":ORDERCODE,"error_msg":f"下单时间不一致，心云:{created_time}，雨诺：{CREATETIME}"}
                            result.append(err_msg)
                        if str(complete_time)!=str(COMMITTIME):
                            err_msg = {"order_code": ORDERCODE,
                                       "error_msg": f"完成时间不一致，心云:{complete_time}，雨诺：{COMMITTIME}"}
                            result.append(err_msg)
                        if str(bill_time)!=str(ACCOUNTTIME):
                            err_msg = {"order_code": ORDERCODE,
                                       "error_msg": f"下账时间不一致，心云:{bill_time}，雨诺：{ACCOUNTTIME}"}
                            result.append(err_msg)
                        if receiver_name!=RECEIVERNAME:
                            err_msg = {"order_code": ORDERCODE,
                                       "error_msg": f"收货人不一致，心云:{receiver_name}，雨诺：{RECEIVERNAME}"}
                            result.append(err_msg)
                        if str(member_no) != str(MEMBERSHIPCODE):
                            err_msg = {"order_code": ORDERCODE,
                                       "error_msg": f"会员卡号不一致，心云:{member_no}，雨诺：{MEMBERSHIPCODE}"}
                            result.append(err_msg)
                        # if receiver_telephone!= RECEIVEPHONE:
                        #     err_msg = {"order_code": ORDERCODE,
                        #                "error_msg": f"收货电话不一致，心云:{receiver_telephone}，雨诺：{RECEIVEPHONE}"}
                        #     result.append(err_msg)
                        if address !=RECEIVEADDRESS:
                            err_msg = {"order_code": ORDERCODE,
                                       "error_msg": f"收货地址不一致，心云:{receiver_telephone}，雨诺：{RECEIVEPHONE}"}
                            result.append(err_msg)
                        order_state_result=order_sale_o2o_state(ORDERSTATUS,order_state)
                        if order_state_result==0:
                            err_msg = {"order_code": ORDERCODE,
                                       "error_msg": f"订单状态不一致，心云:{order_state}，雨诺：{ORDERSTATUS}"}
                            result.append(err_msg)
                        order_erp_state_result=order_sale_o2o_erp_state(ISACCOUNT,erp_state)
                        if order_erp_state_result==0:
                            err_msg = {"order_code": ORDERCODE,
                                       "error_msg": f"订单下账状态不一致，心云:{erp_state}，雨诺：{ISACCOUNT}"}
                            result.append(err_msg)
                        if ISOTC==1:
                            if order_type!=2:
                                err_msg = {"order_code": ORDERCODE,
                                           "error_msg": f"处方单类型错误，心云:{order_type}，雨诺：{ISOTC}"}
                                result.append(err_msg)
        else:
            result_msg={"error_msg":f"订单信息数量不一致，心云:{yx_base_info_count}，雨诺：{yn_base_info_count}"}
            result.append(result_msg)
        return result
    except Exception as e:
        raise e


"""
    迁移订单金额及下账信息比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
def order_sale_o2o_amount(body):
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        store_list = body['store_list']
        # com_num = body['com_num']
        yn_sql=f"""
            SELECT
                ORDERCODE,
                ORDERFROM,
                ISSELFDELIVERY,
                TRANSCOMPANY,
                POSTFEE,
                DISTANCEPOSTFEE,
                POITRANSCOUPON,
                PLATTRANSCOUPON,
                PACKAGEFEE,
                SERVICEFEE,
                PRODUCTSAMOUNT,
                COUPONSAMOUNT,
                COUPONAMOUNT_ORIGIN,
                AMOUNT,
                ORDERAMOUNT,
                ACCOUNTAMOUNT,
                PAYAMOUNT,
                REALPAYAMOUNT 
            FROM
                order_std 
            WHERE
                INNERSTATUS = 1 
                AND CREATETIME >= "{start_time}" 
                AND CREATETIME <= "{end_time}" 
                AND STORECODE IN ({store_list})
                AND ORDERFROM != '104'
                AND ORDERFROM != '108'
                AND ORDERFROM != '12'
                AND ORDERFROM != '4'
                AND ORDERFROM != '111'
        """
        yn_result=db_yn_oms(yn_sql)
        yn_data=yn_result['data']
        yn_count=len(yn_data)
        xy_sql=f"""
            SELECT
                info.third_order_no AS 'third_order_no',
                info.third_platform_code AS 'third_platform_code',
                info.organization_code AS 'organization_code',
                pay.buyer_actual_amount AS 'buyer_actual_amount',
                pay.platform_pack_fee AS 'platform_pack_fee',
                pay.brokerage_amount AS 'brokerage_amount',
                pay.merchant_total_discount_sum_not_delivery_fee AS 'merchant_total_discount_sum_not_delivery_fee',
                pay.total_amount AS 'total_amount',
                pay.merchant_actual_amount AS 'merchant_actual_amount',
                pay.delivery_fee AS 'delivery_fee',
                pay.merchant_delivery_fee_discount AS 'merchant_delivery_fee_discount',
                pay.platform_delivery_fee_discount AS 'platform_delivery_fee_discount',
                pay.platform_discount AS 'platform_discount',
                pay.total_discount AS 'total_discount',
                pay.buyer_cod_service_fee AS 'buyer_cod_service_fee',
                pay.seller_cod_service_fee AS 'seller_cod_service_fee',
                pay.platform_fee_collection AS 'platform_fee_collection',
                pay.manual_fix_amount AS 'manual_fix_amount',
                pay.different_amount AS 'different_amount',
                bill.bill_total_amount AS 'bill_total_amount',
                bill.bill_commodity_amount AS 'bill_commodity_amount',
                bill.merchant_delivery_fee AS 'bill_merchant_delivery_fee' 
            FROM
                order_info info
                LEFT JOIN order_pay_info pay ON info.order_no = pay.order_no
                LEFT JOIN erp_bill_info bill ON info.order_no = bill.order_no 
            WHERE
                info.created >= "{start_time}" 
                AND info.created <= "{end_time}" 
                AND info.organization_code IN ( {store_list} ) 
                AND info.service_mode = 'o2o'
                AND info.third_platform_code !='43'
        """
        xy_result=db_yx_dev_dsclound(xy_sql)
        xy_data=xy_result['data']
        xy_count=len(xy_data)
        print(f"心云：{xy_count},雨诺：{yn_count}")
        if yn_count==xy_count:
            for yn_item in yn_data:
                # 订单号
                order_no = yn_item['ORDERCODE']
                # 是否是门店配送
                ISSELFDELIVERY = yn_item['ISSELFDELIVERY']
                # 订单来源
                ORDERFROM=yn_item['ORDERFROM']
                # 快递公司
                TRANSCOMPANY = yn_item['TRANSCOMPANY']
                # 配送费
                POSTFEE = yn_item['POSTFEE']
                # 远距离配送费
                DISTANCEPOSTFEE = yn_item['DISTANCEPOSTFEE']
                # 商家承担运费优惠
                POITRANSCOUPON = yn_item['POITRANSCOUPON']
                # 平台承担运费优惠
                PLATTRANSCOUPON = yn_item['PLATTRANSCOUPON']
                # 包装费
                PACKAGEFEE = yn_item['PACKAGEFEE']
                # 美团平台包装费为0时，赋值为0.5
                if ORDERFROM=='1' and PACKAGEFEE==0:
                    PACKAGEFEE=Decimal(0.5)
                # 服务费
                SERVICEFEE = yn_item['SERVICEFEE']
                # 商品总额
                PRODUCTSAMOUNT = yn_item['PRODUCTSAMOUNT']
                # 优惠金额(商家承担的货品优惠金额)
                COUPONSAMOUNT = yn_item['COUPONSAMOUNT']
                # 原始优惠金额
                COUPONAMOUNT_ORIGIN = yn_item['COUPONAMOUNT_ORIGIN']
                # 订单总额，原价总额+运费等
                # if ORDERFROM=='1':
                #     AMOUNT = yn_item['ORDERAMOUNT']
                # else:
                AMOUNT = PRODUCTSAMOUNT+PACKAGEFEE+POSTFEE+DISTANCEPOSTFEE
                # 下账金额
                ACCOUNTAMOUNT = yn_item['ACCOUNTAMOUNT']
                # 用户实际付款金额
                PAYAMOUNT = yn_item['PAYAMOUNT']
                #  订单实付金额（包含优惠中平台承担部分）
                REALPAYAMOUNT = yn_item['REALPAYAMOUNT']
                for xy_item in xy_data:
                    # 订单号
                    third_order_no = xy_item['third_order_no']
                    if order_no == third_order_no:
                        # 客户实付
                        buyer_actual_amount = xy_item['buyer_actual_amount']
                        # 平台打包费
                        platform_pack_fee = xy_item['platform_pack_fee']
                        # 交易佣金
                        brokerage_amount = xy_item['brokerage_amount']
                        # 商家商品总优惠
                        merchant_total_discount_sum_not_delivery_fee = xy_item[
                            'merchant_total_discount_sum_not_delivery_fee']
                        # 商品总金额
                        total_amount = xy_item['total_amount']
                        # 商家实收
                        merchant_actual_amount = xy_item['merchant_actual_amount']
                        # 配送费
                        delivery_fee = xy_item['delivery_fee']
                        # 商家配送费优惠金额
                        merchant_delivery_fee_discount = xy_item['merchant_delivery_fee_discount']
                        # 平台配送费优惠金额
                        platform_delivery_fee_discount = xy_item['platform_delivery_fee_discount']
                        # 平台优惠
                        platform_discount = xy_item['platform_discount']
                        # 订单总优惠
                        total_discount = xy_item['total_discount']
                        # 买家到付服务费
                        buyer_cod_service_fee = xy_item['buyer_cod_service_fee']
                        # 卖家到付服务费
                        seller_cod_service_fee = xy_item['seller_cod_service_fee']
                        # 代收平台费
                        platform_fee_collection = xy_item['platform_fee_collection']
                        # 手工调整金额
                        manual_fix_amount = xy_item['manual_fix_amount']
                        # 差异金额
                        different_amount = xy_item['different_amount']
                        # 下账总金额
                        bill_total_amount = xy_item['bill_total_amount']
                        # 下账商品金额
                        bill_commodity_amount = xy_item['bill_commodity_amount']
                        # 商家配送费
                        bill_merchant_delivery_fee = xy_item['bill_merchant_delivery_fee']
                        # 服务费比对
                        if SERVICEFEE - brokerage_amount != 0:
                            err_msg = {"order_no": third_order_no,
                                       "msg": f"服务费数值不一致,雨诺：{SERVICEFEE}，心云：{brokerage_amount}"}
                            result.append(err_msg)
                        # 包装费比对
                        if PACKAGEFEE - platform_pack_fee != 0:
                            if platform_pack_fee-PACKAGEFEE!=0.5:
                                err_msg = {"order_no": third_order_no,
                                           "msg": f"包装费数值不一致,雨诺：{PACKAGEFEE}，心云：{platform_pack_fee}"}
                                result.append(err_msg)
                        # 用户实付比对
                        if PAYAMOUNT - buyer_actual_amount != 0:
                            err_msg = {"order_no": third_order_no,
                                       "msg": f"用户实付数值不一致,雨诺：{PAYAMOUNT}，心云：{buyer_actual_amount}"}
                            result.append(err_msg)
                        # 商品总金额比对
                        if PRODUCTSAMOUNT - total_amount != 0:
                            err_msg = {"order_no": third_order_no,
                                       "msg": f"商品总金额不一致,雨诺：{PRODUCTSAMOUNT}，心云：{total_amount}"}
                            result.append(err_msg)
                        # 商家商品优惠比对
                        if COUPONSAMOUNT - merchant_total_discount_sum_not_delivery_fee != 0:
                            err_msg = {"order_no": third_order_no,
                                       "msg": f"商家商品优惠金额不一致,雨诺：{COUPONSAMOUNT}，心云：{merchant_total_discount_sum_not_delivery_fee}"}
                            result.append(err_msg)
                        # 配送费比对
                        if POSTFEE + DISTANCEPOSTFEE - delivery_fee != 0:
                            err_msg = {"order_no": third_order_no,
                                       "msg": f"配送费金额不一致,雨诺：{POSTFEE}+{DISTANCEPOSTFEE}，心云：{delivery_fee}"}
                            result.append(err_msg)
                        # 商家承担运费优惠
                        if POITRANSCOUPON - merchant_delivery_fee_discount != 0:
                            err_msg = {"order_no": third_order_no,
                                       "msg": f"商家承担运费优惠金额不一致,雨诺：{POITRANSCOUPON}，心云：{merchant_delivery_fee_discount}"}
                            result.append(err_msg)
                        # 平台承担运费优惠
                        if PLATTRANSCOUPON - platform_delivery_fee_discount != 0:
                            err_msg = {"order_no": third_order_no,
                                       "msg": f"平台承担运费优惠金额不一致,雨诺：{PLATTRANSCOUPON}，心云：{platform_delivery_fee_discount}"}
                            result.append(err_msg)
                        # 订单总金额比对
                        if AMOUNT - total_discount - buyer_actual_amount != 0:
                            if PRODUCTSAMOUNT+PACKAGEFEE + POSTFEE + DISTANCEPOSTFEE-total_discount - buyer_actual_amount!=0:
                                err_msg = {"order_no": third_order_no,
                                           "msg": f"订单总金额不一致,雨诺：{AMOUNT}，心云：{total_discount}+{buyer_actual_amount}"}
                                result.append(err_msg)
                        # 平台商品优惠金额比对
                        if ((AMOUNT-PAYAMOUNT-POITRANSCOUPON-PLATTRANSCOUPON-COUPONSAMOUNT) - (platform_discount-platform_delivery_fee_discount) != 0) and platform_discount !=0:
                            err_msg = {"order_no": third_order_no,
                                       "msg": f"平台商品优惠金额不一致,雨诺：{AMOUNT}-{PAYAMOUNT}-{POITRANSCOUPON}-{PLATTRANSCOUPON}-{COUPONSAMOUNT}，心云：{platform_discount}-{platform_delivery_fee_discount}"}
                            result.append(err_msg)
                        # 订单优惠总金额比对
                        if total_discount - (AMOUNT - PAYAMOUNT) != 0:
                            if total_discount -POITRANSCOUPON -COUPONSAMOUNT-PLATTRANSCOUPON !=0:
                                err_msg = {"order_no": third_order_no,
                                           "msg": f"订单优惠总金额不一致,雨诺：{AMOUNT}-{PAYAMOUNT}，心云：{total_discount}"}
                                result.append(err_msg)
                        # 买家到付服务费、卖家到付服务费、代收平台费、手工调整金额、差异金额
                        if buyer_cod_service_fee != 0 or seller_cod_service_fee != 0 or platform_fee_collection != 0 or manual_fix_amount != 0 or different_amount != 0:
                            err_msg = {"order_no": third_order_no,
                                       "msg": f"心云中,买家到付服务费{buyer_cod_service_fee}、卖家到付服务费{seller_cod_service_fee}、代收平台费{platform_fee_collection}、手工调整金额{manual_fix_amount}、差异金额{different_amount}应为0"}
                            result.append(err_msg)
                        # 心云下账金额比对
                        if bill_total_amount - bill_commodity_amount - bill_merchant_delivery_fee != 0:
                            err_msg = {"order_no": third_order_no,
                                       "msg": f"心云下账金额不一致,下账总金额：{bill_total_amount}，下账商品金额：{bill_commodity_amount}，下账配送费：{bill_merchant_delivery_fee}"}
                            result.append(err_msg)
                        # 下账总金额比对
                        if ACCOUNTAMOUNT - bill_total_amount != 0:
                            err_msg = {"order_no": third_order_no,
                                       "msg": f"下账总金额比对不一致,雨诺：{ACCOUNTAMOUNT}，心云：{bill_total_amount}"}
                            result.append(err_msg)
                        if ISSELFDELIVERY =='1':
                            # 商家自配实收金额
                            merchant_actual_amount_yn = PAYAMOUNT - PACKAGEFEE - SERVICEFEE + AMOUNT - PAYAMOUNT - POITRANSCOUPON - COUPONSAMOUNT
                            if merchant_actual_amount - merchant_actual_amount_yn != 0:
                                err_msg = {"order_no": third_order_no,
                                           "msg": f"商家自配实收金额不一致,雨诺：{PAYAMOUNT}-{PACKAGEFEE}-{SERVICEFEE}+（{AMOUNT}-{PAYAMOUNT}-{POITRANSCOUPON}-{COUPONSAMOUNT}）+{PLATTRANSCOUPON}，心云：{merchant_actual_amount}"}
                                result.append(err_msg)
                            # 商家自配下账配送费
                            if bill_merchant_delivery_fee - (POSTFEE + DISTANCEPOSTFEE - POITRANSCOUPON) != 0:
                                err_msg = {"order_no": third_order_no,
                                           "msg": f"商家自配下账配送费金额不一致,雨诺：{POSTFEE}+{DISTANCEPOSTFEE}-{POITRANSCOUPON}，心云：{bill_merchant_delivery_fee}"}
                                result.append(err_msg)
                        else:
                            if TRANSCOMPANY == "1" or TRANSCOMPANY == "2" or TRANSCOMPANY == "4":
                                # 平台配送实收金额
                                merchant_actual_amount_yn = PAYAMOUNT - PACKAGEFEE - SERVICEFEE + (
                                            AMOUNT - PAYAMOUNT - POITRANSCOUPON - COUPONSAMOUNT) - POSTFEE
                                if merchant_actual_amount - merchant_actual_amount_yn != 0:
                                    err_msg = {"order_no": third_order_no,
                                               "msg": f"商家自配实收金额不一致,雨诺：{PAYAMOUNT}-{PACKAGEFEE}-{SERVICEFEE}+（{AMOUNT}-{PAYAMOUNT}-{POITRANSCOUPON}-{COUPONSAMOUNT}）-{POSTFEE}，心云：{merchant_actual_amount}"}
                                    result.append(err_msg)
                                # 下账商品总金额比对
                                if ACCOUNTAMOUNT - bill_commodity_amount != 0:
                                    err_msg = {"order_no": third_order_no,
                                               "msg": f"平台配送下账商品总金额不一致,雨诺：{ACCOUNTAMOUNT}，心云：{bill_commodity_amount}"}
                                    result.append(err_msg)
                                if bill_merchant_delivery_fee != 0:
                                    err_msg = {"order_no": third_order_no,
                                               "msg": f"平台配送下账配送费应为0,心云：{bill_merchant_delivery_fee}"}
                                    result.append(err_msg)
                            else:
                                # 商家自配实收金额
                                merchant_actual_amount_yn = PAYAMOUNT - PACKAGEFEE - SERVICEFEE + AMOUNT - PAYAMOUNT - POITRANSCOUPON - COUPONSAMOUNT
                                if merchant_actual_amount - merchant_actual_amount_yn != 0:
                                    err_msg = {"order_no": third_order_no,
                                               "msg": f"商家自配实收金额不一致,雨诺：{PAYAMOUNT}-{PACKAGEFEE}-{SERVICEFEE}+（{AMOUNT}-{PAYAMOUNT}-{POITRANSCOUPON}-{COUPONSAMOUNT}）+{PLATTRANSCOUPON}，心云：{merchant_actual_amount}"}
                                    result.append(err_msg)
                                # 商家自配下账配送费
                                if bill_merchant_delivery_fee - (POSTFEE + DISTANCEPOSTFEE - POITRANSCOUPON) != 0:
                                    err_msg = {"order_no": third_order_no,
                                               "msg": f"商家自配下账配送费金额不一致,雨诺：{POSTFEE}+{DISTANCEPOSTFEE}-{POITRANSCOUPON}，心云：{bill_merchant_delivery_fee}"}
                                    result.append(err_msg)


        else:
            err_msg = {"msg": f"心云与雨诺数量不一致,雨诺：{yn_count}，心云：{xy_count}"}
            result.append(err_msg)
        print(len(result))
        return result
    except Exception as e:
        raise e






"""
    迁移订单汇总金额比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
def order_sale_o2o_sum_amount(body):
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        store_list = body['store_list']
        yn_sql=f"""
            SELECT
                COUNT(*) AS 'count_num',
                SUM( PRODUCTSAMOUNT ) AS 'sum_productamount',
                SUM( POSTFEE+DISTANCEPOSTFEE ) AS 'sum_post',
                SUM( PAYAMOUNT ) AS 'sum_pay',
                SUM( PACKAGEFEE ) AS 'sum_pack',
                SUM( SERVICEFEE ) AS 'sum_service' 
            FROM
                order_std
            WHERE
                INNERSTATUS = 1 
                AND CREATETIME >= "{start_time}" 
                AND CREATETIME <= "{end_time}" 
                AND STORECODE IN ({store_list})
                AND ORDERFROM != '104'
                AND ORDERFROM != '108'
                AND ORDERFROM != '12'
                AND ORDERFROM != '4'
                AND ORDERFROM != '111'
        """
        yn_sum_result=db_yn_oms(yn_sql)
        yn_sum_data=yn_sum_result['data']
        xy_sql=f"""
            SELECT
                COUNT(*) AS 'count_num',
                SUM( pay.total_amount ) AS 'sum_productamount',
                SUM( pay.delivery_fee ) AS 'sum_post',
                SUM( pay.platform_pack_fee ) AS 'sum_pack',
                SUM( pay.brokerage_amount ) AS 'sum_service',
                SUM( pay.buyer_actual_amount ) AS 'sum_pay' 
            FROM
                order_info info
                LEFT JOIN order_pay_info pay ON info.order_no = pay.order_no 
            WHERE
                info.created >= "{start_time}" 
                AND info.created <= "{end_time}" 
                AND info.organization_code IN ( {store_list} ) 
                AND info.service_mode = 'o2o'
                AND info.third_platform_code != '43'
        """
        xy_sum_result=db_yx_dev_dsclound(xy_sql)
        xy_sum_data=xy_sum_result['data']
        for yn_sum_item in yn_sum_data:
            # 雨诺订单总数
            yn_count_num=yn_sum_item['count_num']
            # 雨诺商品总额
            yn_sum_productamount=yn_sum_item['sum_productamount']
            # 雨诺配送费总额
            yn_sum_post=yn_sum_item['sum_post']
            # 雨诺实付总额
            yn_sum_pay=yn_sum_item['sum_pay']
            # 雨诺包装费总额
            yn_sum_pack=yn_sum_item['sum_pack']
            # 雨诺服务费总额
            yn_sum_service=yn_sum_item['sum_service']
            for xy_sum_item in xy_sum_data:
                # 心云订单总数
                xy_count_num = xy_sum_item['count_num']
                # 心云商品总额
                xy_sum_productamount = xy_sum_item['sum_productamount']
                # 心云配送费总额
                xy_sum_post = xy_sum_item['sum_post']
                # 心云实付总额
                xy_sum_pay = xy_sum_item['sum_pay']
                # 心云包装费总额
                xy_sum_pack = xy_sum_item['sum_pack']
                # 心云服务费总额
                xy_sum_service = xy_sum_item['sum_service']
                if yn_count_num-xy_count_num!=0:
                    err_msg={"msg":f"订单总数不一致，雨诺：{yn_count_num}，心云：{xy_count_num}"}
                    result.append(err_msg)
                if yn_sum_productamount-xy_sum_productamount!=0:
                    err_msg={"msg":f"商品总额不一致，雨诺：{yn_sum_productamount}，心云：{xy_sum_productamount}"}
                    result.append(err_msg)
                if yn_sum_post-xy_sum_post!=0:
                    err_msg={"msg":f"配送费总额不一致，雨诺：{yn_sum_post}，心云：{xy_sum_post}"}
                    result.append(err_msg)
                if yn_sum_pay-xy_sum_pay!=0:
                    err_msg={"msg":f"用户实付总额不一致，雨诺：{yn_sum_pay}，心云：{xy_sum_pay}"}
                    result.append(err_msg)
                if yn_sum_pack-xy_sum_pack!=0:
                    err_msg={"msg":f"包装费总额不一致，雨诺：{yn_sum_pack}，心云：{xy_sum_pack}"}
                    result.append(err_msg)
                if yn_sum_service-xy_sum_service!=0:
                    err_msg={"msg":f"服务费总额不一致，雨诺：{yn_sum_service}，心云：{xy_sum_service}"}
                    result.append(err_msg)
        return result
    except Exception as e:
        raise e


"""
    迁移订单详情汇总金额比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
def order_sale_o2o_sum_detail(body):
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        store_list = body['store_list']
        yn_sql=f"""
            SELECT
                od.ISGIFT AS 'is_gift',
                COUNT(*) AS 'count_num',
                SUM( od.GOODSPRICE ) AS 'sum_price',
                SUM( od.BUYCOUNT ) AS 'sum_buycount',
                SUM( od.DISCOUNTPRICE ) AS 'sum_disprice' 
            FROM
                order_std os
                LEFT JOIN order_detail od ON os.ORDERCODE = od.ORDERCODE 
            WHERE
                od.GOODSCODE NOT IN ( 'POSTFEE', 'PACKAGEFEE', 'SERVICEFEE' ) 
                AND os.INNERSTATUS = 1 
                AND os.CREATETIME >= "{start_time}" 
                AND os.CREATETIME <= "{end_time}" 
                AND os.STORECODE IN ({ store_list }) 
                AND os.ORDERFROM != '104'
                AND os.ORDERFROM != '108'
                AND os.ORDERFROM != '12'
                AND os.ORDERFROM != '4'
                AND os.ORDERFROM != '111'
            GROUP BY
                od.ISGIFT
        """
        yn_detail_result=db_yn_oms(yn_sql)
        yn_detail_data=yn_detail_result['data']
        xy_sql=f"""
            SELECT
                de.is_gift AS 'is_gift',
                COUNT(*) AS 'count_num',
                SUM( de.commodity_count ) AS 'sum_buycount',
                SUM( de.original_price ) AS 'sum_price',
                SUM( de.bill_price ) AS 'sum_disprice' 
            FROM
                order_info info
                LEFT JOIN order_detail de ON info.order_no = de.order_no 
            WHERE
                info.created >= "{start_time}" 
                AND info.created <= "{end_time}" 
                AND info.organization_code IN ( { store_list } ) 
                AND info.service_mode = 'o2o' 
                AND info.third_platform_code != '43'
            GROUP BY
                de.is_gift
        """
        xy_detail_result=db_yx_dscloud(xy_sql)
        xy_detail_data=xy_detail_result['data']
        for yn_detail_item in yn_detail_data:
            # 是否赠品
            yn_is_gift=yn_detail_item['is_gift']
            # 详情总数
            yn_count_num=yn_detail_item['count_num']
            # 购买总数
            yn_sum_buycount=yn_detail_item['sum_buycount']
            # 价格总额
            yn_sum_price=yn_detail_item['sum_price']
            # 折后价总额
            yn_sum_disprice=yn_detail_item['sum_disprice']
            for xy_detail_item in xy_detail_data:
                xy_is_gift=xy_detail_item['is_gift']
                if yn_is_gift==xy_is_gift:
                    # 详情总数
                    xy_count_num = xy_detail_item['count_num']
                    # 购买总数
                    xy_sum_buycount = xy_detail_item['sum_buycount']
                    # 价格总额
                    xy_sum_price = xy_detail_item['sum_price']
                    # 折后价总额
                    xy_sum_disprice = xy_detail_item['sum_disprice']
                    if xy_count_num-yn_count_num!=0:
                        err_msg = {"is_gift":xy_is_gift,"msg": f"订单数量不一致，雨诺：{yn_count_num}，心云：{xy_count_num}"}
                        result.append(err_msg)
                    if yn_sum_buycount-xy_sum_buycount!=0:
                        err_msg = {"is_gift":xy_is_gift,"msg": f"购买数量不一致，雨诺：{yn_sum_buycount}，心云：{xy_sum_buycount}"}
                        result.append(err_msg)
                    if yn_sum_price-xy_sum_price!=0:
                        err_msg = {"is_gift": xy_is_gift, "msg": f"价格总额不一致，雨诺：{yn_sum_price}，心云：{xy_sum_price}"}
                        result.append(err_msg)
                    if yn_sum_disprice-xy_sum_disprice!=0:
                        err_msg = {"is_gift": xy_is_gift, "msg": f"折后价总额不一致，雨诺：{yn_sum_disprice}，心云：{xy_sum_disprice}"}
                        result.append(err_msg)
        return result
    except Exception as e:
        raise e

"""
    迁移订单状态比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_o2o_state(yn_status,xy_status):
    "迁移订单状态比对"
    try:
        if yn_status=="37" or yn_status=="40" or yn_status=="45" or yn_status=="42" or yn_status=="54" or yn_status=="58":
            xy_status_value = 100
        else:
            xy_status_value = 102
        if xy_status==xy_status_value:
            result=1
        else:
            result=0
        return result
    except Exception as e:
        raise e


"""
    迁移订单下账状态比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_o2o_erp_state(yn_status,xy_status):
    "迁移订单下账状态比对"
    try:
        if  yn_status=="2" or yn_status=="3" :
            xy_status_value = 100
        elif yn_status=="1":
            xy_status_value = 30
        else:
            xy_status_value = 110
        if xy_status==xy_status_value:
            result=1
        else:
            result=0
        return result
    except Exception as e:
        raise e
