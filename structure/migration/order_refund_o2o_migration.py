# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/5 11:15
@Auth ： 逗逗的小老鼠
@File ：order_refund_o2o_migration.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.db_conf import db_yx_middle_order,db_yx_dscloud,db_yn_oms,db_yx_dev_dsclound


"""
    迁移退款单信息比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_o2o_refund_info(body):
    try:
        result=[]
        start_time = body['start_time']
        end_time = body['end_time']
        store_list = body['store_list']
        yn_sql=f"""
            SELECT
                re_std.ORDERCODE AS 'order_code',
                re_std.RETURNCODE AS 'return_code',
                re_std.POSTFEE AS 're_postfee',
                re_std.PACKAGEFEE AS 're_packgefee',
                re_std.PRODUCTSAMOUNT AS 're_productsamount',
                re_std.RETURNAMOUNT AS 're_returnamount',
                re_std.RETURNREASON AS 're_returnreason',
                re_std.RETURNSTATUS AS 're_returnstatus',
                re_std.CREATETIME AS 're_createtime',
                re_std.ACCOUNTTIME AS 're_accounttime',
                re_std.ISACCOUNTS AS 're_isaccounts',
                re_std.RETURNTYPE AS 're_returntype',
                re_std.CONFIRMDATE AS 're_coneirmdate',
                re_std.ISFULLREFUND AS 're_isfullrefund',
                std.TRANSCOMPANY AS 'TRANSCOMPANY'
            FROM
                returnorder_std re_std
                LEFT JOIN order_std std ON re_std.ORDERCODE = std.ORDERCODE 
            WHERE
                std.INNERSTATUS = 1 
                AND std.CREATETIME >= "{start_time}" 
                AND std.CREATETIME <= "{end_time}" 
                AND std.STORECODE IN ( {store_list} ) 
                AND std.ORDERFROM != '104' 
                AND std.ORDERFROM != '108' 
                AND std.ORDERFROM != '12' 
                AND std.ORDERFROM != '4' 
                AND std.ORDERFROM != '111'
        """
        print(yn_sql)
        xy_sql=f"""
            SELECT
                info.third_order_no AS 'third_order_no',
                refund.third_refund_no AS 'third_refund_no',
                refund.type AS 'refund_type',
                refund.state AS 'refund_state',
                refund.total_food_amount AS 'refund_total_food_amount',
                refund.total_amount AS 'refund_total_amount',
                refund.consumer_refund AS 'refund_consumer_refund',
                refund.shop_refund AS 'refund_shop_refund',
                refund.fee_refund AS 'refund_fee_refund',
                refund.platform_discount_refund AS 'refund_platform_discount_refund',
                refund.shop_discount_refund AS 'refund_shop_discount_refund',
                refund.platform_refund_delivery_fee AS 'refund_platform_refund_delivery_fee',
                refund.merchant_refund_post_fee AS 'refund_merchant_refund_post_fee',
                refund.platform_refund_pack_fee AS 'refund_platform_refund_pack_fee',
                refund.merchant_refund_pack_fee AS 'refund_merchant_refund_pack_fee',
                refund.detail_discount_amount AS 'refund_detail_discount_amount',
                refund.desc AS 'refund_desc',
                refund.platform_postage AS 'refund_platform_postage',
                refund.erp_state AS 'refund_erp_state',
                refund.bill_time AS 'refund_bill_time',
                refund.complete_time AS 'refund_complete_time',
                refund.refund_type AS 'refund_refund_type',
                refund.create_time AS 'refund_create_time',
                refund.refund_application_time AS 'refund_application_time',
                refund.bill_type AS 'refund_bill_type',
                erp.refund_merchant_total AS 'erp_refund_merchant_total',
                erp.refund_goods_total AS 'erp_refund_goods_total',
                erp.refund_post_fee AS 'erp_refund_post_fee' 
            FROM
                refund_order refund
                LEFT JOIN order_info info ON info.order_no = refund.order_no
                LEFT JOIN erp_refund_info erp ON refund.refund_no = erp.refund_no
            WHERE
                info.created >= "{start_time}" 
                AND info.created <= "{end_time}" 
                AND info.organization_code IN ( {store_list} ) 
                AND info.service_mode = 'o2o' 
                AND info.third_platform_code != '43'
        """
        print(xy_sql)
        yn_result=db_yn_oms(yn_sql)
        yn_data=yn_result['data']
        yn_count=len(yn_data)
        xy_result=db_yx_dev_dsclound(xy_sql)
        xy_data=xy_result['data']
        xy_count=len(xy_data)
        print(f"心云：{xy_count},雨诺：{yn_count}")
        if yn_count==xy_count:
            for yn_item in yn_data:
                # 订单号
                order_code=yn_item['order_code']
                # 快递公司
                TRANSCOMPANY=yn_item['TRANSCOMPANY']
                # 退款单号
                return_code=yn_item['return_code']
                # 退运费金额
                re_postfee=yn_item['re_postfee']
                # 退包装费金额
                re_packgefee=yn_item['re_packgefee']
                # 退款商品总金额
                re_productsamount=yn_item['re_productsamount']
                # 退单/下账金额
                re_returnamount=yn_item['re_returnamount']
                # 退货原因
                re_returnreason=yn_item['re_returnreason']
                # 退款状态
                re_returnstatus=yn_item['re_returnstatus']
                # 申请时间
                re_createtime=yn_item['re_createtime']
                # 下账时间
                re_accounttime=yn_item['re_accounttime']
                # 确认时间
                re_coneirmdate=yn_item['re_coneirmdate']
                # 是否已下帐
                re_isaccounts=yn_item['re_isaccounts']
                # 退款单类型
                re_returntype=yn_item['re_returntype']
                # 是否是整单退
                re_isfullrefund=yn_item['re_isfullrefund']
                for xy_item in xy_data:
                    third_order_no=xy_item['third_order_no']
                    third_refund_no=xy_item['third_refund_no']
                    if third_order_no==order_code and third_refund_no==return_code:
                        # 是否部分退款
                        refund_type=xy_item['refund_type']
                        # 退款状态
                        refund_state=xy_item['refund_state']
                        # 退款商品总金额
                        refund_total_food_amount=xy_item['refund_total_food_amount']
                        # 退款商品退用户金额
                        refund_total_amount=xy_item['refund_total_amount']
                        # 退买家总金额
                        refund_consumer_refund=xy_item['refund_consumer_refund']
                        # 商家退款总金额
                        refund_shop_refund=xy_item['refund_shop_refund']
                        # 退还佣金
                        refund_fee_refund=xy_item['refund_fee_refund']
                        # 退平台优惠
                        refund_platform_discount_refund=xy_item['refund_platform_discount_refund']
                        # 退还商家优惠
                        refund_shop_discount_refund=xy_item['refund_shop_discount_refund']
                        # 退平台配送费
                        refund_platform_refund_delivery_fee=xy_item['refund_platform_refund_delivery_fee']
                        # 退商家配送费
                        refund_merchant_refund_post_fee=xy_item['refund_merchant_refund_post_fee']
                        # 退平台包装费
                        refund_platform_refund_pack_fee=xy_item['refund_platform_refund_pack_fee']
                        # 退商家包装费
                        refund_merchant_refund_pack_fee=xy_item['refund_merchant_refund_pack_fee']
                        # 退商品明细优惠
                        refund_detail_discount_amount=xy_item['refund_detail_discount_amount']
                        # 退款描述
                        refund_desc=xy_item['refund_desc']
                        # 商家退还给平台补贴的金额(单位元)
                        refund_platform_postage=xy_item['refund_platform_postage']
                        # 退款单erp状态
                        refund_erp_state=xy_item['refund_erp_state']
                        # 下账时间
                        refund_bill_time=xy_item['refund_bill_time']
                        # 退款完成时间
                        refund_complete_time=xy_item['refund_complete_time']
                        # 退货类型
                        refund_refund_type=xy_item['refund_refund_type']
                        # 创建时间
                        refund_create_time=xy_item['refund_create_time']
                        # 退款申请时间
                        refund_application_time=xy_item['refund_application_time']
                        # 下账类型
                        refund_bill_type=xy_item['refund_bill_type']
                        # 下账总金额
                        erp_refund_merchant_total=xy_item['erp_refund_merchant_total']
                        # 下账商品总金额
                        erp_refund_goods_total=xy_item['erp_refund_goods_total']
                        # 下账商家配送费
                        erp_refund_post_fee=xy_item['erp_refund_post_fee']

                        # 退款包装费比对
                        if re_packgefee-refund_platform_refund_pack_fee!=0:
                            err_msg = {"order_code": order_code,"return_code":return_code,"error_msg": f"退款包装费不一致，心云:{refund_platform_refund_pack_fee}，雨诺：{re_packgefee}"}
                            result.append(err_msg)
                        # 退款商品总金额比对
                        if re_productsamount-refund_total_food_amount!=0:
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"退款商品总金额不一致，心云:{refund_total_food_amount}，雨诺：{re_productsamount}"}
                            result.append(err_msg)
                        #   退买家总金额
                        if refund_consumer_refund-re_returnamount-re_packgefee-re_postfee!=0:
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"退买家总金额不一致，心云:{refund_consumer_refund}，雨诺：{re_returnamount}+{re_packgefee}+{re_postfee}"}
                            result.append(err_msg)
                        # 退款商品退用户金额
                        if refund_total_amount-re_returnamount!=0:
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"退款商品退用户金额不一致，心云:{refund_total_amount}，雨诺：{re_returnamount}+{re_packgefee}+{re_postfee}"}
                            result.append(err_msg)
                        # 商家退款总金额
                        if refund_shop_refund-re_returnamount!=0:
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"商家退款总金额不一致，心云:{refund_total_amount}，雨诺：{re_returnamount}+{re_packgefee}+{re_postfee}"}
                            result.append(err_msg)
                        # 退还佣金/退还商家优惠/退还商家优惠/退商家包装费/退商品明细优惠/商家退还给平台补贴的金额
                        if refund_fee_refund!=0 or refund_platform_discount_refund!=0 or refund_shop_discount_refund!=0 or refund_merchant_refund_pack_fee!=0 or refund_detail_discount_amount!=0 or refund_platform_postage!=0:
                            err_msg={"order_code": order_code, "return_code": return_code,"error_msg":f"退还佣金：{refund_fee_refund}/退还商家优惠：{refund_platform_discount_refund}/退还商家优惠：{refund_shop_discount_refund}/退商家包装费：{refund_merchant_refund_pack_fee}/退商品明细优惠：{refund_detail_discount_amount}/商家退还给平台补贴的金额：{refund_platform_postage}应为0"}
                            result.append(err_msg)
                        # 退款描述
                        if re_returnreason !=refund_desc:
                            err_msg = {"order_code": order_code, "return_code": return_code,"error_msg": f"退款描述不一致，心云:【{refund_desc}】，雨诺：【{re_returnreason}】"}
                            result.append(err_msg)
                        # 创建时间
                        if str(re_createtime) != str(refund_create_time):
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"创建时间不一致，心云:【{refund_create_time}】，雨诺：【{re_createtime}】"}
                            result.append(err_msg)
                        # 退款申请时间
                        if str(re_createtime) != str(refund_application_time):
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"退款申请时间不一致，心云:【{refund_application_time}】，雨诺：【{re_createtime}】"}
                            result.append(err_msg)
                        # 退款完成时间
                        if str(refund_complete_time) != str(re_coneirmdate):
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"退款完成时间不一致，心云:【{refund_complete_time}】，雨诺：【{re_coneirmdate}】"}
                            result.append(err_msg)
                        # 下账时间
                        if str(refund_bill_time) != str(re_accounttime):
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"下账时间不一致，心云:【{refund_bill_time}】，雨诺：【{re_accounttime}】"}
                            result.append(err_msg)
                        # 是否整单退
                        refund_type_result=refund_order_o2o_isfull(refund_type,re_isfullrefund)
                        if refund_type_result==0:
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"是否整单退不一致，心云:【{refund_type}】，雨诺：【{re_isfullrefund}】"}
                            result.append(err_msg)
                        # 退款单状态
                        refund_state_result=refund_order_o2o_status(refund_state,re_returnstatus)
                        if refund_state_result==0:
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"退款单状态不一致，心云:【{refund_state}】，雨诺：【{re_returnstatus}】"}
                            result.append(err_msg)
                        # 下账状态
                        refund_erp_state_result = refund_order_o2o_erp_status(refund_erp_state, re_isaccounts)
                        if refund_erp_state_result == 0:
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"下账状态不一致，心云:【{refund_erp_state}】，雨诺：【{re_isaccounts}】"}
                            result.append(err_msg)
                        # 退货类型
                        refund_refund_type_result=refund_order_o2o_type(refund_refund_type,re_returntype)
                        if refund_refund_type_result==0:
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"退货类型不一致，心云:【{refund_refund_type}】，雨诺：【{re_returntype}】"}
                            result.append(err_msg)
                        # 下账类型
                        refund_refund_bill_type_result = refund_order_o2o_bill_type(refund_bill_type, re_returntype)
                        if refund_refund_bill_type_result == 0:
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"下账类型不一致，心云:【{refund_bill_type}】，雨诺：【{re_returntype}】"}
                            result.append(err_msg)
                        # 下账金额
                        if erp_refund_merchant_total-erp_refund_post_fee-erp_refund_goods_total!=0 or re_returnamount -erp_refund_merchant_total !=0 :
                            err_msg = {"order_code": order_code, "return_code": return_code,
                                       "error_msg": f"下账金额，心云:{erp_refund_merchant_total}={erp_refund_goods_total}+{erp_refund_post_fee}，雨诺：【{re_returnamount}】"}
                            result.append(err_msg)

                        # 平台配送
                        if TRANSCOMPANY == "1" or TRANSCOMPANY == "2" or TRANSCOMPANY == "4":
                            # 配送费比对
                            if refund_platform_refund_delivery_fee-re_postfee !=0:
                                err_msg = {"order_code": order_code, "return_code": return_code,
                                           "error_msg": f"平台配送退配送费不一致，心云:{refund_platform_refund_delivery_fee}，雨诺：{re_postfee}"}
                                result.append(err_msg)
                            # 下账配送费
                            if erp_refund_post_fee !=0 :
                                err_msg = {"order_code": order_code, "return_code": return_code,
                                           "error_msg": f"平台配送下账配送费应为0，心云:{erp_refund_post_fee}"}
                                result.append(err_msg)
                            # 下账商品金额
                            if  re_returnamount-erp_refund_goods_total !=0:
                                err_msg = {"order_code": order_code, "return_code": return_code,
                                           "error_msg": f"平台配送下账配送费应为0，心云:{erp_refund_goods_total}，雨诺：{re_returnamount}"}
                                result.append(err_msg)
                        else:
                            if refund_merchant_refund_post_fee-re_postfee !=0:
                                err_msg = {"order_code": order_code, "return_code": return_code,
                                           "error_msg": f"商家配送退配送费不一致，心云:{refund_merchant_refund_post_fee}，雨诺：{re_postfee}"}
                                result.append(err_msg)
                            # 下账配送费
                            if re_postfee-erp_refund_post_fee != 0:
                                err_msg = {"order_code": order_code, "return_code": return_code,
                                           "error_msg": f"商家配送下账配送费不一致，心云:{erp_refund_post_fee}，雨诺：{re_postfee}"}
                                result.append(err_msg)
                            # 下账商品金额
                            if re_returnamount-erp_refund_goods_total-erp_refund_post_fee != 0:
                                err_msg = {"order_code": order_code, "return_code": return_code,
                                           "error_msg": f"商家配送下账商品金额不一致，心云:{erp_refund_goods_total}+{erp_refund_post_fee}，雨诺：{re_returnamount}"}
                                result.append(err_msg)
        else:
            err_msg={"msg":f"退款单数量不一致，雨诺：{yn_count},心云：{xy_count}"}
            result.append(err_msg)
        return result
    except Exception as e:
        raise e




"""
    迁移退款单信息比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_o2o_refund_detail_sum(body):
    try:
        result=[]
        start_time = body['start_time']
        end_time = body['end_time']
        store_list = body['store_list']
        # 心云退款总额查询
        xy_return_sql=f"""
            SELECT
                COUNT(*) AS 'return_count',
                SUM( refund.total_food_amount ) AS 'return_productamount',
                SUM( refund.consumer_refund ) AS 'return_consumer',
                SUM( refund.shop_refund ) AS 'return_shop',
                SUM( refund.platform_refund_pack_fee ) AS 'return_pack',
                SUM( refund.merchant_refund_post_fee ) + SUM( refund.platform_refund_delivery_fee ) AS 'return_post' 
            FROM
                refund_order refund
                LEFT JOIN order_info info ON refund.order_no = info.order_no 
            WHERE
                info.created >= "{start_time}" 
                AND info.created <= "{end_time}" 
                AND info.organization_code IN ( {store_list} ) 
                AND info.service_mode = 'o2o' 
                AND info.third_platform_code != '43'
        """
        xy_return_result=db_yx_dev_dsclound(xy_return_sql)
        xy_return_data=xy_return_result['data']
        # 雨诺退款总额查询
        yn_return_sql=f"""
            SELECT
                COUNT(*) AS 'return_count',
                SUM( return_o.PRODUCTSAMOUNT ) AS 'return_productamount',
                SUM( return_o.RETURNAMOUNT ) + SUM( return_o.PACKAGEFEE ) + SUM( return_o.POSTFEE ) AS 'return_consumer',
                SUM( return_o.RETURNAMOUNT ) AS 'return_shop',
                SUM( return_o.PACKAGEFEE ) AS 'return_pack',
                SUM( return_o.POSTFEE ) AS 'return_post' 
            FROM
                returnorder_std return_o
                INNER JOIN order_std order_s ON return_o.ORDERCODE = order_s.ORDERCODE 
            WHERE
                order_s.INNERSTATUS = 1 
                AND order_s.CREATETIME >= "{start_time}" 
                AND order_s.CREATETIME <= "{end_time}" 
                AND order_s.STORECODE IN ( {store_list} ) 
                AND order_s.ORDERFROM != '104' 
                AND order_s.ORDERFROM != '108' 
                AND order_s.ORDERFROM != '12' 
                AND order_s.ORDERFROM != '4' 
                AND order_s.ORDERFROM != '111'
        """
        yn_return_result=db_yn_oms(yn_return_sql)
        yn_return_data=yn_return_result['data']
        for yn_return_item in yn_return_data:
            # 退款单数量
            yn_return_count=yn_return_item['return_count']
            # 退款商品总额
            yn_return_productamount=yn_return_item['return_productamount']
            # 实际退款金额
            yn_return_consumer=yn_return_item['return_consumer']
            # 商家退款金额
            yn_return_shop=yn_return_item['return_shop']
            # 退款包装费总额
            yn_return_pack=yn_return_item['return_pack']
            # 退款配送费总额
            yn_return_post=yn_return_item['return_post']
            for xy_return_item in xy_return_data:
                xy_return_count=xy_return_item['return_count']
                xy_return_productamount=xy_return_item['return_productamount']
                xy_return_consumer=xy_return_item['return_consumer']
                xy_return_shop=xy_return_item['return_shop']
                xy_return_pack=xy_return_item['return_pack']
                xy_return_post=xy_return_item['return_post']
                # 退款单总数比对
                if yn_return_count-xy_return_count!=0:
                    err_msg={"msg":f"退款单总数不一致，心云：{xy_return_count}，雨诺：{yn_return_count}"}
                    result.append(err_msg)
                # 退款商品总额
                if yn_return_productamount-xy_return_productamount!=0:
                    err_msg = {"msg": f"退款商品总额不一致，心云：{xy_return_productamount}，雨诺：{yn_return_productamount}"}
                    result.append(err_msg)
                # 实际退款金额
                if xy_return_consumer-yn_return_consumer!=0:
                    err_msg = {"msg": f"实际退款金额不一致，心云：{xy_return_consumer}，雨诺：{yn_return_consumer}"}
                    result.append(err_msg)
                # 商家退款金额
                if xy_return_shop-yn_return_shop!=0:
                    err_msg = {"msg": f"商家退款金额不一致，心云：{xy_return_shop}，雨诺：{yn_return_shop}"}
                    result.append(err_msg)
                # 退款包装费总额
                if xy_return_pack - yn_return_pack != 0:
                    err_msg = {"msg": f"退款包装费总额不一致，心云：{xy_return_pack}，雨诺：{yn_return_pack}"}
                    result.append(err_msg)
                # 退款配送费总额
                if xy_return_post - yn_return_post != 0:
                    err_msg = {"msg": f"退款配送费总额不一致，心云：{xy_return_post}，雨诺：{yn_return_post}"}
                    result.append(err_msg)

        # 心云退单详情查询
        xy_detail_sql=f"""
            SELECT
                COUNT(*) AS 'detail_count',
                SUM( detail.refund_count ) AS 'refund_count',
                SUM( detail.actual_net_amount ) AS 'bill_amount',
                SUM( detail.bill_price ) AS 'bill_price',
                SUM( detail.unit_refund_price ) AS 'unit_price' 
            FROM
                refund_order refund
                INNER JOIN order_info info ON refund.order_no = info.order_no
                INNER JOIN refund_detail detail ON refund.refund_no = detail.refund_no 
            WHERE
                info.created >= "{start_time}" 
                AND info.created <= "{end_time}" 
                AND info.organization_code IN ( {store_list} ) 
                AND info.service_mode = 'o2o' 
                AND info.third_platform_code != '43'
        """
        xy_detail_result=db_yx_dev_dsclound(xy_detail_sql)
        xy_detail_data=xy_detail_result['data']
        # 雨诺退款详情查询
        yn_detail_sql=f"""
            SELECT
                COUNT(*) AS 'detail_count',
                SUM( detail.RETURNCOUNT ) AS 'refund_count',
                SUM( detail.GOODSAMOUNT ) AS 'bill_amount',
                SUM( detail.DISCOUNTPRICE ) AS 'bill_price',
                SUM( detail.GOODSPRICE ) AS 'unit_price' 
            FROM
                returnorder_std return_o
                INNER JOIN order_std order_s ON return_o.ORDERCODE = order_s.ORDERCODE
                INNER JOIN returnorder_detail detail ON return_o.RETURNCODE = detail.RETURNCODE 
            WHERE
                order_s.INNERSTATUS = 1 
                AND order_s.CREATETIME >= "{start_time}" 
                AND order_s.CREATETIME <= "{end_time}" 
                AND order_s.STORECODE IN ( {store_list} ) 
                AND order_s.ORDERFROM != '104' 
                AND order_s.ORDERFROM != '108' 
                AND order_s.ORDERFROM != '12' 
                AND order_s.ORDERFROM != '4' 
                AND order_s.ORDERFROM != '111'
                AND detail.GOODSCODE NOT IN ( 'POSTFEE', 'PACKAGEFEE', 'SERVICEFEE' ) 
                AND detail.RETURNCOUNT != 0
        """
        yn_detail_result=db_yn_oms(yn_detail_sql)
        yn_detail_data=yn_detail_result['data']
        for yn_detail_item in yn_detail_data:
            # 详情总数
            yn_detail_count=yn_detail_item['detail_count']
            # 退款商品总数
            yn_refund_count=yn_detail_item['refund_count']
            # 商品下账总额
            yn_bill_amount=yn_detail_item['bill_amount']
            # 商品下账单价总额
            yn_bill_price=yn_detail_item['bill_price']
            # 商品单价
            yn_unit_price=yn_detail_item['unit_price']
            for xy_detail_item in xy_detail_data:
                xy_detail_count=xy_detail_item['detail_count']
                xy_refund_count=xy_detail_item['refund_count']
                xy_bill_amount=xy_detail_item['bill_amount']
                xy_bill_price=xy_detail_item['bill_price']
                xy_unit_price=xy_detail_item['unit_price']
                # 详情总数
                if yn_detail_count-xy_detail_count!=0:
                    msg_result={"msg": f"退款详情总数不一致，心云：{xy_detail_count}，雨诺：{yn_detail_count}"}
                    result.append(msg_result)
                # 退款商品总数
                if yn_refund_count-xy_refund_count!=0:
                    msg_result = {"msg": f"退款商品总数不一致，心云：{xy_refund_count}，雨诺：{yn_refund_count}"}
                    result.append(msg_result)
                # 商品下账总额
                if yn_bill_amount-xy_bill_amount!=0:
                    msg_result = {"msg": f"退款商品下账总额不一致，心云：{xy_bill_amount}，雨诺：{yn_bill_amount}"}
                    result.append(msg_result)
                # 商品下账单价总额
                if yn_bill_price-xy_bill_price!=0:
                    msg_result = {"msg": f"退款商品下账单价总额不一致，心云：{xy_bill_price}，雨诺：{yn_bill_price}"}
                    result.append(msg_result)
                # 商品单价
                if yn_unit_price-xy_unit_price!=0:
                    msg_result = {"msg": f"退款商品单价总额不一致，心云：{yn_unit_price}，雨诺：{xy_unit_price}"}
                    result.append(msg_result)
        return result

    except Exception as e:
        raise e






"""
    迁移退款单状态比对
    :param xy_status:心云状态
    :param yn_status:雨诺状态
    :return result_flag：比对结果
"""
@exception(logger)
def refund_order_o2o_status(xy_status,yn_status):
    try:
        # 驳回
        if yn_status=='124':
            xy_value=102
        # # 待退款
        # elif yn_status=='120':
        #     xy_value=10
        # # 待退货
        # elif yn_status=='122':
        #     xy_value=20
        # 取消
        elif yn_status=='125' or yn_status=='110':
            xy_value=103
        # 已完成
        else:
            xy_value=100
        if xy_value==xy_status:
            result_flag=1
        else:
            result_flag = 0
        return result_flag
    except Exception as e:
        raise e

"""
    迁移退款单下账状态比对
    :param xy_status:心云状态
    :param yn_status:雨诺状态
    :return result_flag：比对结果
"""
@exception(logger)
def refund_order_o2o_erp_status(xy_status,yn_status):
    try:
        # 已下账
        if yn_status==1:
            xy_value=100
        # 待下账
        elif yn_status==0:
            xy_value=20
        # 无需下账
        else:
            xy_value=102
        if xy_value==xy_status:
            result_flag=1
        else:
            result_flag = 0
        return result_flag
    except Exception as e:
        raise e


"""
    迁移退款单下账类型比对
    :param xy_status:心云状态
    :param yn_status:雨诺状态
    :return result_flag：比对结果
"""
@exception(logger)
def refund_order_o2o_bill_type(xy_status,yn_status):
    try:
        # 退货退款
        if yn_status==0:
            xy_value=2
        # 仅退款
        else:
            xy_value=1
        if xy_value==xy_status:
            result_flag=1
        else:
            result_flag = 0
        return result_flag
    except Exception as e:
        raise e


"""
    迁移退款单退款类型比对
    :param xy_status:心云状态
    :param yn_status:雨诺状态
    :return result_flag：比对结果
"""
@exception(logger)
def refund_order_o2o_type(xy_status,yn_status):
    try:
        # 退货退款
        if yn_status==0:
            xy_value='1'
        # 仅退款
        else:
            xy_value='0'
        if xy_value==xy_status:
            result_flag=1
        else:
            result_flag = 0
        return result_flag
    except Exception as e:
        raise e

"""
    迁移退款单是否部分退款比对
    :param xy_status:心云状态
    :param yn_status:雨诺状态
    :return result_flag：比对结果
"""
@exception(logger)
def refund_order_o2o_isfull(xy_status,yn_status):
    try:
        # 部分退
        if yn_status==0:
            xy_value='0'
        # 整单退
        else:
            xy_value='1'
        if xy_value==xy_status:
            result_flag=1
        else:
            result_flag = 0
        return result_flag
    except Exception as e:
        raise e