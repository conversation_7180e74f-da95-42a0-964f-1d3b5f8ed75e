from structure.jenkins.get_token import aesEncrypt
from structure.jenkins.get_token import setHeaders
import requests

#获取运行环境id
def get_test_plan_env(s,host,projectId,name):
    url = host + "/api/environment/list/{}".format(projectId)
    ms_id=requests.get(url=url,headers=s)
    data = ms_id.json().get("data")
    #print(data)
    if data:
        for dat in data:
            if dat.get("name") == name:
                return dat.get("id")
    else:
        print("没有data值")

#name为项目ID下面环境配置中的名称
#s=requests.session()
#t=setHeaders(s,accessKey='INXG05Txzg51hTLs',secretKey='SU45pvmxqU78HAtD')
##a=get_test_plan_env(s=t,projectId='b0eb2b45-a15c-4e4c-9b8b-5057979ea718',name='药学服务一期test环境',host='http://metersphere.hxyxt.com')
#print(a)


#测试报告
def get_report_url(s,host,customData):
    url = host + "/track/share/generate/expired"
    body = {"customData":customData,"shareType":"PLAN_DB_REPORT","lang":None}
    r = requests.post(url,json=body,headers=s)
    if r:
        data = r.json().get("data")
        if data:
            shareUrl=data.get("shareUrl")
            report_url = host + "/track/share-plan-report" + shareUrl
            #print(report_url)
            return report_url
    else:
        print("没有返回值...")
