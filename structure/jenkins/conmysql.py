from pymysql import connect

def con_mysql(sql):
    conn = connect(
        host='***********',
        port=3306,
        user='superqa',
        password='PeLwvsXy0C',
        db='metersphere',
        charset='utf8')
    cursor = conn.cursor()
    cursor.execute(sql)
    # 获取数量
    results = cursor.fetchone()
    conn.close()
    #print(results[0])
    return results
"""
p="hydee-middle-sdp"
e="test"
sql=f"select ms_envName,ms_testplanId,ms_testplanName,ms_projectID from jenkins_run_ms where projectname='{p}' and run_env='{e}'"
sql_res = con_mysql(sql)
"""

