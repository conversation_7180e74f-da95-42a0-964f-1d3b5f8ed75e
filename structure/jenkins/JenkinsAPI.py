from flask import Flask, request, jsonify
import json
from structure.jenkins.run_exec import exec_run
# apps = Flask(__name__)
from structure.jenkins.conmysql import con_mysql

# @apps.route('/jenkins_run_MStest', methods=['POST'])

def post_json(data):
    # # 确保请求体中包含JSON数据
    # if not request.json:
    #     return jsonify({'error': 'No JSON data provided'}), 400
    # data=request.json
    # 获取JSON数据
    if 'key' in data:
        if data['key']=='WlZkc05HRlhOVEJaVnpWdQ==' :
            env=data['env']
            project=data['project']
            envs=('test','dev','proc')
            sql1='select jenkins_job_name from jenkins_run_ms'
            sql_res1 = con_mysql(sql1)
            if project in sql_res1 and env in envs:
                sql=f"select ms_env_name,ms_testplan_id,ms_testplan_name,ms_project_id from jenkins_run_ms where jenkins_job_name='{project}' and run_env='{env}'"
                sql_res = con_mysql(sql)
                msg=exec_run(host='http://metersphere.hxyxt.com', projectId=sql_res[3],testPlanName=sql_res[2],project=project,env=env)
            else:
                return jsonify({'error': "项目环境不存在或该项目没有自动化脚本"}), 201
        else:
            return jsonify({'error': 'key不正确'}), 201
    else:
        return jsonify({'error': '请求不正确'}), 201
    # 需要修改为run_exec.py的响应数据
    return jsonify({'message': msg}), 200

# if __name__ == '__main__':
#     # apps.run(debug=True)