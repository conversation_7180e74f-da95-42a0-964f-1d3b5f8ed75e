from structure.jenkins.get_token import setHeaders
from structure.jenkins.get_token import aesEncrypt
import requests
from structure.jenkins.get_MS_info import get_test_plan_env,get_report_url
import json
from structure.jenkins.conmysql import con_mysql

def test_plan_run(s,host,testPlanId,projectId,envId,userId='jenkins'):
    url = host + "/track/test/plan/run"
    body = {
   "mode": "serial",
   "reportType": "iddReport",
   "onSampleError": True,
   "runWithinResourcePool": False,
   "resourcePoolId": None,
   "envMap": {
      projectId: envId
   },
   "testPlanId": testPlanId,
   "projectId": projectId,
   "userId": userId,
   "triggerMode": "MANUAL",
   "environmentType": "JSON",
   "environmentGroupId": "",
   "requestOriginator": "TEST_PLAN"
}

    r=requests.post(url=url,json=body,headers=s)
    r=json.loads(r.text)
    return r


def exec_run(host,projectId,testPlanName,project,env):
    #设置请求头
    s = setHeaders(requests.session(), accessKey='EGpYFBFjBXGgNt8I', secretKey='UW3YmIWl4PDcMogc')
    # 获取环境ID

    sql = f"select ms_env_name,ms_testplan_id,ms_testplan_name,ms_project_id from jenkins_run_ms where jenkins_job_name='{project}' and run_env='{env}'"
    sql_res=con_mysql(sql)
    #print(sql_res[3],sql_res[0])
    envId=get_test_plan_env(s=s,projectId=sql_res[3],name=sql_res[0],host=host)

    testPlanId=sql_res[1]

    r = test_plan_run(s,host,testPlanId=testPlanId,projectId=sql_res[3],envId=envId)

    if r["success"] == True:
        data = r["data"]
        #print(data)
        report_url = get_report_url(s=s, host=host, customData=data)  # 获取测试报告
        return "接口测试计划调用成功,测试计划报告地址(24小时内有效)为: {}".format(report_url)
    else:
        msg='接口自动化调用失败,请排查问题'
        return msg

#exec_run(host='http://metersphere.hxyxt.com',projectId='0eb2b45-a15c-4e4c-9b8b-5057979ea718',testPlanName="Jenkinstest")
