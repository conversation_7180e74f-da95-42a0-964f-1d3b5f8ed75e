import base64
import time
import uuid
from Crypto.Cipher import AES
import requests

def aesEncrypt(text, secretKey, iv):
    BS = AES.block_size  # 这个等于16
    mode = AES.MODE_CBC
    def pad(s): return s + (BS - len(s) % BS) * \
                       chr(BS - len(s) % BS)
    cipher = AES.new(secretKey.encode('UTF-8'), mode, iv.encode('UTF-8'))
    encrypted = cipher.encrypt(pad(text).encode('UTF-8'))
    # 通过aes加密后，再base64加密
    b_encrypted = base64.b64encode(encrypted)
    return b_encrypted

def setHeaders(s,accessKey,secretKey):
    timeStamp = int(round(time.time() * 1000))
    combox_key = accessKey + '|' + str(uuid.uuid4()) + '|' + str(timeStamp)
    signature = aesEncrypt(combox_key, secretKey, accessKey)
    #print(signature.decode('UTF-8'))
    header = {'Content-Type': 'application/json',
              'ACCEPT': 'application/json',
              'accessKey': accessKey,
              'signature': signature.decode('UTF-8'),
              'Connection': 'close'}
    s.headers.update(header)
    return s.headers

