# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/26 18:11
@Auth ： 逗逗的小老鼠
@File ：operate_order_stock.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.deal_ini import readini
from lib.deal_db_mysql import db_mysql_connect
import requests
import json
import itertools
"""
    库存释放
    :param order_list:释放接口清单
    :return 
"""
@exception(logger)
def order_sale_o2o_stock_free(order_list):
    try:
        url=readini("order_api.ini","order_stock","free_api")
        headers = {"Content-Type": "application/json"}
        data = order_list
        response = requests.post(url, data=json.dumps(data), headers=headers)
        result=response.status_code
        print(result)
        return result
    except Exception as e:
        raise e

@exception(logger)
def order_b2c_stock_exception_record(start_time,end_time,**kwargs):
    "查询B2C订单库存占用释放异常记录"
    try:
        order_no_free_list=[]
        oms_order_no_free_list=[]
        no_free_list=[]
        repeat_list=[]

        # 查询时间内发货的wms订单
        wms_sql=f"""
            SELECT
                order_no,
                oms_order_no
            FROM
                oms_order_info 
            WHERE
                ship_time >= %s
                AND ship_time <= %s
                AND join_wms = 1
        """
        query_value=(start_time,end_time)
        wms_query_result = db_mysql_connect("dscloud", wms_sql, sql_val=query_value, **kwargs)
        wms_order_list=wms_query_result['data']
        # 查询时间内下账的OMS订单
        oms_sql=f"""
            SELECT
                order_no,
                oms_order_no
            FROM
                oms_order_info 
            WHERE
                bill_time >= %s
                AND bill_time <= %s
                AND join_wms = 0
        """
        oms_query_result = db_mysql_connect("dscloud", oms_sql, sql_val=query_value, **kwargs)
        oms_order_list=oms_query_result['data']
        order_list=list(itertools.chain(wms_order_list,oms_order_list))

        # 查询时间内的订单释放记录
        free_sql=f"""
        SELECT
            order_no
        FROM
            commodity_stock 
        WHERE
            create_time >= %s
            AND create_time <= %s
            AND store_id IS NOT NULL 
            AND type =2
        """
        free_query_result = db_mysql_connect("dscloud", free_sql, sql_val=query_value, **kwargs)
        free_order_list=free_query_result['data']
        free_order_no_list=[item['order_no'] for item in free_order_list]
        for order_item in order_list:
            order_no=order_item['order_no']
            oms_order_no=order_item['oms_order_no']
            # order_no和oms_order_no均无调用记录
            if order_no not in free_order_no_list and oms_order_no not in free_order_no_list:
                no_free_list.append(order_item)
            else:
                # order_no和oms_order_no均有调用记录
                if order_no in free_order_no_list and oms_order_no in free_order_no_list:
                    repeat_list.append(order_item)
                else:
                    # order_no有调用记录
                    if order_no in free_order_no_list:
                        order_no_free_list.append(order_item)
                    if oms_order_no in free_order_no_list:
                        oms_order_no_free_list.append(order_item)
        no_free_order_no_list=[item['order_no'] for item in no_free_list]
        no_free_oms_order_no_list = [item['oms_order_no'] for item in no_free_list]
        no_free_order_list = list(itertools.chain(no_free_order_no_list, no_free_oms_order_no_list))
        placeholders_list = [f'{item}' for item in no_free_order_list]
        placeholders = ','.join(placeholders_list)
        stock_sql=f"""
            SELECT * FROM commodity_stock WHERE order_no IN ({placeholders})
        """
        stock_query_val=()
        stock_query_result = db_mysql_connect("dscloud", stock_sql, sql_val=stock_query_val, **kwargs)
        stock_data=stock_query_result['data']
        has_free_list=[]
        stock_fail_list=[]
        stock_success_list=[]
        for no_free_item in no_free_order_list:
            for stock_item in stock_data:
                stock_order_no=stock_item['order_no']
                stock_type=stock_item['type']
                if str(no_free_item)==str(stock_order_no):
                    if stock_type==2:
                        has_free_list.append(no_free_item)
                    elif stock_type==1:
                        stock_success_list.append(no_free_item)
                    else:
                        stock_fail_list.append(no_free_item)
        todo_free=[]
        for sucess_item in stock_success_list:
            if sucess_item in has_free_list:
                pass
            else:
                todo_free.append(sucess_item)

        print(len(no_free_order_list))
        result={"todo_free":todo_free,"stock_fail_list":stock_fail_list,"stock_success_list":stock_success_list,"has_free_list":has_free_list}
        return result
    except Exception as e:
        raise e



if __name__=="__main__":
    order_list=["1786321894573309953"]
    # order_sale_o2o_stock_free(order_list)
    result=order_b2c_stock_exception_record("2024-10-17 14:20:00","2024-10-17 18:20:00",environment_flag="prod")
    print(json.dumps(result))