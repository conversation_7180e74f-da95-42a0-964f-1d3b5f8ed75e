# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/26 9:32
@Auth ： 逗逗的小老鼠
@File ：operate_commodity_syn.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from lib.db_conf_test import db_yx_test_dsclound
from lib.db_conf_dev import yx_dev_dsclound


@exception(logger)
def operate_commodity_stock_syn(body):
    try:
        online_list=["test_20240426_90001"]
        if "syn_online_store" in body:
            syn_store=body['syn_online_store']
            online_list.extend(syn_store)
        online_store=",".join(['"{}"'.format(item) for item in online_list])
        # 修改库存同步和价格同步为不同步
        test_syn_sql=f"""
                UPDATE ds_online_store_config 
                SET sync_stock = 0,
                    sync_price = 0 
                WHERE
                    online_store_id NOT IN (
                    SELECT
                        id 
                    FROM
                        ds_online_store 
                    WHERE
                    online_store_code IN ( {online_store} ) 
                    )
        """
        # test环境修改数据
        test_syn_update_result=db_yx_test_dsclound(test_syn_sql)
        # 修改数据
        test_syn_update_data=test_syn_update_result['data']
        # dev环境修改数据
        dev_syn_update_result = yx_dev_dsclound(test_syn_sql)
        # 修改数据
        dev_syn_update_data = dev_syn_update_result['data']
        # 查询当前正在同步的门店清单
        test_syn_query_sql="""
            SELECT
                store.platform_name AS 'platform_name',
                store.online_store_code AS 'online_store_code',
                store.online_store_name AS 'online_store_name',
                config.sync_stock AS 'sync_stock',
                config.sync_price AS 'sync_price' 
            FROM
                ds_online_store_config config
                LEFT JOIN ds_online_store store ON config.online_store_id = store.id 
            WHERE
                config.sync_stock = 1 
                OR config.sync_price =1
        """
        test_syn_query_result=db_yx_test_dsclound(test_syn_query_sql)
        test_syn_query_data=test_syn_query_result['data']
        dev_syn_query_result = yx_dev_dsclound(test_syn_query_sql)
        dev_syn_query_data = dev_syn_query_result['data']
        data={"test_syn_update_num":test_syn_update_data,"test_syn_store_info":test_syn_query_data,"dev_syn_update_num":dev_syn_update_data,"dev_syn_store_info":dev_syn_query_data}
        result={"code":10000,"data":data}
        print(result)
        return result
    except Exception as e:
        raise e


if __name__=="__main__":
    body={"syn_online_store":['123614_2694687']}
    operate_commodity_stock_syn(body)

