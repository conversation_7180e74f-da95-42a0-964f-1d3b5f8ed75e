# -*- coding: utf-8 -*-
"""
@Time ： 2024/8/30 15:06
@Auth ： 逗逗的小老鼠
@File ：operate_b2c_commodity_release.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from datetime import datetime, timedelta
from lib.deal_db_mysql import db_mysql_connect
import requests,json
from lib.deal_config_json import read_json_file

def release_b2c_commodity_stock(**kwargs):
    try:
        result=[]
        # 获取环境变量，若无环境变量，默认为test
        environment_flag = kwargs.get("environment_flag", "test")
        # 获取当前时间
        now = datetime.now()
        # 获取24小时前的时间
        yesterday = now - timedelta(days=1)
        now_time=now.strftime("%Y-%m-%d %H:%M:%S")
        yesterday_time=yesterday.strftime("%Y-%m-%d %H:%M:%S")
        start_time=kwargs.get("start_time",yesterday_time)
        end_time=kwargs.get("end_time",now_time)
        # 主机地址获取
        json_path = "host_url_conf.json"
        host_url_conf = read_json_file(json_path)
        # 获取对应环境下，对应服务的地址
        host_url = host_url_conf.get(environment_flag).get("middle-merchandise").get("url")
        # 查询wms发货的订单
        query_sql = f"""
            SELECT
                info.third_order_no AS 'third_order_no',
                info.order_no AS 'order_no',
                info.deleted AS 'deleted',
                info.online_store_name AS 'online_store_name',
                stock.erp_code AS 'erp_code',
                stock.stock_qty AS 'stock_qty',
                stock.type AS 'type',
                stock.order_detail_id AS 'order_detail_id',
                stock.organization_code AS 'organization_code',
                stock.store_id AS 'store_id',
                stock.serial_number AS 'serial_number'
            FROM
                commodity_stock stock
                LEFT JOIN order_info info ON info.order_no = stock.order_no 
            WHERE
                info.created >= %s 
                AND info.created <= %s 
                AND info.online_store_code IN (
                SELECT
                    store.online_store_code 
                FROM
                    ds_online_store store
                    LEFT JOIN ds_online_store_config config ON store.id = config.online_store_id 
                WHERE
                    store.service_mode = 'B2C' 
                    AND config.oms_pick_type = 0 
                ) 
            GROUP BY
                info.order_no
        """
        # 查询参数，用于防止SQL注入
        query_val = (start_time,end_time)
        # 调用数据库连接函数，执行查询，获取查询结果
        query_result = db_mysql_connect("dscloud", query_sql, sql_val=query_val, **kwargs)
        query_data = query_result["data"]
        order_no_list=[]
        # 遍历查询结果，将order_no 添加到列表中
        for item in query_data:
            order_no = item["order_no"]
            order_no_list.append(order_no)

        release_result={}
        # print(order_no_list)
        # 使用循环来按每10个元素进行分片
        for i in range(0, len(order_no_list), 500):
            # 切片操作，从i开始到i+10（但不包括i+10），步长为1
            # 如果i+10超过了列表长度，切片会自动停止在列表末尾
            slice_of_list = order_no_list[i:i + 500]

            # 调用（或处理）这个分片
            print(slice_of_list)
            # 订单中台释放库存请求头
            req_body=slice_of_list
            # 订单中台释放库存请求地址
            req_url = f"http://hydee-business-order.svc.k8s.pre.hxyxt.com/1.0/test/tool/B2C"
            # 商品中台释放库存请求体
            # req_body = {
            #         "orderNoList": slice_of_list,
            #         "orderType": "b2c",
            #         "storeIdList": []
            #     }
            release_result["req_body"] = slice_of_list
            # 商品中台释放库存请求地址
            # req_url = f"{host_url}/test/manualReleaseStock"
            req_headers = {"Content-Type": "application/json;charset=UTF-8"}
            res = requests.post(url=req_url, headers=req_headers, data=json.dumps(req_body), verify=False)
            if res.status_code == 200:
                res_json = res.text
                release_result["res_data"]=res_json
                release_result["num"]=len(order_no_list)
            else:
                release_result["res_data"]=res.status_code
            result.append(release_result)
        return result
    except Exception as e:
        raise e