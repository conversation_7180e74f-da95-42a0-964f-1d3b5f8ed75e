# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/12 15:29
@Auth ： 逗逗的小老鼠
@File ：operate_mt_shop.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.deal_ini import readini
from lib.db_conf import db_insight
import requests
import json
"""
    美团测试门店自动续期
    :param order_list:释放接口清单
    :return 
"""
def mt_shop_renewal(body):
    try:
        result=[]
        # 查询测试门店清单
        shop_list_sql=f"""SELECT id,app_id,wmPoiId,shop_name,shop_id,app_name FROM mt_shop_test_list WHERE is_delete=0"""
        shop_list_result=db_insight(shop_list_sql)
        for shop_item in shop_list_result['data']:
            app_id=shop_item['app_id']
            wmPoiId=shop_item['wmPoiId']
            # 初始化需求请求cookies
            cookies=readini('mt_shop_config.ini',"meituan_open","cookies")
            renewal_urlf="https://tscc.meituan.com/bete/poi/manager/apply/extension"
            query_param="""?yodaReady=h5&csecplatform=4&csecversion=2.4.0&mtgsig={"a1":"1.1","a2":1710225849606,"a3":"67469v3zwx6w57z21639159z551zv84781yu35938zw979581yv8yy4v","a5":"bMVYCNqPGeAQ9dq4sot//Z==","a6":"hs1.4aOG4x69iuIGtADfqn9IKcU/O7IltwuR65tgLSu8BNXEGhBuOKZ8Evcuc+tDbrR8VfYSU9aEUCdDleArQhsNOSQ==","x0":4,"d1":"ae56cb858a2fa3ba9a0bda532479682d"}'"""
            url=renewal_urlf+query_param
            print(url)
            headers = {"Content-Type": "application/x-www-form-urlencoded","Cookie":cookies.encode("utf-8")}
            print(headers)
            body={"app_id":app_id,"wmPoiId":wmPoiId,"applyExtensionDay":15}
            print(body)
            res=requests.post(url=url.encode("utf-8"),headers=headers,data=body)
            print(res)
            status_code=res.status_code
            if status_code==200:
                res_json=res.json()
                msg=res_json['msg']
                code=res_json['code']
            else:
                msg="接口访问失败"
                code=status_code
            shop_item["result"]={"msg":msg,"code":code}
            result.append(shop_item)
        print(result)
        return result
    except Exception as e:
        raise e


if __name__=="__main__":
    mt_shop_renewal()