# -*- coding: utf-8 -*-
"""
@Time ： 2024/5/21 16:07
@Auth ： 逗逗的小老鼠
@File ：moni_order_address_info.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from lib.deal_text import text_find, text_write
from structure.operate.operate_order_stock import order_sale_o2o_stock_free
import datetime

momi = "pro"
if momi == "test":
    from lib.db_conf_test import db_yx_test_dsclound as db_dscloud
if momi == "pro":
    from lib.db_conf import db_yx_dscloud as db_dscloud

"""
    美团订单顾客信息解谜异常
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""


def moni_order_address(body):
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        o2o_result = moni_order_address_O2O(body)
        b2c_result = moni_order_address_B2C(body)
        log_sql = f"""SELECT order_no, COUNT(*) AS 'count' FROM order_receiver_decrypt_log WHERE operation_time >= "{start_time}" AND operation_time <= "{end_time}" GROUP BY	order_no"""
        log_result = db_dscloud(log_sql)
        log_data = log_result['data']
        # 遍历O2O解密失败的数据
        for o2o_item in o2o_result:
            o2o_order_no = o2o_item['order_no']
            for log_item in log_data:
                order_no = log_item['order_no']
                count = log_item['count']
                # 若日志已记录，则变更order_log标识为True
                if str(order_no) == str(o2o_order_no):
                    o2o_item['order_log'] = True
            result.append(o2o_item)
        # 遍历B2C解密失败的数据
        for b2c_item in b2c_result:
            b2c_order_no = b2c_item['order_no']
            for log_item in log_data:
                order_no = log_item['order_no']
                count = log_item['count']
                # 若日志已记录，则变更order_log标识为True
                if str(order_no) == str(b2c_order_no):
                    b2c_item['order_log'] = True
            result.append(b2c_item)
        return result
    except Exception as e:
        raise e


"""
    O2O美团订单顾客信息解谜异常
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""


@exception(logger)
def moni_order_address_O2O(body):
    "O2O美团订单顾客信息解谜异常"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        # 查询O2O美团订单自配送模式的顾客信息
        address_o2o_sql = f"""
            SELECT
                address.order_no AS 'order_no',
                address.receiver_name AS 'receiver_name',
                address.receiver_telephone AS 'receiver_telephone',
                address.receiver_mobile AS 'receiver_mobile',
                address.full_address AS 'full_address',
                address.address AS 'address',
                address.receiver_name_privacy AS 'receiver_name_privacy',
                address.receiver_address_privacy AS 'receiver_address_privacy',
                address.receiver_phone_privacy AS 'receiver_phone_privacy'
            FROM
                order_info info
                LEFT JOIN order_delivery_record record ON info.order_no = record.order_no
                LEFT JOIN order_delivery_address address ON info.order_no = address.order_no 
            WHERE
                info.created >= "{start_time}" 
                AND info.created <= "{end_time}"
                AND info.third_platform_code=27
                AND info.service_mode='O2O'
        """
        address_result = db_dscloud(address_o2o_sql)
        address_data = address_result['data']
        # 遍历收货人信息详情
        for address_item in address_data:
            order_no = address_item['order_no']
            null_value_list = []
            null_privacy_list = []
            # 遍历详情各个字段的值
            for item in address_item:
                address_value = address_item[item]
                # 判断值是否为空
                if address_value is None or address_value == "":
                    # 判断为空字段是否为原始密文
                    if 'privacy' in item:
                        null_privacy_list.append(item)
                    else:
                        null_value_list.append(item)
            if len(null_value_list) > 0:
                null_dict = {"order_no": order_no, "exec_type": "param_exec", "exec_msg": "参数值未完成解密",
                             "param": null_value_list, "order_log": False}
                result.append(null_dict)
            if len(null_privacy_list) > 0:
                null_dict = {"order_no": order_no, "exec_type": "privacy_exec", "exec_msg": "原始密文缺失",
                             "param": null_privacy_list, "order_log": False}
                result.append(null_dict)
        return result
    except Exception as e:
        raise e


"""
    B2C美团订单顾客信息解谜异常
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""


@exception(logger)
def moni_order_address_B2C(body):
    "B2C美团订单顾客信息解谜异常"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        # 查询B2C美团订单自配送模式的顾客信息
        address_B2C_sql = f"""
            SELECT
                address.oms_order_no AS 'order_no',
                address.receiver_name AS 'receiver_name',
                address.receiver_telephone AS 'receiver_telephone',
                address.receiver_mobile AS 'receiver_mobile',
                address.full_address AS 'full_address',
                address.address AS 'address',
                address.province AS 'province',
                address.city AS 'city',
                address.district AS 'district',
                address.town AS 'town',
                address.original_full_address AS 'original_full_address',
                address.privacy_detail AS 'privacy_detail',
                address.receiver_mobile_desen AS 'receiver_mobile_desen',
                address.receiver_name_privacy AS 'receiver_name_privacy',
                address.receiver_address_privacy AS 'receiver_address_privacy',
                address.receiver_phone_privacy AS 'receiver_phone_privacy'
            FROM
                oms_order_info info
                LEFT JOIN order_delivery_address address ON info.oms_order_no = address.oms_order_no 
            WHERE
                info.created >= "{start_time}" 
                AND info.created <= "{end_time}"
                AND info.third_platform_code=27

        """
        address_result = db_dscloud(address_B2C_sql)
        address_data = address_result['data']
        # 遍历收货人信息详情
        for address_item in address_data:
            order_no = address_item['order_no']
            null_value_list = []
            null_privacy_list = []
            # 遍历详情各个字段的值
            for item in address_item:
                address_value = address_item[item]
                # 判断值是否为空
                if address_value is None or address_value == "":
                    # 判断为空字段是否为原始密文
                    if 'privacy' in item:
                        null_privacy_list.append(item)
                    else:
                        null_value_list.append(item)
                if item == "privacy_detail":
                    if address_value == "{}":
                        null_privacy_list.append(item)
            if len(null_value_list) > 0:
                null_dict = {"order_no": order_no, "exec_type": "param_exec", "exec_msg": "参数值未完成解密",
                             "param": null_value_list,"order_log": False}
                result.append(null_dict)
            if len(null_privacy_list) > 0:
                null_dict = {"order_no": order_no, "exec_type": "privacy_exec", "exec_msg": "原始密文缺失",
                             "param": null_privacy_list,"order_log": False}
                result.append(null_dict)
        return result
    except Exception as e:
        raise e


if __name__ == "__main__":
    body = {"start_time": "2024-05-25 00:00:00", "end_time": "2024-05-27 00:00:00"}
    result = moni_order_address(body)
    print(result)
