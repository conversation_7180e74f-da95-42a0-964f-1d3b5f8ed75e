# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/10 17:51
@Auth ： 逗逗的小老鼠
@File ：moni_order_weshop.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from lib.db_conf import db_yx_middle_order,db_yx_base_info,db_insight
from lib.deal_ini import readini
from lib.deal_sql import dict_to_sql_insert
import datetime


"""
    微商城订单折扣异常
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_weshop_discount(body):
    "微商城订单折扣异常"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        if 'related_id' in body:
            related_id=body['related_id']
        else:
            related_id = int(datetime.datetime.now().timestamp())
        order_discount_value = readini("weshop_moni.ini", "order_threshold", "order_discount_value")
        amount_sql=f"""
        SELECT 
            id,store_id,member_id,member_card,member_phone,total_goods_number,total_order_amount,integral_deduction,coupon_deduction,activity_discount_amont,other_discount_amont,total_actual_order_amount,actually_paid,is_b2c_order,(actually_paid/total_order_amount)*100 AS 'discount_value'
        FROM 
            order_info 
        WHERE 
            order_time>="{start_time}" AND order_time<="{end_time}" AND pay_status=1 AND order_status !=30
        """
        amount_result=db_yx_middle_order(amount_sql)
        amount_data=amount_result['data']
        exec_list=[]
        member_card_list=[]
        member_phone_list=[]
        for amount_item in amount_data:
            id=amount_item['id']
            store_id=amount_item['store_id']
            total_order_amount=amount_item['total_order_amount']
            discount_value=amount_item['discount_value']
            actually_paid=amount_item['actually_paid']
            member_card=amount_item['member_card']
            member_phone=amount_item['member_phone']
            if discount_value is None or discount_value=="":
                pass
            else:
                if float(discount_value) < float(order_discount_value):
                    discount_value = float(discount_value)
                    member_phone_list.append(member_phone)
                    member_card_list.append(member_card)
                    data = {"msg": "订单折扣异常", "order_no": str(id), "store_id": store_id,"member_card":member_card,"member_phone":member_phone,
                            "total_order_amount": round(float(total_order_amount), 2),"start_time":start_time,"end_time":end_time,"related_id":related_id,"threshold_value":order_discount_value,
                            "actually_paid": round(float(actually_paid), 2), "discount_value": round(discount_value, 2)}
                    exec_list.append(data)
                    result.append(data)
        # 判断下单人是否系统用户
        baseinfo_result = base_info_employee(member_card_list, member_phone_list)
        for exec_item in exec_list:
            member_card = exec_item['member_card']
            member_phone = exec_item['member_phone']
            for baseinfo_item in baseinfo_result:
                mobile = baseinfo_item['mobile']
                user_code = baseinfo_item['user_code']
                if member_card == user_code or member_phone == mobile:
                    if exec_item in result:
                        result.remove(exec_item)
        return result
    except Exception as e:
        raise e


"""
    微商城会员下单异常
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_weshop_member(body):
    "微商城会员下单异常"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        if 'related_id' in body:
            related_id = body['related_id']
        else:
            related_id = int(datetime.datetime.now().timestamp())
        order_member_buynum = readini("weshop_moni.ini", "order_threshold", "order_member_buynum")
        amount_sql=f"""
        SELECT member_id,member_card,member_phone,COUNT(*) AS 'buy_count' FROM order_info WHERE order_time>="{start_time}" AND order_time<="{end_time}"  GROUP BY member_id
        """
        member_result=db_yx_middle_order(amount_sql)
        member_data=member_result['data']
        exec_list = []
        member_card_list = []
        member_phone_list = []
        for amember_item in member_data:
            member_id=amember_item['member_id']
            member_card=amember_item['member_card']
            member_phone=amember_item['member_phone']
            buy_count=amember_item['buy_count']
            if buy_count is None or buy_count=="":
                pass
            else:
                if int(buy_count) >int(order_member_buynum):
                    member_phone_list.append(member_phone)
                    member_card_list.append(member_card)
                    data = {"member_id": member_id, "member_card": member_card, "member_phone": member_phone,"start_time":start_time,"end_time":end_time,"related_id":related_id,"threshold_value":order_member_buynum,
                            "buy_count": buy_count, "msg": "用户下单量异常"}
                    exec_list.append(data)
                    result.append(data)
        # 判断下单人是否系统用户
        baseinfo_result = base_info_employee(member_card_list, member_phone_list)
        for exec_item in exec_list:
            member_card = exec_item['member_card']
            member_phone = exec_item['member_phone']
            for baseinfo_item in baseinfo_result:
                mobile = baseinfo_item['mobile']
                user_code = baseinfo_item['user_code']
                if member_card == user_code or member_phone == mobile:
                    if exec_item in result:
                        result.remove(exec_item)
        return result
    except Exception as e:
        raise e


"""
    微商城门店订单异常
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_weshop_store(body):
    "微商城门店订单异常"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        if 'related_id' in body:
            related_id = body['related_id']
        else:
            related_id = int(datetime.datetime.now().timestamp())
        order_store_sellnum = readini("weshop_moni.ini", "order_threshold", "order_store_sellnum")
        sell_sql=f"""
        SELECT store_id,send_store_code,COUNT(*) AS 'sell_count' FROM order_info WHERE order_time>="{start_time}" AND order_time<="{end_time}" GROUP BY store_id
        """
        sell_result=db_yx_middle_order(sell_sql)
        sell_data=sell_result['data']
        for sell_item in sell_data:
            store_id=sell_item['store_id']
            send_store_code=sell_item['send_store_code']
            sell_count=sell_item['sell_count']
            if store_id =="10060040" or store_id=="WSC1111":
                pass
            else:
                if int(sell_count) >int(order_store_sellnum):
                    data={"store_id":store_id,"send_store_code":send_store_code,"sell_count":sell_count,"msg":"门店销售单量异常","start_time":start_time,"end_time":end_time,"related_id":related_id,"threshold_value":order_store_sellnum}
                    result.append(data)
        return result
    except Exception as e:
        raise e


"""
    微商城单个订单商品数量异常
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_weshop_goods(body):
    "微商城单个订单商品数量异常"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        if 'related_id' in body:
            related_id = body['related_id']
        else:
            related_id = int(datetime.datetime.now().timestamp())
        order_goods_number = readini("weshop_moni.ini", "order_threshold", "order_goods_number")
        good_num=int(order_goods_number)
        goods_sql=f"""
        SELECT id,member_card,member_phone,store_id,send_store_code,total_goods_number FROM order_info WHERE order_time>="{start_time}" AND order_time<="{end_time}" AND total_goods_number>{good_num}
        """
        goods_result=db_yx_middle_order(goods_sql)
        goods_data=goods_result['data']
        exec_list = []
        member_card_list = []
        member_phone_list = []
        for goods_item in goods_data:
            order_no=goods_item['id']
            member_card=goods_item['member_card']
            send_store_code=goods_item['send_store_code']
            total_goods_number=goods_item['total_goods_number']
            store_id=goods_item['store_id']
            member_phone=goods_item['member_phone']
            # 排除云仓店
            if store_id =="10060040" or store_id=="WSC1111":
                pass
            else:
                member_phone_list.append(member_phone)
                member_card_list.append(member_card)
                data={"order_no":order_no,"member_card":member_card,"member_phone":member_phone,"send_store_code":send_store_code,"total_goods_number":total_goods_number,"store_id":store_id,"msg":"订单商品数量异常","start_time":start_time,"end_time":end_time,"related_id":related_id,"threshold_value":order_goods_number}
                exec_list.append(data)
                result.append(data)
        # 判断下单人是否系统用户
        baseinfo_result = base_info_employee(member_card_list, member_phone_list)
        for exec_item in exec_list:
            member_card = exec_item['member_card']
            member_phone = exec_item['member_phone']
            for baseinfo_item in baseinfo_result:
                mobile = baseinfo_item['mobile']
                user_code = baseinfo_item['user_code']
                if member_card == user_code or member_phone == mobile:
                    if exec_item in result:
                        result.remove(exec_item)
        return result
    except Exception as e:
        raise e


"""
    数据写入数据库
"""
def order_sale_weshop_db_insert(body,detail_result):
    try:
        insert_info_result=[]
        insert_detail_result = []
        info_list = []
        start_time = body['start_time']
        end_time = body['end_time']
        info_table=body['info_table']
        detail_table=body['detail_table']
        related_id=body['related_id']
        exec_count=len(detail_result)
        info_result={"related_id":related_id,"start_time":start_time,"end_time":end_time,"exec_count":exec_count}
        info_list.append(info_result)
        # 将结果信息写入汇总信息表
        info_sql_list=dict_to_sql_insert(info_table,info_list)
        for info_sql_item in info_sql_list:
            info_insert_result=db_insight(info_sql_item)
            insert_info_result.append(info_insert_result)
        # 将详情数据写入详情数据表
        detail_sql_item = dict_to_sql_insert(detail_table, detail_result)
        # 将监测数据写入库
        for detail_sql_item in detail_sql_item:
            detail_insert_result = db_insight(detail_sql_item)
            insert_detail_result.append(detail_insert_result)
        result={"insert_detail_result":insert_detail_result,"insert_info_result":insert_info_result}
        return result
    except Exception as e:
        raise e

"""
    判断用户是否员工
"""
def base_info_employee(member_card_list,member_phone_list):
    try:
        member_card_text=""
        for member_card_item in member_card_list:
            member_card_file=f"'{member_card_item}',"
            member_card_text=member_card_text+member_card_file

        member_card_text=member_card_text+"'test'"
        member_phone_text=""
        for member_phone_item in member_phone_list:
            member_phone_file=f"'{member_phone_item}',"
            member_phone_text=member_phone_text+member_phone_file
        member_phone_text = member_phone_text + "'test'"

        base_info_sql=f"""SELECT id,user_code,account,mobile,user_type FROM sys_user WHERE (mobile IN ({member_phone_text}) OR user_code IN ({member_card_text})) AND user_status=1"""
        print(base_info_sql)
        base_info_result=db_yx_base_info(base_info_sql)
        base_info_data=base_info_result['data']
        return base_info_data
    except Exception as e:
        raise e


if __name__=="__main__":
    bady={"start_time":"2024-04-10 10:00:00","end_time":"2024-04-10 22:00:00"}
    # data=order_sale_weshop_discount(bady)
    # print(data)
    memeber=order_sale_weshop_member(bady)
    print(memeber)
    # member_card="************"
    # member_phone="***********"
    # base_info=base_info_employee(member_card,member_phone)
    # print(base_info)