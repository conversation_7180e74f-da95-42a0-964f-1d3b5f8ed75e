# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/14 11:44
@Auth ： 逗逗的小老鼠
@File ：moni_order_stock.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.db_conf import db_yx_middle_order,db_yx_dscloud,db_yn_oms,db_yx_commodity_base
from lib.deal_text import text_find,text_write
from structure.operate.operate_order_stock import order_sale_o2o_stock_free
import datetime

"""
    下账订单库存释放信息核对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_o2o_stock_info(body):
    try:
        result = []
        # 释放记录文件名称
        stock_record_file="order_stock_free_record.txt"
        # 需要释放的订单列表
        free_order_list=[]
        # 查询下账开始时间
        start_time = body['start_time']
        # 查询下账结束时间
        end_time = body['end_time']
        query_order_in=""
        # 获取时间内下账的订单号
        order_list_sql=f"""SELECT info.third_order_no AS 'third_order_no',info.order_no AS 'order_no' FROM order_info info WHERE info.erp_state=100 AND info.bill_time>='{start_time}' AND info.bill_time<='{end_time}' AND info.order_type !=5 AND  info.service_mode='O2O' AND info.mer_code='500001'"""
        order_list_result=db_yx_dscloud(order_list_sql)
        for order_list_item in order_list_result['data']:
            order_no=order_list_item['order_no']
            query_order_in=query_order_in+f"'{order_no}',"
        query_order_in=query_order_in+"'0'"
        # 查询库存释放异常的订单
        stock_info_sql=f"""SELECT order_no AS 'order_no',erp_code AS 'erp_code',SUM(change_stock) AS 'stock' FROM commodity_stock_order_log WHERE order_no IN ({query_order_in})  GROUP BY order_no,erp_code  HAVING SUM(change_stock)!=0"""
        stock_info_result=db_yx_commodity_base(stock_info_sql)
        for stock_info_item in stock_info_result['data']:
            sys_order_no=stock_info_item['order_no']
            print(sys_order_no)
            erp_code=stock_info_item['erp_code']
            stock=stock_info_item['stock']
            # 累计库存小于0 库存未释放
            if stock<0:
                # 将订单号转为str类型
                str_order_no=str(sys_order_no)
                # 获取释放记录中，订单号的释放次数
                free_count=text_find(stock_record_file,str_order_no)
                stock_result={"sys_order_no":sys_order_no,"erp_code":erp_code,"stock":stock,"exce_obj":"O2O占用库存未释放","free_count":free_count}
                # 将未释放的订单号添加到需要释放的列表中
                free_order_list.append(str_order_no)
            # 累计库存大于0 库存释放重复
            else:
                stock_result={"sys_order_no":sys_order_no,"erp_code":erp_code,"stock":stock,"exce_obj":"O2O占用库存释放重复","free_count":0}
            result.append(stock_result)
        # 释放未释放的订单
        free_result=order_sale_o2o_stock_free(free_order_list)
        current_date = datetime.datetime.now()
        if free_result==200:
            order_free_record={"request_time":current_date,"order_list":free_order_list}
            text_write(stock_record_file,order_free_record)
        return result
    except Exception as e:
        raise e


"""
    未下账订单库存释放信息核对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_nobill_o2o_stock_info(body):
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        query_order_in=""
        # 获取时间内未下账的订单号
        order_list_sql = f"""
            SELECT info.third_order_no AS 'third_order_no', info.order_no AS 'order_no' 
            FROM order_info info 
            WHERE info.erp_state != 100 AND info.created >= "{start_time}" 
                AND info.created <= "{end_time}" 
                AND info.service_mode = 'O2O' 
                AND info.organization_code IS NOT NULL 
                AND info.order_no NOT IN (
                    SELECT
                        order_no 
                    FROM
                        order_info 
                    WHERE
                        created >= "{start_time}" 
                        AND created <= "{end_time}" 
                        AND service_mode = 'O2O' 
                    AND order_state = 102 
                    AND cancel_time IS NULL)        
        """
        order_list_result = db_yx_dscloud(order_list_sql)
        for order_list_item in order_list_result['data']:
            order_no=order_list_item['order_no']
            query_order_in=query_order_in+f"'{order_no}',"
        query_order_in=query_order_in+"'0'"
        # 获取时间内未下账商品详情
        order_detail_sql=f"""
                SELECT
                    info.third_order_no AS 'third_order_no',
                    info.order_no AS 'order_no',
                    info.erp_state AS 'erp_state',
                    info.created AS 'order_created', 
                    info.lock_msg AS  'lock_msg',                 
                    MAX(stock.serial_number) AS 'serial_number',
                    stock.type AS 'stock_type',
                    detail.id AS 'order_detail_id',
                    detail.erp_code AS 'erp_code',
                    detail.STATUS AS 'status',
                    ( detail.commodity_count-detail.refund_count ) AS 'commodity_count' 
                FROM
                    order_detail detail
                    LEFT JOIN order_info info ON detail.order_no = info.order_no 
                    LEFT JOIN commodity_stock stock ON stock.order_no =info.order_no AND detail.id=stock.order_detail_id
                WHERE
                    info.erp_state != 100 
                    AND info.erp_state != 110
                    AND info.created >= "{start_time}" 
                    AND info.created <= "{end_time}" 
                    AND info.service_mode = 'O2O' 
                    AND info.organization_code IS NOT NULL 
                    AND info.order_type !=5 
                    AND info.mer_code='500001'
                    AND info.order_no NOT IN ( SELECT order_no FROM order_info WHERE created >= "{start_time}" AND created <= "{end_time}" AND service_mode = 'O2O' AND order_state = 102 AND cancel_time IS NULL AND mer_code='500001') 
                GROUP BY
                    detail.order_no,
                    detail.id
        """
        order_detail_result=db_yx_dscloud(order_detail_sql)
        # 根据订单号列表获取库存详情
        order_stock_sql=f"""SELECT order_no AS 'order_no',erp_code AS 'erp_code',SUM(change_stock) AS 'stock',serial_number AS 'serial_number' FROM commodity_stock_order_log WHERE order_no IN ({query_order_in})  GROUP BY order_no,erp_code,serial_number"""
        order_stock_result=db_yx_commodity_base(order_stock_sql)
        # 遍历商品详情
        for detail_item in order_detail_result['data']:
            # 订单库订单号
            detail_order_no=detail_item['order_no']
            # 订单中台订单下账状态
            detail_erp_state=detail_item['erp_state']
            # 订单中台erp码
            detail_erp_code=detail_item['erp_code']
            # 订单中台商品状态
            detail_status=detail_item['status']
            # 订单中台下单时间
            order_created=detail_item['order_created']
            # 订单中台商品数量
            detail_count=detail_item['commodity_count']
            # 订单中台库存占用流水号
            detail_serial_number = detail_item['serial_number']
            # 订单中台库存占用状态
            stock_type=detail_item['stock_type']
            # 锁定信息
            lock_msg_flag=""
            lock_msg=detail_item['lock_msg']
            if lock_msg=='强审通过':
                lock_msg_flag="强审导致"
            # 遍历库存详情
            order_no_flag=0
            erp_code_flag=0
            stock_flag=0
            for stock_item in order_stock_result['data']:
                # 商品库占用记录订单号
                stock_order_no=stock_item['order_no']
                # 商品库占用记录erp码
                stock_erp_code=stock_item['erp_code']
                # 商品库实际占用库存数量
                stock_stock=stock_item['stock']
                # 商品中台库存占用流水号
                stock_serial_number=stock_item['serial_number']
                # 订单号一致
                if detail_order_no==stock_order_no:
                    order_no_flag=1
                    # 库存占用流水号一致
                    if str(detail_serial_number)==str(stock_serial_number):
                        stock_flag=1
                        # erp编码一致
                        if detail_erp_code == stock_erp_code:
                            erp_code_flag = 1
                            # 判断下账状态是否为：已取消/已退款
                            if detail_erp_state==110 or detail_erp_state==120:
                                if stock_stock != 0:
                                    stock_result = {"sys_order_no": detail_order_no, "detail_erp_code": detail_erp_code,"detail_serial_number":detail_serial_number,"detail_erp_state":detail_erp_state,"detail_status":detail_status,
                                                    "detail_num": 0, "stock_num": stock_stock,
                                                    "exce_obj": "已取消下账商品占用库存应为0"}
                                    result.append(stock_result)
                            else:
                                # 订单：商品状态为正常
                                if detail_status == 0:
                                    # 订单：是否存在占用商品
                                    if detail_count + stock_stock == 0:
                                        break
                                    else:
                                        order_status=order_erp_status(detail_order_no)
                                        if order_status==100 or detail_erp_state==110 or detail_erp_state==120:
                                            if stock_stock==0:
                                                print(f"{detail_order_no}订单已下账")
                                                break
                                        else:
                                            stock_result = {"sys_order_no": detail_order_no, "detail_erp_code": detail_erp_code,"detail_serial_number":detail_serial_number,"detail_erp_state":detail_erp_state,"detail_status":detail_status,
                                                            "detail_num": detail_count, "stock_num": stock_stock,
                                                            "exce_obj": f"O2O占用库存数量错误【{lock_msg_flag}】"}
                                            result.append(stock_result)
                                # 订单：商品状态为库存不足或商品不存在
                                elif detail_status == 1 or detail_status == 2:
                                    # 订单可能存在正常单手工标记为异常，故而会存在占用记录，但占用数量应为0
                                    if stock_stock!=0:
                                        stock_result = {"sys_order_no": detail_order_no, "detail_erp_code": detail_erp_code,"detail_serial_number":detail_serial_number,"detail_erp_state":detail_erp_state,"detail_status":detail_status,
                                                        "detail_num": detail_count, "stock_num": stock_stock,
                                                        "exce_obj": "库存不足/商品不存在时不应占用库存或库存占用为0"}
                                        result.append(stock_result)
                                # 订单：商品状态为已换货或已退款
                                elif detail_status == 10 or detail_status == 11:
                                # 判断是否存在对应的商品占用库存记录，若存在则需要占用库存为0
                                    if stock_stock != 0:
                                        stock_result = {"sys_order_no": detail_order_no, "detail_erp_code": detail_erp_code,"detail_serial_number":detail_serial_number,"detail_erp_state":detail_erp_state,"detail_status":detail_status,
                                                        "detail_num": 0, "stock_num": stock_stock,
                                                        "exce_obj": "已换货/已退款商品占用库存应为0"}
                                        result.append(stock_result)
            # 若订单未找到对应库存数据且商品状态为正常则判断为订单占用库存数据缺失
            if order_no_flag==0:
                if detail_status==0:
                    stock_result = {"sys_order_no": detail_order_no, "detail_erp_code": detail_erp_code,"detail_erp_state":detail_erp_state,"detail_serial_number":detail_serial_number,"detail_status":detail_status,
                                    "detail_num": detail_count, "stock_num": "",
                                    "exce_obj": f"订单占用库存数据缺失【{lock_msg_flag}】"}
                    result.append(stock_result)
            else:
                if detail_serial_number == "" or detail_serial_number is None:
                    stock_result = {"sys_order_no": detail_order_no, "detail_erp_code": detail_erp_code,
                                    "detail_serial_number": detail_serial_number,
                                    "detail_erp_state": detail_erp_state, "detail_status": detail_status,
                                    "detail_num": detail_count, "stock_num": "",
                                    "exce_obj": "订单中商品未生成库存占用流水号"}
                    result.append(stock_result)
                else:
                    # 若未找到商品占用信息且商品状态为正常则判断商品占用库存失败
                    if stock_flag==0 :
                        if detail_status==0 and detail_erp_state!=99:
                            stock_result = {"sys_order_no": detail_order_no, "detail_erp_code": detail_erp_code,"detail_erp_state":detail_erp_state,"detail_serial_number":detail_serial_number,"detail_status":detail_status,
                                            "detail_num": detail_count, "stock_num": "",
                                            "exce_obj": f"商品占用库存失败/缺失【{lock_msg_flag}】"}
                            result.append(stock_result)
                    else:
                        if erp_code_flag==0:
                            stock_result = {"sys_order_no": detail_order_no, "detail_erp_code": detail_erp_code,
                                            "detail_erp_state": detail_erp_state,
                                            "detail_serial_number": detail_serial_number,
                                            "detail_status": detail_status,
                                            "detail_num": detail_count, "stock_num": "",
                                            "exce_obj": "占用记录中同流水号中erp不一致"}
                            result.append(stock_result)
        return result
    except Exception as e:
        raise e



"""
    订单下账状态查询
    :param order_no:系统订单号
    :return result：查询结果
"""
@exception(logger)
def order_erp_status(order_no):
    try:
        erp_status=0
        order_sql=f""" SELECT erp_state FROM order_info WHERE order_no='{order_no}' """
        order_result=db_yx_dscloud(order_sql)
        for order_item in  order_result['data']:
            erp_status=order_item['erp_state']


        return erp_status
    except Exception as e:
        raise e



if __name__=="__main__":
    body={"start_time":"2024-08-13 09:00:00","end_time":"2024-08-13 14:00:00"}
    # result=order_sale_o2o_stock_info(body)
    result=order_sale_nobill_o2o_stock_info(body)
    print(len(result))
    print(result)