# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/22 11:24
@Auth ： 逗逗的小老鼠
@File ：moni_order_erp_status.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from lib.db_conf import db_yx_middle_order, db_yx_dscloud, db_yn_oms

"""
    销售单下账状态信息监测
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""


@exception(logger)
def order_sale_o2o_erp_status_xy(body):
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        erp_status_sql = f"""
            SELECT
                SUBSTRING(organization_code, 1, 1) AS 'organization_code',
                SUM(CASE WHEN erp_state=100  THEN 1 ELSE 0 END ) AS 'success_value',
                SUM(CASE WHEN erp_state=30  THEN 1 ELSE 0 END ) AS 'todo_value',
                SUM(CASE WHEN erp_state=20  THEN 1 ELSE 0 END ) AS 'lock_value',
                SUM(CASE WHEN erp_state=110  THEN 1 ELSE 0 END ) AS 'cancle_value',
                SUM(CASE WHEN erp_state=99  THEN 1 ELSE 0 END ) AS 'fail_value',
                SUM(CASE WHEN (erp_state!=99 AND erp_state!=100 AND erp_state!=30 AND erp_state!=20 AND erp_state!=110) THEN 1 ELSE 0 END ) AS 'unknow_value',
                COUNT(*) AS 'count' 
            FROM
                order_info 
            WHERE
                created >= "{start_time}" 
                AND created <= "{end_time}" 
                AND service_mode = 'O2O' 
            GROUP BY
                SUBSTRING(organization_code, 1, 1)    
        """
        erp_status_result = db_yx_dscloud(erp_status_sql)
        erp_status_data = erp_status_result['data']
        success_value_sum = 0
        todo_value_sum = 0
        lock_value_sum = 0
        cancle_value_sum = 0
        fail_value_sum = 0
        unknow_value_sum = 0
        erp_count_sum = 0
        for erp_item in erp_status_data:
            organization_code = erp_item['organization_code']
            # 下账成功
            success_value = erp_item['success_value']
            # 待下账
            todo_value = erp_item['todo_value']
            # 待锁定
            lock_value = erp_item['lock_value']
            # 取消下账
            cancle_value = erp_item['cancle_value']
            # 下账失败
            fail_value = erp_item['fail_value']
            # 未知状态
            unknow_value = erp_item['unknow_value']
            # 总数
            erp_count = erp_item['count']
            # 根据首字母获取省份名称
            province_value = order_province(organization_code)
            # 计算各个状态比例
            success_proportion = round(success_value / erp_count * 100, 2)
            todo_proportion = round(todo_value / erp_count * 100, 2)
            lock_proportion = round(lock_value / erp_count * 100, 2)
            cancle_proportion = round(cancle_value / erp_count * 100, 2)
            fail_proportion = round(fail_value / erp_count * 100, 2)
            unkonw_proportion = round(unknow_value / erp_count * 100, 2)
            success_msg = {"province_value": f"{province_value}({organization_code})", "success_value": success_value,
                           "success_proportion": success_proportion,
                           "todo_value": todo_value, "todo_proportion": todo_proportion, "lock_value": lock_value,
                           "lock_proportion": lock_proportion, "cancle_value": cancle_value,
                           "cancle_proportion": cancle_proportion,
                           "fail_value": fail_value, "fail_proportion": fail_proportion, "unknow_value": unknow_value,
                           "unkonw_proportion": unkonw_proportion, "erp_count": erp_count}
            result.append(success_msg)
            # 汇总各个状态数据
            success_value_sum = success_value_sum + success_value
            todo_value_sum = todo_value_sum + todo_value
            lock_value_sum = lock_value_sum + lock_value
            cancle_value_sum = cancle_value_sum + cancle_value
            fail_value_sum = fail_value_sum + fail_value
            unknow_value_sum = unknow_value_sum + unknow_value
            erp_count_sum = erp_count_sum + erp_count
        sum_success_proportion = round(success_value_sum / erp_count_sum * 100, 2)
        sum_todo_proportion = round(todo_value_sum / erp_count_sum * 100, 2)
        sum_lock_proportion = round(lock_value_sum / erp_count_sum * 100, 2)
        sum_cancle_proportion = round(cancle_value_sum / erp_count_sum * 100, 2)
        sum_fail_proportion = round(fail_value_sum / erp_count_sum * 100, 2)
        sum_unkonw_proportion = round(unknow_value_sum / erp_count_sum * 100, 2)
        sum_success_msg = {"province_value": f"全国", "success_value": success_value_sum,
                           "success_proportion": sum_success_proportion,
                           "todo_value": todo_value_sum, "todo_proportion": sum_todo_proportion,
                           "lock_value": lock_value_sum,
                           "lock_proportion": sum_lock_proportion, "cancle_value": cancle_value_sum,
                           "cancle_proportion": sum_cancle_proportion,
                           "fail_value": fail_value_sum, "fail_proportion": sum_fail_proportion,
                           "unknow_value": unknow_value_sum,
                           "unkonw_proportion": sum_unkonw_proportion, "erp_count": erp_count_sum}
        result.append(sum_success_msg)
        return result
    except Exception as e:
        raise e


"""
    订单所属省份字典
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""


@exception(logger)
def order_province(province_key):
    try:
        if province_key == 'A':
            province_value = '云南'
        elif province_key == 'B':
            province_value = '广西'
        elif province_key == 'C':
            province_value = '贵州'
        elif province_key == 'H':
            province_value = '四川'
        elif province_key == 'G':
            province_value = '陕西'
        elif province_key == 'F':
            province_value = '重庆'
        elif province_key == 'F':
            province_value = '重庆'
        elif province_key == 'M':
            province_value = '海南'
        elif province_key == 'K':
            province_value = '天津'
        elif province_key == 'E':
            province_value = '攀枝花'
        elif province_key == 'N':
            province_value = '河南'
        elif province_key == 'J':
            province_value = '上海'
        elif province_key is None:
            province_value = '未知'
        else:
            province_value = '加盟店'
        return province_value
    except Exception as e:
        raise e
