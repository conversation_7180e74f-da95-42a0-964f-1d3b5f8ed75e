# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/10 10:00
@Auth ： 逗逗的小老鼠
@File ：compare_order_info.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.db_conf import db_yx_middle_order,db_yx_dscloud,db_yn_oms
from structure.monitor.compare_order_status import order_return_status_dict,order_sale_status_dict
from decimal import Decimal


"""
    销售单信息比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_o2o_info(body):
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        # 心云中台销售单详情查询
        xy_order_sale_sql=f"""SELECT info.third_order_no AS 'order_no',info.order_state AS 'status',dict.is_open_new AS 'is_open',pay.buyer_actual_amount AS 'actual_amount',pay.total_amount AS 'total_amount',erp.bill_total_amount AS 'bill_total_amount' FROM order_info info LEFT JOIN inner_store_dictionary dict ON info.organization_code=dict.organization_code LEFT JOIN order_pay_info pay ON info.order_no=pay.order_no LEFT JOIN erp_bill_info erp ON info.order_no=erp.order_no WHERE info.third_platform_code='43' AND info.created>"{start_time}" AND info.created < "{end_time}" AND info.service_mode='O2O' """
        xy_order_sale_result=db_yx_dscloud(xy_order_sale_sql)
        # 心云中台未切店订单赠品数量查询
        xy_order_sale_gift_sql=f"""SELECT info.third_order_no AS 'order_no', SUM(detail.commodity_count) AS 'gift_count' FROM order_info info LEFT JOIN inner_store_dictionary dict ON info.organization_code=dict.organization_code  LEFT JOIN order_detail detail ON info.order_no=detail.order_no WHERE info.third_platform_code='43' AND info.created>"{start_time}" AND info.created < "{end_time}" AND info.service_mode='O2O' AND dict.is_open_new=2 AND detail.bill_price=0 GROUP BY info.third_order_no"""
        xy_order_sale_gift_result=db_yx_dscloud(xy_order_sale_gift_sql)
        # 微商城销售单详情查询
        middle_order_sale_sql=f"""SELECT id AS 'order_no',order_status AS 'status',delivery_type AS 'delivery_type',actually_paid+pay_balance_amount AS 'actual_amount',total_order_amount AS 'total_amount' FROM order_info WHERE order_time>"{start_time}" AND order_time < "{end_time}" AND order_status !=20 AND order_status !=2 AND is_b2c_order=0"""
        middle_order_sale_result=db_yx_middle_order(middle_order_sale_sql)
        # 雨诺销售单详情查询
        yn_order_sale_sql=f"""SELECT ORDERCODE AS 'order_no',ORDERSTATUS AS 'status',PAYAMOUNT AS 'actual_amount',PRODUCTSAMOUNT AS 'total_amount',ACCOUNTAMOUNT AS 'bill_amount' FROM order_std WHERE ORDERFROM='111' AND CREATETIME>'{start_time}' AND CREATETIME<'{end_time}' AND INNERSTATUS=1"""
        yn_order_sale_result=db_yn_oms(yn_order_sale_sql)

        # 订单信息比对
        # 遍历微商城订单信息
        for middle_item in middle_order_sale_result['data']:
            # 微商城订单号
            middle_order_no=str(middle_item['order_no'])
            # 微商城订单状态
            middle_status=middle_item['status']
            # 微商城实付金额
            middle_actual_amount=middle_item['actual_amount']
            # 微商城订单商品金额
            middle_total_amount=middle_item['total_amount']
            # 微商城订单配送模式：0普通快递1配送上门2门店自提
            middle_delivery_type=middle_item['delivery_type']
            # 定义心云中台与微商城订单号匹配标识，若匹配则变更为1
            ds_middle_order_flag=0
            for xy_item in xy_order_sale_result['data']:
                # 心云中台订单号
                xy_order_no = str(xy_item['order_no'])
                # 心云中台订单状态
                xy_status = xy_item['status']
                # 心云中台实付金额
                xy_actual_amount = xy_item['actual_amount']
                # 心云中台订单商品金额
                xy_total_amount = xy_item['total_amount']
                # 心云中台订单下账金额
                xy_bill_amount = xy_item['bill_total_amount']
                # 心云中台订单切店状态
                xy_open_status=xy_item['is_open']
                if middle_order_no==xy_order_no:
                    ds_middle_order_flag=1
                    xy_status_value=order_sale_status_dict("xy",xy_status)
                    # 微商城状态为待退货/待退款不进行比对
                    if middle_status !=8 and middle_status!=10:
                        middle_status_value=order_sale_status_dict("middle",middle_status)
                        if middle_delivery_type==2:
                            if xy_status_value=="待拣货" or xy_status_value=="待配送" or xy_status_value=="配送中" :
                                xy_middle_status = "待收货"
                            elif xy_status_value=="已取消":
                                xy_middle_status="已关闭"
                            else:
                                xy_middle_status=xy_status_value
                        else:
                            if  xy_status_value=="待拣货"  or xy_status_value=="待配送":
                                xy_middle_status="待发货"
                            elif xy_status_value=="配送中":
                                xy_middle_status="待收货"
                            elif xy_status_value=="已取消":
                                xy_middle_status="已关闭"
                            else:
                                xy_middle_status=xy_status_value
                        # 心云微商城状态比对
                        if xy_middle_status !=middle_status_value:
                            ds_middle_order_code_result = {"diff_sys": "心云 VS 微商城", "diff_obj": "订单状态不匹配",
                                                            "order_code": middle_order_no,
                                                            "front_sys_value": f"{xy_status_value}({xy_status})",
                                                            "behind_value": f"{middle_status_value}({middle_status})"}
                            result.append(ds_middle_order_code_result)
                    # 心云微商城实付金额比对
                    if xy_actual_amount!=middle_actual_amount:
                        xy_refund_consumer_refund=0
                        xy_refund_sql=f"""SELECT SUM(total_food_amount) AS 'total_amount',SUM(consumer_refund) AS 'consumer_refund' FROM refund_order refund LEFT JOIN order_info info ON refund.third_order_no=info.third_order_no WHERE refund.third_order_no='{xy_order_no}' AND refund.type=0 AND refund.state=100 AND (IF(ISNULL(info.pick_time),NOW(),info.pick_time)) >refund.create_time"""
                        xy_refund_result=db_yx_dscloud(xy_refund_sql)
                        if xy_refund_result['data'][0]['consumer_refund'] != None and xy_refund_result['data'][0]['consumer_refund'] != "":
                            xy_refund_consumer_refund=xy_refund_consumer_refund+xy_refund_result['data'][0]['consumer_refund']
                        if (xy_actual_amount+xy_refund_consumer_refund)!=middle_actual_amount:
                            ds_middle_actual_amount_result = {"diff_sys": "心云 VS 微商城", "diff_obj": "订单实付金额不一致",
                                                            "order_code": middle_order_no,
                                                            "front_sys_value": f"{xy_actual_amount}【订单金额】+{xy_refund_consumer_refund}【已退金额】",
                                                            "behind_value": middle_actual_amount}
                            result.append(ds_middle_actual_amount_result)
                    # 心云微商城商品金额比对
                    if xy_total_amount != middle_total_amount:
                        xy_refund_total_amount=0
                        xy_refund_sql = f"""SELECT SUM(total_food_amount) AS 'total_amount',SUM(consumer_refund) AS 'consumer_refund' FROM refund_order refund LEFT JOIN order_info info ON refund.third_order_no=info.third_order_no WHERE refund.third_order_no='{xy_order_no}' AND refund.type=0 AND refund.state=100 AND (IF(ISNULL(info.pick_time),NOW(),info.pick_time)) >refund.create_time"""
                        print(xy_refund_sql)
                        xy_refund_result = db_yx_dscloud(xy_refund_sql)

                        if xy_refund_result['data'][0]['total_amount'] != None and xy_refund_result['data'][0]['total_amount'] != "":
                            xy_refund_total_amount = xy_refund_total_amount+xy_refund_result['data'][0]['total_amount']
                        if (xy_total_amount+xy_refund_total_amount) != middle_total_amount:
                            ds_middle_total_amount_result = {"diff_sys": "心云 VS 微商城", "diff_obj": "订单商品金额不一致",
                                                              "order_code": middle_order_no,
                                                              "front_sys_value": f"{xy_total_amount}【订单金额】+{xy_refund_total_amount}【已退金额】",
                                                              "behind_value": middle_total_amount}
                            result.append(ds_middle_total_amount_result)
                    if xy_open_status=='2':
                        xy_yn_order_flag=0
                        xy_gift_amount=0
                        for xy_gift_item in xy_order_sale_gift_result['data']:
                            xy_gift_orderno=xy_gift_item['order_no']
                            if xy_gift_orderno == xy_order_no:
                                xy_gift_count=xy_gift_item['gift_count']
                                xy_gift_amount=Decimal('0.01')*xy_gift_count+xy_gift_amount

                        for yn_item in yn_order_sale_result['data']:
                            # 雨诺订单号
                            yn_order_no = str(yn_item['order_no'])
                            # 雨诺订单状态
                            yn_status = yn_item['status']
                            # 雨诺实付金额
                            yn_actual_amount = yn_item['actual_amount']
                            # 雨诺订单商品金额
                            yn_total_amount = yn_item['total_amount']
                            # 雨诺订单下账金额
                            yn_bill_amount = yn_item['bill_amount']
                            if xy_order_no==yn_order_no:
                                xy_yn_order_flag=1
                                yn_status_value=order_sale_status_dict("yn",yn_status)
                                if xy_status_value=="已关闭" and yn_status_value=="已完成":
                                    pass
                                elif xy_status_value !=yn_status_value:
                                    ds_yn_order_code_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "订单状态不匹配",
                                                                   "order_code": xy_order_no,
                                                                   "front_sys_value": f"{xy_status_value}({xy_status})",
                                                                   "behind_value": f"{yn_status_value}({yn_status})"}
                                    result.append(ds_yn_order_code_result)
                                if xy_bill_amount !=yn_bill_amount-xy_gift_amount:
                                    ds_yn_bill_amount_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "订单下账金额不一致",
                                                                   "order_code": xy_order_no,
                                                                   "front_sys_value": xy_bill_amount,
                                                                   "behind_value": yn_bill_amount}
                                    result.append(ds_yn_bill_amount_result)
                                if xy_total_amount !=yn_total_amount-xy_gift_amount:
                                    ds_yn_total_amount_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "订单商品金额不一致",
                                                                   "order_code": xy_order_no,
                                                                   "front_sys_value": xy_total_amount,
                                                                   "behind_value": yn_total_amount}
                                    result.append(ds_yn_total_amount_result)
                                if xy_actual_amount !=yn_actual_amount-xy_gift_amount:
                                    ds_yn_actual_amount_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "订单实付金额不一致",
                                                                   "order_code": xy_order_no,
                                                                   "front_sys_value": xy_actual_amount,
                                                                   "behind_value": yn_actual_amount}
                                    result.append(ds_yn_actual_amount_result)
                        if xy_yn_order_flag == 0:
                            ds_yn_return_code_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "订单号不存在",
                                                            "order_code": xy_order_no,
                                                            "front_sys_value": "",
                                                            "behind_value": xy_order_no}
                            result.append(ds_yn_return_code_result)
            if ds_middle_order_flag==0:
                ds_middle_return_code_result={"diff_sys": "心云 VS 微商城", "diff_obj": "订单号不存在",
                                  "order_code":middle_order_no,
                                  "front_sys_value": "",
                                  "behind_value": middle_order_no}
                result.append(ds_middle_return_code_result)
        return result
    except Exception as e:
        raise e



"""
    退款单信息比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_return_o2o_info(body):
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        # 微商城退款单详情查询
        middle_return_sql=f"""SELECT quest.id AS 'return_code',quest.order_id AS 'order_code',(quest.refund_amount+quest.freight_amount) AS 'refund_amount',(quest.actual_refund_amount+(IF(ISNULL(quest.refund_balance_amount),0,quest.refund_balance_amount))) AS 'actual_refund_amount',quest.`status` AS 'return_status' FROM return_quest quest LEFT JOIN order_info info ON quest.order_id=info.id WHERE info.order_time>"{start_time}" AND info.order_time < "{end_time}" AND info.order_status !=20 AND info.order_status !=2 AND info.is_b2c_order=0 AND quest.refund_reason != '商家发起退款'"""
        middle_return_result=db_yx_middle_order(middle_return_sql)
        # 心云中台退款单详情查询
        ds_return_sql=f"""SELECT dict.is_open_new AS 'is_open',refund.third_refund_no AS 'return_code',refund.consumer_refund AS 'consumer_refund',refund.total_food_amount AS 'total_amount',erp.refund_merchant_total AS 'bill_total_amount',refund.state AS 'return_state' FROM refund_order refund LEFT JOIN order_info info ON refund.order_no = info.order_no LEFT JOIN inner_store_dictionary dict ON info.organization_code=dict.organization_code LEFT JOIN erp_refund_info erp ON refund.refund_no=erp.refund_no WHERE info.third_platform_code='43' AND info.created>"{start_time}" AND info.created < "{end_time}" AND info.service_mode='O2O'"""
        ds_return_result=db_yx_dscloud(ds_return_sql)
        # 雨诺退款单详情查询
        yn_return_sql=f"""SELECT returnorder.RETURNCODE AS 'RETURNCODE',returnorder.PRODUCTSAMOUNT AS 'PRODUCTSAMOUNT',returnorder.RETURNAMOUNT AS 'RETURNAMOUNT',returnorder.RETURNSTATUS AS 'RETURNSTATUS'  FROM returnorder_std returnorder LEFT JOIN order_std order_s ON returnorder.ORDERCODE =order_s.ORDERCODE WHERE order_s.ORDERFROM='111' AND order_s.CREATETIME>'{start_time}' AND order_s.CREATETIME<'{end_time}' AND order_s.INNERSTATUS=1"""
        yn_return_result=db_yn_oms(yn_return_sql)

        # 差异订单信息比对
        for middle_return_item in middle_return_result['data']:
            # 微商城正向单号
            middle_order_code=str(middle_return_item['order_code'])
            # 微商城退款单号
            middle_return_code=str(middle_return_item['return_code'])
            # 微商城退款金额
            middle_return_amount=middle_return_item['refund_amount']
            # 微商城实退金额
            middle_return_actual_amount=middle_return_item['actual_refund_amount']
            # 微商城退款状态
            middle_return_status=middle_return_item['return_status']
            # 定义心云与微商城退款单号标识，默认为0，若匹配则变更为1
            ds_middle_returncode_flag = 0

            for ds_return_item in ds_return_result['data']:
                # 心云中台退款单号
                ds_return_code = str(ds_return_item['return_code'])
                # 心云中台退款金额
                ds_return_amount = ds_return_item['total_amount']
                # 心云中台实退金额
                ds_return_actual_amount = ds_return_item['consumer_refund']
                # 心云中台退款状态
                ds_return_status = ds_return_item['return_state']
                # 心云中台下账金额
                ds_return_bill_amount = ds_return_item['bill_total_amount']
                # 心云中台切店状态
                ds_return_open_status = ds_return_item['is_open']
                # 判断心云系统退款单号与微商城退款单号是否一致
                if middle_return_code==ds_return_code:
                    ds_middle_returncode_flag=1
                    # 判断心云系统退款单状态与微商城退款单状态是否一致
                    ds_return_status_value=order_return_status_dict('ds',ds_return_status)
                    middle_return_status_value=order_return_status_dict('middle',middle_return_status)
                    if ds_return_status_value!=middle_return_status_value:
                        ds_middle_return_status_result={"diff_sys": "心云 VS 微商城", "diff_obj": "退款状态不一致","order_code":middle_order_code,"return_order_code": middle_return_code,"front_sys_value": f"{ds_return_status_value}({ds_return_status})","behind_value": f"{middle_return_status_value}({middle_return_status})"}
                        result.append(ds_middle_return_status_result)
                    # 判断心云系统退款金额与微商城退款金额是否一致
                    if ds_return_actual_amount!=middle_return_amount:
                        ds_middle_return_amount_result = {"diff_sys": "心云 VS 微商城", "diff_obj": "退款金额不一致",
                                                          "return_order_code": middle_return_code,
                                                          "order_code":middle_order_code,
                                                          "front_sys_value": ds_return_actual_amount,
                                                          "behind_value": middle_return_amount}
                        result.append(ds_middle_return_amount_result)
                    # 判断心云系统实际金额与微商城实际退款金额是否一致(仅判断，退款完成的订单)
                    if ds_return_status==100:
                        if ds_return_actual_amount!=middle_return_actual_amount:
                            ds_middle_return_actual_result = {"diff_sys": "心云 VS 微商城", "diff_obj": "退款金额不一致",
                                                              "return_order_code": middle_return_code,
                                                              "order_code": middle_order_code,
                                                              "front_sys_value": ds_return_actual_amount,
                                                              "behind_value": middle_return_actual_amount}
                            result.append(ds_middle_return_actual_result)

                    if ds_return_open_status=='2':
                        # 定义心云与雨诺退款单号标识，默认为0，若匹配则变更为1
                        ds_yn_returncode_flag = 0
                        for yn_return_item in yn_return_result['data']:
                            # 雨诺退款单号
                            yn_return_code=yn_return_item['RETURNCODE']
                            # 雨诺退款金额
                            yn_return_amount = yn_return_item['PRODUCTSAMOUNT']
                            # 雨诺退款状态
                            yn_return_status = yn_return_item['RETURNSTATUS']
                            # 雨诺下账金额
                            yn_return_bill_amount = yn_return_item['RETURNAMOUNT']
                            if ds_return_code==yn_return_code:
                                ds_yn_returncode_flag=1
                                # 获取雨诺系统退款状态值
                                yn_return_status_value=order_return_status_dict('yn',yn_return_status)
                                # 判断心云与雨诺退款状态是否一致
                                if ds_return_status_value!=yn_return_status_value:
                                    ds_yn_return_status_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "退款状态不一致",
                                                                      "return_order_code": ds_return_code,
                                                                    "order_code": middle_order_code,
                                                                      "front_sys_value": f"{ds_return_status_value}({ds_return_status})",
                                                                      "behind_value": f"{yn_return_status_value}({yn_return_status})"}
                                    result.append(ds_yn_return_status_result)
                                # 判断心云与雨诺下账金额是否一致
                                if ds_return_bill_amount != yn_return_bill_amount:
                                    ds_yn_return_bill_amount_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "下账金额不一致",
                                                                  "return_order_code": ds_return_code,
                                                                       "order_code": middle_order_code,
                                                                  "front_sys_value": ds_return_bill_amount,
                                                                  "behind_value": yn_return_bill_amount}
                                    result.append(ds_yn_return_bill_amount_result)
                                # 判断心云与雨诺退款金额是否一致
                                if ds_return_bill_amount != yn_return_amount:
                                    ds_yn_return_amount_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "退款金额不一致",
                                                                  "return_order_code": ds_return_code,"order_code":middle_order_code,
                                                                  "front_sys_value": ds_return_bill_amount,
                                                                  "behind_value": yn_return_amount}
                                    result.append(ds_yn_return_amount_result)
                        if ds_yn_returncode_flag == 0:
                            ds_yn_return_code_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "订单号不存在",
                                                        "return_order_code": ds_return_code,"order_code":middle_order_code,
                                                        "front_sys_value": ds_return_code,
                                                        "behind_value": ""}
                            result.append(ds_yn_return_code_result)
            if ds_middle_returncode_flag==0:
                ds_middle_return_code_result={"diff_sys": "心云 VS 微商城", "diff_obj": "订单号不存在",
                                  "return_order_code": middle_return_code,"order_code":middle_order_code,
                                  "front_sys_value": "",
                                  "behind_value": middle_return_code}
                result.append(ds_middle_return_code_result)

        return result
    except Exception as e:
        raise e