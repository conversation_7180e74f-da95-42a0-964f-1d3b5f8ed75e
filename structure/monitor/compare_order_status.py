# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/10 11:04
@Auth ： 逗逗的小老鼠
@File ：compare_order_status.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from lib.deal_ini import readini

# 退款单退款状态比对
def comapare_order_return_status(base_sys_name,base_sys_status,com_sys_name,com_sys_status):
    try:
        com_result_flag=0
        com_result_msg="不一致"
        base_status_value=order_return_status_dict(base_sys_name,base_sys_status)
        com_status_value=order_return_status_dict(com_sys_name,com_sys_status)
        if base_status_value==com_status_value:
            com_result_flag=1
            com_result_msg="一致"
        result={"com_msg":com_result_msg,"com_result_flag":com_result_flag}
        return result
    except Exception as e:
        raise e
# 比对订单状态
def order_sale_status_dict(sys_name, status):
    try:
        # 心云中台状态判断
        if sys_name == "xy":
            # 已完成：100   待接单：10  待处理：5  已关闭：101  待拣货：20  待配送：30   配送中：40   已取消：102
            if status == 100:
                status_value = "已完成"
            elif status == 10:
                status_value = "待接单"
            elif status == 5:
                status_value = "待处理"
            elif status == 101:
                status_value = "已关闭"
            elif status == 20:
                status_value = "待拣货"
            elif status == 30:
                status_value = "待配送"
            elif status == 40:
                status_value = "配送中"
            elif status == 102:
                status_value = "已取消"
            else:
                status_value = "未知"
        # 微商城状态判断
        if sys_name == "middle":
            # 2.待付款 4.待发货 6.待收货、待提货 8.待退货 10.待退款 12.已完成 20.已取消 30.退款完成
            if status == 12:
                status_value = "已完成"
            elif status == 30:
                status_value = "已关闭"
            elif status == 4:
                status_value = "待发货"
            elif status == 6:
                status_value = "待收货"
            else:
                status_value = "未知"
        # 雨诺系统状态判断
        if sys_name == "yn":
            # 已完成：58   待接单：32/34  待处理：31  已关闭：33  待拣货：35  待配送：37/40/45/42   配送中：54   已取消：110/120/126/130
            if status == "58":
                status_value = "已完成"
            elif status == "32" or status=="34":
                status_value = "待接单"
            elif status == "31":
                status_value = "待处理"
            elif status == "33" or status == "110" or status=="120" or status=="126" or status=="130":
                status_value = "已关闭"
            elif status == "35":
                status_value = "待拣货"
            elif status == "37" or status=="40" or status=="45" or status=="42":
                status_value = "待配送"
            elif status == "54":
                status_value = "配送中"
            else:
                status_value = "未知"
        return status_value
    except Exception as e:
        raise e

# 比对订单状态
def order_return_status_dict(sys_name, status):
    try:
        # 心云中台状态判断
        if sys_name == "ds":
            # 10 = 待退款
            # 20 = 待退货
            # 100 = 已完成
            # 102 = 已拒绝
            # 103 = 已取消
            if status == 10:
                status_value = "待退款"
            elif status == 20:
                status_value = "待退货"
            elif status == 100:
                status_value = "已完成"
            elif status == 102:
                status_value = "已拒绝"
            elif status == 103:
                status_value = "已取消"
            else:
                status_value = "未知"
        # 微商城状态判断
        if sys_name == "middle":
            # 1 = 待退款
            # 0 = 待退货
            # 2 = 已完成
            # 3 = 已拒绝
            if status == 1:
                status_value = "待退款"
            elif status == 0:
                status_value = "待退货"
            elif status == 2:
                status_value = "已完成"
            elif status == 3:
                status_value = "已拒绝"
            else:
                status_value = "未知"
        # 雨诺系统状态判断
        if sys_name == "yn":
            # 120 = 待退款
            # 122 = 待退货
            # 126 128 = 已完成
            # 124 = 已拒绝
            # 110 125 = 已拒绝
            if status == '120':
                status_value = "待退款"
            elif status == '122':
                status_value = "待退货"
            elif status == '126' or status == '128':
                status_value = "已完成"
            elif status == '124':
                status_value = "已拒绝"
            elif status == '125' or status == '110':
                status_value = "已拒绝"
            else:
                status_value = "未知"
        return status_value
    except Exception as e:
        raise e


if __name__=="__main__":
    result=comapare_order_return_status("ds",100,"yn",126)
    print(result)