# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/11 17:44
@Auth ： 逗逗的小老鼠
@File ：moni_order_bill_exce.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from lib.db_conf import db_yx_dscloud,db_insight
from lib.deal_sql import dict_to_sql_insert
from structure.monitor.moni_order_bill_amount import order_sale_o2o_delivery_bill
from decimal import Decimal
from lib.deal_text import text_find

"""
    O2O订单下账异常检查
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_o2o_bill_exce(body):
    "O2O订单下账异常数据检查"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        bill_todo_count=0
        fail_count=0
        no_stock=0
        erp_null=0
        amount_err=0
        discount_minus=0
        fail_other=0
        # 待下账数据查询
        bill_todo_sql=f"""SELECT COUNT(*) AS 'bill_tode_count' FROM order_info WHERE service_mode = 'o2o' AND erp_state = 30 AND order_state>30 AND created >= '{start_time}' AND created <= '{end_time}'"""
        bill_todo_result=db_yx_dscloud(bill_todo_sql)
        for bill_todo_item in bill_todo_result['data']:
            # 待下账数量
            bill_todo_count=bill_todo_count+bill_todo_item['bill_tode_count']
        # 下账失败数据查询
        bill_fail_sql=f"""
            SELECT
                COUNT(*) AS  'fail_count',
                SUM( CASE WHEN extend_info LIKE '%库存不足%' THEN 1 ELSE 0 END ) AS 'no_stock',
                SUM( CASE WHEN extend_info LIKE '%商品编码为空%' THEN 1 ELSE 0 END ) AS 'erp_null',
                SUM( CASE WHEN extend_info LIKE '%金额错误%' THEN 1 ELSE 0 END ) AS 'amount_err',
                SUM( CASE WHEN extend_info LIKE '%分摊折扣为负数%' THEN 1 ELSE 0 END ) AS 'discount_minus' 
            FROM
                order_info 
            WHERE
                service_mode = 'o2o' 
                AND erp_state = 99 
                AND created >= '{start_time}'
                AND created <= '{end_time}'
        """
        bill_fail_result=db_yx_dscloud(bill_fail_sql)
        for bill_fail_item in bill_fail_result['data']:
            # 下账失败总数
            fail_count=fail_count+bill_fail_item['fail_count']
            # 库存不足数量
            no_stock=no_stock+bill_fail_item['no_stock']
            # 商品编码为空数量
            erp_null=erp_null+bill_fail_item['erp_null']
            # 金额错误
            amount_err=amount_err+bill_fail_item['amount_err']
            # 折扣为负数
            discount_minus=discount_minus+bill_fail_item['discount_minus']
            # 其他下账异常原因
            fail_other=fail_other+fail_count-no_stock-erp_null-amount_err-discount_minus
        # 结果数据组装
        bill_count_result={"start_time":start_time,"end_time":end_time,"bill_todo_count":int(bill_todo_count),"fail_count":int(fail_count),"no_stock":int(no_stock),"erp_null":int(erp_null),"amount_err":int(amount_err),"discount_minus":int(discount_minus),"fail_other":int(fail_other)}
        result.append(bill_count_result)
        # 将结果数据转为SQL
        table_name = "order_bill_exce_record"
        result_sql_item=dict_to_sql_insert(table_name,result)
        # 将监测数据写入库
        for sql_item in result_sql_item:
            result_insert_result=db_insight(sql_item)
            print(result_insert_result)
        return result
    except Exception as e:
        raise e

"""
    O2O订单下账异常检查结果处理
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
def order_sale_o2o_bill_exce_data(body):
    try:
        bill_exec_result=order_sale_o2o_bill_exce(body)
        result=[]
        for diff_item in bill_exec_result:
            bill_todo_count = diff_item['bill_todo_count']
            fail_count=diff_item['fail_count']
            no_stock=diff_item['no_stock']
            erp_null=diff_item['erp_null']
            amount_err=diff_item['amount_err']
            discount_minus=diff_item['discount_minus']
            fail_other=diff_item['fail_other']
            data={}
            if bill_todo_count>0:
                data={"bill_status":"待下账总数","bill_num":bill_todo_count}
            if fail_count>0:
                data = {"bill_status": "下账失败总数", "bill_num": fail_count}
            if no_stock>0:
                data = {"bill_status": "下账失败:库存不足", "bill_num": no_stock}
            if erp_null>0:
                data = {"bill_status": "下账失败:商品编码为空", "bill_num": erp_null}
            if amount_err>0:
                data = {"bill_status": "下账失败:金额错误", "bill_num": amount_err}
            if discount_minus>0:
                data = {"bill_status": "下账失败:分摊金额为负", "bill_num": discount_minus}
            if fail_other>0:
                data = {"bill_status": "下账失败:其他原因", "bill_num": fail_other}
            result.append(data)
        return result
    except Exception as e:
        raise e


if __name__=="__main__":
    body={"start_time":"2024-03-01 00:00:00","end_time":"2024-03-09 00:00:00"}
    order_sale_o2o_bill_exce(body)