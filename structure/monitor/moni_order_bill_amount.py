# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/27 9:41
@Auth ： 逗逗的小老鼠
@File ：moni_order_bill_amount.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from lib.db_conf import db_yx_dscloud, db_insight
import re, time
from decimal import Decimal
from lib.deal_text import text_find
from lib.deal_sql import dict_to_sql_insert

"""
    O2O订单下账金额比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""


@exception(logger)
def order_sale_o2o_bill_amount(body):
    "O2O订单下账金额比对"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        if "related_id" in body:
            related_id = body['related_id']
        else:
            related_id = int(time.time())
        # 统计订单中台中有单独配送单的订单
        freight_sql = f"""
                SELECT
                    info.third_order_no AS 'third_order_no',
                    info.freight_order_no AS 'freight_order_no'
                FROM
                    order_info info
                WHERE
                    info.service_mode = 'O2O' 
                    AND info.mer_code='500001'
                    AND info.bill_time >= '{start_time}' 
                    AND info.bill_time <= '{end_time}' 
                    AND info.order_type !=5
                    AND info.freight_order_no != ""
        """
        freight_result = db_yx_dscloud(freight_sql)
        freight_data = freight_result['data']
        freight_order_list = []
        for freight_item in freight_data:
            freight_order_no = freight_item['freight_order_no']
            freight_order_list.append(str(freight_order_no))
        freight_order_param = '10001' + ",".join(freight_order_list)
        # 查询订单中台配送单下账信息
        freight_bill_sql = f"""
                SELECT
                    info.third_order_no AS 'third_order_no',
                    info.organization_code AS 'organization_code',
                    info.third_platform_code AS 'third_platform_code',
                    info.order_no AS 'order_no',
                    erp.bill_total_amount AS 'bill_total_amount',
                    erp.bill_commodity_amount AS 'bill_commodity_amount' 
                FROM
                    order_info info
                    LEFT JOIN erp_bill_info erp ON info.order_no = erp.order_no
                WHERE
                    info.order_no in ({freight_order_param})
        """
        freight_bill_result = db_yx_dscloud(freight_bill_sql)
        freight_bill_data = freight_bill_result['data']
        # 查询中台订单O2O非配送单下账信息
        amount_sql = f"""
                SELECT
                    info.third_order_no AS 'third_order_no',
                    info.organization_code AS 'organization_code',
                    info.third_platform_code AS 'third_platform_code',
                    info.extend_info AS 'extend_info',
                    info.freight_order_no AS 'freight_order_no',
                    delivery.delivery_type AS 'delivery_type',
                    pay.delivery_fee AS 'delivery_fee',
                    pay.total_amount AS 'total_amount',
                    pay.platform_delivery_fee AS 'platform_delivery_fee',
                    pay.merchant_total_discount_sum_not_delivery_fee AS 'merchant_goods_fee',
                    pay.merchant_delivery_fee AS 'merchant_delivery_fee',
                    pay.merchant_delivery_fee_discount AS 'merchant_delivery_fee_discount',
                    pay.platform_discount AS 'platform_discount',
	                pay.platform_delivery_fee_discount AS 'platform_delivery_fee_discount',
                    erp.bill_total_amount AS 'bill_total_amount',
                    erp.bill_commodity_amount AS 'bill_commodity_amount',
                    erp.merchant_delivery_fee AS 'bill_merchant_delivery_fee',
                    erp.platform_discount AS 'bill_platform_discount',
                    erp.merchant_discount AS 'bill_merchant_discount',
                    erp.platform_delivery_fee AS 'bill_platform_delivery_fee',
                    erp.merchant_pack_fee AS 'bill_merchant_pack_fee',
                    erp.platform_pack_fee AS 'bill_platform_pack_fee',
                    erp.detail_discount_amount AS 'bill_detail_discount_amount',
                    erp.plat_brokerage_amount AS 'bill_plat_brokerage_amount' ,
                    erp.client_conf_id AS 'client_conf_id'
                FROM
                    order_info info
                    LEFT JOIN order_pay_info pay ON info.order_no = pay.order_no
                    LEFT JOIN erp_bill_info erp ON info.order_no = erp.order_no
                    LEFT JOIN order_delivery_record delivery ON info.order_no = delivery.order_no 
                WHERE
                    info.mer_code='500001'
                    AND info.service_mode = 'O2O' 
                    AND info.bill_time >= '{start_time}' 
                    AND info.bill_time <= '{end_time}'                     
                    AND info.order_type !=5"""
        # print(amount_sql)
        amount_result = db_yx_dscloud(amount_sql)
        for amount_item in amount_result['data']:
            # 下账配置ID
            client_conf_id = amount_item['client_conf_id']
            # 平台订单号
            third_order_no = amount_item['third_order_no']
            # 配送方式： 1平台配送 2平台合作方配送 3自配送 4到店自取 0未知
            delivery_type = amount_item['delivery_type']
            # 门店编码
            organization_code = amount_item['organization_code']
            # 第三方平台编码
            third_platform_code = amount_item['third_platform_code']
            # 应收配送费
            delivery_fee = amount_item['delivery_fee']
            # 商品总金额
            total_amount = amount_item['total_amount']
            # 商家商品总优惠
            merchant_goods_fee = amount_item['merchant_goods_fee']
            # 商家配送费
            merchant_delivery_fee = amount_item['merchant_delivery_fee']
            # 平台配送费
            platform_delivery_fee = amount_item['platform_delivery_fee']
            # 平台优惠总金额
            platform_discount = amount_item['platform_discount']
            # 平台配送费优惠金额
            platform_delivery_fee_discount = amount_item['platform_delivery_fee_discount']
            # 平台商品优惠金额
            platform_goods_fee = platform_discount - platform_delivery_fee_discount
            # 商家配送费优惠金额
            merchant_delivery_fee_discount = amount_item['merchant_delivery_fee_discount']
            # 下账总金额
            bill_total_amount = amount_item['bill_total_amount']
            # 下账商家配送费
            bill_merchant_delivery_fee = amount_item['bill_merchant_delivery_fee']
            # 下账商品金额
            bill_commodity_amount = amount_item['bill_commodity_amount']
            # 下账平台优惠金额
            bill_platform_discount = amount_item['bill_platform_discount']
            # 下账商家优惠金额
            bill_merchant_discount = amount_item['bill_merchant_discount']
            # 下账平台配送费
            bill_platform_delivery_fee = amount_item['bill_platform_delivery_fee']
            # 下账商家包装费
            bill_merchant_pack_fee = amount_item['bill_merchant_pack_fee']
            # 下账平台包装费
            bill_platform_pack_fee = amount_item['bill_platform_pack_fee']
            # 下账商品优惠总金额
            bill_detail_discount_amount = amount_item['bill_detail_discount_amount']
            # 下账平台收取佣金
            bill_plat_brokerage_amount = amount_item['bill_plat_brokerage_amount']
            # 获取配送方式对应映射值
            delivery_type_value = order_sale_o2o_delivery_type(delivery_type)
            # 扩展信息
            extend_info = amount_item['extend_info']
            # 订单关联配送单
            freight_order_no = amount_item['freight_order_no']
            # 误差类型标识
            inaccuracy_type = "请核查"
            freight_bill_flag = 0
            if freight_order_no != None and freight_order_no != "":
                freight_bill_third_order_no = ""
                freight_bill_total_amount = Decimal(0.00)
                freight_bill_commodity_amount = Decimal(0.00)
                for freight_bill_item in freight_bill_data:
                    freight_bill_order_no = freight_bill_item['order_no']
                    if freight_order_no == freight_bill_order_no:
                        freight_bill_total_amount = freight_bill_item['bill_total_amount']
                        freight_bill_commodity_amount = freight_bill_item['bill_commodity_amount']
                        freight_bill_third_order_no = freight_bill_item['third_order_no']
                        freight_bill_flag = 1
                        break
                if freight_bill_flag == 0:
                    freight_bill_result = {"third_order_no": third_order_no, "delivery_type": delivery_type_value,
                                           "exec": f"订单未找到关联配送单信息", "related_id": related_id}
                    result.append(freight_bill_result)
                else:
                    if freight_bill_total_amount - freight_bill_commodity_amount != 0:
                        freight_bill_result = {"third_order_no": third_order_no, "delivery_type": delivery_type_value,
                                               "exec": f"订单关联配送单下账金额【{freight_bill_total_amount}】与下账商品金额【{freight_bill_commodity_amount}】不一致",
                                               "related_id": related_id}
                        result.append(freight_bill_result)
                    freight_bill_third_order_no_true = str(third_order_no) + "A"
                    print(freight_bill_third_order_no_true)
                    if freight_bill_third_order_no_true != freight_bill_third_order_no:
                        freight_bill_result = {"third_order_no": third_order_no, "delivery_type": delivery_type_value,
                                               "exec": f"订单关联配送单平台订单号【{freight_bill_third_order_no}】错误不一致",
                                               "related_id": related_id}
                        result.append(freight_bill_result)
                    # 配送费需下账：下账运费金额=应收运费-商家承担的运费优惠
                    if freight_bill_total_amount != delivery_fee - merchant_delivery_fee_discount:
                        bill_delivery_result = {"third_order_no": third_order_no,
                                                "delivery_type": delivery_type_value, "related_id": related_id,
                                                "exec": f"配送单下账商家配送费【{bill_merchant_delivery_fee}】 != 应收配送费【{delivery_fee}】 - 商家配送费优惠金额【{merchant_delivery_fee_discount}】"}
                        result.append(bill_delivery_result)
            # 判断是否存在机构信息
            if organization_code is None or organization_code == '':
                organization_code_result = {"third_order_no": third_order_no, "delivery_type": delivery_type_value,
                                            "exec": f"订单无机构信息", "related_id": related_id}
                result.append(organization_code_result)
            # 赔付单无需处理
            elif "P" in third_order_no:
                pass
            else:
                # 判断下账商品优惠总金额是否为0
                if bill_detail_discount_amount != 0:
                    bill_detail_discount_result = {"third_order_no": third_order_no,
                                                   "delivery_type": delivery_type_value,
                                                   "exec": f"下账平台配送费【{bill_detail_discount_amount}】应为0",
                                                   "related_id": related_id}
                    result.append(bill_detail_discount_result)
                # 判断下账平台配送费/下账平台收取佣金是否为0
                if bill_platform_delivery_fee != 0 or bill_plat_brokerage_amount != 0:
                    bill_delivery_fee_result = {"third_order_no": third_order_no,
                                                "delivery_type": delivery_type_value,
                                                "exec": f"下账平台配送费【{bill_platform_delivery_fee}】/下账平台收取佣金{bill_plat_brokerage_amount}应为0",
                                                "related_id": related_id}
                    result.append(bill_delivery_fee_result)
                # 判断下账平台/商家包装费是否为0
                if bill_platform_pack_fee != 0 or bill_merchant_pack_fee != 0:
                    bill_pack_fee_result = {"third_order_no": third_order_no,
                                            "delivery_type": delivery_type_value,
                                            "exec": f"下账平台包装费【{bill_platform_pack_fee}】/下账商家包装费【{bill_merchant_pack_fee}】 应为0",
                                            "related_id": related_id}
                    result.append(bill_pack_fee_result)
                # 判断下账平台/商家优惠金额是否为0
                if bill_platform_discount != 0 or bill_merchant_discount != 0:
                    bill_discount_result = {"third_order_no": third_order_no,
                                            "delivery_type": delivery_type_value,
                                            "exec": f"下账平台优惠金额【{bill_platform_discount}】/下账商家优惠金额【{bill_merchant_discount}】 应为0",
                                            "related_id": related_id}
                    result.append(bill_discount_result)
                # # 判断下账商品金额是否正确
                # # 下账配置为505/606时，平台优惠需要下账
                # if client_conf_id==505 or client_conf_id==606:
                #     if bill_commodity_amount != total_amount - merchant_goods_fee:
                #         if abs(bill_commodity_amount - ( total_amount - merchant_goods_fee-platform_goods_fee)) <= 0.1:
                #             inaccuracy_type = "精度问题"
                #         bill_commodity_amount_result = {"third_order_no": third_order_no,
                #                                         "delivery_type": delivery_type_value,
                #                                         "exec": f"【{inaccuracy_type}】下账商品金额【{bill_commodity_amount}】 != 商品总金额【{total_amount}】 - 商品商家优惠【{merchant_goods_fee}】- 商品平台优惠【{platform_goods_fee}】","related_id":related_id}
                #         result.append(bill_commodity_amount_result)
                # # 非京东平台
                # else:
                #     if bill_commodity_amount != total_amount - merchant_goods_fee:
                #         if abs(bill_commodity_amount - ( total_amount - merchant_goods_fee)) <= 0.1:
                #             inaccuracy_type = "精度问题"
                #         bill_commodity_amount_result = {"third_order_no": third_order_no,
                #                                         "delivery_type": delivery_type_value,
                #                                         "exec": f"【{inaccuracy_type}】下账商品金额【{bill_commodity_amount}】 != 商品总金额【{total_amount}】 - 商品商家优惠【{merchant_goods_fee}】","related_id":related_id}
                #         result.append(bill_commodity_amount_result)

                # 计算下账商品误差金额
                # 下账配置为505/606时，平台优惠需要下账
                if client_conf_id == 505 or client_conf_id == 606:
                    # 配送费无需下账差异额
                    bill_goods_difference_amount = abs(bill_commodity_amount - (total_amount - merchant_goods_fee - platform_goods_fee))
                    # 配送费需要下账差异额
                    bill_delivery_difference_amount=abs(bill_total_amount - (total_amount - merchant_goods_fee - platform_goods_fee + bill_merchant_delivery_fee))
                else:
                    # 配送费无需下账差异额
                    bill_goods_difference_amount = abs(bill_commodity_amount - (total_amount - merchant_goods_fee))
                    # 配送费需要下账差异额
                    bill_delivery_difference_amount = abs(bill_total_amount - (
                                total_amount - merchant_goods_fee + bill_merchant_delivery_fee))

                # 判断下账总金额是否正确
                if bill_total_amount != bill_commodity_amount + bill_merchant_delivery_fee:
                    bill_commodity_amount_result = {"third_order_no": third_order_no,
                                                    "delivery_type": delivery_type_value,
                                                    "exec": f"下账总金额【{bill_total_amount}】 != 下账商品金额【{bill_commodity_amount}】 + 下账商家配送费【{bill_merchant_delivery_fee}】",
                                                    "related_id": related_id}
                    result.append(bill_commodity_amount_result)
                # 根据配送类型进行判断
                if delivery_type == '1' or delivery_type == '2' or delivery_type == '4':
                    # 判断配送费是否需要下账
                    delivery_is_bill = order_sale_o2o_mt_qkp(extend_info)
                    if delivery_is_bill == 1:
                        delivery_type_value = delivery_type_value + "【需下账】"
                        # 下账配置为505/606时，平台优惠需要下账
                        if client_conf_id == 505 or client_conf_id == 606:
                            # 配送费需下账：下账金额 = 商品应收-商家承担的商品优惠-平台承担的商品优惠+下账运费金额
                            if bill_total_amount != total_amount - merchant_goods_fee - platform_goods_fee + bill_merchant_delivery_fee:
                                if bill_delivery_difference_amount <= 0.1:
                                    inaccuracy_type = "精度问题"
                                bill_total_amount_result = {"third_order_no": third_order_no,
                                                            "delivery_type": delivery_type_value,
                                                            "related_id": related_id,
                                                            "exec": f"差额：【{bill_delivery_difference_amount}】，【{inaccuracy_type}】下账总金额【{bill_total_amount}】 != 商品总金额【{total_amount}】 - 商品商家优惠【{merchant_goods_fee}】-商品平台优惠【{platform_goods_fee}】 + 下账商家配送费【{bill_merchant_delivery_fee}】"}
                                result.append(bill_total_amount_result)
                        else:
                            # 配送费需下账：下账金额 = 商品应收-商家承担的商品优惠+下账运费金额
                            if bill_total_amount != total_amount - merchant_goods_fee + bill_merchant_delivery_fee:
                                if bill_delivery_difference_amount <= 0.1:
                                    inaccuracy_type = "精度问题"
                                bill_total_amount_result = {"third_order_no": third_order_no,
                                                            "delivery_type": delivery_type_value,
                                                            "related_id": related_id,
                                                            "exec": f"差额：【{bill_delivery_difference_amount}】，【{inaccuracy_type}】下账总金额【{bill_total_amount}】 != 商品总金额【{total_amount}】 - 商品商家优惠【{merchant_goods_fee}】 + 下账商家配送费【{bill_merchant_delivery_fee}】"}
                                result.append(bill_total_amount_result)
                        if freight_bill_flag == 0:
                            # 配送费需下账：下账运费金额=应收运费-商家承担的运费优惠
                            if bill_merchant_delivery_fee != delivery_fee - merchant_delivery_fee_discount:
                                bill_delivery_result = {"third_order_no": third_order_no,
                                                        "delivery_type": delivery_type_value, "related_id": related_id,
                                                        "exec": f"下账商家配送费【{bill_merchant_delivery_fee}】 != 应收配送费【{delivery_fee}】 - 商家配送费优惠金额【{merchant_delivery_fee_discount}】"}
                                result.append(bill_delivery_result)
                    else:
                        # 配送费无需下账： 下账金额 = 商品应收-商家承担的商品优惠
                        delivery_type_value = delivery_type_value + "【无需下账】"
                        # 下账配置为505/606时，平台优惠需要下账
                        if client_conf_id == 505 or client_conf_id == 606:
                            if bill_total_amount != total_amount - merchant_goods_fee - platform_goods_fee:
                                if bill_goods_difference_amount <= 0.1:
                                    inaccuracy_type = "精度问题"
                                bill_total_amount_result = {"third_order_no": third_order_no,
                                                            "delivery_type": delivery_type_value,
                                                            "related_id": related_id,
                                                            "exec": f"差额：【{bill_goods_difference_amount}】，【{inaccuracy_type}】下账总金额【{bill_total_amount}】 != 商品总金额【{total_amount}】 - 商品商家优惠【{merchant_goods_fee}】 - 商品平台优惠【{platform_goods_fee}】"}
                                result.append(bill_total_amount_result)
                        else:
                            if bill_total_amount != total_amount - merchant_goods_fee:
                                if bill_goods_difference_amount <= 0.1:
                                    inaccuracy_type = "精度问题"
                                bill_total_amount_result = {"third_order_no": third_order_no,
                                                            "delivery_type": delivery_type_value,
                                                            "related_id": related_id,
                                                            "exec": f"差额：【{bill_goods_difference_amount}】，【{inaccuracy_type}】下账总金额【{bill_total_amount}】 != 商品总金额【{total_amount}】 - 商品商家优惠【{merchant_goods_fee}】 "}
                                result.append(bill_total_amount_result)
                        # 配送费无需下账： 商品配送金额应为0
                        if merchant_delivery_fee != 0 or bill_merchant_delivery_fee != 0:
                            delivery_result = {"third_order_no": third_order_no, "delivery_type": delivery_type_value,
                                               "exec": f"商家配送费【{merchant_delivery_fee}】/ 下账商家配送费【{bill_merchant_delivery_fee}】 应为 0 ",
                                               "related_id": related_id}
                            result.append(delivery_result)
                elif delivery_type == '3' or delivery_type == '5':
                    # 下账配置为505/606时，平台优惠需要下账
                    if client_conf_id == 505 or client_conf_id == 606:
                        # 自配送：下账金额 = 商品应收-商家承担的商品优惠-平台承担的商品优惠+下账运费金额
                        if bill_total_amount != total_amount - merchant_goods_fee - platform_goods_fee + bill_merchant_delivery_fee:
                            if bill_delivery_difference_amount <= 0.1:
                                inaccuracy_type = "精度问题"
                            bill_total_amount_result = {"third_order_no": third_order_no,
                                                        "delivery_type": delivery_type_value, "related_id": related_id,
                                                        "exec": f"差额：【{bill_delivery_difference_amount}】，【{inaccuracy_type}】下账总金额【{bill_total_amount}】 != 商品总金额【{total_amount}】 - 商品商家优惠【{merchant_goods_fee}】 - 商品平台优惠【{platform_goods_fee}】 + 下账商家配送费【{bill_merchant_delivery_fee}】"}
                            result.append(bill_total_amount_result)
                    else:
                        # 自配送：下账金额 = 商品应收-商家承担的商品优惠+下账运费金额
                        if bill_total_amount != total_amount - merchant_goods_fee + bill_merchant_delivery_fee:
                            if bill_delivery_difference_amount <= 0.1:
                                inaccuracy_type = "精度问题"
                            bill_total_amount_result = {"third_order_no": third_order_no,
                                                        "delivery_type": delivery_type_value, "related_id": related_id,
                                                        "exec": f"差额：【{bill_delivery_difference_amount}】，【{inaccuracy_type}】下账总金额【{bill_total_amount}】 != 商品总金额【{total_amount}】 - 商品商家优惠【{merchant_goods_fee}】 + 下账商家配送费【{bill_merchant_delivery_fee}】"}
                            result.append(bill_total_amount_result)
                    if freight_bill_flag == 0:
                        # 自配送：下账运费金额=应收运费-商家承担的运费优惠
                        if bill_merchant_delivery_fee != delivery_fee - merchant_delivery_fee_discount:
                            bill_delivery_result = {"third_order_no": third_order_no,
                                                    "delivery_type": delivery_type_value, "related_id": related_id,
                                                    "exec": f"下账商家配送费【{bill_merchant_delivery_fee}】 != 应收配送费【{delivery_fee}】 - 商家配送费优惠金额【{merchant_delivery_fee_discount}】"}
                            result.append(bill_delivery_result)
                else:
                    # 配送方式为未知
                    delivery_result = {"third_order_no": third_order_no, "delivery_type": delivery_type_value,
                                       "related_id": related_id,
                                       "exec": f"订单配送方式异常【{delivery_type}】"}
                    result.append(delivery_result)
        return result
    except Exception as e:
        raise e


"""
    O2O订单下账金额比对异常信息入库
"""


def order_sale_o2o_bill_amount_db_insert(body, detail_result):
    try:
        insert_info_result = []
        insert_detail_result = []
        info_list = []
        start_time = body['start_time']
        end_time = body['end_time']
        info_table = body['info_table']
        detail_table = body['detail_table']
        related_id = body['related_id']
        exec_count = len(detail_result)
        info_result = {"related_id": related_id, "start_time": start_time, "end_time": end_time,
                       "exec_count": exec_count}
        info_list.append(info_result)
        # 将结果信息写入汇总信息表
        info_sql_list = dict_to_sql_insert(info_table, info_list)
        for info_sql_item in info_sql_list:
            info_insert_result = db_insight(info_sql_item)
            insert_info_result.append(info_insert_result)
        # 将详情数据写入详情数据表
        detail_sql_item = dict_to_sql_insert(detail_table, detail_result)
        # 将监测数据写入库
        for detail_sql_item in detail_sql_item:
            detail_insert_result = db_insight(detail_sql_item)
            insert_detail_result.append(detail_insert_result)
        result = {"insert_detail_result": insert_detail_result, "insert_info_result": insert_info_result}
        return result
    except Exception as e:
        raise e


"""
    O2O订单配送类型判断
    :param type_key:类型值
    :return result：查询结果
"""


@exception(logger)
def order_sale_o2o_delivery_type(type_key):
    "O2O订单配送类型判断"
    try:
        # 配送方式： 1平台配送 2平台合作方配送 3自配送 4到店自取 5快递配送 0未知
        if type_key == '1':
            type_value = "平台配送"
        elif type_key == '2':
            type_value = "平台合作方配送"
        elif type_key == '3':
            type_value = "自配送"
        elif type_key == '4':
            type_value = "到店自提"
        elif type_key == '5':
            type_value = "快递配送"
        else:
            type_value = "未知"
        return type_value
    except Exception as e:
        raise e


"""
    O2O订单是否需要下账判断
    :param type_key:类型值
    :return result：查询结果
"""


@exception(logger)
def order_sale_o2o_delivery_bill(organization_code, third_platform_code):
    "O2O订单配送类型判断"
    try:
        is_needbill = 0
        # 判断美团平台配送费是否需要下账
        if third_platform_code == '27':
            # 美团配送费需要下账的门店清单
            mt_list_file = "mt_delivery_fee_bill_list.txt"
            bill_count = text_find(mt_list_file, organization_code)
            if bill_count > 0:
                is_needbill = 1
            else:
                is_needbill = 0
        # 判断饿百平台配送费是否需要下账
        elif third_platform_code == '24':
            # 饿百配送费需要下账的门店清单
            eb_list_file = "eb_delivery_fee_bill_list.txt"
            bill_count = text_find(eb_list_file, organization_code)
            if bill_count > 0:
                is_needbill = 1
            else:
                is_needbill = 0
        # 判断京东平台配送费是否需要细致
        elif third_platform_code == '11':
            # 饿百配送费需要下账的门店清单
            eb_list_file = "jddj_delivery_fee_bill_list.txt"
            bill_count = text_find(eb_list_file, organization_code)
            if bill_count > 0:
                is_needbill = 1
            else:
                is_needbill = 0
        # 其他平台
        else:
            is_needbill = 1
        return is_needbill
    except Exception as e:
        raise e


"""
    判断是否企客配
    :param type_key:类型值
    :return result：查询结果
"""


@exception(logger)
def order_sale_o2o_mt_qkp(extend_json):
    "O2O订单配送类型判断"
    try:
        is_needbill = 0
        if extend_json != None:
            search_result = re.search(r'"deliveryType": "\s*(.*?)\s*", "isMeiTuanQKP"', extend_json)
            if search_result != None:
                result_value = search_result.group(1)
                if result_value == '4001' or result_value == '4011' or result_value == '4012' or result_value == '4015':
                    is_needbill = 1
                else:
                    is_needbill = 0
            else:
                is_needbill = 0

        return is_needbill
    except Exception as e:
        raise e


if __name__ == "__main__":
    body = {
        "start_time": "2024-08-05 00:00:29",
        "end_time": "2024-08-05 23:27:29"}
    result = order_sale_o2o_bill_amount(body)

    # # test='{"deliveryType": "4001", "isMeiTuanQKP": "1"}'
    # test='{"orderAccountFailReason": "库存不足: 150629 - ********** - 1.0000 - 0"}'
    # test_result=order_sale_o2o_mt_qkp(test)
    print(result)
