# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/2 13:49
@Auth ： 逗逗的小老鼠
@File ：moni_order_bill_config.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：O2O订单下账配置检查
"""
from lib.get_log import logger, exception
from lib.db_conf import db_yx_dscloud
from structure.monitor.moni_order_bill_amount import order_sale_o2o_delivery_bill
from decimal import Decimal
from lib.deal_text import text_find

"""
    O2O订单下账配置检查
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""


@exception(logger)
def order_sale_o2o_bill_config(body):
    "O2O订单下账配置检查"
    try:
        resullt = []
        bill_config_sql = """
            SELECT
                id,
                `name`,
                platform_code,
                client_code,
                store_code,
                freight_fee_fetch,
                freight_fee_inventory,
                package_fee_fetch,
                package_fee_inventory,
                mc_discount_inventory,
                mc_discount_share,
                ptf_discount_inventory,
                ptf_discount_share,
                ptf_commission_inventory,
                ptf_commission_share,
                mc_dtl_discount_inventory,
                mc_dtl_discount_share,
                cashier_source 
            FROM
                store_bill_config 
            WHERE
                id IN (
                SELECT
                    MAX( bill.id ) 
                FROM
                    store_bill_config bill
                    LEFT JOIN inner_store_dictionary inn ON inn.organization_code = bill.store_code
                    LEFT JOIN ds_online_store store ON bill.store_code = store.online_store_code 
                    AND store.online_client_code = bill.client_code 
                WHERE
                    inn.is_open_new = 1 
                    AND bill.conf_type != 2 
                    AND store.STATUS = 1
                    AND store.service_mode='O2O' 
                GROUP BY
                    bill.store_code,
                bill.platform_code 
                )       
        """
        bill_config_result = db_yx_dscloud(bill_config_sql)
        for bill_config_item in bill_config_result['data']:
            # 配置ID
            bill_id = bill_config_item['id']
            # 配置名称
            bill_name = bill_config_item['name']
            # 平台编码
            platform_code = bill_config_item['platform_code']
            # 网店编码
            client_code = bill_config_item['client_code']
            # 门店编码
            store_code = bill_config_item['store_code']
            # 配送费, 收取方式: 1-平台收取, 2-商家收取
            freight_fee_fetch = bill_config_item['freight_fee_fetch']
            # 配送费, 是否下账: 0-不下账, 1-下账
            freight_fee_inventory = bill_config_item['freight_fee_inventory']
            # 包装费, 收取方式: 1-平台收取, 2-商家收取
            package_fee_fetch = bill_config_item['package_fee_fetch']
            # 包装费, 是否下账: 0-不下账, 1-下账
            package_fee_inventory = bill_config_item['package_fee_inventory']
            # 商家优惠金额, 是否下账: 0-不下账, 1-下账
            mc_discount_inventory = bill_config_item['mc_discount_inventory']
            # 商家优惠金额, 是否分摊: 0-下账商家优惠金额, 1-分摊至商品明细
            mc_discount_share = bill_config_item['mc_discount_share']
            # 平台优惠金额, 是否下账: 0-不下账, 1-下账
            ptf_discount_inventory = bill_config_item['ptf_discount_inventory']
            # 平台优惠金额, 是否分摊: 0-下账平台优惠金额, 1-分摊至商品明细
            ptf_discount_share = bill_config_item['ptf_discount_share']
            # 平台收取佣金, 是否下账: 0 - 不下账, 1 - 下账
            ptf_commission_inventory = bill_config_item['ptf_commission_inventory']
            # 平台收取佣金, 是否分摊: 0-下账平台佣金, 1-分摊至商品明细
            ptf_commission_share = bill_config_item['ptf_commission_share']
            # 商家明细优惠金额, 是否下账: 0-不下账, 1-下账
            mc_dtl_discount_inventory = bill_config_item['mc_dtl_discount_inventory']
            # 商家明细优惠金额, 是否分摊: 0-下账商品明细优惠金额, 1-分摊至商品明细
            mc_dtl_discount_share = bill_config_item['mc_dtl_discount_share']
            # 收银员取值: 1-同拣货员, 2-ERP中获取
            cashier_source = bill_config_item['cashier_source']
            if platform_code=='43':
                # 判断非配送费下账配置是否正确
                if package_fee_fetch != 2 or package_fee_inventory != 1 or mc_discount_inventory != 1 or mc_discount_share != 0 or ptf_discount_inventory != 0 or ptf_commission_inventory != 0 or mc_dtl_discount_inventory != 1 or mc_dtl_discount_share != 1:
                    other_config_result = {"exec": "下账配置(除配送费)值错误", "config": bill_config_item}
                    resullt.append(other_config_result)
            else:
                # 判断非配送费下账配置是否正确
                if package_fee_fetch != 1 or package_fee_inventory != 0 or mc_discount_inventory != 1 or mc_discount_share != 1 or ptf_discount_inventory != 0 or ptf_commission_inventory != 0 or mc_dtl_discount_inventory != 1 or mc_dtl_discount_share != 1:
                    other_config_result = {"exec": "下账配置(除配送费)值错误", "config": bill_config_item}
                    resullt.append(other_config_result)
            # 判断门店配送费是否需要下账
            delivery_is_need = order_sale_o2o_delivery_bill(store_code,platform_code)
            # 商家收取，需要下账
            if delivery_is_need == 1:
                if freight_fee_fetch != 2 or freight_fee_inventory != 1:
                    other_config_result = {"exec": "商家下账配送费配置错误", "config": bill_config_item}
                    resullt.append(other_config_result)
            # 平台收取，不需要下账
            else:
                if freight_fee_fetch != 1 or freight_fee_inventory != 0:
                    other_config_result = {"exec": "平台下账配送费配置错误", "config": bill_config_item}
                    resullt.append(other_config_result)
        return resullt
    except Exception as e:
        raise e
