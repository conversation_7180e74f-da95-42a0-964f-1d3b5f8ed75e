# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/30 15:38
@Auth ： 逗逗的小老鼠
@File ：compare_order_fee.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from structure.monitor.order_detail_platfrom.order_original_mt_mongdb import mongdb_mt_order_detail_anal
from structure.monitor.order_detail_platfrom.order_db_detail import order_detail_fee_info
from lib.deal_format_convert import filed_format_convert,value_mathematics


"""
    美团平台费用比较
"""
@exception(logger)
def compare_order_fee_mt(order_list):
    result=[]
    try:
        mt_order_data=mongdb_mt_order_detail_anal(order_list)
        db_order_fee_data=order_detail_fee_info(order_list)
        # 美团订单信息获取
        for mt_order_item in mt_order_data:
            third_order_no=mt_order_item['third_order_no']
            mt_order_detail_list=mt_order_item['order_detail_list']
            # 美团商品详情信息获取
            for mt_order_detail_item in mt_order_detail_list:
                # 第三方详情ID
                item_id=mt_order_detail_item['item_id']
                # erp编码
                app_medicine_code=mt_order_detail_item['app_medicine_code']
                # 商品名称
                food_name=mt_order_detail_item['food_name']
                # 商品条形编码
                upc=mt_order_detail_item['upc']
                # 商品规格
                spec=mt_order_detail_item['spec']
                # 商品数量
                quantity=mt_order_detail_item['quantity']
                # 商品单价(打印使用)
                original_price=mt_order_detail_item['original_price']
                # 商品单价(详情使用)
                price=mt_order_detail_item['price']
                # 下账价格
                bill_price=mt_order_detail_item['bill_price']
                # 下账金额
                actual_net_amount=mt_order_detail_item['actual_net_amount']
                # 商品原价总金额
                total_amount=mt_order_detail_item['total_amount']
                # 是否是赠品
                is_gift=mt_order_detail_item['is_gift']
                # 优惠分摊
                discountShare=mt_order_detail_item['discountShare']
                # 商品数据标识
                comodity_flag=0
                for db_item in db_order_fee_data:
                    db_third_order_no=db_item['third_order_no']
                    db_third_detail_id=db_item['third_detail_id']
                    if str(db_third_order_no)==str(third_order_no) and str(item_id)==str(db_third_detail_id):
                        comodity_flag=1
                        db_erp_code=db_item['erp_code']
                        db_commodity_count=db_item['commodity_count']
                        db_original_price=db_item['original_price']
                        db_price=db_item['price']
                        db_actual_net_amount=db_item['actual_net_amount']
                        db_bill_price=db_item['bill_price']
                        db_total_amount=db_item['total_amount']
                        db_bar_code=db_item['bar_code']
                        db_commodity_name=db_item['commodity_name']
                        db_is_gift=db_item['is_gift']
                        db_commodity_spec=db_item['commodity_spec']
                        db_discount_share=db_item['discount_share']
                        db_status=db_item['status']
                        db_swap_id=db_item['swap_id']
                        # erp编码比对
                        if str(app_medicine_code) ==str(db_erp_code):
                            # 商品名称比对
                            if str(food_name) != str(db_commodity_name):
                                compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                                  "diff_key": "商品名称", "plat_value": str(food_name),
                                                  "xy_value": str(db_commodity_name),
                                                  "msg": f"美团值【{food_name}】与系统值【{db_commodity_name}】不一致"}
                                result.append(compare_result)
                            # 商品规格比对
                            if str(spec) != str(db_commodity_spec):
                                compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                                  "diff_key": "商品规格", "plat_value": str(spec),
                                                  "xy_value": str(db_commodity_spec),
                                                  "msg": f"美团值【{spec}】与系统值【{db_commodity_spec}】不一致"}
                                result.append(compare_result)
                            # 商品条形码比对
                            if str(upc) != str(db_bar_code):
                                compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                                  "diff_key": "商品条形码", "plat_value": str(upc),
                                                  "xy_value": str(db_bar_code),
                                                  "msg": f"美团值【{upc}】与系统值【{db_bar_code}】不一致"}
                                result.append(compare_result)
                            # 赠品标识比对
                            if str(is_gift) != str(db_is_gift):
                                compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                                  "diff_key": "赠品标识", "plat_value": str(is_gift),
                                                  "xy_value": str(db_is_gift),
                                                  "msg": f"美团值【{is_gift}】与系统值【{db_is_gift}】不一致"}
                                result.append(compare_result)
                        else:
                            if db_swap_id == "" or db_swap_id == None:
                                compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                                  "diff_key": "ERP编码", "plat_value": str(app_medicine_code),
                                                  "xy_value": str(db_erp_code),
                                                  "msg": f"美团值【{app_medicine_code}】与系统值【{db_erp_code}】不一致"}
                                result.append(compare_result)
                        # 商品单价(打印使用)值比较
                        diff_original_price=value_mathematics(original_price, db_original_price, "-")
                        if (diff_original_price)!=0:
                            compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                              "diff_key": "商品单价(打印使用)", "plat_value": original_price,
                                              "xy_value": db_original_price,
                                              "msg": f"美团值【{original_price}】与系统值【{db_original_price}】不一致"}
                            result.append(compare_result)
                        # 商品单价(详情使用)值比较
                        diff_price = value_mathematics(price, db_price, "-")
                        if abs(diff_price) > 0.01:
                            compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                              "diff_key": "商品单价(详情使用)", "plat_value": price,
                                              "xy_value": db_price,
                                              "msg": f"美团值【{price}】与系统值【{db_price}】不一致"}
                            result.append(compare_result)
                        # 下账价格值比较
                        diff_price = value_mathematics(bill_price, db_bill_price, "-")
                        if abs(diff_price) > 0.01:
                            compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                              "diff_key": "下账价格", "plat_value": bill_price,
                                              "xy_value": db_bill_price,
                                              "msg": f"美团值【{bill_price}】与系统值【{db_bill_price}】不一致"}
                            result.append(compare_result)

                        # 如果商品数量相同
                        if int(quantity)==int(db_commodity_count):
                            # 商品原价总金额
                            if value_mathematics(total_amount,db_total_amount,"-")!=0:
                                compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                                  "diff_key": "商品原价总金额", "plat_value": total_amount,
                                                  "xy_value": db_total_amount,
                                                  "msg": f"美团值【{total_amount}】与系统值【{db_total_amount}】不一致"}
                                result.append(compare_result)
                            # 下账总金额
                            if value_mathematics(actual_net_amount, db_actual_net_amount, "-") != 0:
                                compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                                  "diff_key": "下账总金额", "plat_value": actual_net_amount,
                                                  "xy_value": db_actual_net_amount,
                                                  "msg": f"美团值【{actual_net_amount}】与系统值【{db_actual_net_amount}】不一致"}
                                result.append(compare_result)
                            # 优惠分摊金额
                            if value_mathematics(discountShare, db_discount_share, "-") != 0:
                                compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                                  "diff_key": "优惠分摊金额", "plat_value": discountShare,
                                                  "xy_value": db_discount_share,
                                                  "msg": f"美团值【{discountShare}】与系统值【{db_discount_share}】不一致"}
                                result.append(compare_result)
                        else:
                            # 平台单个商品的分摊金额
                            per_discountShare=value_mathematics(discountShare,quantity)
                            # 系统单个商品的分摊金额
                            per_db_discount_share=value_mathematics(db_discount_share,db_commodity_count)
                            # 单个商品的分摊金额比较
                            diff_per_discountShare = value_mathematics(per_discountShare, per_db_discount_share, "-")
                            if abs(diff_per_discountShare) > 0.01:
                                compare_result = {"third_order_no": third_order_no, "item_id": item_id,
                                                  "diff_key": "单个商品的分摊金额", "plat_value": per_discountShare,
                                                  "xy_value": per_db_discount_share,
                                                  "msg": f"美团值【{per_discountShare}】与系统值【{per_db_discount_share}】不一致"}
                                result.append(compare_result)


                if comodity_flag==0:
                    compare_result = {"third_order_no": third_order_no, "item_id": item_id, "diff_key": "系统中未查到对应商品信息",
                                      "plat_value": str(app_medicine_code), "xy_value": "",
                                      "msg": f"美团值【{app_medicine_code}】与系统值【null】不一致"}
                    result.append(compare_result)
        return result
    except Exception as e:
        raise e


if __name__=="__main__":
    order_list = ["3801048863144842960", "3801048882888637755"]
    compare_result=compare_order_fee_mt(order_list)
    print(compare_result)