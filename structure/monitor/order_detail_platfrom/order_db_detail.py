# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/30 15:06
@Auth ： 逗逗的小老鼠
@File ：order_db_detail.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from lib.db_conf_test import db_yx_test_dsclound


"""
    订单详情查询
"""
@exception(logger)
def order_detail_fee_info(order_list):
    "查询订单的商品详情"
    try:
        order_no_filter=','.join(order_list)
        order_detail_sql=f"""
            SELECT
                info.third_order_no AS 'third_order_no',
                detail.erp_code AS 'erp_code',
                detail.commodity_count AS 'commodity_count',
                detail.original_price AS 'original_price',
                detail.price AS 'price',
                detail.actual_net_amount AS 'actual_net_amount',
                detail.bill_price AS 'bill_price',
                detail.bar_code AS 'bar_code',
                detail.commodity_name AS 'commodity_name',
                detail.is_gift AS 'is_gift' ,
                detail.third_detail_id AS 'third_detail_id',
                detail.commodity_spec AS 'commodity_spec',
                detail.total_amount AS 'total_amount',
                detail.discount_share AS 'discount_share',
                detail.status AS 'status',
                detail.swap_id AS 'swap_id'
            FROM
                order_info info
                LEFT JOIN order_detail detail ON info.order_no = detail.order_no 
            WHERE
                info.third_order_no IN ({order_no_filter})        
        """
        order_detail_result=db_yx_test_dsclound(order_detail_sql)
        order_detail_data=order_detail_result['data']
        return order_detail_data
    except Exception as e:
        raise e



if __name__=="__main__":
    order_list=["3801048863144842960","3801048882888637755"]
    order_detail_fee_info(order_list)

