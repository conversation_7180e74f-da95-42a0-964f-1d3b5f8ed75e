# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/17 9:31
@Auth ： 逗逗的小老鼠
@File ：moni_order_efficiency.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.db_conf import db_yx_middle_order,db_yx_dscloud,db_yn_oms
from decimal import Decimal



"""
    非处方单接单效率监控
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_o2o_nomal_accept(body):
    "非处方单接单效率监控"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        # 非处方单接单时间统计
        nomal_eff_accept_sql=f"""
        SELECT
        COUNT(*) AS 'total',
        SUM(CASE WHEN TIMESTAMPDIFF( MINUTE, created, accept_time )<= 0.5 THEN 1 ELSE 0 END ) AS 'accpt_less_30',
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 0.5 AND TIMESTAMPDIFF( MINUTE, created, accept_time )<= 1 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_less_60' ,
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 1 AND TIMESTAMPDIFF( MINUTE, created, accept_time )<= 2 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_less_120',
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 2 AND TIMESTAMPDIFF( MINUTE, created, accept_time )<= 3 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_less_180',
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 3 AND TIMESTAMPDIFF( MINUTE, created, accept_time )<= 4 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_less_240',
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 4 AND TIMESTAMPDIFF( MINUTE, created, accept_time )<= 5 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_less_300',
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 5 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_greater_300'
        FROM
            order_info 
        WHERE
            prescription_flag = 0  
            AND created >= '{start_time}'  AND created <= '{end_time}'
        """
        nomal_eff_accept_result=db_yx_dscloud(nomal_eff_accept_sql)
        nomal_eff_accept_data=nomal_eff_accept_result['data']
        for accept_item in nomal_eff_accept_data:
            total=accept_item['total']
            accpt_less_30=accept_item['accpt_less_30']
            accpt_less_60=accept_item['accpt_less_60']
            accpt_less_120=accept_item['accpt_less_120']
            accpt_less_180=accept_item['accpt_less_180']
            accpt_less_240=accept_item['accpt_less_240']
            accpt_less_300=accept_item['accpt_less_300']
            accpt_greater_300=accept_item['accpt_greater_300']
            accpt_less_30_bfb=round(accpt_less_30/total*100,2)
            accpt_less_60_bfb=round(accpt_less_60/total*100,2)
            accpt_less_120_bfb = round(accpt_less_120 / total * 100,2)
            accpt_less_180_bfb = round(accpt_less_180 / total * 100,2)
            accpt_less_240_bfb = round(accpt_less_240 / total * 100,2)
            accpt_less_300_bfb = round(accpt_less_300 / total * 100,2)
            accpt_greater_300_bfb=round(accpt_greater_300/total*100,2)
            # 总数
            total_msg={"entry_name":"总数","entry_value":total,"proportion":"100"}
            result.append(total_msg)
            # 30秒以内
            accpt_less_30_msg = {"entry_name": "30秒以内", "entry_value": accpt_less_30, "proportion": accpt_less_30_bfb}
            result.append(accpt_less_30_msg)
            # 1分钟以内
            accpt_less_60_msg = {"entry_name": "1分钟以内", "entry_value": accpt_less_60, "proportion": accpt_less_60_bfb}
            result.append(accpt_less_60_msg)
            # 2分钟以内
            accpt_less_120_msg = {"entry_name": "2分钟以内", "entry_value": accpt_less_120, "proportion": accpt_less_120_bfb}
            result.append(accpt_less_120_msg)
            # 3分钟以内
            accpt_less_180_msg = {"entry_name": "3分钟以内", "entry_value": accpt_less_180, "proportion": accpt_less_180_bfb}
            result.append(accpt_less_180_msg)
            # 4分钟以内
            accpt_less_240_msg = {"entry_name": "4分钟以内", "entry_value": accpt_less_240, "proportion": accpt_less_240_bfb}
            result.append(accpt_less_240_msg)
            # 5分钟以内
            accpt_less_300_msg = {"entry_name": "5分钟以内", "entry_value": accpt_less_300, "proportion": accpt_less_300_bfb}
            result.append(accpt_less_300_msg)
            # 超过5分钟
            accpt_greater_300_msg = {"entry_name": "超过5分钟", "entry_value": accpt_greater_300, "proportion": accpt_greater_300_bfb}
            result.append(accpt_greater_300_msg)
        return result
    except Exception as e:
        raise e


"""
    处方单接单效率监控
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_o2o_prescription_accept(body):
    "处方单接单效率监控"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        # 处方单接单时间统计
        prescription_eff_accept_sql=f"""
        SELECT
        COUNT(*) AS 'total',
        SUM(CASE WHEN TIMESTAMPDIFF( MINUTE, created, accept_time )<= 0.5 THEN 1 ELSE 0 END ) AS 'accpt_less_30',
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 0.5 AND TIMESTAMPDIFF( MINUTE, created, accept_time )<= 1 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_less_60' ,
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 1 AND TIMESTAMPDIFF( MINUTE, created, accept_time )<= 2 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_less_120',
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 2 AND TIMESTAMPDIFF( MINUTE, created, accept_time )<= 3 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_less_180',
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 3 AND TIMESTAMPDIFF( MINUTE, created, accept_time )<= 4 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_less_240',
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 4 AND TIMESTAMPDIFF( MINUTE, created, accept_time )<= 5 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_less_300',
        SUM(CASE WHEN ( TIMESTAMPDIFF( MINUTE, created, accept_time )> 5 ) THEN
                1 ELSE 0 
            END 
            ) AS 'accpt_greater_300'
        FROM
            order_info 
        WHERE
            prescription_flag = 1 
            AND created >= '{start_time}'  AND created <= '{end_time}'
        """
        prescription_eff_accept_result=db_yx_dscloud(prescription_eff_accept_sql)
        prescription_eff_accept_data=prescription_eff_accept_result['data']
        for accept_item in prescription_eff_accept_data:
            total=accept_item['total']
            accpt_less_30=accept_item['accpt_less_30']
            accpt_less_60=accept_item['accpt_less_60']
            accpt_less_120=accept_item['accpt_less_120']
            accpt_less_180=accept_item['accpt_less_180']
            accpt_less_240=accept_item['accpt_less_240']
            accpt_less_300=accept_item['accpt_less_300']
            accpt_greater_300=accept_item['accpt_greater_300']
            accpt_less_30_bfb=round(accpt_less_30/total*100,2)
            accpt_less_60_bfb=round(accpt_less_60/total*100,2)
            accpt_less_120_bfb = round(accpt_less_120 / total * 100,2)
            accpt_less_180_bfb = round(accpt_less_180 / total * 100,2)
            accpt_less_240_bfb = round(accpt_less_240 / total * 100,2)
            accpt_less_300_bfb = round(accpt_less_300 / total * 100,2)
            accpt_greater_300_bfb=round(accpt_greater_300/total*100,2)
            # 总数
            total_msg={"entry_name":"总数","entry_value":total,"proportion":"100"}
            result.append(total_msg)
            # 30秒以内
            accpt_less_30_msg = {"entry_name": "30秒以内", "entry_value": accpt_less_30, "proportion": accpt_less_30_bfb}
            result.append(accpt_less_30_msg)
            # 1分钟以内
            accpt_less_60_msg = {"entry_name": "1分钟以内", "entry_value": accpt_less_60, "proportion": accpt_less_60_bfb}
            result.append(accpt_less_60_msg)
            # 2分钟以内
            accpt_less_120_msg = {"entry_name": "2分钟以内", "entry_value": accpt_less_120, "proportion": accpt_less_120_bfb}
            result.append(accpt_less_120_msg)
            # 3分钟以内
            accpt_less_180_msg = {"entry_name": "3分钟以内", "entry_value": accpt_less_180, "proportion": accpt_less_180_bfb}
            result.append(accpt_less_180_msg)
            # 4分钟以内
            accpt_less_240_msg = {"entry_name": "4分钟以内", "entry_value": accpt_less_240, "proportion": accpt_less_240_bfb}
            result.append(accpt_less_240_msg)
            # 5分钟以内
            accpt_less_300_msg = {"entry_name": "5分钟以内", "entry_value": accpt_less_300, "proportion": accpt_less_300_bfb}
            result.append(accpt_less_300_msg)
            # 超过5分钟
            accpt_greater_300_msg = {"entry_name": "超过5分钟", "entry_value": accpt_greater_300, "proportion": accpt_greater_300_bfb}
            result.append(accpt_greater_300_msg)
        return result
    except Exception as e:
        raise e


"""
    订单拣货效率监控
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_o2o_pick(body):
    "处方单接单效率监控"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        # 订单接单时间统计
        eff_pcik_sql=f"""
            SELECT
                COUNT(*) AS 'total',
                SUM( CASE WHEN TIMESTAMPDIFF( MINUTE, accept_time, pick_time )<= 3 THEN 1 ELSE 0 END ) AS 'pick_less_3',
                SUM( CASE WHEN ( TIMESTAMPDIFF( MINUTE, accept_time, pick_time )> 3 AND TIMESTAMPDIFF( MINUTE, accept_time, pick_time )<= 5 ) THEN 1 ELSE 0 END ) AS 'pick_less_5',
		        SUM( CASE WHEN ( TIMESTAMPDIFF( MINUTE, accept_time, pick_time )> 5 AND TIMESTAMPDIFF( MINUTE, accept_time, pick_time )<= 10 ) THEN 1 ELSE 0 END ) AS 'pick_less_10',
		        SUM( CASE WHEN ( TIMESTAMPDIFF( MINUTE, accept_time, pick_time )> 10 AND TIMESTAMPDIFF( MINUTE, accept_time, pick_time )<= 20 ) THEN 1 ELSE 0 END ) AS 'pick_less_20',
		        SUM( CASE WHEN ( TIMESTAMPDIFF( MINUTE, accept_time, pick_time )> 20 AND TIMESTAMPDIFF( MINUTE, accept_time, pick_time )<= 30 ) THEN 1 ELSE 0 END ) AS 'pick_less_30',
		        SUM( CASE WHEN ( TIMESTAMPDIFF( MINUTE, accept_time, pick_time )> 30 AND TIMESTAMPDIFF( MINUTE, accept_time, pick_time )<= 60 ) THEN 1 ELSE 0 END ) AS 'pick_less_60',
				SUM( CASE WHEN ( TIMESTAMPDIFF( MINUTE, accept_time, pick_time )> 60 ) THEN 1 ELSE 0 END ) AS 'accpt_greater_60' 
			FROM order_info 
            WHERE
            created >= '{start_time}'  AND created <= '{end_time}'
            AND pick_time IS NOT NULL
        """
        eff_pick_result=db_yx_dscloud(eff_pcik_sql)
        eff_pick_data=eff_pick_result['data']
        for pick_item in eff_pick_data:
            total=pick_item['total']
            pick_less_3=pick_item['pick_less_3']
            pick_less_5=pick_item['pick_less_5']
            pick_less_10 = pick_item['pick_less_10']
            pick_less_20 = pick_item['pick_less_20']
            pick_less_30 = pick_item['pick_less_30']
            pick_less_60 = pick_item['pick_less_60']
            accpt_greater_60=pick_item['accpt_greater_60']
            pick_less_3_bfb=round(pick_less_3/total*100,2)
            pick_less_5_bfb=round(pick_less_5/total*100,2)
            pick_less_10_bfb = round(pick_less_10 / total * 100,2)
            pick_less_20_bfb = round(pick_less_20 / total * 100,2)
            pick_less_30_bfb = round(pick_less_30 / total * 100,2)
            pick_less_60_bfb = round(pick_less_60 / total * 100,2)
            pick_greater_60_bfb=round(accpt_greater_60/total*100,2)
            # 总数
            total_msg={"entry_name":"总数","entry_value":total,"proportion":"100"}
            result.append(total_msg)
            # 30秒以内
            accpt_less_30_msg = {"entry_name": "3分钟以内", "entry_value": pick_less_3, "proportion": pick_less_3_bfb}
            result.append(accpt_less_30_msg)
            # 1分钟以内
            accpt_less_60_msg = {"entry_name": "5分钟以内", "entry_value": pick_less_5, "proportion": pick_less_5_bfb}
            result.append(accpt_less_60_msg)
            # 2分钟以内
            accpt_less_120_msg = {"entry_name": "10分钟以内", "entry_value": pick_less_10, "proportion": pick_less_10_bfb}
            result.append(accpt_less_120_msg)
            # 3分钟以内
            accpt_less_180_msg = {"entry_name": "20分钟以内", "entry_value": pick_less_20, "proportion": pick_less_20_bfb}
            result.append(accpt_less_180_msg)
            # 4分钟以内
            accpt_less_240_msg = {"entry_name": "30分钟以内", "entry_value": pick_less_30, "proportion": pick_less_30_bfb}
            result.append(accpt_less_240_msg)
            # 5分钟以内
            accpt_less_300_msg = {"entry_name": "60分钟以内", "entry_value": pick_less_60, "proportion": pick_less_60_bfb}
            result.append(accpt_less_300_msg)
            # 超过5分钟
            accpt_greater_300_msg = {"entry_name": "超过60分钟", "entry_value": accpt_greater_60, "proportion": pick_greater_60_bfb}
            result.append(accpt_greater_300_msg)
        return result
    except Exception as e:
        raise e