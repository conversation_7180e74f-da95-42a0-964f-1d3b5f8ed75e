# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/24 11:01
@Auth ： 逗逗的小老鼠
@File ：moni_order_erpcode.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from lib.db_conf import db_yx_dscloud

"""
    H1海典erp编码异常
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""


@exception(logger)
def order_H1_erpcode(body):
    "H1海典erp编码异常"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        erpcode_sql=f"""
            SELECT
                info.order_no AS 'order_no',
                info.third_platform_code AS 'third_platform_code',
                info.created AS 'created',
                info.erp_state AS 'erp_state',
                info.order_state AS 'order_state',
                detail.erp_code AS 'erp_code',
                detail.commodity_name AS 'commodity_name' 
            FROM
                order_info info
                LEFT JOIN order_detail detail ON info.order_no = detail.order_no
                LEFT JOIN inner_store_dictionary inner_dict ON info.organization_code = inner_dict.organization_code 
            WHERE
                inner_dict.pos_mode = 1 
                AND LENGTH( detail.erp_code )> 6 
                AND info.created >= "{start_time}" 
                AND info.created <= "{end_time}" 
            GROUP BY
                info.order_no,
                detail.erp_code
            """
        erpcode_result=db_yx_dscloud(erpcode_sql)
        erpcode_data=erpcode_result['data']
        for erpcode_item in erpcode_data:
           order_no=erpcode_item['order_no']
           third_platform_code = erpcode_item['third_platform_code']
           created = erpcode_item['created']
           erp_state = erpcode_item['erp_state']
           order_state = erpcode_item['order_state']
           erp_code = erpcode_item['erp_code']
           commodity_name = erpcode_item['commodity_name']
           if third_platform_code == "27":
               third_platform_name = "美团"
           elif third_platform_code == "11":
               third_platform_name = "京东到家"
           elif third_platform_code == "43":
               third_platform_name = "微商城"
           elif third_platform_code == "24":
               third_platform_name = "饿了么"
           else:
               third_platform_name = "未知"
           erp_msg={"order_no":order_no,"third_platform_name":f"{third_platform_name}({third_platform_code})","created":created,"erp_state":erp_state,"order_state":order_state,"erp_code":erp_code,"commodity_name":commodity_name}
           result.append(erp_msg)
        return result
    except Exception as e:
        raise e
