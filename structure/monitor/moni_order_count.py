# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/6 15:51
@Auth ： 逗逗的小老鼠
@File ：moni_order_count.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.db_conf import db_yx_middle_order,db_yx_dscloud,db_yn_oms
from decimal import Decimal

"""
    销售单数量比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_count_o2o_yx(body):
    "销售单数量比对"
    try:
        result=[]
        start_time=body['start_time']
        end_time=body['end_time']
        # 统计中台订单O2O数量
        ds_count=0
        ds_syn_count=0
        ds_syn_sql=f'''SELECT dict.is_open_new AS 'is_open',COUNT(*) AS 'ds_count' FROM order_info info LEFT JOIN inner_store_dictionary dict ON info.organization_code=dict.organization_code WHERE info.third_platform_code='43' AND info.created>"{start_time}" AND info.created < "{end_time}" AND info.service_mode='O2O' GROUP BY dict.is_open_new '''
        print(ds_syn_sql)
        ds_syn_count_result=db_yx_dscloud(ds_syn_sql)
        for ds_count_item in ds_syn_count_result['data']:
            is_open=ds_count_item['is_open']
            ds_count_value=ds_count_item['ds_count']
            ds_count=ds_count+ds_count_value
            if is_open=='2':
                ds_syn_count=ds_count_value

        # 统计前台订单O2O数量
        middle_sql=f'''SELECT COUNT(*) AS 'middle_count' FROM order_info WHERE order_time>"{start_time}" AND order_time < "{end_time}" AND order_status !=20 AND order_status !=2 AND is_b2c_order=0'''
        print(middle_sql)
        middle_count_result=db_yx_middle_order(middle_sql)
        middle_count=middle_count_result['data'][0]['middle_count']
        # print(middle_count)

        # 统计雨诺订单O2O数量
        yn_sql=f'''SELECT COUNT(*) AS 'yn_count' FROM order_std WHERE ORDERFROM='111' AND CREATETIME>'{start_time}' AND CREATETIME<'{end_time}' AND INNERSTATUS=1 '''
        print(yn_sql)
        yn_count_result=db_yn_oms(yn_sql)
        yn_count=yn_count_result['data'][0]['yn_count']
        # print(yn_count)
        # 心云与微商城订单数差值
        diff_xy_weshop_count=ds_count-middle_count
        # 心云与雨诺订单数差值
        diff_xy_yn_count=ds_syn_count-yn_count

        diff_xy_weshop_result={"diff_sys":"心云 VS 微商城","diff_obj":"订单量差","diff_value":diff_xy_weshop_count,"front_sys_value":ds_count,"behind_value":middle_count}
        diff_xy_yn_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "订单量差", "diff_value": diff_xy_yn_count,
                                 "front_sys_value": ds_syn_count, "behind_value": yn_count}
        result.append(diff_xy_weshop_result)
        result.append(diff_xy_yn_result)
        return result
    except Exception as e:
        raise e

"""
    销售单金额比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_amount_o2o_yx(body):
    "销售单金额比对"
    try:
        result=[]
        start_time = body['start_time']
        end_time = body['end_time']
        # 心云中台金额查询
        ds_actual_amount=0
        ds_total_amount=0
        ds_bill_amount=0
        ds_syn_actual_amount = 0
        ds_syn_total_amount = 0
        ds_syn_bill_amount = 0
        ds_amount_sql=f'''SELECT dict.is_open_new AS 'is_open',SUM(pay.buyer_actual_amount) AS 'actual_amount',SUM(pay.total_amount) AS 'total_amount',SUM(erp.bill_total_amount) AS 'bill_total_amount' FROM order_info info LEFT JOIN inner_store_dictionary dict ON info.organization_code=dict.organization_code LEFT JOIN order_pay_info pay ON info.order_no=pay.order_no LEFT JOIN erp_bill_info erp ON info.order_no=erp.order_no WHERE info.third_platform_code='43' AND info.created>"{start_time}" AND info.created < "{end_time}" AND info.service_mode='O2O' GROUP BY dict.is_open_new'''
        ds_gift_sql=f"""SELECT SUM(detail.commodity_count) AS 'gift_count' FROM order_info info LEFT JOIN inner_store_dictionary dict ON info.organization_code=dict.organization_code  LEFT JOIN order_detail detail ON info.order_no=detail.order_no WHERE info.third_platform_code='43' AND info.created>"{start_time}" AND info.created < "{end_time}" AND info.service_mode='O2O' AND dict.is_open_new=2 AND detail.bill_price=0"""
        print(ds_amount_sql)
        ds_amount_result=db_yx_dscloud(ds_amount_sql)
        ds_gift_result=db_yx_dscloud(ds_gift_sql)
        ds_syn_gift_amount=0
        # 心云中台金额查询
        for ds_amount_item in ds_amount_result['data']:
            is_open=ds_amount_item['is_open']
            actual_amount=ds_amount_item['actual_amount']
            total_amount=ds_amount_item['total_amount']
            bill_amount=ds_amount_item['bill_total_amount']
            ds_actual_amount=ds_actual_amount+actual_amount
            ds_total_amount=ds_total_amount+total_amount
            ds_bill_amount=ds_bill_amount+bill_amount
            if is_open=="2":
                ds_syn_actual_amount = actual_amount
                ds_syn_total_amount = total_amount
                ds_syn_bill_amount = bill_amount
        for ds_gift_item in ds_gift_result['data']:
            ds_gift_count=ds_gift_item['gift_count']
            ds_syn_gift_amount=Decimal('0.01')*(ds_gift_count)+ds_syn_gift_amount
        # 小前台金额查询
        middle_amount_sql=f'''SELECT SUM(actually_paid+pay_balance_amount) AS 'actual_amount',SUM(total_order_amount) AS 'total_amount' FROM order_info WHERE order_time>"{start_time}" AND order_time < "{end_time}" AND order_status !=20 AND order_status !=2 AND is_b2c_order=0'''
        print(middle_amount_sql)
        middle_amount_result=db_yx_middle_order(middle_amount_sql)
        middle_total_amount=middle_amount_result['data'][0]['total_amount']
        middle_actual_amount=middle_amount_result['data'][0]['actual_amount']

        # 雨诺金额查询
        yn_amount_sql=f'''SELECT SUM(PAYAMOUNT) AS 'actual_amount',SUM(PRODUCTSAMOUNT) AS 'total_amount',SUM(ACCOUNTAMOUNT) AS 'bill_amount' FROM order_std WHERE ORDERFROM='111' AND CREATETIME>'{start_time}' AND CREATETIME<'{end_time}' AND INNERSTATUS=1'''
        print(yn_amount_sql)
        yn_amount_result=db_yn_oms(yn_amount_sql)
        yn_actual_amount=yn_amount_result['data'][0]['actual_amount']
        yn_total_amount = yn_amount_result['data'][0]['total_amount']
        yn_bill_amount = yn_amount_result['data'][0]['bill_amount']

        # 心云中台与微商城实付总额差值
        diff_xy_weshop_actual_amount=ds_actual_amount-middle_actual_amount
        # 云心中台与微商城商品总额差值
        diff_xy_weshop_total_amount =ds_total_amount- middle_total_amount
        # 心云中台与雨诺实付总额差值
        diff_xy_yn_actual_amount = ds_syn_actual_amount - yn_actual_amount+ds_syn_gift_amount
        # 心云中台与雨诺下账总额差值
        diff_xy_yn_bill_amount = ds_syn_bill_amount - yn_bill_amount+ds_syn_gift_amount
        # 心云中台与雨诺商品总额差值
        diff_xy_yn_total_amount = ds_syn_total_amount - yn_total_amount+ds_syn_gift_amount
        # 组装对比结果
        diff_xy_weshop_actual_amount_result = {"diff_sys": "心云 VS 微商城", "diff_obj": "实付差额", "diff_value": diff_xy_weshop_actual_amount,
                                 "front_sys_value": ds_actual_amount, "behind_value": middle_actual_amount}
        # 添加到返回结果中
        result.append(diff_xy_weshop_actual_amount_result)

        diff_xy_weshop_actual_total_amount_result = {"diff_sys": "心云 VS 微商城", "diff_obj": "商品差额",
                                               "diff_value": diff_xy_weshop_total_amount,
                                               "front_sys_value": ds_total_amount,
                                               "behind_value": middle_total_amount}
        result.append(diff_xy_weshop_actual_total_amount_result)
        diff_xy_yn_actual_amount_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "实付差额",
                                              "diff_value": diff_xy_yn_actual_amount,
                                              "front_sys_value": ds_syn_actual_amount,
                                              "behind_value": f"({yn_actual_amount}【赠：{ds_syn_gift_amount}】)"}
        result.append(diff_xy_yn_actual_amount_result)
        diff_xy_yn_bill_amount_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "下账差额",
                                           "diff_value": diff_xy_yn_bill_amount,
                                           "front_sys_value": ds_syn_bill_amount,
                                           "behind_value": f"({yn_bill_amount}【赠：{ds_syn_gift_amount}】)"}
        result.append(diff_xy_yn_bill_amount_result)
        diff_xy_yn_total_amount_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "商品差额",
                                         "diff_value": diff_xy_yn_total_amount,
                                         "front_sys_value": ds_syn_total_amount,
                                         "behind_value": f"({yn_total_amount}【赠：{ds_syn_gift_amount}】)"}
        result.append(diff_xy_yn_total_amount_result)
        return result
    except Exception as e:
        raise e



"""
    退款单数量金额比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_return_amount_o2o_yx(body):
    "退款单数量金额比对"
    try:
        result=[]
        start_time = body['start_time']
        end_time = body['end_time']
        ds_return_sql=f"""SELECT dict.is_open_new AS 'is_open',COUNT(refund.refund_no) AS 'ds_count',SUM(refund.consumer_refund) AS 'consumer_refund',SUM(refund.total_food_amount) AS 'total_amount',SUM(erp.refund_merchant_total) AS 'bill_total_amount' FROM refund_order refund LEFT JOIN order_info info ON refund.order_no = info.order_no LEFT JOIN inner_store_dictionary dict ON info.organization_code=dict.organization_code LEFT JOIN erp_refund_info erp ON refund.refund_no=erp.refund_no WHERE info.third_platform_code='43' AND info.created>"{start_time}" AND info.created < "{end_time}" AND info.service_mode='O2O' GROUP BY dict.is_open_new"""
        ds_return_result=db_yx_dscloud(ds_return_sql)
        # 心云系统退款单数量
        ds_return_count=0
        # 心云系统退买家总金额
        ds_return_consumer_refund=0
        # 心云系统退款商品金额
        ds_return_total_amount=0
        # 心云系统退款下账金额
        ds_return_bill_amount=0
        # 心云系统未切店退款单数量
        ds_syn_count = 0
        # 心云系统未切店退买家总金额
        ds_syn_consumer_refund = 0
        # 心云系统未切店退款商品金额
        ds_syn_total_amount = 0
        # 心云系统未切店退款下账金额
        ds_syn_bill_amount = 0
        for ds_item in ds_return_result['data']:
            is_open=ds_item['is_open']
            ds_count=ds_item['ds_count']
            ds_consumer_refund=ds_item['consumer_refund']
            ds_total_amount=ds_item['total_amount']
            ds_bill_total_amount=ds_item['bill_total_amount']
            ds_return_count=ds_return_count+ds_count
            ds_return_consumer_refund=ds_return_consumer_refund+ds_consumer_refund
            ds_return_total_amount=ds_return_total_amount+ds_total_amount
            ds_return_bill_amount=ds_return_bill_amount+ds_bill_total_amount
            if is_open=="2":
                ds_syn_count=ds_count
                ds_syn_consumer_refund=ds_consumer_refund
                ds_syn_total_amount=ds_total_amount
                ds_syn_bill_amount=ds_bill_total_amount
        # 心云退款完成实际退款金额查询
        ds_actual_sql=f"""SELECT dict.is_open_new AS 'is_open',COUNT(refund.refund_no) AS 'ds_count',SUM(refund.consumer_refund) AS 'consumer_refund',SUM(refund.total_food_amount) AS 'total_amount',SUM(erp.refund_merchant_total) AS 'bill_total_amount' FROM refund_order refund LEFT JOIN order_info info ON refund.order_no = info.order_no LEFT JOIN inner_store_dictionary dict ON info.organization_code=dict.organization_code LEFT JOIN erp_refund_info erp ON refund.refund_no=erp.refund_no WHERE info.third_platform_code='43' AND info.created>"{start_time}" AND info.created < "{end_time}" AND info.service_mode='O2O' AND refund.state=100"""
        ds_actual_result=db_yx_dscloud(ds_actual_sql)
        ds_actual_amount=ds_actual_result['data'][0]['consumer_refund']

        # 微商城退款完成实际退款金额查询
        middle_actual_sql=f"""SELECT COUNT(quest.id) AS 'middle_count', SUM(quest.refund_amount)+SUM((IF(ISNULL(quest.freight_amount),0,quest.freight_amount))) AS 'refund_amount',SUM(quest.actual_refund_amount)+SUM((IF(ISNULL(quest.refund_balance_amount),0,quest.refund_balance_amount))) AS 'actual_refund_amount' FROM return_quest quest LEFT JOIN order_info info ON quest.order_id=info.id WHERE info.order_time>"{start_time}" AND info.order_time < "{end_time}" AND info.order_status !=20 AND info.order_status !=2 AND info.is_b2c_order=0 AND quest.refund_reason != '商家发起退款' AND quest.`status`=2"""
        middle_actual_result=db_yx_middle_order(middle_actual_sql)
        middle_actual_amount=middle_actual_result['data'][0]['actual_refund_amount']


        # 微商城退款单信息查询
        middle_return_sql=f"""SELECT COUNT(quest.id) AS 'middle_count', SUM(quest.refund_amount)+SUM((IF(ISNULL(quest.freight_amount),0,quest.freight_amount))) AS 'refund_amount',SUM(quest.actual_refund_amount)+SUM((IF(ISNULL(quest.refund_balance_amount),0,quest.refund_balance_amount))) AS 'actual_refund_amount'  FROM return_quest quest LEFT JOIN order_info info ON quest.order_id=info.id WHERE info.order_time>"{start_time}" AND info.order_time < "{end_time}" AND info.order_status !=20 AND info.order_status !=2 AND info.is_b2c_order=0 AND quest.refund_reason != '商家发起退款'"""
        middle_return_result=db_yx_middle_order(middle_return_sql)
        # 微商城退款单数量
        middle_return_count=middle_return_result['data'][0]['middle_count']
        # 微商城退款商品金额
        middle_return_total_amount=middle_return_result['data'][0]['refund_amount']
        # 微商城退用户金额
        middle_return_consumer_refund=middle_return_result['data'][0]['actual_refund_amount']

        # 雨诺退款单信息查询
        yn_return_sql = f"""SELECT COUNT(returnorder.RETURNCODE) AS 'yn_count',SUM(returnorder.PRODUCTSAMOUNT) AS 'PRODUCTSAMOUNT',SUM(returnorder.RETURNAMOUNT) AS 'RETURNAMOUNT' FROM returnorder_std returnorder LEFT JOIN order_std order_s ON returnorder.ORDERCODE =order_s.ORDERCODE WHERE order_s.ORDERFROM='111' AND order_s.CREATETIME>'{start_time}' AND order_s.CREATETIME<'{end_time}' AND order_s.INNERSTATUS=1"""
        yn_return_result = db_yn_oms(yn_return_sql)
        # 雨诺退款单数量
        yn_return_count = yn_return_result['data'][0]['yn_count']
        # 雨诺退款商品金额
        yn_return_total_amount = yn_return_result['data'][0]['PRODUCTSAMOUNT']
        # 雨诺退用户金额
        yn_return_consumer_refund = yn_return_result['data'][0]['RETURNAMOUNT']

        diff_ds_weshop_count=ds_return_count-middle_return_count
        diff_ds_weshop_total_amount=ds_return_consumer_refund-middle_return_total_amount
        # diff_ds_weshop_consumer_refund=ds_return_consumer_refund-middle_return_consumer_refund
        diff_ds_weshop_actual_amount=ds_actual_amount-middle_actual_amount
        diff_ds_yn_count=ds_syn_count-yn_return_count
        diff_ds_yn_total_amount=ds_syn_consumer_refund-yn_return_total_amount
        diff_ds_yn_consumer_refund=ds_syn_bill_amount-yn_return_consumer_refund
        diff_ds_weshop_count_result={"diff_sys": "心云 VS 微商城", "diff_obj": "订单量差",
                                         "diff_value": diff_ds_weshop_count,
                                         "front_sys_value": ds_return_count,
                                         "behind_value": middle_return_count}
        result.append(diff_ds_weshop_count_result)
        diff_ds_weshop_total_amount_result = {"diff_sys": "心云 VS 微商城", "diff_obj": "退款商品差额",
                                       "diff_value": diff_ds_weshop_total_amount,
                                       "front_sys_value": ds_return_consumer_refund,
                                       "behind_value": middle_return_total_amount}
        result.append(diff_ds_weshop_total_amount_result)
        diff_ds_weshop_actual_amount_result = {"diff_sys": "心云 VS 微商城", "diff_obj": "实际完成退款差额",
                                                 "diff_value": diff_ds_weshop_actual_amount,
                                                 "front_sys_value": ds_actual_amount,
                                                 "behind_value": middle_actual_amount}
        result.append(diff_ds_weshop_actual_amount_result)
        # diff_ds_weshop_consumer_refund_result = {"diff_sys": "心云 VS 微商城", "diff_obj": "退用户差额",
        #                                       "diff_value": diff_ds_weshop_consumer_refund,
        #                                       "front_sys_value": ds_return_consumer_refund,
        #                                       "behind_value": middle_return_consumer_refund}
        # result.append(diff_ds_weshop_consumer_refund_result)
        diff_ds_yn_count_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "订单量差",
                                                 "diff_value": diff_ds_yn_count,
                                                 "front_sys_value": ds_syn_count,
                                                 "behind_value": yn_return_count}
        result.append(diff_ds_yn_count_result)
        diff_ds_yn_total_amount_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "退款商品差额",
                                   "diff_value": diff_ds_yn_total_amount,
                                   "front_sys_value": ds_syn_consumer_refund,
                                   "behind_value": yn_return_total_amount}
        result.append(diff_ds_yn_total_amount_result)
        diff_ds_yn_consumer_refund_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "退用户差额",
                                          "diff_value": diff_ds_yn_consumer_refund,
                                          "front_sys_value": ds_syn_bill_amount,
                                          "behind_value": yn_return_consumer_refund}
        result.append(diff_ds_yn_consumer_refund_result)
        return result
    except Exception as e:
        raise e



if __name__=="__main__":
    start_time="2023-12-03 00:00:00"
    end_time="2023-12-05 00:00:00"
    order_sale_count_o2o_yx(start_time,end_time)
    order_sale_amount_o2o_yx(start_time,end_time)