# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/3 10:41
@Auth ： 逗逗的小老鼠
@File ：moni_order_amount_discount.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.db_conf import db_yx_middle_order,db_yx_dscloud,db_yn_oms
"""
    销售单折扣异常监控
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_o2o_amount_discount(body):
    "销售单折扣异常监控"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        amount_discount_sql=f"""
                SELECT
                    info.third_order_no AS 'third_order_no',
                    info.third_platform_code AS 'third_platform_code',
                    pay.buyer_actual_amount AS 'buyer_actual_amount',
                    pay.total_amount AS 'total_amount',
                    ROUND( pay.buyer_actual_amount / pay.total_amount * 100, 2 ) AS 'discount_value' 
                FROM
                    order_pay_info pay
                    LEFT JOIN order_info info ON info.order_no = pay.order_no 
                WHERE
                    info.created > "{start_time}" 
                    AND info.created < "{end_time}" 
                    AND info.service_mode = "o2o" 
                    AND pay.buyer_actual_amount / pay.total_amount < 0.3 
                ORDER BY
                    discount_value ASC
        """
        amount_discount_result=db_yx_dscloud(amount_discount_sql)
        for amount_item in amount_discount_result['data']:
            # 平台订单号
            third_order_no=amount_item['third_order_no']
            # 三方平台码
            third_platform_code=amount_item['third_platform_code']
            # 用户实付金额
            buyer_actual_amount=amount_item['buyer_actual_amount']
            # 商品总金额
            total_amount=amount_item['total_amount']
            # 折扣比
            discount_value=amount_item['discount_value']
            if third_platform_code=="27":
                third_platform_name="美团"
            elif third_platform_code=="11":
                third_platform_name="京东到家"
            elif third_platform_code=="43":
                third_platform_name="微商城"
            elif third_platform_code=="24":
                third_platform_name="饿了么"
            else:
                third_platform_name="未知"
            amount_result={"third_order_no":third_order_no,"third_platform_name":third_platform_name,"total_amount":total_amount,"buyer_actual_amount":buyer_actual_amount,"discount_value":f"{discount_value}%"}
            result.append(amount_result)
        return result
    except Exception as e:
        raise e

