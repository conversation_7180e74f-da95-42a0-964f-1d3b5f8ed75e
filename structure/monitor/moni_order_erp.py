# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/8 16:38
@Auth ： 逗逗的小老鼠
@File ：moni_order_erp.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.db_conf import db_yx_middle_order,db_yx_dscloud,db_yn_oms
"""
    销售单下账状态比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_sale_iserp_o2o_xy(body):
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        # 查询心云系统下账状态数据
        xy_iserp_sql=f"""SELECT info.erp_state AS 'erp_state',COUNT(*) AS 'ds_count' FROM order_info info LEFT JOIN inner_store_dictionary dict ON info.organization_code=dict.organization_code WHERE info.third_platform_code='43' AND info.created>"{start_time}" AND info.created < "{end_time}" AND info.service_mode='O2O' AND dict.is_open_new=2 GROUP BY info.erp_state"""
        print(xy_iserp_sql)
        xy_iserp_result=db_yx_dscloud(xy_iserp_sql)
        # 待下账
        xy_erp_todo=0
        # 已下账
        xy_erp_done=0
        # 取消下账
        xy_erp_cancle=0
        # 未知状态
        xy_erp_unknow=0
        for xy_erp_item in xy_iserp_result['data']:
            xy_erp_state=str(xy_erp_item['erp_state'])
            xy_erp_count=xy_erp_item['ds_count']
            if xy_erp_state=="20" or xy_erp_state=="30":
                xy_erp_todo=xy_erp_todo+xy_erp_count
            elif xy_erp_state=="100":
                xy_erp_done=xy_erp_count
            elif xy_erp_state=="110":
                xy_erp_cancle=xy_erp_count
            else:
                xy_erp_unknow=xy_erp_count
        # 查询雨诺系统下账状态数据
        yn_iserp_sql=f"""SELECT ISACCOUNTS,COUNT(*) AS 'yn_count' FROM order_std WHERE ORDERFROM='111' AND CREATETIME>'{start_time}' AND CREATETIME<'{end_time}' AND INNERSTATUS=1 GROUP BY ISACCOUNTS"""
        print(yn_iserp_sql)
        yn_iserp_result=db_yn_oms(yn_iserp_sql)
        # 待下账
        yn_erp_todo = 0
        # 已下账
        yn_erp_done = 0
        # 取消下账
        yn_erp_cancle = 0
        # 未知状态
        yn_erp_unknow = 0
        for yn_erp_item in yn_iserp_result['data']:
            yn_erp_state=str(yn_erp_item['ISACCOUNTS'])
            yn_erp_count=yn_erp_item['yn_count']
            if yn_erp_state=="0" or yn_erp_state=="1":
                yn_erp_todo=yn_erp_todo+yn_erp_count
            elif yn_erp_state=="2" or yn_erp_state=="3":
                yn_erp_done=yn_erp_done+yn_erp_count
            else:
                yn_erp_unknow=yn_erp_count
        # 计算差值
        diff_erp_todo=xy_erp_todo-yn_erp_todo
        diff_erp_done = xy_erp_done - yn_erp_done
        diff_erp_cancle=xy_erp_cancle-yn_erp_cancle
        diff_erp_unknow=xy_erp_unknow-yn_erp_unknow
        diff_erp_todo_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "待下账差量",
                                          "diff_value": diff_erp_todo,
                                          "front_sys_value": xy_erp_todo,
                                          "behind_value": yn_erp_todo}
        result.append(diff_erp_todo_result)
        diff_erp_done_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "已下账差量",
                                "diff_value": diff_erp_done,
                                "front_sys_value": xy_erp_done,
                                "behind_value": yn_erp_done}
        result.append(diff_erp_done_result)
        diff_erp_cancle_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "取消下账差量",
                                "diff_value": diff_erp_cancle,
                                "front_sys_value": xy_erp_cancle,
                                "behind_value": yn_erp_cancle}
        result.append(diff_erp_cancle_result)
        diff_erp_unknow_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "未知状态差量",
                                  "diff_value": diff_erp_unknow,
                                  "front_sys_value": xy_erp_unknow,
                                  "behind_value": yn_erp_unknow}
        result.append(diff_erp_unknow_result)
        return result
    except Exception as e:
        raise e


"""
    退款单下账状态比对
    :param start_time:开始时间
    :param end_time:结束时间
    :return result：查询结果
"""
@exception(logger)
def order_return_iserp_o2o_xy(body):
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        # 查询心云系统下账状态数据
        xy_iserp_sql=f"""SELECT refund.erp_state AS 'erp_state',COUNT(*) AS 'ds_count' FROM refund_order refund LEFT JOIN order_info info ON refund.order_no=info.order_no  LEFT JOIN inner_store_dictionary dict ON info.organization_code=dict.organization_code WHERE info.third_platform_code='43' AND info.created>"{start_time}" AND info.created < "{end_time}" AND info.service_mode='O2O' AND dict.is_open_new=2 GROUP BY refund.erp_state"""
        print(xy_iserp_sql)
        xy_iserp_result=db_yx_dscloud(xy_iserp_sql)
        # 待下账
        xy_erp_todo=0
        # 已下账
        xy_erp_done=0
        # 取消下账
        xy_erp_cancle=0
        # 未知状态
        xy_erp_unknow=0
        for xy_erp_item in xy_iserp_result['data']:
            xy_erp_state=str(xy_erp_item['erp_state'])
            xy_erp_count=xy_erp_item['ds_count']
            if xy_erp_state=="20" :
                xy_erp_todo=xy_erp_count
            elif xy_erp_state=="100":
                xy_erp_done=xy_erp_count
            elif xy_erp_state=="102":
                xy_erp_cancle=xy_erp_count
            else:
                xy_erp_unknow=xy_erp_count
        # 查询雨诺系统下账状态数据
        yn_iserp_sql=f"""SELECT returnorder.ISACCOUNTS AS 'ISACCOUNTS',COUNT(*) AS 'yn_count' FROM returnorder_std returnorder LEFT JOIN order_std order_s ON returnorder.ORDERCODE =order_s.ORDERCODE WHERE order_s.ORDERFROM='111' AND order_s.CREATETIME>'{start_time}' AND order_s.CREATETIME<'{end_time}' AND order_s.INNERSTATUS=1 GROUP BY returnorder.ISACCOUNTS"""
        print(yn_iserp_sql)
        yn_iserp_result=db_yn_oms(yn_iserp_sql)
        # 待下账
        yn_erp_todo = 0
        # 已下账
        yn_erp_done = 0
        # 取消下账
        yn_erp_cancle = 0
        # 未知状态
        yn_erp_unknow = 0
        for yn_erp_item in yn_iserp_result['data']:
            yn_erp_state=str(yn_erp_item['ISACCOUNTS'])
            yn_erp_count=yn_erp_item['yn_count']
            if yn_erp_state=="0" :
                yn_erp_todo=yn_erp_todo+yn_erp_count
            elif yn_erp_state=="1" or yn_erp_state=="2" or yn_erp_state=="5":
                yn_erp_done=yn_erp_done+yn_erp_count
            else:
                yn_erp_unknow=yn_erp_count
        # 计算差值
        diff_erp_todo=xy_erp_todo-yn_erp_todo
        diff_erp_done = xy_erp_done - yn_erp_done
        diff_erp_cancle=xy_erp_cancle-yn_erp_cancle
        diff_erp_unknow=xy_erp_unknow-yn_erp_unknow
        diff_erp_todo_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "待下账差量",
                                          "diff_value": diff_erp_todo,
                                          "front_sys_value": xy_erp_todo,
                                          "behind_value": yn_erp_todo}
        result.append(diff_erp_todo_result)
        diff_erp_done_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "已下账差量",
                                "diff_value": diff_erp_done,
                                "front_sys_value": xy_erp_done,
                                "behind_value": yn_erp_done}
        result.append(diff_erp_done_result)
        diff_erp_cancle_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "取消下账差量",
                                "diff_value": diff_erp_cancle,
                                "front_sys_value": xy_erp_cancle,
                                "behind_value": yn_erp_cancle}
        result.append(diff_erp_cancle_result)
        diff_erp_unknow_result = {"diff_sys": "心云 VS 雨诺", "diff_obj": "未知状态差量",
                                  "diff_value": diff_erp_unknow,
                                  "front_sys_value": xy_erp_unknow,
                                  "behind_value": yn_erp_unknow}
        result.append(diff_erp_unknow_result)
        return result
    except Exception as e:
        raise e