# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/24 9:19
@Auth ： 逗逗的小老鼠
@File ：moni_kc_heartbeat.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from lib.db_conf import db_yx_dscloud

"""
    科传门店自动下账心跳异常
    :return result：查询结果
"""


@exception(logger)
def order_kc_heartbeat(body):
    "科传门店自动下账心跳异常"
    try:
        result = []
        # 查询30天内存在待下账的订单的心跳异常的门店
        kc_heartbeat_sql=f"""
            SELECT
                inner_dict.organization_code AS 'organization_code',
                inner_dict.organization_name AS 'organization_name',
                inner_dict.auto_bill_timestamp AS 'auto_bill_timestamp',
                inner_dict.difftime AS 'difftime',
                order_count.count_num AS 'count_num' 
            FROM
                (
                SELECT
                    organization_code,
                    organization_name,
                    auto_bill_timestamp,
                    TIMESTAMPDIFF(
                        MINUTE,
                        auto_bill_timestamp,
                    NOW()) AS difftime 
                FROM
                    inner_store_dictionary 
                WHERE
                    pos_mode = 3 
                    AND is_open_new = 1 
                    AND ( TIMESTAMPDIFF( MINUTE, auto_bill_timestamp, NOW())> 10 OR auto_bill_timestamp IS NULL ) 
                ) AS inner_dict
                INNER JOIN (
                SELECT
                    organization_code,
                    COUNT(*) AS 'count_num' 
                FROM
                    order_info 
                WHERE
                    organization_code IN (
                    SELECT
                        organization_code 
                    FROM
                        inner_store_dictionary 
                    WHERE
                        pos_mode = 3 
                        AND is_open_new = 1 
                        AND ( TIMESTAMPDIFF( MINUTE, auto_bill_timestamp, NOW())> 10 OR auto_bill_timestamp IS NULL ) 
                    ) 
                    AND TIMESTAMPDIFF(
                        DAY,
                        created,
                    NOW())< 31 
                    AND erp_state = 30 
                    AND service_mode = 'O2O' 
                    AND mer_code = '500001' 
                GROUP BY
                    organization_code 
                ) AS order_count ON inner_dict.organization_code = order_count.organization_code 
            ORDER BY
                count_num DESC,
                difftime DESC
        """
        kc_hearbeat_result=db_yx_dscloud(kc_heartbeat_sql)
        kc_hearbeat_data=kc_hearbeat_result['data']
        for kc_hearbeat_item in kc_hearbeat_data:
            organization_code=kc_hearbeat_item['organization_code']
            organization_name=kc_hearbeat_item['organization_name']
            auto_bill_timestamp=kc_hearbeat_item['auto_bill_timestamp']
            # 无心跳时间长(分钟)
            difftime=kc_hearbeat_item['difftime']
            # 待下账订单数量
            count_num=kc_hearbeat_item['count_num']
            outtime_msg={"organization_code":organization_code,"organization_name":organization_name,"auto_bill_timestamp":auto_bill_timestamp,"difftime":difftime,"count_num":count_num}
            result.append(outtime_msg)
        return result
    except Exception as e:
        raise e

