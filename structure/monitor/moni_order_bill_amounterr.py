# -*- coding: utf-8 -*-
"""
@Time ： 2024/6/3 16:15
@Auth ： 逗逗的小老鼠
@File ：moni_order_bill_amounterr.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from lib.deal_format_convert import value_mathematics
momi = "pro"
if momi == "test":
    from lib.db_conf_test import db_yx_test_dsclound as db_dscloud
if momi == "pro":
    from lib.db_conf import db_yx_dscloud as db_dscloud


"""
    订单下账失败中，金额错误信息分析
"""
@exception(logger)
def order_bill_amount_error(body):
    try:
        result = []
        o2o_result=order_o2o_bill_amount_error(body)
        result.extend(o2o_result)
        return result
    except Exception as e:
        raise e

"""
    订单O2O下账失败的订单信息分析
"""
@exception(logger)
def order_o2o_bill_amount_error(body):
    "订单O2O下账失败的订单信息分析"
    try:
        result = []
        start_time = body['start_time']
        end_time = body['end_time']
        # 查询因金额错误导致下账失败的订单
        amount_error_sql = f"""
                    SELECT
                        info.order_no AS 'order_no',
                        info.third_platform_code AS 'third_platform_code',
                        info.third_order_no AS 'third_order_no',
                        bill.client_conf_id AS 'client_conf_id',
                        bill.bill_total_amount AS 'bill_total_amount',
                        bill.bill_commodity_amount AS 'bill_commodity_amount',
                        bill.merchant_delivery_fee AS 'merchant_delivery_fee'
                    FROM
                        order_info info
                        LEFT JOIN erp_bill_info bill ON info.order_no=bill.order_no
                    WHERE
                     info.erp_state = 99 
                        AND info.created >= '{start_time}' 
                        AND info.created <= '{end_time}' 
                        AND info.service_mode='O2O'
                        AND (
                        info.extend_info LIKE '%金额错误%' 
                        OR info.extend_info LIKE '%分摊折扣为负数%')
                """
        amount_error_result = db_dscloud(amount_error_sql)
        amount_error_data = amount_error_result['data']
        # print(amount_error_data)

        # 生成order_no的列表
        order_no_list = ['1']
        for amount_error_item in amount_error_data:
            order_no = amount_error_item['order_no']
            order_no_list.append(str(order_no))
        order_no_filter = ",".join(order_no_list)
        # 查询下账失败的商品详情
        # is_joint 字段说明：0：非组合商品，1：组合商品子品，2：组合商品父品
        order_detail_sql=f"""
                SELECT
                    order_no,
                    erp_code,
                    commodity_count,
                    original_price,
                    price,
                    total_amount,
                    discount_share,
                    bill_price,
                    actual_net_amount,
                    refund_count,
                    is_joint 
                FROM
                    order_detail 
                WHERE
                    order_no IN ( {order_no_filter} ) 
                    AND is_joint !=2
        """
        order_detail_result=db_dscloud(order_detail_sql)
        order_detail_data=order_detail_result['data']
        # 退款单查询
        refund_order_sql=f"""
            SELECT order_no,COUNT(*) AS 're_count' FROM refund_order WHERE order_no IN ({order_no_filter}) GROUP BY order_no
        """
        refund_order_result=db_dscloud(refund_order_sql)
        refund_order_data=refund_order_result['data']

        for order_item in amount_error_data:
            order_no = order_item['order_no']
            third_platform_code=order_item['third_platform_code']
            client_conf_id=order_item['client_conf_id']
            bill_total_amount=order_item['bill_total_amount']
            bill_commodity_amount=order_item['bill_commodity_amount']
            merchant_delivery_fee=order_item['merchant_delivery_fee']
            total_commodity_amount=0.00
            total_bill_amount=0.00
            for order_detail_item in order_detail_data:
                detail_order_no=order_detail_item['order_no']
                if str(order_no)==str(detail_order_no):
                    erp_code=order_detail_item['erp_code']
                    commodity_count=order_detail_item['commodity_count']
                    original_price=order_detail_item['original_price']
                    price=order_detail_item['price']
                    total_amount=order_detail_item['total_amount']
                    discount_share=order_detail_item['discount_share']
                    bill_price=order_detail_item['bill_price']
                    refund_count=order_detail_item['refund_count']
                    actual_net_amount=order_detail_item['actual_net_amount']
                    actual_count=int(commodity_count)-int(refund_count)
                    total_commodity_amount=value_mathematics(total_commodity_amount,total_amount,"+")
                    total_bill_amount=value_mathematics(total_bill_amount,actual_net_amount,"+")
                    # 实际下账金额-（商品总金额-分摊金额）！=0
                    if value_mathematics(actual_net_amount,value_mathematics(total_amount,discount_share,"-"),"-")!=0:
                        exec_data={"order_no":str(order_no),"exec_msg":f"商品【{erp_code}】实际下账金额【{actual_net_amount}】!=总金额【{total_amount}】-分摊金额【{discount_share}】"}
                        result.append(exec_data)
                    # 商品总金额 ！= 单价*数量
                    if value_mathematics(total_amount,value_mathematics(price,actual_count,"*"),"-")!=0:
                        exec_data = {"order_no": str(order_no),
                                     "exec_msg": f"商品【{erp_code}】商品总金额【{total_amount}】!=单价【{price}】*数量【{actual_count}】"}
                        result.append(exec_data)
                    # 下账金额 ！= 下账单价*数量
                    if value_mathematics(actual_net_amount,value_mathematics(bill_price,actual_count,"*"),"-")!=0:
                        exec_data = {"order_no": str(order_no),
                                     "exec_msg": f"商品【{erp_code}】下账金额【{actual_net_amount}】!=下账单价【{bill_price}】*数量【{actual_count}】"}
                        result.append(exec_data)
            # 下账商品金额！=明细汇总金额
            if value_mathematics(bill_commodity_amount,total_bill_amount,"-")!=0:
                print(value_mathematics(bill_commodity_amount,total_bill_amount,"-"))
                exec_data = {"order_no": str(order_no),
                             "exec_msg": f"下账商品金额【{bill_commodity_amount}】!=明细汇总金额【{total_bill_amount}】"}
                result.append(exec_data)
            else:
                for refund_order_item in refund_order_data:
                    refund_order_no=refund_order_item['order_no']
                    if str(order_no)==str(refund_order_no):
                        exec_data = {"order_no": str(order_no),
                                     "exec_msg": f"疑似关联退款单导致下账失败，请介入处理"}
                        result.append(exec_data)
        return result
    except Exception as e:
        raise e



if __name__=="__main__":
    body={"start_time":"2024-06-03 00:00:00","end_time":"2024-06-06 00:00:00"}
    data=order_bill_amount_error(body)
    print(data)


