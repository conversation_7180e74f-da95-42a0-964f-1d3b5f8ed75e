# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/24 11:11
@Auth ： 逗逗的小老鼠
@File ：msg_order_erpcode.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception, logger
from structure.monitor.moni_order_erpcode import order_H1_erpcode
from lib.deal_wx_msg import message_wechat_push
import datetime

"""
    订单海典H1商品编码异常监控结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""


@exception(logger)
def order_h1_erpcode_message_generated(start_time, end_time):
    try:
        wx_msg = f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**订单海典H1商品编码异常监控：**\n"""
        data = {"start_time": start_time, "end_time": end_time}
        h1_erpcode_result=order_H1_erpcode(data)
        h1_erpcode_len=len(h1_erpcode_result)
        h1_erpcodecount_msg=f"""**<font color=\"info\">H1系统erp编码异常订单共</font><font color=\"warning\">{h1_erpcode_len}</font>个**\n"""
        h1_erpcode_content_msg = order_h1_erpcode_content(h1_erpcode_result)
        wx_msg = wx_msg + h1_erpcodecount_msg+h1_erpcode_content_msg
        return wx_msg
    except Exception as e:
        raise e


"""
    订单海典H1商品编码异常监控结果信息
    :return push_result：推送结果
"""


@exception(logger)
def order_h1_erpcode_message_push():
    "订单海典H1商品编码异常监控信息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(minutes=5)
        next_time = next_date.replace(second=0, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(days=1)
        yester_time = yesterday.replace(hour=6,minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info = order_h1_erpcode_message_generated(str(yester_time), str(next_time))
        message_info = sale_message_info + "\n"
        print(message_info)
        msg_task_code="order_h1_erpcode_message_push"
        push_result=message_wechat_push(message_info,msg_task_code)
        print(push_result)
        return push_result
    except Exception as e:
        raise e


"""
    订单海典H1商品编码异常监控结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""


@exception(logger)
def order_h1_erpcode_content(diff_list):
    "订单海典H1商品编码异常监控结果内容"
    try:
        result_msg = ""
        max_length = 0
        for diff_item in diff_list:
            max_length = max_length + 1
            order_no = diff_item['order_no']
            third_platform_name = diff_item['third_platform_name']
            created = diff_item['created']
            erp_state = diff_item['erp_state']
            order_state=diff_item['order_state']
            erp_code=diff_item['erp_code']
            commodity_name=diff_item['commodity_name']
            diff_msg = f"""><font color=\"warning\">订单号:{order_no}  订单来源：{third_platform_name}  erp编码：{erp_code}    下单时间:{created}    下账状态：{erp_state}  订单状态：{order_state}</font>\n"""
            result_msg = result_msg + diff_msg
            if max_length>15:
                break
        return result_msg
    except Exception as e:
        raise e