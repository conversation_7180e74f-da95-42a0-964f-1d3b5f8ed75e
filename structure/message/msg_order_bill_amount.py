# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/27 11:14
@Auth ： 逗逗的小老鼠
@File ：msg_order_bill_amount.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from structure.monitor.moni_order_bill_amount import order_sale_o2o_bill_amount,order_sale_o2o_bill_amount_db_insert
from lib.deal_wx_msg import message_wechat_push
import datetime

"""
    生成正向单下账金额比对结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def diff_sale_bill_amount_message_generated(start_time,end_time):
    try:
        related_id = int(datetime.datetime.now().timestamp())
        info_table = "order_sale_o2o_bill_amount_info"
        detail_table = "order_sale_o2o_bill_amount_detail"
        wx_msg=f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**销售单下账金额比对结果：**\n"""
        data = {"start_time": start_time, "end_time": end_time, "related_id": related_id, "info_table": info_table,
                "detail_table": detail_table}
        sale_bill_result = order_sale_o2o_bill_amount(data)
        order_sale_o2o_bill_amount_db_insert(data,sale_bill_result)
        sale_bill_count = len(sale_bill_result)
        sale_bill_info_msg = ""
        if sale_bill_count > 0:
            sale_bill_msg = f"""><font color=\"warning\">O2O销售单下账金额异常数量：【{sale_bill_count}】</font>\n"""
            sale_bill_info_msg = diff_sale_bill_amount_content(sale_bill_result)
        else:
            sale_bill_msg = f"""><font color=\"info\">O2O销售单下账金额异常数量：【{sale_bill_count}】</font>\n"""
        wx_msg=wx_msg+sale_bill_msg+sale_bill_info_msg
        return wx_msg
    except Exception as e:
        raise e

"""
    推送正向单下账金额比对结果信息
    :return push_result：推送结果
"""
@exception(logger)
def order_bill_amount_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(days=1)
        next_time = next_date.replace(hour=23, minute=59, second=59, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(days=1)
        yester_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info=diff_sale_bill_amount_message_generated(str(yester_time),str(next_time))
        message_info=sale_message_info+"\n"
        print(message_info)

        msg_task_code = "order_bill_amount_message_push"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e


"""
    生成正向单下账金额对比结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def diff_sale_bill_amount_content(diff_list):
    try:
        result_msg=""
        max_length=0
        for diff_item in diff_list:
            max_length=max_length+1
            third_order_no=diff_item['third_order_no']
            delivery_type=diff_item['delivery_type']
            exec_msg=diff_item['exec']
            diff_msg=f"""><font color=\"warning\">{third_order_no}【{delivery_type}】:   {exec_msg}</font>\n"""
            result_msg=result_msg+diff_msg
            if max_length>19:
                break
        return result_msg
    except Exception as e:
        raise e