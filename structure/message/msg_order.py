# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/7 10:58
@Auth ： 逗逗的小老鼠
@File ：msg_order.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from structure.monitor.moni_order_count import order_sale_amount_o2o_yx,order_sale_count_o2o_yx,order_return_amount_o2o_yx
from structure.monitor.compare_order_info import order_return_o2o_info,order_sale_o2o_info
from lib.deal_wx_msg import message_wechat_push
import datetime


"""
    生成正向单比对结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def diff_order_message_generated(start_time,end_time):
    try:
        wx_msg=f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**销售单比对结果：**\n"""
        data={"start_time":start_time,"end_time":end_time}
        sale_count_result = order_sale_count_o2o_yx(data)
        sale_count_msg=diff_order_content(sale_count_result)
        sale_amount_result=order_sale_amount_o2o_yx(data)
        sale_amount_msg=diff_order_content(sale_amount_result)
        sale_info_result=order_sale_o2o_info(data)
        sale_info_msg=diff_sale_info_content(sale_info_result)
        wx_msg=wx_msg+sale_count_msg+sale_amount_msg+f"""**异常单详情：**\n"""+sale_info_msg
        return wx_msg
    except Exception as e:
        raise e

"""
    生成退款单比对结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def diff_return_order_message_generated(start_time,end_time):
    try:
        wx_msg=f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**退款单比对结果：**\n"""
        data={"start_time":start_time,"end_time":end_time}
        # 退款单总差额比对信息生成
        return_order_result=order_return_amount_o2o_yx(data)
        return_order_msg=diff_order_content(return_order_result)
        # 退款单详情比对信息生成
        return_order_info_result=order_return_o2o_info(data)
        return_order_info_msg=diff_return_info_content(return_order_info_result)
        wx_msg=wx_msg+return_order_msg+f"""**异常单详情：**\n"""+return_order_info_msg
        return wx_msg
    except Exception as e:
        raise e

"""
    推送订单比对结果消息
    :return push_result：推送结果
"""
@exception(logger)
def order_sale_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(minutes=5)
        next_time = next_date.replace(second=0, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(days=1)
        yester_time=yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info=diff_order_message_generated(str(yester_time),str(next_time))
        message_info=sale_message_info
        print(message_info)
        rebot_url="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=bed5d632-451e-4693-961a-0774896878c0"
        notification_person=[]
        msg_task_code = "order_sale_message_push"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e

"""
    推送退款单比对结果消息
    :return push_result：推送结果
"""
@exception(logger)
def order_return_message_push():
    "推送退款单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(minutes=5)
        next_time = next_date.replace(second=0, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(days=1)
        yester_time=yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        print(yester_time)
        return_message_info=diff_return_order_message_generated(str(yester_time),str(next_time))
        message_info=return_message_info
        print(message_info)
        msg_task_code = "order_return_message_push"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e

"""
    生成退款单总额对比结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def diff_order_content(diff_list):
    try:
        result_msg=""
        for diff_item in diff_list:
            diff_sys=diff_item['diff_sys']
            diff_obj=diff_item['diff_obj']
            diff_value=diff_item['diff_value']
            front_sys_value=diff_item['front_sys_value']
            behind_value=diff_item['behind_value']
            if diff_value==0:
                diff_msg=f"""><font color=\"info\">{diff_sys}   {diff_obj}: {diff_value}</font>\n"""
            else:
                diff_msg=f"""><font color=\"warning\">{diff_sys}   {diff_obj}: {front_sys_value} - {behind_value}=【{diff_value}】</font>\n"""
            result_msg=result_msg+diff_msg
        return result_msg
    except Exception as e:
        raise e

"""
    生成退款单信息对比结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def diff_return_info_content(diff_list):
    try:
        result_msg=""
        for diff_item in diff_list:
            diff_sys=diff_item['diff_sys']
            diff_obj=diff_item['diff_obj']
            order_code=diff_item['order_code']
            return_order_code=diff_item['return_order_code']
            behind_value=diff_item['behind_value']
            front_sys_value=diff_item['front_sys_value']
            diff_msg=f"""><font color=\"warning\">{diff_sys}   {order_code}【{return_order_code}】  {diff_obj}: {front_sys_value} VS {behind_value}</font>\n"""
            result_msg=result_msg+diff_msg
        return result_msg
    except Exception as e:
        raise e

"""
    生成销售单信息对比结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def diff_sale_info_content(diff_list):
    try:
        result_msg=""
        max_length=0
        for diff_item in diff_list:
            max_length=max_length+1
            diff_sys=diff_item['diff_sys']
            diff_obj=diff_item['diff_obj']
            order_code=diff_item['order_code']
            behind_value=diff_item['behind_value']
            front_sys_value=diff_item['front_sys_value']
            diff_msg=f"""><font color=\"warning\">{diff_sys}   {order_code}  {diff_obj}: {front_sys_value} VS {behind_value}</font>\n"""
            result_msg=result_msg+diff_msg
            if max_length>19:
                break
        return result_msg
    except Exception as e:
        raise e


if __name__=="__main__":
    order_sale_message_push()