# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/10 18:48
@Auth ： 逗逗的小老鼠
@File ：msg_order_weshop_exce.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception, logger
from structure.monitor.moni_order_weshop import order_sale_weshop_discount,order_sale_weshop_member,order_sale_weshop_store,order_sale_weshop_goods,order_sale_weshop_db_insert
from lib.deal_wx_msg import message_wechat_push
import datetime


"""
    微商城订单商品数量监控
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def order_sale_weshop_goods_message_generated(start_time, end_time):
    try:
        related_id = int(datetime.datetime.now().timestamp())
        info_table="order_sale_weshop_goods_info"
        detail_table="order_sale_weshop_goods_detail"
        wx_msg = f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**微商城订单商品数量监控结果：**\n"""
        data = {"start_time": start_time, "end_time": end_time,"related_id":related_id,"info_table":info_table,"detail_table":detail_table}
        result_info = order_sale_weshop_goods(data)
        order_sale_weshop_db_insert(data,result_info)
        result_count = len(result_info)
        msg_info = ""
        if result_count > 0:
            weshop_msg = f"""><font color=\"warning\">微商城订单商品数异常数量：【{result_count}】</font>\n"""
            msg_info = order_sale_weshop_goods_content(result_info)
        else:
            weshop_msg = f"""><font color=\"info\">微商城订单商品数异常数量：【{result_count}】</font>\n"""
        wx_msg = wx_msg + weshop_msg + f"""**异常详情：**\n""" + msg_info
        return wx_msg
    except Exception as e:
        raise e



"""
    微商城订单折扣异常数量监控
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def order_sale_weshop_discount_message_generated(start_time, end_time):
    try:
        related_id = int(datetime.datetime.now().timestamp())
        info_table = "order_sale_weshop_discount_info"
        detail_table = "order_sale_weshop_discount_detail"
        wx_msg = f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**微商城订单折扣异常数量监控结果：**\n"""
        data = {"start_time": start_time, "end_time": end_time,"related_id":related_id,"info_table":info_table,"detail_table":detail_table}
        result_info = order_sale_weshop_discount(data)
        order_sale_weshop_db_insert(data, result_info)
        result_count = len(result_info)
        msg_info = ""
        if result_count > 0:
            weshop_msg = f"""><font color=\"warning\">微商城订单折扣异常数量：【{result_count}】</font>\n"""
            msg_info = order_sale_weshop_discount_content(result_info)
        else:
            weshop_msg = f"""><font color=\"info\">微商城订单折扣异常数量：【{result_count}】</font>\n"""
        wx_msg = wx_msg + weshop_msg + f"""**异常详情：**\n""" + msg_info
        return wx_msg
    except Exception as e:
        raise e

"""
    微商城会员下单数量监控
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def order_sale_weshop_member_message_generated(start_time, end_time):
    try:
        related_id = int(datetime.datetime.now().timestamp())
        info_table = "order_sale_weshop_member_info"
        detail_table = "order_sale_weshop_member_detail"
        wx_msg = f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**微商城会员下单数量监控：**\n"""
        data = {"start_time": start_time, "end_time": end_time,"related_id":related_id,"info_table":info_table,"detail_table":detail_table}
        result_info = order_sale_weshop_member(data)
        order_sale_weshop_db_insert(data, result_info)
        result_count = len(result_info)
        msg_info = ""
        if result_count > 0:
            weshop_msg = f"""><font color=\"warning\">微商城会员下单数量异常量：【{result_count}】</font>\n"""
            msg_info = order_sale_weshop_member_content(result_info)
        else:
            weshop_msg = f"""><font color=\"info\">微商城会员下单数量异常量：【{result_count}】</font>\n"""
        wx_msg = wx_msg + weshop_msg + f"""**异常详情：**\n""" + msg_info
        return wx_msg
    except Exception as e:
        raise e


"""
    微商城门店销售单量监控
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def order_sale_weshop_store_message_generated(start_time, end_time):
    try:
        related_id = int(datetime.datetime.now().timestamp())
        info_table = "order_sale_weshop_store_info"
        detail_table = "order_sale_weshop_store_detail"
        wx_msg = f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**微商城门店销售单量监控：**\n"""
        data = {"start_time": start_time, "end_time": end_time,"related_id":related_id,"info_table":info_table,"detail_table":detail_table}
        result_info = order_sale_weshop_store(data)
        order_sale_weshop_db_insert(data, result_info)
        result_count = len(result_info)
        msg_info = ""
        if result_count > 0:
            weshop_msg = f"""><font color=\"warning\">微商城门店销售单量异常门店数：【{result_count}】</font>\n"""
            msg_info = order_sale_weshop_store_content(result_info)
        else:
            weshop_msg = f"""><font color=\"info\">微商城门店销售单量异常门店数：【{result_count}】</font>\n"""
        wx_msg = wx_msg + weshop_msg + f"""**异常详情：**\n""" + msg_info
        return wx_msg
    except Exception as e:
        raise e

"""
    微商城门店销售单量监控结果信息
    :return push_result：推送结果
"""
@exception(logger)
def order_sale_weshop_store_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(days=1)
        next_time = next_date.replace(hour=23, minute=59, second=59, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(days=1)
        yester_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info = order_sale_weshop_store_message_generated(str(yester_time), str(next_time))
        message_info = sale_message_info + "\n"
        print(message_info)
        msg_task_code = "order_sale_weshop_store_message_push"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e



"""
    微商城订单数量异常监控消息推送
    :return push_result：推送结果
"""
@exception(logger)
def order_sale_weshop_goods_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(days=1)
        next_time = next_date.replace(hour=23, minute=59, second=59, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(days=1)
        yester_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info = order_sale_weshop_goods_message_generated(str(yester_time), str(next_time))
        message_info = sale_message_info + "\n"
        print(message_info)
        msg_task_code = "order_sale_weshop_goods_message_push"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e


"""
    微商城会员下单数量监控结果信息
    :return push_result：推送结果
"""
@exception(logger)
def order_sale_weshop_member_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(days=1)
        next_time = next_date.replace(hour=23, minute=59, second=59, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(days=1)
        yester_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info = order_sale_weshop_member_message_generated(str(yester_time), str(next_time))
        message_info = sale_message_info + "\n"
        print(message_info)
        msg_task_code = "order_sale_weshop_member_message_push"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e


"""
    微商城订单折扣异常数量监控结果信息
    :return push_result：推送结果
"""
@exception(logger)
def order_sale_weshop_discount_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(days=1)
        next_time = next_date.replace(hour=23, minute=59, second=59, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(days=1)
        yester_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info = order_sale_weshop_discount_message_generated(str(yester_time), str(next_time))
        message_info = sale_message_info + "\n"
        print(message_info)

        msg_task_code = "order_sale_weshop_discount_message_push"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e

"""
    微商城订单折扣异常数量监控结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def order_sale_weshop_discount_content(diff_list):
    try:
        result_msg = ""
        for diff_item in diff_list:
            msg = diff_item['msg']
            order_no=diff_item['order_no']
            store_id=diff_item['store_id']
            total_order_amount=diff_item['total_order_amount']
            actually_paid=diff_item['actually_paid']
            discount_value=diff_item['discount_value']
            result_msg = result_msg + f"""><font color=\"warning\">{msg}：    订单号：{order_no}    订单金额：{total_order_amount}     实付金额：{actually_paid}【折扣：{discount_value}%】</font>\n"""
        return result_msg
    except Exception as e:
        raise e


"""
    微商城订单折扣异常数量监控结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def order_sale_weshop_member_content(diff_list):
    try:
        result_msg = ""
        for diff_item in diff_list:
            msg = diff_item['msg']
            member_card=diff_item['member_card']
            member_phone=diff_item['member_phone']
            buy_count=diff_item['buy_count']
            result_msg = result_msg + f"""><font color=\"warning\">{msg}：    会员卡号：{member_card}    会员手机号：{member_phone}     购买单量：{buy_count}</font>\n"""
        return result_msg
    except Exception as e:
        raise e


"""
    微商城门店销售单量监控内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def order_sale_weshop_store_content(diff_list):
    try:
        result_msg = ""
        for diff_item in diff_list:
            msg = diff_item['msg']
            send_store_code=diff_item['send_store_code']
            store_id=diff_item['store_id']
            sell_count=diff_item['sell_count']
            result_msg = result_msg + f"""><font color=\"warning\">{msg}：    门店编码：{send_store_code}【ID：{store_id}】    销售单量：{sell_count}</font>\n"""
        return result_msg
    except Exception as e:
        raise e



"""
    微商城门店销售单商品数量监控内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def order_sale_weshop_goods_content(diff_list):
    try:
        result_msg = ""
        for diff_item in diff_list:
            msg = diff_item['msg']
            order_no=diff_item['order_no']
            send_store_code=diff_item['send_store_code']
            total_goods_number=diff_item['total_goods_number']
            result_msg = result_msg + f"""><font color=\"warning\">{msg}：   订单号：{order_no}    门店编码：{send_store_code}    商品数量：{total_goods_number}</font>\n"""
        return result_msg
    except Exception as e:
        raise e



if __name__=="__main__":
    current_date = datetime.datetime.now()
    timestamp = int(datetime.datetime.now().timestamp())
    print(timestamp)