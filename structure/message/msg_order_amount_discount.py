# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/3 14:33
@Auth ： 逗逗的小老鼠
@File ：msg_order_amount_discount.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from structure.monitor.moni_order_amount_discount import order_sale_o2o_amount_discount
from lib.deal_wx_msg import message_wechat_push,message_config_info
import datetime

"""
    生成销售单折扣异常结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def diff_sale_amount_discount_message_generated(start_time,end_time):
    try:
        wx_msg=f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**销售单实付低于3折订单清单：**\n"""
        data={"start_time":start_time,"end_time":end_time}
        sale_bill_result = order_sale_o2o_amount_discount(data)
        sale_bill_count = len(sale_bill_result)
        sale_bill_info_msg = ""
        if sale_bill_count > 0:
            sale_bill_msg = f"""><font color=\"warning\">O2O销售单实付低于3折数量：【{sale_bill_count}】</font>\n"""
            sale_bill_info_msg = diff_sale_amount_discount_content(sale_bill_result)
        else:
            sale_bill_msg = f"""><font color=\"info\">O2O销售单实付低于3折数量：【{sale_bill_count}】</font>\n"""
        wx_msg=wx_msg+sale_bill_msg+sale_bill_info_msg
        return wx_msg
    except Exception as e:
        raise e

"""
    推送销售单折扣异常结果信息
    :return push_result：推送结果
"""
@exception(logger)
def order_amount_discount_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(minutes=5)
        next_time=next_date.replace(second=0, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(hours=24)
        yester_time=yesterday.replace( minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info=diff_sale_amount_discount_message_generated(str(yester_time),str(next_time))
        message_info=sale_message_info+"\n"
        print(message_info)
        msg_task_code="order_amount_discount_message_push"
        push_result=message_wechat_push(message_info,msg_task_code)
        return push_result
    except Exception as e:
        raise e


"""
    生成销售单折扣异常结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def diff_sale_amount_discount_content(diff_list):
    "生成销售单折扣异常结果内容"
    try:
        result_msg=""
        max_length=0
        for diff_item in diff_list:
            max_length=max_length+1
            third_order_no=diff_item['third_order_no']
            third_platform_name=diff_item['third_platform_name']
            total_amount=diff_item['total_amount']
            buyer_actual_amount=diff_item['buyer_actual_amount']
            discount_value=diff_item['discount_value']
            diff_msg=f"""><font color=\"warning\">【{third_platform_name}】{third_order_no}:  商品总金额【{total_amount}】 实付金额【{buyer_actual_amount}】   折扣【{discount_value}】</font>\n"""
            result_msg=result_msg+diff_msg
            if max_length>20:
                break
        return result_msg
    except Exception as e:
        raise e