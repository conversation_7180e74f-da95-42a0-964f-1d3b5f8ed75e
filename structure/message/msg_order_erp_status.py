# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/22 14:51
@Auth ： 逗逗的小老鼠
@File ：msg_order_erp_status.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception, logger
from structure.monitor.moni_order_erp_status import order_sale_o2o_erp_status_xy
from lib.deal_wx_msg import message_wechat_push
import datetime

"""
    生成正向单下账状态结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""


@exception(logger)
def order_sale_o2o_erp_status_message_generated(start_time, end_time):
    try:
        wx_msg = f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**订单下账状态监测结果：**\n"""
        data = {"start_time": start_time, "end_time": end_time}
        order_erp_status_result = order_sale_o2o_erp_status_xy(data)
        order_erp_status_msg = order_sale_o2o_erp_status_content(order_erp_status_result)
        wx_msg = wx_msg + order_erp_status_msg
        return wx_msg
    except Exception as e:
        raise e


"""
    推送正向单下账状态结果信息
    :return push_result：推送结果
"""


@exception(logger)
def order_sale_o2o_erp_status_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(minutes=5)
        next_time = next_date.replace(second=0, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(hours=24)
        yester_time = yesterday.replace(minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info = order_sale_o2o_erp_status_message_generated(str(yester_time), str(next_time))
        message_info = sale_message_info + "\n"
        print(message_info)
        msg_task_code="order_sale_o2o_erp_status_message_push"
        push_result=message_wechat_push(message_info,msg_task_code)
        return push_result
    except Exception as e:
        raise e


"""
    生成正向单下账状态结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""


@exception(logger)
def order_sale_o2o_erp_status_content(diff_list):
    try:
        result_msg = ""
        max_length = 0
        for diff_item in diff_list:
            max_length = max_length + 1
            province_value = diff_item['province_value']
            success_value = diff_item['success_value']
            success_proportion = diff_item['success_proportion']
            todo_value = diff_item['todo_value']
            todo_proportion = diff_item['todo_proportion']
            lock_value = diff_item['lock_value']
            lock_proportion = diff_item['lock_proportion']
            cancle_value = diff_item['cancle_value']
            cancle_proportion = diff_item['cancle_proportion']
            fail_value = diff_item['fail_value']
            fail_proportion = diff_item['fail_proportion']
            unknow_value = diff_item['unknow_value']
            unkonw_proportion = diff_item['unkonw_proportion']
            erp_count = diff_item['erp_count']
            diff_msg = f"""><font color=\"warning\">{province_value}:下账成功:{success_value}（{success_proportion}%），待下账:{todo_value}（{todo_proportion}%），待锁定:{lock_value}（{lock_proportion}%），取消下账:{cancle_value}（{cancle_proportion}%），下账失败:{fail_value}（{fail_proportion}%），未知状态:{unknow_value}({unkonw_proportion}%)，订单总量:{erp_count}</font>\n"""
            result_msg = result_msg + diff_msg
        return result_msg
    except Exception as e:
        raise e
