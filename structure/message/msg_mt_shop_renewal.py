# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/12 16:45
@Auth ： 逗逗的小老鼠
@File ：msg_mt_shop_renewal.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception, logger
from structure.operate.operate_mt_shop import mt_shop_renewal
from lib.deal_wx_msg import message_wechat_push
import datetime

"""
    生成正向单下账异常情况结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def mt_shop_renewal_message_generated(start_time, end_time):
    try:
        wx_msg = f"""**测试门店续期结果：**\n"""
        data = {"start_time": start_time, "end_time": end_time}
        order_bill_exce_result = mt_shop_renewal()
        order_bill_exce_msg = mt_shop_renewal_content(order_bill_exce_result)
        wx_msg = wx_msg + order_bill_exce_msg
        return wx_msg
    except Exception as e:
        raise e


"""
    推送正向单下账异常情况结果信息
    :return push_result：推送结果
"""


@exception(logger)
def mt_shop_renewal_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(days=1)
        next_time = next_date.replace(hour=23, minute=59, second=59, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(days=1)
        yester_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info = mt_shop_renewal_message_generated(str(yester_time), str(next_time))
        message_info = sale_message_info + "\n"
        print(message_info)
        msg_task_code = "mt_shop_renewal_message_push"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e


"""
    生成正向单下账异常情况结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""


@exception(logger)
def mt_shop_renewal_content(diff_list):
    try:
        result_msg = ""
        for diff_item in diff_list:
            app_id = diff_item['app_id']
            wmPoiId=diff_item['wmPoiId']
            shop_name=diff_item['shop_name']
            shop_id=diff_item['shop_id']
            app_name=diff_item['app_name']
            res_msg=diff_item['result']['msg']
            res_code=diff_item['result']['code']
            result_msg = result_msg + f"""><font color=\"info\">测试门店：{shop_name}    应用名称：{app_name}     续期结果：{res_msg}【{res_code}】  </font>\n"""
        return result_msg
    except Exception as e:
        raise e