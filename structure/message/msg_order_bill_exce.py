# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/11 19:12
@Auth ： 逗逗的小老鼠
@File ：msg_order_bill_exce.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception, logger
from structure.monitor.moni_order_bill_exce import order_sale_o2o_bill_exce
from lib.deal_wx_msg import message_wechat_push
import datetime

"""
    生成正向单下账异常情况结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""


@exception(logger)
def order_sale_o2o_bill_exce_message_generated(start_time, end_time):
    try:
        wx_msg = f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**订单下账异常监测结果：**\n"""
        data = {"start_time": start_time, "end_time": end_time}
        order_bill_exce_result = order_sale_o2o_bill_exce(data)
        order_bill_exce_msg = order_sale_o2o_bill_exce_content(order_bill_exce_result)
        wx_msg = wx_msg + order_bill_exce_msg
        return wx_msg
    except Exception as e:
        raise e


"""
    推送正向单下账异常情况结果信息
    :return push_result：推送结果
"""


@exception(logger)
def order_sale_o2o_order_bill_exce_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(days=1)
        next_time = next_date.replace(hour=23, minute=59, second=59, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(days=1)
        yester_time = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info = order_sale_o2o_bill_exce_message_generated(str(yester_time), str(next_time))
        message_info = sale_message_info + "\n"
        print(message_info)
        msg_task_code = "order_sale_o2o_order_bill_exce_message_push"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e


"""
    生成正向单下账异常情况结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""


@exception(logger)
def order_sale_o2o_bill_exce_content(diff_list):
    try:
        result_msg = ""
        for diff_item in diff_list:
            bill_todo_count = diff_item['bill_todo_count']
            fail_count=diff_item['fail_count']
            no_stock=diff_item['no_stock']
            erp_null=diff_item['erp_null']
            amount_err=diff_item['amount_err']
            discount_minus=diff_item['discount_minus']
            fail_other=diff_item['fail_other']
            if bill_todo_count>0:
                result_msg=f"""**<font color=\"warning\">待下账：{bill_todo_count}条</font>**\n"""
            else:
                result_msg = f"""**<font color=\"info\">待下账：{bill_todo_count}条</font>**\n"""
            if fail_count>0:
                result_msg=result_msg+f"""**<font color=\"warning\">下账失败：{fail_count}条</font>**\n"""
            else:
                result_msg = result_msg + f"""**<font color=\"info\">下账失败：{fail_count}条</font>，异常原因如下：**\n"""
            if no_stock>0:
                result_msg = result_msg + f"""><font color=\"warning\">库存不足：{no_stock}条</font>\n"""
            else:
                result_msg = result_msg + f"""><font color=\"info\">库存不足：{no_stock}条</font>\n"""
            if erp_null>0:
                result_msg = result_msg + f"""><font color=\"warning\">商品编码为空：{erp_null}条</font>\n"""
            else:
                result_msg = result_msg + f"""><font color=\"info\">商品编码为空：{erp_null}条</font>\n"""
            if amount_err>0:
                result_msg = result_msg + f"""><font color=\"warning\">金额错误：{amount_err}条</font>\n"""
            else:
                result_msg = result_msg + f"""><font color=\"info\">金额错误：{amount_err}条</font>\n"""
            if discount_minus>0:
                result_msg = result_msg + f"""><font color=\"warning\">分摊金额为负：{discount_minus}条</font>\n"""
            else:
                result_msg = result_msg + f"""><font color=\"info\">分摊金额为负：{discount_minus}条</font>\n"""
            if fail_other>0:
                result_msg = result_msg + f"""><font color=\"warning\">其他：{fail_other}条</font>\n"""
            else:
                result_msg = result_msg + f"""><font color=\"info\">其他：{fail_other}条</font>\n"""
        return result_msg
    except Exception as e:
        raise e