# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/8 15:05
@Auth ： 逗逗的小老鼠
@File ：msg_order_erp.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from structure.monitor.moni_order_erp import order_sale_iserp_o2o_xy,order_return_iserp_o2o_xy
from lib.deal_wx_msg import message_wechat_push
from structure.message.msg_order import diff_order_content
import datetime

"""
    生成正向单下账状态比对结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def diff_sale_erp_message_generated(start_time,end_time):
    try:
        wx_msg=f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**销售单下账状态比对结果：**\n"""
        data={"start_time":start_time,"end_time":end_time}
        sale_erp_result = order_sale_iserp_o2o_xy(data)
        sale_erp_msg=diff_order_content(sale_erp_result)
        return_erp_result=order_return_iserp_o2o_xy(data)
        return_erp_msg=diff_order_content(return_erp_result)
        wx_msg=wx_msg+sale_erp_msg
        return wx_msg
    except Exception as e:
        raise e

"""
    生成退款单下账状态比对结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def diff_return_erp_message_generated(start_time,end_time):
    try:
        wx_msg=f"""**退款单下账状态比对结果：**\n"""
        data={"start_time":start_time,"end_time":end_time}
        return_erp_result=order_return_iserp_o2o_xy(data)
        return_erp_msg=diff_order_content(return_erp_result)
        wx_msg=wx_msg+return_erp_msg
        return wx_msg
    except Exception as e:
        raise e


"""
    推送正向单下账状态比对结果信息
    :return push_result：推送结果
"""
@exception(logger)
def order_erp_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(minutes=5)
        next_time = next_date.replace(second=0, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(days=1)
        yester_time=yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info=diff_sale_erp_message_generated(str(yester_time),str(next_time))
        return_message_info=diff_return_erp_message_generated(str(yester_time),str(next_time))
        message_info=sale_message_info+"\n"+return_message_info
        print(message_info)
        msg_task_code = "order_erp_compare"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e