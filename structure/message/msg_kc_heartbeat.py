# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/24 9:31
@Auth ： 逗逗的小老鼠
@File ：msg_kc_heartbeat.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception, logger
from structure.monitor.moni_kc_heartbeat import order_kc_heartbeat
from lib.deal_wx_msg import message_wechat_push
import datetime

"""
    科传心跳监测结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""


@exception(logger)
def order_kc_heartbeat_message_generated(start_time, end_time):
    try:
        wx_msg = f"""**科传系统心跳异常监控：**\n"""
        kc_heartbeat_result=order_kc_heartbeat()
        kc_heartbeat_len=len(kc_heartbeat_result)
        kc_count_msg=f"""**<font color=\"info\">科传心跳异常门店共计</font><font color=\"warning\">{kc_heartbeat_len}</font>家**\n"""
        kc_content_msg = order_kc_heartbeat_content(kc_heartbeat_result)
        wx_msg = wx_msg + kc_count_msg+kc_content_msg
        return wx_msg
    except Exception as e:
        raise e


"""
    科传心跳监测结果信息
    :return push_result：推送结果
"""


@exception(logger)
def order_kc_heartbeat_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(minutes=5)
        next_time = next_date.replace(hour=0, minute=0, second=0, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(hours=24)
        yester_time = yesterday.replace(minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info = order_kc_heartbeat_message_generated(str(yester_time), str(next_time))
        message_info = sale_message_info + "\n"
        print(message_info)
        msg_task_code = "order_kc_heartbeat_message_push"
        push_result = message_wechat_push(message_info,msg_task_code )
        return push_result
    except Exception as e:
        print(e)
        raise e


"""
    科传心跳监测结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""


@exception(logger)
def order_kc_heartbeat_content(diff_list):
    try:
        result_msg = ""
        max_length = 0
        for diff_item in diff_list:
            max_length = max_length + 1
            organization_code = diff_item['organization_code']
            organization_name = diff_item['organization_name']
            auto_bill_timestamp = diff_item['auto_bill_timestamp']
            difftime = diff_item['difftime']
            count_num=diff_item['count_num']
            diff_msg = f"""><font color=\"warning\">{organization_name}({organization_code})：   待下账订单数：【{count_num}】    最后一次心跳时间：{auto_bill_timestamp}  异常时长：【{difftime}】分钟</font>\n"""
            result_msg = result_msg + diff_msg
            if max_length>16:
                break
        return result_msg
    except Exception as e:
        raise e