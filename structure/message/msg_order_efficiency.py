# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/17 10:14
@Auth ： 逗逗的小老鼠
@File ：msg_order_efficiency.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from structure.monitor.moni_order_efficiency import order_sale_o2o_nomal_accept,order_sale_o2o_prescription_accept,order_sale_o2o_pick
from lib.deal_wx_msg import message_wechat_push
import datetime
"""
    生成正向单处理效率结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def diff_sale_efficiency_message_generated(start_time,end_time):
    try:
        wx_msg=f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**非处方单接单效率监测结果：**\n"""
        data={"start_time":start_time,"end_time":end_time}
        order_sale_o2o_nomal_accept_result = order_sale_o2o_nomal_accept(data)
        order_sale_o2o_nomal_accept_msg = diff_sale_efficiency_content(order_sale_o2o_nomal_accept_result)
        prescription_msg = f"""\n**处方单接单效率监测结果：**\n"""
        data = {"start_time": start_time, "end_time": end_time}
        order_sale_o2o_prescription_accept_result = order_sale_o2o_prescription_accept(data)
        order_sale_o2o_prescription_accept_msg = diff_sale_efficiency_content(order_sale_o2o_prescription_accept_result)
        pick_msg = f"""\n**订单拣货效率监测结果：**\n"""
        data = {"start_time": start_time, "end_time": end_time}
        order_sale_o2o_pick_result = order_sale_o2o_pick(data)
        order_sale_o2o_pick_msg = diff_sale_efficiency_content(order_sale_o2o_pick_result)
        wx_msg=wx_msg+order_sale_o2o_nomal_accept_msg+prescription_msg+order_sale_o2o_prescription_accept_msg+pick_msg+order_sale_o2o_pick_msg
        return wx_msg
    except Exception as e:
        raise e

"""
    推送正向单处理效率结果信息
    :return push_result：推送结果
"""
@exception(logger)
def order_sale_efficiency_message_push():
    "推送订单比对结果消息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(minutes=5)
        next_time = next_date.replace(second=0, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(hours=24)
        yester_time=yesterday.replace( minute=0, second=0, microsecond=0)
        print(yester_time)
        sale_message_info=diff_sale_efficiency_message_generated(str(yester_time),str(next_time))
        message_info=sale_message_info+"\n"
        print(message_info)
        msg_task_code="order_sale_efficiency_message_push"
        push_result=message_wechat_push(message_info,msg_task_code)
        return push_result
    except Exception as e:
        raise e


"""
    生成正向单处理效率对比结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def diff_sale_efficiency_content(diff_list):
    try:
        result_msg=""
        max_length=0
        for diff_item in diff_list:
            max_length=max_length+1
            entry_name=diff_item['entry_name']
            entry_value=diff_item['entry_value']
            proportion=diff_item['proportion']
            diff_msg=f"""><font color=\"warning\">{entry_name}： {entry_value}    【占比：{proportion}%】</font>\n"""
            result_msg=result_msg+diff_msg
            if max_length>19:
                break
        return result_msg
    except Exception as e:
        raise e