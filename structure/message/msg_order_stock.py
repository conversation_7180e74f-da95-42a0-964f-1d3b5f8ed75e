# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/14 17:07
@Auth ： 逗逗的小老鼠
@File ：msg_order_stock.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from structure.monitor.moni_order_stock import order_sale_o2o_stock_info,order_sale_nobill_o2o_stock_info
from lib.deal_wx_msg import message_wechat_push
import datetime


"""
    生成销售单O2O占用库存释放比对结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def diff_order_stock_message_generated(start_time,end_time):
    "生成销售单O2O占用库存释放比对结果信息"
    try:
        wx_msg=f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**销售单O2O占用库存释放比对结果：**\n"""
        data={"start_time":start_time,"end_time":end_time}
        sale_stock_result = order_sale_o2o_stock_info(data)
        sale_stock_count=len(sale_stock_result)
        sale_stock_info_msg=""
        if sale_stock_count>0:
            sale_stock_msg = f"""><font color=\"warning\">已下账未释放O2O占用库存数量：【{sale_stock_count}】</font>\n"""
            sale_stock_info_msg=diff_sale_stock_content(sale_stock_result)
        else:
            sale_stock_msg =f"""><font color=\"info\">已下账未释放O2O占用库存数量：【{sale_stock_count}】</font>\n"""

        wx_msg=wx_msg+sale_stock_msg+f"""**异常详情：**\n"""+sale_stock_info_msg
        return wx_msg
    except Exception as e:
        raise e




"""
    推送销售单O2O占用库存释放比对结果信息
    :return push_result：推送结果
"""
@exception(logger)
def order_sale_stock_message_push():
    "推送销售单O2O占用库存释放比对结果信息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(minutes=5)
        next_time = next_date.replace(second=0, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(hours=12)
        print(yesterday)
        yester_time=yesterday.replace(minute=0,second=0, microsecond=0)
        print(yester_time)
        sale_message_info=diff_order_stock_message_generated(str(yester_time),str(next_time))
        message_info=sale_message_info
        print(message_info)
        msg_task_code = "order_stock_compare"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e

"""
    生成销售单O2O占用库存释放比对结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def diff_sale_stock_content(diff_list):
    "生成销售单O2O占用库存释放比对结果内容"
    try:
        result_msg = ""
        max_length = 0
        for diff_item in diff_list:
            max_length = max_length + 1
            sys_order_no=diff_item['sys_order_no']
            erp_code=diff_item['erp_code']
            stock=diff_item['stock']
            exce_obj=diff_item['exce_obj']
            free_count=diff_item['free_count']
            diff_msg=f"""><font color=\"warning\">{exce_obj}    系统单号：{sys_order_no}   ERP码：{erp_code}   异常值：【{stock}】   执行次数：【{free_count}】</font>\n"""
            result_msg=result_msg+diff_msg
            if max_length>19:
                break
        return result_msg
    except Exception as e:
        raise e






"""
    推送未下账的销售单O2O占用库存释放比对结果信息
    :return push_result：推送结果
"""
@exception(logger)
def order_sale_nobill_stock_message_push():
    "推送未下账的销售单O2O占用库存释放比对结果信息"
    try:
        current_date = datetime.datetime.now()
        next_date = current_date - datetime.timedelta(minutes=5)
        next_time = next_date.replace(second=0, microsecond=0)
        print(next_time)
        yesterday = current_date - datetime.timedelta(hours=12)
        print(yesterday)
        yester_time=yesterday.replace(minute=0,second=0, microsecond=0)
        print(yester_time)
        sale_message_info=diff_order_nobill_stock_message_generated(str(yester_time),str(next_time))
        message_info=sale_message_info
        print(message_info)
        msg_task_code = "order_nobill_stock_compare"
        push_result = message_wechat_push(message_info, msg_task_code)
        return push_result
    except Exception as e:
        raise e



"""
    生成未下账的销售单O2O占用库存释放比对结果信息
    :param start_time:对比开始时间
    :param end_time:对比结束时间
    :return wx_msg：微信消息内容
"""
@exception(logger)
def diff_order_nobill_stock_message_generated(start_time,end_time):
    "生成未下账的销售单O2O占用库存释放比对结果信息"
    try:
        wx_msg=f"""**比对周期：**<font color=\"info\">{start_time}~{end_time}</font>\n**未下账O2O占用库存比对结果：**\n"""
        data={"start_time":start_time,"end_time":end_time}
        sale_stock_result = order_sale_nobill_o2o_stock_info(data)
        sale_stock_count=len(sale_stock_result)
        sale_stock_info_msg=""
        if sale_stock_count>0:
            sale_stock_msg = f"""><font color=\"warning\">未下账O2O占用库存异常数量：【{sale_stock_count}】</font>\n"""
            sale_stock_info_msg=diff_sale_nobill_stock_content(sale_stock_result)
        else:
            sale_stock_msg =f"""><font color=\"info\">未下账O2O占用库存异常数量：【{sale_stock_count}】</font>\n"""

        wx_msg=wx_msg+sale_stock_msg+f"""**异常详情：**\n"""+sale_stock_info_msg
        return wx_msg
    except Exception as e:
        raise e


"""
    生成未下账的销售单O2O占用库存比对结果内容
    :param diff_list:对比结果列表
    :return result：消息内容
"""
@exception(logger)
def diff_sale_nobill_stock_content(diff_list):
    "生成未下账的销售单O2O占用库存比对结果内容"
    try:
        result_msg = ""
        max_length = 0
        for diff_item in diff_list:
            max_length = max_length + 1
            sys_order_no=diff_item['sys_order_no']
            detail_erp_code=diff_item['detail_erp_code']
            detail_erp_state=diff_item['detail_erp_state']
            detail_status=diff_item['detail_status']
            detail_num=diff_item['detail_num']
            stock_num=diff_item['stock_num']
            exce_obj=diff_item['exce_obj']
            diff_msg=f"""><font color=\"warning\">{exce_obj}    系统订单号：{sys_order_no}   ERP码：{detail_erp_code}   预期占用：【{detail_num}】 实际占用：【{stock_num}】</font>\n"""
            result_msg=result_msg+diff_msg
            if max_length>19:
                break
        return result_msg
    except Exception as e:
        raise e