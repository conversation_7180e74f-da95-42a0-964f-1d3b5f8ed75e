# -*- coding: utf-8 -*-
"""
@Time ： 2024/10/8 10:06
@Auth ： 逗逗的小老鼠
@File ：order_pay_calculate.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：对订单的支付信息进行二次计算处理
"""

from lib.get_log import logger, exception

"""
订单支付信息计算
:param order_pay_info_list: 订单的支付信息list
:return order_pay_calc:将计算完成的数据返回
"""
@exception(logger)
def order_pay_info_calculate(order_pay_info_list):
    "计算订单的支付信息"
    try:
        # 需要计算的字段list
        """
            Field	Type	Null	Key		Default		Extra	Comment
            buyer_actual_amount	decimal(16,2)	YES		0.00		客户实付
            merchant_actual_amount	decimal(16,2)	YES		0.00		商家实收
            total_amount	decimal(16,2)	YES		0.00		商品总金额
            total_discount	decimal(16,2)	YES		0.00		订单总优惠
            merchant_total_discount_sum	decimal(16,2)	YES		0.00		商家订单总优惠(包含所有优惠)
            merchant_total_discount_sum_not_delivery_fee	decimal(16,2)	YES		0.00		商家商品总优惠
            merchant_discount_sum	decimal(16,2)	YES		0.00		商家整单优惠
            discount_fee_dtl	decimal(16,2)	YES		0.00		商品明细折扣汇总
            platform_discount	decimal(16,2)	YES		0.00		平台优惠
            post_fee_discount	decimal(16,2)	YES		0.00		运费优惠
            merchant_delivery_fee	decimal(16,2)	YES		0.00		商家配送费
            merchant_delivery_fee_discount	decimal(16,2)	YES		0.00		商家配送费优惠金额
            merchant_pack_fee	decimal(16,2)	YES		0.00		商家打包费
            platform_delivery_fee	decimal(16,2)	YES		0.00		平台配送费
            platform_delivery_fee_discount	decimal(16,2)	YES		0.00		平台配送费优惠金额
            platform_pack_fee	decimal(16,2)	YES		0.00		平台打包费
            buyer_cod_service_fee	decimal(16,2)	YES		0.00		买家到付服务费
            seller_cod_service_fee	decimal(16,2)	YES		0.00		卖家到付服务费
            brokerage_amount	decimal(16,2)	YES		0.00		交易佣金
            buyer_cod_amount	decimal(16,2)	YES		0.00		（买家）到付金额
            platform_fee_collection	decimal(16,2)	YES		0.00		代收平台费
            manual_fix_amount	decimal(16,2)	YES		0.00		手工调整金额
            detail_discount_collect	decimal(16,2)	YES		0.00		商品明细优惠汇总
            different_amount	decimal(16,2)	YES		0.00		差异金额
            delivery_fee	decimal(16,2)	YES				配送费(原始值)
            pack_fee	decimal(16,2)	YES		0.00		包装费
            remain_brokerage_amount	decimal(16,2)	YES		0.00		剩余交易佣金(实时)
            health_value	decimal(16,2)	YES				健康贝换算金额
            warehouse_agency_fee	decimal(16,2)	YES		0.00		仓库代发费用
            medicare_amount	decimal(16,2)	YES		0.00		
            total_discount_sum_not_delivery_fee	decimal(16,2)	YES				商品优惠总额
            accounts_receivable	decimal(16,2)	YES				应收金额
            platform_discount_sum_not_delivery_fee	decimal(16,2)	YES				平台商品优惠
        """
        pay_calc_key_list=[
            "buyer_actual_amount",
            "merchant_actual_amount",
            "total_amount",
            "total_discount",
            "merchant_total_discount_sum",
            "merchant_total_discount_sum_not_delivery_fee",
            "merchant_discount_sum",
            "discount_fee_dtl",
            "platform_discount",
            "post_fee_discount",
            "merchant_delivery_fee",
            "merchant_delivery_fee_discount",
            "merchant_pack_fee",
            "platform_delivery_fee",
            "platform_delivery_fee_discount",
            "platform_pack_fee",
            "buyer_cod_service_fee",
            "seller_cod_service_fee",
            "brokerage_amount",
            "buyer_cod_amount",
            "platform_fee_collection",
            "manual_fix_amount",
            "detail_discount_collect",
            "different_amount",
            "delivery_fee",
            "pack_fee",
            "remain_brokerage_amount",
            "health_value",
            "warehouse_agency_fee",
            "medicare_amount",
            "total_discount_sum_not_delivery_fee",
            "accounts_receivable",
            "platform_discount_sum_not_delivery_fee"

        ]
        order_pay_calc=order_amount_calculate(order_pay_info_list,pay_calc_key_list)
        return order_pay_calc
    except Exception as e:
        raise e


"""
订单商品详情信息计算
"""
@exception(logger)
def order_detail_info_calculate(order_detail_info_list):
    "订单商品详情信息计算"
    try:
        order_detail_list=[]
        # 需要将商品详情列表中的已换货商品过滤掉
        for order_detail_info_item in order_detail_info_list:
            status=order_detail_info_item.get("status")
            goods_type=order_detail_info_item.get("goods_type")
            is_joint=order_detail_info_item.get("is_joint")
            # O2O订单商品已换货标识
            if status == 10:
                break
            # B2C订单商品已换货标识
            if goods_type==4:
                break
            # B2C组合商品原商品
            if is_joint==2:
                break
            order_detail_list.append(order_detail_info_item)
        # 订单商品详情需要计算的字段
        detail_calc_key_list=["total_amount","discount_amount","actual_amount","discount_share","actual_net_amount","platform_discount_fee","merchant_discount_fee"]
        detail_calc_reslt=order_amount_calculate(order_detail_list,detail_calc_key_list)
        return detail_calc_reslt
    except Exception as e:
        raise e



"""
订单金额信息计算
:param order_amount_info_list: 订单原始报文
:param calc_key_list：需要计算的字段列表
:return order_amount_calc:计算完成后的字典
"""
@exception(logger)
def order_amount_calculate(order_amount_info_list,calc_key_list):
    "订单金额计算"
    try:
        order_amount_calc={}
        for order_pay_info in order_amount_info_list:
            # 获取订单支付信息中的字段和对应值
            for key,value in order_pay_info.items():
                # 如果订单支付信息中的字段存在于需要计算的字段列表中，则进行计算
                if value == None or value == '':
                    value=0
                if key in calc_key_list:
                    # 若当前字段已存在于计算结果中，则进行累加计算，否则直接赋值为0
                    calc_value=order_amount_calc.get(key,0)+value
                else:
                    calc_value=value
                order_amount_calc[key]=calc_value
        return order_amount_calc
    except Exception as e:
        raise e

if __name__ == '__main__':
    pay_list=[
                    {
                        "pay_id": 3032977,
                        "order_no": 1813034781630218501,
                        "pay_status": "1",
                        "pay_channel": None,
                        "buyer_actual_amount": 24.0,
                        "merchant_actual_amount": 24.0,
                        "total_amount": 25.0,
                        "total_discount": 1.0,
                        "merchant_total_discount_sum": 0.0,
                        "merchant_total_discount_sum_not_delivery_fee": 1.0,
                        "merchant_discount_sum": 1.0,
                        "discount_fee_dtl": 0.0,
                        "platform_discount": 0.0,
                        "post_fee_discount": 0.0,
                        "merchant_delivery_fee": 0.0,
                        "merchant_delivery_fee_discount": 0.0,
                        "merchant_pack_fee": 0.0,
                        "platform_delivery_fee": 0.0,
                        "platform_delivery_fee_discount": 0.0,
                        "platform_pack_fee": 0.0,
                        "buyer_cod_service_fee": 0.0,
                        "seller_cod_service_fee": 0.0,
                        "brokerage_amount": 0.0,
                        "buyer_cod_amount": 0.0,
                        "platform_fee_collection": 0.0,
                        "manual_fix_amount": 0.0,
                        "detail_discount_collect": 0.0,
                        "different_amount": 0.0,
                        "create_time": "2024-10-16T10:11:59",
                        "modify_time": "2024-10-16T10:11:59",
                        "pay_code": "809080",
                        "delivery_fee": 0.0,
                        "pack_fee": 0.0,
                        "oms_order_no": 1813034795434721797,
                        "remain_brokerage_amount": 0.0,
                        "health_num": 0,
                        "health_value": None,
                        "warehouse_agency_fee": 0.0,
                        "apportion_type": None,
                        "medicare_amount": 0.0,
                        "medicare_order_id": None,
                        "pay_sale_info": None,
                        "total_discount_sum_not_delivery_fee": None,
                        "accounts_receivable": None,
                        "platform_discount_sum_not_delivery_fee": None
                    }
                ]
    clac_result=order_pay_info_calculate(pay_list)
    print(clac_result)