# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/17 18:17
@Auth ： 逗逗的小老鼠
@File ：weshop_order_query_preprocessing.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json
from lib.get_log import logger, exception
from datetime import datetime,timedelta


"""
设置订单数据查询条件

本函数用于根据提供的参数构建订单数据的查询条件。支持通过订单号、第三方订单号、
开始时间和结束时间的组合来筛选订单。如果这些参数没有提供或为空，会默认使用当前日期构建查询条件。

参数:
**kwargs: 可变关键字参数，可能包含以下关键词:
    - order_no (str or list): 平台订单号或列表
    - start_time (str): 查询的开始时间，格式为"YYYY-MM-DD HH:MM:SS"
    - end_time (str): 查询的结束时间，格式为"YYYY-MM-DD HH:MM:SS"

返回:
dict: 包含两个键：
    - sql_condition (str): SQL查询条件字符串
    - query_value (tuple): 查询值的元组

异常:
如果处理过程中发生任何异常，将抛出以便于调用者进行进一步处理。
"""
@exception(logger)
def weshop_order_query_condition(**kwargs):
    "设置订单数据查询条件"
    try:
        # 初始化SQL条件字符串和查询值元组
        sql_condition = ""
        query_value = ()

        # 获取当前日期和时间
        now = datetime.now()

        # 从kwargs中获取参数值
        order_no = kwargs.get("order_no", "")
        start_time = kwargs.get("start_time", "")
        end_time = kwargs.get("end_time", "")
        query_time_long = kwargs.get("query_time_long", 0)
        # query_time_long = int(query_time_long)
        try:
            query_time_long = int(query_time_long)
        except:
            query_time_long = 5

        # 根据参数构建查询条件
        if order_no == "" or order_no is None:
            if query_time_long == 0 or query_time_long is None or query_time_long == "" or query_time_long =='0':
                if start_time == "" or start_time is None:
                    if end_time == "" or end_time is None:
                        # 如果没有指定结束时间，默认为今天结束
                        end_time = now.replace(hour=23, minute=59, second=59, microsecond=0)
                        end_time = end_time.strftime("%Y-%m-%d %H:%M:%S")
                        # 开始时间为今天开始
                        start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
                        start_time = start_time.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        # 如果指定了结束时间，则开始时间为结束时间的前一天
                        fomatted_end_time = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                        start_time = fomatted_end_time - timedelta(days=1)
                        start_time = start_time.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    if end_time == "" or end_time is None:
                        # 如果指定了开始时间但没有指定结束时间，则结束时间为开始时间的后一天
                        fomatted_start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                        end_time = fomatted_start_time + timedelta(days=1)
                        end_time = end_time.strftime("%Y-%m-%d %H:%M:%S")
                    else:
                        # 如果开始时间和结束时间都指定了，则直接使用这些值
                        end_time = now.replace(second=0, microsecond=0)
                        end_time = end_time.strftime("%Y-%m-%d %H:%M:%S")
                # 构建基于时间的查询条件
                sql_condition = "AND info.order_time >= %s AND info.order_time <= %s"
                query_value = (start_time, end_time)
            else:
                five_minutes_ago = now - timedelta(minutes=5)
                time_long_ago=five_minutes_ago-timedelta(minutes=query_time_long)
                end_time = five_minutes_ago.strftime("%Y-%m-%d %H:%M:%S")
                start_time = time_long_ago.strftime("%Y-%m-%d %H:%M:%S")
                sql_condition = "AND info.order_time >= %s AND info.order_time <= %s"
                query_value = (start_time, end_time)
        else:
            # 如果提供了订单号，构建基于订单号的查询条件
            if isinstance(order_no, list):
                order_no_list = [f'{item}' for item in order_no]
                order_no_string = ','.join(order_no_list)
            else:
                order_no_string = order_no
            sql_condition = "AND info.id IN (%s)"
            query_value = (order_no_string,)
        # 构建并返回查询结果
        result = {"sql_condition": sql_condition, "query_value": query_value}
        return result
    except Exception as e:
        # 如果发生异常，抛出以便于调用者处理
        raise e



if __name__ == '__main__':
    query_time_long='aaa'
    third_order_no=['a',"bbb",3,4]
    print(weshop_order_query_condition(third_order_no=third_order_no))