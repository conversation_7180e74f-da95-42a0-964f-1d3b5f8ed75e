# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/17 18:05
@Auth ： 逗逗的小老鼠
@File ：weshop_order_info_query.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import decimal
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect
from structure.data_monitoring_system.weshop_info.weshop_order_query_preprocessing import weshop_order_query_condition
import json
from datetime import datetime
from lib.decorator_func_timeout import timeout_decorator

@exception(logger)
def full_weshop_information_assembly(**kwargs):
    "组装微商城订单信息，包括订单基本信息、商品信息等"
    try:
        query_start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"开始查询微商城订单基础数据，时间：{query_start_time}")
        weshop_order_info = weshop_order_info_data_query(**kwargs)
        query_main_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"微商城订单基础数据查询完成，时间：{query_main_time}")
        weshop_order_detail = weshop_order_detail_data_query(**kwargs)
        query_detail_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"微商城订单商品数据查询完成，时间：{query_detail_time}")
        full_weshop_order_information_list=[]
        for weshop_item in weshop_order_info:
            order_no=weshop_item.get('order_no',"")
            full_weshop_order_information={}
            full_weshop_order_information['order_no']=order_no
            # 微商城订单基础信息组装
            weshop_information=[]
            weshop_information.append(weshop_item)
            full_weshop_order_information['order_info']=weshop_information
            # 微商城商品信息组装
            weshop_detail=[]
            for weshop_detail_item in weshop_order_detail:
                if weshop_detail_item['order_no']==order_no:
                    weshop_detail.append(weshop_detail_item)
            full_weshop_order_information['order_detail'] = weshop_detail
            full_weshop_order_information_list.append(full_weshop_order_information)
        return full_weshop_order_information_list
    except Exception as e:
        logger.error(f"组装微商城订单信息失败，失败原因：{e}")
        raise e

@timeout_decorator(10,scheduler_name="weshop_order_data_verify")
@exception(logger)
def weshop_order_info_data_query(**kwargs):
    "微商城订单主数据查询"
    try:
        query_condition = weshop_order_query_condition(**kwargs)
        sql_condition = query_condition['sql_condition']
        # 查询参数
        query_value = query_condition['query_value']
        data_sql=f"""
            SELECT
                info.id AS "order_no",
                info.mer_code AS "mer_code",
                info.store_id AS "store_id",
                info.serial_number AS "serial_number",
                info.member_id AS "member_id",
                info.member_card AS "member_card",
                info.order_type AS "order_type",
                info.source_channel AS "source_channel",
                info.source_media AS "source_media",
                info.from_channel AS "from_channel",
                info.order_status AS "order_status",
                info.order_time AS "order_time",
                info.pay_mode AS "pay_mode",
                info.pay_type AS "pay_type",
                info.pay_status AS "pay_status",
                info.pay_time AS "pay_time",
                info.total_goods_number AS "total_goods_number",
                info.total_order_amount AS "total_order_amount",
                info.integral_deduction AS "integral_deduction",
                info.coupon_deduction AS "coupon_deduction",
                info.activity_discount_amont AS "activity_discount_amont",
                info.other_discount_amont AS "other_discount_amont",
                info.original_freight_amount AS "original_freight_amount",
                info.actual_freight_amount AS "actual_freight_amount",
                info.freight_deduction_of_hb AS "freight_deduction_of_hb",
                info.total_actual_order_amount AS "total_actual_order_amount",
                info.total_actual_hb AS "total_actual_hb",
                info.actually_paid AS "actually_paid",
                info.amount_tobepaid AS "amount_tobepaid",
                info.refund_amount AS "refund_amount",
                info.is_invoice AS "is_invoice",
                info.order_message AS "order_message",
                info.order_remark AS "order_remark",
                info.is_locked AS "is_locked",
                info.locked_name AS "locked_name",
                info.locked_time AS "locked_time",
                info.cancel_name AS "cancel_name",
                info.cancel_time AS "cancel_time",
                info.cancel_reason AS "cancel_reason",
                info.prescription_sheet_mark AS "prescription_sheet_mark",
                info.is_new AS "is_new",
                info.is_new_customer AS "is_new_customer",
                info.is_push_erp AS "is_push_erp",
                info.push_erp_time AS "push_erp_time",
                info.is_page_order AS "is_page_order",
                info.isvalid AS "isvalid",
                info.create_name AS "create_name",
                info.create_time AS "create_time",
                info.modify_name AS "modify_name",
                info.modify_time AS "modify_time",
                info.system_check AS "system_check",
                info.is_border_order AS "is_border_order",
                info.delivery_type AS "delivery_type",
                info.delivery_code AS "delivery_code",
                info.delivery_status AS "delivery_status",
                info.prescription_status AS "prescription_status",
                info.total_goods_weight AS "total_goods_weight",
                info.is_push_payment_notice AS "is_push_payment_notice",
                info.send_time AS "send_time",
                info.confirm_time AS "confirm_time",
                info.st_code AS "st_code",
                info.is_agroup AS "is_agroup",
                info.group_code AS "group_code",
                info.group_operate_type AS "group_operate_type",
                info.medical_user_id AS "medical_user_id",
                info.special_medical_user_id AS "special_medical_user_id",
                info.tcc_commit AS "tcc_commit",
                info.pay_balance_amount AS "pay_balance_amount",
                info.refund_balance_amount AS "refund_balance_amount",
                info.member_phone AS "member_phone",
                info.reg_medium AS "reg_medium",
                info.reg_source AS "reg_source",
                info.distance_from_store AS "distance_from_store",
                info.send_store_code AS "send_store_code",
                info.send_store_id AS "send_store_id",
                info.epidemic_registration AS "epidemic_registration",
                info.pay_medical_amount AS "pay_medical_amount",
                info.spread_store_code AS "spread_store_code",
                info.is_b2c_order AS "is_b2c_order",
                info.sp_code AS "sp_code",
                info.share_commission AS "share_commission",
                info.distributor_order AS "distributor_order",
                info.is_all_return AS "is_all_return",
                info.pay_after_audit AS "pay_after_audit",
                info.presale_order_type AS "presale_order_type",
                info.total_deposit_pay_amount AS "total_deposit_pay_amount",
                info.total_final_payment_amount AS "total_final_payment_amount",
                info.wx_no AS "wx_no",
                info.share_name AS "share_name",
                info.order_close_time AS "order_close_time",
                info.store_employee_code AS "store_employee_code",
                info.shared_warehouse_org_id AS "shared_warehouse_org_id",
                info.shared_warehouse_org_code AS "shared_warehouse_org_code",
                info.data_version AS "data_version",
                info.order_extend_info AS "order_extend_info",
                info.pay_sale_info AS "pay_sale_info",
                info.emp_code AS "emp_code",
                info.is_virtual_goods_order AS "is_virtual_goods_order",
                info.seller_remark AS "seller_remark",
                info.invitation_code AS "invitation_code",
                info.seller_remark_time AS "seller_remark_time",
                info.main_order_id AS "main_order_id",
                info.is_split AS "is_split" 
            FROM
                order_info info 
            WHERE
                1=1 
                AND info.mer_code = '500001'   
                {sql_condition}
        """
        # 调用数据库连接函数执行查询
        query_result = db_mysql_connect("middle_order", data_sql, sql_val=query_value, **kwargs)
        info_data = query_result['data']
        return info_data
    except Exception as e:
        raise e


@timeout_decorator(10,scheduler_name="weshop_order_data_verify")
@exception(logger)
def weshop_order_detail_data_query(**kwargs):
    "查询订单详情数据，order_detail表"
    try:
        query_condition = weshop_order_query_condition(**kwargs)
        sql_condition = query_condition['sql_condition']
        # 查询参数
        query_value = query_condition['query_value']
        detail_sql=f"""
            SELECT
                detail.id AS "detail_id",
                detail.mer_code AS "mer_code",
                detail.order_id AS "order_no",
                detail.commodity_id AS "commodity_id",
                detail.commodity_code AS "commodity_code",
                detail.commodity_name AS "commodity_name",
                detail.commodity_type AS "commodity_type",
                detail.origin AS "origin",
                detail.drug_type AS "drug_type",
                detail.commodity_number AS "commodity_number",
                detail.cost_price AS "cost_price",
                detail.mprice AS "mprice",
                detail.commodity_price AS "commodity_price",
                detail.exchange_hb AS "exchange_hb",
                detail.total_amount AS "total_amount",
                detail.total_hb AS "total_hb",
                detail.coupon_amount AS "coupon_amount",
                detail.total_actual_amount AS "total_actual_amount",
                detail.STATUS AS "status",
                detail.origin_type AS "origin_type",
                detail.origin_st_code AS "origin_st_code",
                detail.expect_delivery_time AS "expect_delivery_time",
                detail.is_direct_delivery AS "is_direct_delivery",
                detail.promotion_id AS "promotion_id",
                detail.activity_discount_json AS "activity_discount_json",
                detail.package_id AS "package_id",
                detail.isvalid AS "isvalid",
                detail.create_name AS "create_name",
                detail.create_time AS "create_time",
                detail.modify_name AS "modify_name",
                detail.modify_time AS "modify_time",
                detail.return_request_id AS "return_request_id",
                detail.m_pic AS "m_pic",
                detail.is_combined_commodity AS "is_combined_commodity",
                detail.is_main_commodity AS "is_main_commodity",
                detail.main_commodity_id AS "main_commodity_id",
                detail.is_promotion AS "is_promotion",
                detail.sku_value AS "sku_value",
                detail.spec_id AS "spec_id",
                detail.activity_discount_amont AS "activity_discount_amont",
                detail.pmt_product_type AS "pmt_product_type",
                detail.source_channel_type AS "source_channel_type",
                detail.source_channel_id AS "source_channel_id",
                detail.is_medical AS "is_medical",
                detail.activity_rule_json AS "activity_rule_json",
                detail.medical_insurance_code AS "medical_insurance_code",
                detail.delivery_channel_code AS "delivery_channel_code",
                detail.price_id AS "price_id",
                detail.cus_distribute_amount AS "cus_distribute_amount",
                detail.epidemic_registration AS "epidemic_registration",
                detail.deposit_pay_amount AS "deposit_pay_amount",
                detail.final_payment_amount AS "final_payment_amount",
                detail.promotion_ratio_id AS "promotion_ratio_id",
                detail.promotion_ratio AS "promotion_ratio",
                detail.verification_quantity AS "verification_quantity",
                detail.is_virtual AS "is_virtual",
                detail.expiring AS "expiring",
                detail.vip_discount AS "vip_discount",
                detail.vip_discount_plus AS "vip_discount_plus",
                detail.sp_code AS "sp_code",
                detail.cash_coupon_amount AS "cash_coupon_amount" 
            FROM
                order_detail detail
                LEFT JOIN order_info info ON info.id = detail.order_id 
            WHERE
                1=1 
                AND info.mer_code = '500001'   
                {sql_condition}
        """
        # 调用数据库连接函数执行查询
        query_result = db_mysql_connect("middle_order", detail_sql, sql_val=query_value, **kwargs)
        info_data = query_result['data']
        return info_data
    except Exception as e:
        raise e



if __name__ == "__main__":
    order_id="1818745928518249479"
    data=weshop_order_detail_data_query(order_no=order_id,)
    print(data)