# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/11 14:58
@Auth ： 逗逗的小老鼠
@File ：deal_verify_result.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：处理验证结果数据
"""
import copy
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect
from lib.deal_sql import dict_to_sql_insert,dict_to_sql_update
from datetime import datetime


"""
    写入校验数据
"""
@exception(logger)
def monitor_verify_info_insert(verify_result,**kwargs):
    "将校验结果数据插入到数据库"
    try:
        sql_detail=[]
        sql_info=[]
        current_timestamp: int = int(datetime.now().timestamp() * 1000)
        monitor_start_time=kwargs.get('monitor_start_time',datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        monitor_end_time=kwargs.get('monitor_end_time',datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        environment_flag=kwargs.get('environment_flag','')
        # 遍历结果数据
        for result_item in verify_result:
            order_no=result_item.get('order_no',"")
            oms_order_no=result_item.get('oms_order_no',"")
            third_order_no=result_item.get('third_order_no',"")
            service_mode=result_item.get('service_mode',"")
            third_platform_code=result_item.get('third_platform_code',"")
            exception_list=result_item.get('exception_list',[])
            exception_info_sum=0
            exception_error_sum = 0
            exception_waring_sum = 0
            exception_other_sum = 0
            exception_sum=len(exception_list)
            # 遍历各个订单的异常数据
            for exception_item in exception_list:
                exception_level=exception_item.get('exception_level')
                # 错误异常
                if exception_level=='error':
                    exception_error_sum+=1
                # 警告异常
                elif exception_level=='waring':
                    exception_waring_sum+=1
                # 提示类异常
                elif exception_level=='info':
                    exception_info_sum+=1
                # 类型无法识别
                else:
                    exception_other_sum+=1
                exception_item['order_no']=order_no
                exception_item['oms_order_no']=oms_order_no
                exception_item['third_order_no']=third_order_no
                exception_item['service_mode']=service_mode
                exception_item['third_platform_code']=third_platform_code
                exception_item['association_id']=current_timestamp
                exception_item['environment_flag']=environment_flag
                sql_detail.append(exception_item)
            info_item = copy.deepcopy(result_item)
            info_item.pop('exception_list')
            info_item['exception_info_sum']=exception_info_sum
            info_item['exception_error_sum']=exception_error_sum
            info_item['exception_waring_sum']=exception_waring_sum
            info_item['exception_other_sum']=exception_other_sum
            info_item['exception_sum']=exception_sum
            info_item['association_id']=current_timestamp
            info_item['monitor_start_time']=monitor_start_time
            info_item['monitor_end_time']=monitor_end_time
            info_item['environment_flag']=environment_flag
            sql_info.append(info_item)
        sql_info_list=dict_to_sql_insert('business_monitor_verify_info',sql_info)
        sql_detail_list=dict_to_sql_insert('business_monitor_verify_detail',sql_detail)
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        info_insert_count=0
        detail_insert_count=0
        for sql_info_item in sql_info_list:
            info_result=db_mysql_connect("insight", sql_info_item,environment_flag="common", **kwargs)
            info_insert_num=info_result.get('data',0)
            info_insert_count+=info_insert_num
        for sql_detail_item in sql_detail_list:
            detail_result=db_mysql_connect("insight", sql_detail_item,environment_flag="common", **kwargs)
            detail_insert_num=detail_result.get('data',0)
            detail_insert_count+=detail_insert_num
        result={"info_result":info_insert_count, "detail_result":detail_insert_count}
        return result
    except Exception as e:
        raise e



"""
    查询校验结果数据
"""
def monitor_verify_info_query(**kwargs):
    "查询校验结果数据"
    try:
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        query_value = []
        query_where_sql = ""
        col_query_sql = "SELECT * FROM business_monitor_verify_info WHERE id=0"
        col_query_result=db_mysql_connect("insight", col_query_sql,environment_flag="common", **kwargs)
        col_query_cloumns=col_query_result.get('column',[])
        for col_item in col_query_cloumns:
            if col_item in kwargs:
                value=kwargs.get(col_item)
                query_where_sql+=f" AND {col_item}=%s"
                query_value.append(value)
        data_query_sql=f"SELECT * FROM business_monitor_verify_info WHERE 1=1 {query_where_sql}"
        query_val=tuple(query_value)
        data_query_result=db_mysql_connect("insight", data_query_sql,sql_val=query_val,environment_flag="common", **kwargs)
        data_query_data=data_query_result.get('data',[])
        return data_query_data
    except Exception as e:
        raise e

"""
    查询校验详情结果数据
"""
def monitor_verify_detail_query(**kwargs):
    "查询校验结果数据"
    try:
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        query_value = []
        query_where_sql = ""
        col_query_sql = "SELECT * FROM business_monitor_verify_detail WHERE id=0"
        col_query_result=db_mysql_connect("insight", col_query_sql,environment_flag="common", **kwargs)
        col_query_cloumns=col_query_result.get('column',[])
        # 需要移除id字段
        if 'id' in col_query_cloumns:
            col_query_cloumns.remove('id')
        for col_item in col_query_cloumns:
            if col_item in kwargs:
                value=kwargs.get(col_item)
                query_where_sql+=f" AND {col_item}=%s"
                query_value.append(value)
        data_query_sql=f"SELECT * FROM business_monitor_verify_detail WHERE 1=1 {query_where_sql}"
        query_val=tuple(query_value)
        data_query_result=db_mysql_connect("insight", data_query_sql,sql_val=query_val,environment_flag="common", **kwargs)
        data_query_data=data_query_result.get('data',[])
        return data_query_data
    except Exception as e:
        raise e


def monitor_verify_repair(**kwargs):
    "修复异常数据"
    try:
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        filter_dict={}
        # 支持批量处理的字段标识
        repair_list=["third_order_no","oms_order_no","order_no","exception_code","environment_flag","verification_code","service_mode","association_id"]
        for repair_item in repair_list:
            if repair_item in kwargs:
                filter_dict[repair_item]=kwargs.get(repair_item)
        deal_status=kwargs.get("deal_status","")
        deal_desc=kwargs.get("deal_desc","")
        is_delete=kwargs.get("is_delete","")
        if is_delete in [0,1,"0","1"]:
            update_dict={"is_delete":is_delete}
        else:
            if deal_status in [0,1,2,3,"0","1","2","3"]:
                update_dict={"deal_status":deal_status,"deal_desc":deal_desc}
            else:
                msg="is_delete 接收值【0,1】，deal_status接收值【1,2,3】，请核对后重新请求"
                result = {"success": "false", "msg": msg,
                          "data": {}}
                return result
        # 如果存在“exception_code”和“verification_code”则证明处理事务为详情处理
        if "exception_code" in filter_dict or "verification_code" in filter_dict:
            detail_update_sql=dict_to_sql_update("business_monitor_verify_detail",update_dict,filter_dict)
            detail_result=db_mysql_connect("insight",detail_update_sql,environment_flag="common", **kwargs)
            todo_detail_num=detail_result.get('data',0)
            info_filter_dict=copy.deepcopy(filter_dict)
            # 需要去除详情表字段，以进行待处理的同条件信息查询
            if "exception_code" in info_filter_dict:
                del info_filter_dict['exception_code']
            if "verification_code" in info_filter_dict:
                del info_filter_dict['verification_code']
            # 进行待处理条件查询
            info_filter_dict['deal_status']='0'
            # 查询详情表中同条件的待处理的数量
            todo_detail_query=monitor_verify_detail_query(**info_filter_dict)
            todo_detail_count=len(todo_detail_query)
            todo_info_num=0
            # 如果详情表中不存在待处理的数据，则进行信息表同条件查询
            if todo_detail_count==0:
                todo_info_query=monitor_verify_info_query(**info_filter_dict)
                todo_info_num=len(todo_info_query)
                # 如果信息表中待处理的数量大于0，则需要将对应待处理的数据处理状态置为已处理
                if todo_info_num>0:
                    info_update_sql=dict_to_sql_update("business_monitor_verify_info",update_dict,info_filter_dict)
                    info_result=db_mysql_connect("insight",info_update_sql,environment_flag="common", **kwargs)
                    todo_info_num=info_result.get('data',0)
            result={"success":"true","msg":"处理成功","data":{"detail_num":todo_detail_num,"info_num":todo_info_num}}
        else:
            info_update_sql=dict_to_sql_update("business_monitor_verify_info",update_dict,filter_dict)
            info_result=db_mysql_connect("insight",info_update_sql,environment_flag="common", **kwargs)
            todo_info_num=info_result.get('data',0)
            detail_filter_dict=copy.deepcopy(filter_dict)
            detail_filter_dict['deal_status']='0'
            # 如果详情表中待处理的数量大于0，则需要将同条件的详情数据全部处理状态置为已处理
            todo_detail_query=monitor_verify_detail_query(**detail_filter_dict)
            todo_detail_num=len(todo_detail_query)
            if todo_detail_num>0:
                detail_update_sql=dict_to_sql_update("business_monitor_verify_detail",update_dict,detail_filter_dict)
                detail_result=db_mysql_connect("insight",detail_update_sql,environment_flag="common", **kwargs)
                todo_detail_num=detail_result.get('data',0)
            result = {"success": "true", "msg": "处理成功",
                      "data": {"detail_num": todo_detail_num, "info_num": todo_info_num}}
        return result
    except Exception as e:
        raise e


if __name__=="__main__":
    data=monitor_verify_detail_query(association_id='1733904968733')
    print(data)