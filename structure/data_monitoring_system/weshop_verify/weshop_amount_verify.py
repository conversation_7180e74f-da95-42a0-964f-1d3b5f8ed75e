# -*- coding: utf-8 -*-
"""
@Time ： 2025/1/1 15:19
@Auth ： 逗逗的小老鼠
@File ：weshop_amount_verify.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from datetime import datetime
from structure.data_monitoring_system.monitoring_verify.monitoring_amount_verify import order_amount_verify

@exception(logger)
def weshop_detail_amount_verify(order_detail,**kwargs):
    "微商城订单明细金额校验"
    try:
        detail_amount_verify_result=[]
        verification_code_value = "weshop_detail_verify"
        # 循环调用
        for order_item in order_detail:
            detail_amount_verify=order_amount_verify(order_item,verification_code_value,applicable_scenarios="weshop",**kwargs)
            detail_amount_verify_result.extend(detail_amount_verify)
        return detail_amount_verify_result
    except Exception as e:
        raise e


@exception(logger)
def weshop_info_amount_verify(order_info,**kwargs):
    "微商城订单主单金额校验"
    try:
        info_amount_verify_result=[]
        verification_code_value = "weshop_info_verify"
        info_amount_verify=order_amount_verify(order_info,verification_code_value,applicable_scenarios="weshop",**kwargs)
        if info_amount_verify:
            info_amount_verify_result.extend(info_amount_verify)
        return info_amount_verify_result
    except Exception as e:
        raise e
