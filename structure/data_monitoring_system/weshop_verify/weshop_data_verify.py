# -*- coding: utf-8 -*-
"""
@Time ： 2025/1/2 10:49
@Auth ： 逗逗的小老鼠
@File ：weshop_data_verify.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from structure.data_monitoring_system.weshop_calculate.weshop_data_calculate import weshop_detail_calculate,weshop_info_calculate
from structure.data_monitoring_system.weshop_verify.weshop_amount_verify import weshop_detail_amount_verify,weshop_info_amount_verify



@exception(logger)
def weshop_data_verify_result(full_order_information_list):
    try:
        result = []
        logger.info('开始执行订单数据校验')
        for weshop_data_item in full_order_information_list:
            order_no=weshop_data_item.get('order_no','')
            # 获取订单基础信息
            order_info=weshop_data_item.get('order_info',[])
            # 获取订单详情数据
            order_detail=weshop_data_item.get('order_detail',[])
            # 对订单基础信息进行汇总计算
            order_info_calc=weshop_info_calculate(order_info)
            # 对订单详情进行汇总计算
            order_detail_calc=weshop_detail_calculate(order_detail)
            exception_list = []
            # 对微商城订单基础信息进行数据校验
            weshop_info_verify_result=weshop_info_amount_verify(order_info_calc)
            if weshop_info_verify_result:
                exception_list.extend(weshop_info_verify_result)
            # 对微商城订单详情数据进行数据校验
            weshop_detail_verify_result=weshop_detail_amount_verify(order_detail)
            if weshop_detail_verify_result:
                exception_list.extend(weshop_detail_verify_result)
            # 如果存在异常，则组装数据
            if len(exception_list) > 0:
                oms_exception_dict = {"order_no": order_no,"exception_list": exception_list}
                result.append(oms_exception_dict)
        return result
    except Exception as e:
        logger.error(f"微商城订单数据校验异常，原因为：{e}")
        raise e