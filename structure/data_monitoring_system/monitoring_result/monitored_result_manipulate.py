# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/21 15:20
@Auth ： 逗逗的小老鼠
@File ：monitored_result_manipulate.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：对检测结果数据进行增删改查的操作
"""
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect
from lib.deal_sql import dict_to_sql_insert, dict_to_sql_update, dict_to_convert_sql_insert
from structure.data_monitoring_system.monitoring_result.monitored_result_detection import monitor_result_data_detection,result_data_config,result_data_column_detection,monitor_result_data_column
import copy
from lib.deal_ThreadPool import combined_decorator


"""
    写入校验数据
"""
@combined_decorator(timeout_seconds=300, max_workers=5)
@exception(logger)
def monitor_result_data_insert(verify_result,data_group_name, **kwargs):
    "将校验结果数据插入到数据库"
    try:
        info_insert_count = 0
        detail_insert_count = 0
        # 获取检测结果配置信息
        monitor_reslt_config_data=result_data_config(data_group_name, **kwargs)
        # 跟据数据处理结果，写入结果数据
        association_id,sql_detail, sql_info=monitor_result_data_detection(verify_result,monitor_reslt_config_data,data_group_name,**kwargs )
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        # 写入主信息数据
        sql_info_list = dict_to_sql_insert('business_monitor_result_info', sql_info)
        info_sql, info_val = dict_to_convert_sql_insert('business_monitor_result_info', sql_info)
        info_result = db_mysql_connect("insight", info_sql,sql_val=info_val, environment_flag="common", **kwargs)
        info_insert_count = info_result.get('data', 0)
        # 写入详情数据
        detail_sql, detail_val = dict_to_convert_sql_insert('business_monitor_result_detail', sql_detail)
        detail_result = db_mysql_connect("insight", detail_sql,sql_val=detail_val, environment_flag="common", **kwargs)
        detail_insert_count = detail_result.get('data', 0)
        result = {"info_result": info_insert_count, "detail_result": detail_insert_count}
        return association_id,result
    except Exception as e:
        logger.error(f"检测结果数据写入发生异常，原因：{e}")
        raise



"""
    查询校验结果数据
"""
def monitor_result_info_query(**kwargs):
    "查询校验结果数据"
    try:
        result_columns_info = monitor_result_data_column("business_monitor_result_info")
        query_where_sql = ""
        if kwargs:
            if "environment_flag" in kwargs:
                del kwargs['environment_flag']
            conditions_data=result_data_column_detection(result_columns_info,kwargs)
            query_where_sql = " AND ".join(
                [f"{key} = '{value}'" if isinstance(value, str) else f"{key} = {value}" for key, value in
                 conditions_data.items()])
            if query_where_sql:
                query_where_sql=" AND "+query_where_sql
        query_value = []
        data_query_sql=f"SELECT * FROM business_monitor_result_info WHERE 1=1 {query_where_sql}"
        query_val=tuple(query_value)
        data_query_result=db_mysql_connect("insight", data_query_sql,sql_val=query_val,environment_flag="common", **kwargs)
        data_query_data=data_query_result.get('data',[])
        return data_query_data
    except Exception as e:
        raise e

"""
    查询校验详情结果数据
"""
def monitor_result_detail_query(**kwargs):
    "查询校验结果数据"
    try:

        result_columns_detail = monitor_result_data_column("business_monitor_result_detail")
        if kwargs:
            if "environment_flag" in kwargs:
                del kwargs['environment_flag']
            conditions_data=result_data_column_detection(result_columns_detail,kwargs)
            if 'id' in conditions_data:
                conditions_data.pop('id')
            query_where_sql = " AND ".join(
                [f"{key} = '{value}'" if isinstance(value, str) else f"{key} = {value}" for key, value in
                 conditions_data.items()])
            if query_where_sql:
                query_where_sql=" AND "+query_where_sql
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        data_query_sql=f"SELECT * FROM business_monitor_result_detail WHERE 1=1 {query_where_sql}"
        data_query_result=db_mysql_connect("insight", data_query_sql,environment_flag="common", **kwargs)
        data_query_data=data_query_result.get('data',[])
        return data_query_data
    except Exception as e:
        raise e


def monitor_result_data_repair(**kwargs):
    "修复异常数据"
    try:
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        filter_dict={}
        # 支持批量处理的字段标识
        repair_list=["exception_code","environment_flag","verification_code","service_mode","association_id"]
        for repair_item in repair_list:
            if repair_item in kwargs:
                filter_dict[repair_item]=kwargs.get(repair_item)
        deal_status=kwargs.get("deal_status","")
        deal_desc=kwargs.get("deal_desc","")
        is_delete=kwargs.get("is_delete","")
        if is_delete in [0,1,"0","1"]:
            update_dict={"is_delete":is_delete}
        else:
            if deal_status in [0,1,2,3,"0","1","2","3"]:
                update_dict={"deal_status":deal_status,"deal_desc":deal_desc}
            else:
                msg="is_delete 接收值【0,1】，deal_status接收值【1,2,3】，请核对后重新请求"
                result = {"success": "false", "msg": msg,
                          "data": {}}
                return result
        # 如果存在“exception_code”和“verification_code”则证明处理事务为详情处理
        if "exception_code" in filter_dict or "verification_code" in filter_dict:
            detail_update_sql=dict_to_sql_update("business_monitor_result_detail",update_dict,filter_dict)
            detail_result=db_mysql_connect("insight",detail_update_sql,environment_flag="common", **kwargs)
            todo_detail_num=detail_result.get('data',0)
            info_filter_dict=copy.deepcopy(filter_dict)
            # 需要去除详情表字段，以进行待处理的同条件信息查询
            if "exception_code" in info_filter_dict:
                del info_filter_dict['exception_code']
            if "verification_code" in info_filter_dict:
                del info_filter_dict['verification_code']
            # 进行待处理条件查询
            info_filter_dict['deal_status']='0'
            # 查询详情表中同条件的待处理的数量
            todo_detail_query=monitor_result_info_query(**info_filter_dict)
            todo_detail_count=len(todo_detail_query)
            todo_info_num=0
            # 如果详情表中不存在待处理的数据，则进行信息表同条件查询
            if todo_detail_count==0:
                todo_info_query=monitor_result_detail_query(**info_filter_dict)
                todo_info_num=len(todo_info_query)
                # 如果信息表中待处理的数量大于0，则需要将对应待处理的数据处理状态置为已处理
                if todo_info_num>0:
                    info_update_sql=dict_to_sql_update("business_monitor_result_info",update_dict,info_filter_dict)
                    info_result=db_mysql_connect("insight",info_update_sql,environment_flag="common", **kwargs)
                    todo_info_num=info_result.get('data',0)
            result={"success":"true","msg":"处理成功","data":{"detail_num":todo_detail_num,"info_num":todo_info_num}}
        else:
            info_update_sql=dict_to_sql_update("business_monitor_verify_info",update_dict,filter_dict)
            info_result=db_mysql_connect("insight",info_update_sql,environment_flag="common", **kwargs)
            todo_info_num=info_result.get('data',0)
            detail_filter_dict=copy.deepcopy(filter_dict)
            detail_filter_dict['deal_status']='0'
            # 如果详情表中待处理的数量大于0，则需要将同条件的详情数据全部处理状态置为已处理
            todo_detail_query=monitor_result_detail_query(**detail_filter_dict)
            todo_detail_num=len(todo_detail_query)
            if todo_detail_num>0:
                detail_update_sql=dict_to_sql_update("business_monitor_verify_detail",update_dict,detail_filter_dict)
                detail_result=db_mysql_connect("insight",detail_update_sql,environment_flag="common", **kwargs)
                todo_detail_num=detail_result.get('data',0)
            result = {"success": "true", "msg": "处理成功",
                      "data": {"detail_num": todo_detail_num, "info_num": todo_info_num}}
        return result
    except Exception as e:
        raise e