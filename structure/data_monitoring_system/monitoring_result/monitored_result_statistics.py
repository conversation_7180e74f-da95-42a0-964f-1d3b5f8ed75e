# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/9 09:54
@Auth ： 逗逗的小老鼠
@File ：monitored_result_statistics.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
from lib.deal_db_mysql import db_mysql_connect
from datetime import datetime, timedelta


@exception(logger)
def monitored_result_data_statistics(**kwargs):
    "对检测结果数据进行统计"
    try:
        environment_flag = kwargs.get("environment_flag", "")
        query_time_long = kwargs.get("query_time_long", 1440)
        data_group_name = kwargs.get("data_group_name", "")
        current_time = datetime.now()
        past_time = current_time - timedelta(minutes=query_time_long)
        monitor_start_time = past_time.strftime('%Y-%m-%d %H:%M:%S')
        monitor_end_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
        query_info_sql = f"""
            SELECT
                info.association_id AS 'association_id',
                info.reserved_field_1 AS 'reserved_field_1',
                info.reserved_field_2 AS 'reserved_field_2',
                info.reserved_field_3 AS 'reserved_field_3',
                info.reserved_field_4 AS 'reserved_field_4',
                info.reserved_field_5 AS 'reserved_field_5',
                COUNT(*) AS 'count'
            FROM
                business_monitor_result_info info
                LEFT JOIN business_monitor_result_detail detail ON info.association_id = detail.association_id AND detail.data_group_name=info.data_group_name
            WHERE
                info.data_group_name = %s
                AND info.monitor_start_time >= %s 
                AND info.monitor_end_time <= %s 
                AND info.environment_flag = %s
            GROUP BY
                info.association_id,
                info.reserved_field_1,
                info.reserved_field_2,
                info.reserved_field_3,
                info.reserved_field_4,
                info.reserved_field_5;"""
        query_info_result = db_mysql_connect("insight", query_info_sql,sql_val=(data_group_name,monitor_start_time,monitor_end_time,environment_flag), environment_flag="common")
        query_info_data = query_info_result['data']
        info_err_count=0
        detail_err_count=0
        if query_info_data:
            for info_data_item in query_info_data:
                detail_count=info_data_item.get("count",0)
                detail_err_count+=detail_count
                info_err_count=info_err_count+1

        result={"environment_flag": environment_flag, "task_name": data_group_name,
         "message": "⚠️检测异常结果统计", "monitor_data_count": "未明确", "exception_data_count": info_err_count,
         "exception_count": detail_err_count, "monitor_start_time": monitor_start_time,
         "monitor_end_time": monitor_start_time}
        return result
    except Exception as e:
        logger.error(f"检测结果数据统计发生异常，原因：{e}")
        raise