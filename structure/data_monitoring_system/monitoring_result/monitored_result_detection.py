# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/21 14:14
@Auth ： 逗逗的小老鼠
@File ：monitored_result_detection.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import copy
from lib.get_log import logger, exception
from lib.deal_json import json_loads
from datetime import datetime
from structure.data_monitoring_system.monitoring_config.monitored_result_config import monitor_result_data_column
from structure.data_monitoring_system.monitoring_config.monitored_result_config import monitor_result_config_query
from lib.decorator_func_timeout import timeout_decorator

@timeout_decorator(1000)
@exception(logger)
def monitor_result_data_detection(verify_result, monitor_reslt_config_data,data_group_name, **kwargs):
    "检测结果数据信息处理"
    try:
        """
            字段映射关系字段格式说明，reserved_field_x：表示预留字段名称，即business_monitor_result_info、business_monitor_result_detail中的预留字段
            result_data_mapping={
                                    "reserved_field_1": "字段名1",
                                    "reserved_field_2": "字段名2",
                                    "reserved_field_3": "字段名3",
                                    "reserved_field_4": "字段名4",
                                    "reserved_field_5": "字段名5"
                                }
        """
        sql_detail = []
        sql_info = []
        current_timestamp: int = int(datetime.now().timestamp() * 1000)
        monitor_start_time = kwargs.get('monitor_start_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        monitor_end_time = kwargs.get('monitor_end_time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        environment_flag = kwargs.get('environment_flag', '')

        result_columns_detail = monitor_result_data_column("business_monitor_result_detail")
        result_columns_info=monitor_result_data_column("business_monitor_result_info")

        # 遍历结果数据
        for result_item in verify_result:
            # print("开始:"+datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            mapping_value={}
            if monitor_reslt_config_data:
                for monitor_reslt_config_item in monitor_reslt_config_data:
                    # 获取结果配置字段映射关系
                    result_data_mapping = json_loads(monitor_reslt_config_item.get('result_data_mapping', []))
                    if result_data_mapping and isinstance(result_data_mapping, dict):
                        # 获取映射关系中的键值和值，键值对应数据库预留字段名称，值对应结果数据中的字段名称
                        for map_key, map_value in result_data_mapping.items():
                            # 根据映射关系中的映射值，获取结果数据中的对应字段的值
                            data_value = result_item.get(map_value, "")
                            # 如果值存在则，将映射关系中的键值作为结果数据中的字段名称，将值作为结果数据中的字段值
                            if data_value:
                                mapping_value[map_key] = data_value
            exception_list = result_item.get('exception_list', [])
            exception_info_sum = 0
            exception_error_sum = 0
            exception_waring_sum = 0
            exception_other_sum = 0
            exception_sum = len(exception_list)
            # print("步骤一完成:" + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            # 遍历各个订单的异常数据
            for exception_item in exception_list:
                exception_level = exception_item.get('exception_level')
                # 错误异常
                if exception_level == 'error':
                    exception_error_sum += 1
                # 警告异常
                elif exception_level == 'waring':
                    exception_waring_sum += 1
                # 提示类异常
                elif exception_level == 'info':
                    exception_info_sum += 1
                # 类型无法识别
                else:
                    exception_other_sum += 1
                if monitor_reslt_config_data:
                    for monitor_reslt_config_item in monitor_reslt_config_data:
                        # 获取结果配置字段映射关系
                        result_data_mapping = monitor_reslt_config_item.get('result_data_mapping', [])
                        if result_data_mapping and isinstance(result_data_mapping, dict):
                            # 获取映射关系中的键值和值，键值对应数据库预留字段名称，值对应结果数据中的字段名称
                            for map_key, map_value in result_data_mapping.items():
                                # 根据映射关系中的映射值，获取结果数据中的对应字段的值
                                data_value = result_item.get(map_value, "")
                                # 如果值存在则，将映射关系中的键值作为结果数据中的字段名称，将值作为结果数据中的字段值
                                if data_value:
                                    exception_item[map_key] = data_value
                # print("步骤二:" + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                exception_item['association_id'] = current_timestamp
                exception_item['environment_flag'] = environment_flag
                exception_item['data_group_name'] = data_group_name
                detail_exception_item = {**exception_item, **mapping_value}
                detail_data=result_data_column_detection(result_columns_detail,detail_exception_item)
                sql_detail.append(detail_data)
            # print("步骤三:" + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            info_item = copy.deepcopy(result_item)
            info_item.pop('exception_list')
            info_item['exception_info_sum'] = exception_info_sum
            info_item['exception_error_sum'] = exception_error_sum
            info_item['exception_waring_sum'] = exception_waring_sum
            info_item['exception_other_sum'] = exception_other_sum
            info_item['exception_sum'] = exception_sum
            info_item['association_id'] = current_timestamp
            info_item['monitor_start_time'] = monitor_start_time
            info_item['monitor_end_time'] = monitor_end_time
            info_item['environment_flag'] = environment_flag
            info_item['data_group_name']  = data_group_name
            info_exception_item = {**info_item, **mapping_value}
            # print("步骤四:" + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            info_data=result_data_column_detection(result_columns_info,info_exception_item)
            sql_info.append(info_data)
        return current_timestamp,sql_detail, sql_info
    except Exception as e:
        logger.error(f"检测结果数据处理发生异常，原因：{e}")
        raise


"""处理结果数据的字段信息

    Args:
        table_name: 目标表名称
        result_data: 待过滤的原始数据字典

    Returns:
        过滤后的有效字段字典
"""
@exception(logger)
def result_data_column_detection(result_columns, result_data: dict) -> dict:
    "处理结果数据的字段信息"
    try:
        if not result_data:  # 提前处理空输入
            return {}

        if not isinstance(result_columns, (list, set)):  # 类型校验
            raise TypeError("result_columns应为可迭代容器类型")

        # 使用字典推导式优化过滤逻辑
        valid_columns = set(result_columns)
        return {
            key: value
            for key, value in result_data.items()
            if key in valid_columns
        }

    except Exception as e:
        raise  # 重新抛出原始异常堆栈


@exception(logger)
def result_data_field_detection(data_item,monitor_reslt_config_data):
    "处理结果数据的字段信息"
    try:
        if monitor_reslt_config_data:
            for monitor_reslt_config_item in monitor_reslt_config_data:
                # 获取结果配置字段映射关系
                result_data_mapping = json_loads(monitor_reslt_config_item.get('result_data_mapping', []))
                if result_data_mapping and isinstance(result_data_mapping, dict):
                    # 获取映射关系中的键值和值，键值对应数据库预留字段名称，值对应结果数据中的字段名称
                    for map_key, map_value in result_data_mapping.items():
                        # 根据映射关系中的映射值，获取结果数据中的对应字段的值
                        data_value = data_item.get(map_value, "")
                        # 如果值存在则，将映射关系中的键值作为结果数据中的字段名称，将值作为结果数据中的字段值
                        if data_value:
                            data_item[map_key] = data_value
                # 处理映射关系为空或格式错误时，直接按照非list和dict依次进行存储
                else:
                    index=1
                    for key,value in data_item.items():
                        if index > 5:
                            break
                        if value and not isinstance(value,list) and not isinstance(value,dict):
                            data_item[f"reserved_field_{index}"]=value
                            index+=1
        # 处理映射关系为空或格式错误时，直接按照非list和dict依次进行存储
        else:
            num = 1
            for key, value in data_item.items():
                if num > 5:
                    break
                if value and not isinstance(value, list) and not isinstance(value, dict):
                    data_item[f"reserved_field_{num}"] = value
                    num += 1
        return data_item
    except Exception as e:
        logger.error(f"检测结果数据字段映射关系异常 | 映射关系:{monitor_reslt_config_data} | 错误:{e}")
        raise


def result_data_config(data_group_name, **kwargs):
    "获取检测结果配置信息"
    try:
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        monitor_reslt_config = monitor_result_config_query(**kwargs)
        monitor_reslt_config_data = []
        for monitor_reslt_config_item in monitor_reslt_config:
            config_group_name = monitor_reslt_config_item.get('data_group_name')
            if data_group_name and config_group_name and data_group_name == config_group_name:
                monitor_reslt_config_data.append(monitor_reslt_config_item)
        return monitor_reslt_config_data
    except Exception as e:
        logger.error(f"获取检测结果配置信息异常 | 映射关系:{monitor_reslt_config_data} | 错误:{e}")
        raise