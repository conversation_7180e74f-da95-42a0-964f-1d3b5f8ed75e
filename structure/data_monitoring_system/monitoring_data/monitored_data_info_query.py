# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/13 13:48
@Auth ： 逗逗的小老鼠
@File ：monitored_data_info_query.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：对被检数据使用数据库SQL进行查询
"""
import decimal
import subprocess
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect
from structure.data_monitoring_system.monitoring_data.monitored_data_query_preprocessing import monitored_data_query_condition
import json
from datetime import datetime
from lib.decorator_func_timeout import timeout_decorator
import datetime
from lib.deal_json import json_loads,json_dumps





"""
生成带有复杂查询条件的SQL语句，包括联表查询和条件筛选。

该函数首先根据输入参数构建查询条件，然后定义查询字段和联表查询方式，
最后组合生成完整的SQL查询语句。

参数:
**kwargs: 关键字参数，用于构建查询条件。

返回:
生成的SQL查询语句。
"""
# 使用装饰器设置函数执行的超时时间和异常处理
@timeout_decorator(1000)
@exception(logger)
def monitored_data_query(query_field_list,join_query_list,shcema_name,key_field_list,**kwargs):
    "生成带有复杂查询条件的SQL语句，包括联表查询和条件筛选"
    try:
        # 构建查询条件
        query_condition = monitored_data_query_condition(key_field_list,query_field_list,shcema_name,**kwargs)
        sql_condition = query_condition.get('sql_condition', '')
        query_value = query_condition.get('query_value', {})

        #  定义查询字段的数据字段格式
        # query_field_list = [
        #     {"table_name": "info", "field_name": "id"},
        #     {"table_name": "info", "field_name": "name"},
        #     {"table_name": "info", "field_name": "age"}
        # ]
        #
        # # 定义联表查询方式
        # join_query_list = [
        #     {
        #         "table_name": "order_info",
        #         "join_type": "LEFT JOIN",
        #         "join_relationship": [
        #             {"table_filed": "id", "join_table_name": "order_info", "join_table_filed": "id"}
        #         ]
        #     },
        #     {
        #         "table_name": "order_detail",
        #         "join_type": "LEFT JOIN",
        #         "join_relationship": [
        #             {"table_filed": "order_id", "join_table_name": "order_info", "join_table_filed": "id"}
        #         ]
        #     }
        # ]

        # 构建SELECT子句
        select_clause_list=[]
        for item in query_field_list:
            replace_table_name=item.get("replace_table_name")
            table_name = item.get("table_name")
            field_name = item.get("field_name")
            if replace_table_name:
                select_clause_list.append(f"{replace_table_name}.{field_name} AS {table_name}_{field_name}")
            else:
                select_clause_list.append(f"{table_name}.{field_name} AS {table_name}_{field_name}")
        select_clause = ", ".join(select_clause_list)

        # 构建FROM子句
        from_clause_list = []
        if join_query_list:

            for index, join_query_item in enumerate(join_query_list):
                table_name = join_query_item.get("table_name")
                replace_table_name = join_query_item.get("replace_table_name")
                join_type = join_query_item.get("join_type", "LEFT JOIN")
                join_relationship = join_query_item.get("join_relationship", [])
                join_on_clause_str_list = []
                if replace_table_name:
                    table_name = replace_table_name

                if join_relationship:
                    for join_relationship_item in join_relationship:
                        table_filed_name = join_relationship_item.get("table_filed")
                        join_replace_table_name = join_query_item.get("replace_table_name")
                        join_table_name = join_relationship_item.get("join_table_name")
                        if join_replace_table_name:
                            join_table_name = join_replace_table_name
                        join_table_filed = join_relationship_item.get("join_table_filed")
                        join_on_clause_str_list.append(f" {table_name}.{table_filed_name} = {join_table_name}.{join_table_filed}")

                    join_on_clause_str = " AND ".join(join_on_clause_str_list)
                    from_clause_list.append(f"{table_name} {join_type} {join_table_name}  ON {join_on_clause_str}")
                else:
                    from_clause_list.append(f"{table_name}")
        # 若没有配置联表查询方式，则直接使用第一个查询字段的表名作为FROM子句
        else:
            if "replace_table_name" in query_field_list[0]:
                from_clause_list.append(f"{query_field_list[0]['replace_table_name']}")
            else:
                from_clause_list.append(f"{query_field_list[0]['table_name']}")

        from_clause = " ".join(from_clause_list)

        # 组合完整的SQL查询语句
        data_sql = f"""
            SELECT
                {select_clause}
            FROM
                {from_clause}
            WHERE
                1=1 {sql_condition}
        """
        # 调用数据库连接函数执行查询
        query_result = db_mysql_connect(shcema_name, data_sql, sql_val=query_value, **kwargs)
        info_data = query_result['data']
        return info_data

    except Exception as e:
        # 记录异常信息并重新抛出异常
        logger.error(f"Error occurred: {e}", exc_info=True)
        raise


#



if __name__ == '__main__':

    print(monitored_data_query())