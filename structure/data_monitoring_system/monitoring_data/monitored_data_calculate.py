# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/14 10:08
@Auth ： 逗逗的小老鼠
@File ：monitored_data_calculate.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：对被检测数据进行二次聚合计算
"""

import json
from lib.get_log import logger, exception
from collections import defaultdict
from decimal import Decimal, InvalidOperation, getcontext, ROUND_HALF_UP
import re

# 设置Decimal精度为28位，足够处理大多数金融计算
getcontext().prec = 28
getcontext().rounding = ROUND_HALF_UP

# 安全地将值转换为Decimal类型
def safe_decimal_convert(value, default=0):
    """
    安全地将值转换为Decimal类型

    Args:
        value: 要转换的值
        default: 转换失败时的默认值

    Returns:
        Decimal: 转换后的Decimal对象
    """
    if value is None:
        return Decimal(default)

    if isinstance(value, Decimal):
        return value

    try:
        # 确保字符串表示干净，移除可能导致问题的字符
        if isinstance(value, str):
            # 移除非数字、小数点和负号以外的字符
            clean_value = re.sub(r'[^\d.-]', '', value)
            # 如果清理后为空，返回默认值
            if not clean_value or clean_value in ['.', '-', '-.']:
                return Decimal(default)
            return Decimal(clean_value)
        return Decimal(str(value))
    except (InvalidOperation, ValueError, TypeError):
        logger.debug(f"无法将 {value} 转换为Decimal，使用默认值 {default}")
        return Decimal(default)


@exception(logger)
def monitored_data_calculate(monitored_data, calculate_field_list, calculate_type, calculate_filter_list, **kwargs):
    "对被监控数据进行二次计算"
    try:
        """
        monitoring_data = {"id": 1,
                          "order_detail": [{"bill_price": 1, "share_amount": 1}, {"bill_price": 2, "share_amount": 2}]}
        # 定义聚合计算字段格式
        aggregation_calculate_field_list = [{"view_name": "order_detail", "field_name": "bill_price"},
                                            {"view_name": "order_detail", "field_name": "share_amount"}]
        # 定义合并计算字段格式
        merge_calculate_field_list = [[{"view_name": "order_detail", "field_name": "bill_price"},
                                       {"view_name": "order_detail", "field_name": "share_amount"}]]
        # 定义计算方式格式：SUM求和、AVG平均值、MAX最大值、MIN最小值、COUNT计数、JOIN关联
        calculate_type = "SUM"
        # 定义数据过滤条件格式：过滤字段、过滤值
        calculate_filter_list =[{"filter_data": [{"view_name": "order_detail", "field_name": "order_detail_drug_type", "filter_value": 3},
         {"view_name": "order_detail", "field_name": "order_detail_status", "filter_value": 20}], "filter_type": "and"}]
            过滤说明：若同级中，存在多个表/视图，会对每个视图单独判断进行过滤，不支持跨表/视图判断。
                and：对同级中，相相同表/视图中，所有条件均满足的子项，进行过滤
                or：对同级中，相同表/视图中，任意条件均满足的子项，进行过滤
                not：对同级中，相同表/视图中，所有条件均不满足的子项，进行过滤
        """

        logger.info("开始对被监控数据进行二次计算")
        calculate_type_list = ["SUM", "AVG", "MAX", "MIN", "COUNT", "JOIN"]
        result_data = []

        # 根据过滤条件，对数据进行过滤
        monitored_data = monitored_data_filter(monitored_data, calculate_filter_list)

        # 检查计算类型是否支持
        if calculate_type in calculate_type_list:
            if calculate_type == "JOIN":
                result_data = monitored_data_merge_calculate(monitored_data, calculate_field_list, calculate_type)
            else:
                # 二次处理计算数据
                calculate_conditions_list = defaultdict(list)
                for calculate_field_item in calculate_field_list:
                    view_name, field_name = monitored_field_value_processing(calculate_field_item)
                    calculate_conditions_list[view_name].append(field_name)
                result_data = monitored_data_aggregation_calculate(monitored_data, calculate_conditions_list,
                                                                   calculate_type)
        else:
            logger.error(
                f"不支持的计算方式: {calculate_type}，仅支持【SUM求和、AVG平均值、MAX最大值、MIN最小值、COUNT计数、JOIN关联】")
            raise Exception(
                f"不支持的计算方式: {calculate_type}，仅支持【SUM求和、AVG平均值、MAX最大值、MIN最小值、COUNT计数、JOIN关联】")
        return result_data

    except Exception as e:
        logger.error(f"被监控数据二次计算异常，原因为：{e}")
        raise


"""
    对数据进行聚合计算

    参数:
    monitoring_data: 监控得到的数据，结构为字典，key为视图名，value为该视图下的数据列表
    calculate_type: 计算类型，支持SUM, AVG, MAX, MIN, COUNT
    calculate_conditions_list: 计算条件列表，结构为字典，key为视图名，value为需要计算的字段名列表

    返回:
    返回计算结果，为一个字典，key为视图名和字段名的组合，value为计算结果
"""


@exception(logger)
def monitored_data_aggregation_calculate(monitored_data, calculate_conditions_list, calculate_type):
    "对数据进行聚合计算"
    try:
        # 记录计算开始的日志
        logger.info("开始对被监控数据进行二次计算")

        # 检查计算条件和监控数据是否为空
        if not calculate_conditions_list or not monitored_data:
            logger.warning("计算条件或被监控数据为空，直接返回空结果")
            return {}

        # 初始化计算结果字典
        monitored_calculate_result = {}

        # 定义计算函数字典，使用高精度计算
        calculation_functions = {
            "SUM": lambda values: sum(values).quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP) if values else Decimal('0'),
            "AVG": lambda values: (sum(values) / Decimal(len(values))).quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP) if values else Decimal('0'),
            "MAX": lambda values: max(values).quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP) if values else Decimal('0'),
            "MIN": lambda values: min(values).quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP) if values else Decimal('0'),
            "COUNT": lambda values: Decimal(len(values))
        }

        # 遍历计算条件列表
        for view_name, field_name_list in calculate_conditions_list.items():
            # 获取当前视图的数据列表
            monitored_data_list = monitored_data.get(view_name, [])
            if isinstance(monitored_data_list, dict):
                monitored_data_list = [monitored_data_list]
            # 初始化转换后的数据字典
            converted_data = {}

            # 遍历每个需要计算的字段
            for field_name in field_name_list:
                converted_data_list = []

                # 遍历数据列表中的每一项
                for item in monitored_data_list:
                    # 检查字段是否存在
                    if field_name in item:
                        # 使用安全转换函数将值转换为Decimal
                        field_value = safe_decimal_convert(item.get(field_name, 0))
                        converted_data_list.append(field_value)
                    else:
                        # 如果字段不存在，添加默认值0
                        converted_data_list.append(Decimal('0'))

                # 将转换后的列表存储到字典中
                converted_data[field_name] = converted_data_list


            # 对每个字段进行计算
            for field_name, monitored_value_list in converted_data.items():
                # 检查计算类型是否支持
                if calculate_type not in calculation_functions:
                    raise Exception(
                        f"不支持的计算方式: {calculate_type}，仅支持【SUM求和、AVG平均值、MAX最大值、MIN最小值、COUNT计数、JOIN关联】")

                try:
                    # 执行计算并存储结果
                    result = calculation_functions[calculate_type](monitored_value_list)

                    # 确保结果是Decimal类型并进行精确范围内的范围
                    if isinstance(result, Decimal):
                        # 已经是Decimal类型，保持精度
                        monitored_calculate_result[field_name] = result
                    else:
                        # 如果不是Decimal类型，转换为Decimal并进行格式化
                        monitored_calculate_result[field_name] = safe_decimal_convert(result).quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP)

                    # 记录计算结果的详细信息，便于调试
                    logger.debug(f"计算 {view_name}_{field_name} 结果: {monitored_calculate_result[field_name]}")

                except ZeroDivisionError:
                    # 处理空列表的平均值计算异常
                    logger.warning(f"计算 {view_name}_{field_name} 的平均值时，列表为空，设置为 0")
                    monitored_calculate_result[field_name] = Decimal('0')

                except (InvalidOperation, ValueError, TypeError) as e:
                    # 处理无效操作或值错误
                    logger.error(f"计算 {view_name}_{field_name} 时发生错误: {e}")
                    monitored_calculate_result[field_name] = Decimal('0')
        result_data = [monitored_calculate_result]
        # 返回计算结果
        return result_data

    except Exception as e:
        # 记录异常错误日志并抛出异常
        logger.error(f"对数据进行聚合计算异常，原因为：{e}")
        raise


"""
    对数据进行合并计算

    该函数接收被监控的数据，根据指定的合并计算字段列表和计算类型，
    对数据进行合并和计算。它支持多条件合并，但目前仅适用于固定表的合并，
    不支持多表多条件合并。

    参数:
    - monitoring_data: 被监控的数据，应为字典格式
    - merge_calculate_field_list: 合并计算字段列表，应为列表格式
    - calculate_type: 计算类型，目前未在函数中使用

    返回:
    - result_data: 合并计算后的结果数据，为列表格式
"""


@exception(logger)
def monitored_data_merge_calculate(monitored_data, merge_calculate_field_list, calculate_type):
    "对数据进行合并计算"
    try:
        result_data = []
        logger.info("开始对被监控数据合并计算")

        # 该映射关系仅仅适用于固定表多条件合并，暂不支持多表多条件合并
        # merge_calculate_field_list = [[{"view_name": "order_detail", "field_name": "bill_price"},{"view_name": "order_detail", "field_name": "share_amount"}]]

        merged_data = []
        if merge_calculate_field_list and isinstance(merge_calculate_field_list, list):
            for index, merge_field_item in enumerate(merge_calculate_field_list):
                if index == 0:
                    # 锚定数据视图和字段
                    merge_anchor_view_name, merge_anchor_field_name = monitored_field_value_processing(
                        merge_field_item[0])

                    merge_anchor_data = monitored_data.get(merge_anchor_view_name, [])
                    # 如果获取的锚定数据为dict，则将其转换为list
                    if isinstance(merge_anchor_data, dict):
                        merge_anchor_data = [merge_anchor_data]

                    for merge_anchor_item in merge_anchor_data:
                        merged_item_data = {}
                        merged_item_data[merge_anchor_view_name] = [merge_anchor_item]

                        for merge_field_item_item in merge_field_item[1:]:
                            # 参考数据视图和字段
                            merge_reference_view_name, merge_reference_field_name = monitored_field_value_processing(
                                merge_field_item_item)

                            merge_reference_data = monitored_data.get(merge_reference_view_name, [])
                            if isinstance(merge_reference_data, dict):
                                merge_reference_data = [merge_reference_data]

                            merge_reference_item_data = []

                            anchor_data_value = merge_anchor_item.get(merge_anchor_field_name)

                            for merge_reference_item in merge_reference_data:
                                merge_reference_value = merge_reference_item.get(merge_reference_field_name)
                                if anchor_data_value == merge_reference_value:
                                    merge_reference_item_data.append(merge_reference_item)

                            merged_item_data[merge_reference_view_name] = merge_reference_item_data

                        merged_data.append(merged_item_data)
                else:
                    # 如果存在多个匹配条件，则对已经合并的数据进行进一步处理
                    for merged_data_item in merged_data:
                        merge_anchor_view_name, merge_anchor_field_name = monitored_field_value_processing(
                            merge_field_item[0])

                        merge_anchor_data = merged_data_item.get(merge_anchor_view_name, [])
                        if isinstance(merge_anchor_data, dict):
                            merge_anchor_data = [merge_anchor_data]

                        for merge_anchor_item in merge_anchor_data:
                            merged_item_data = {}
                            merged_item_data[merge_anchor_view_name] = [merge_anchor_item]

                            for merge_field_item_item in merge_field_item[1:]:
                                merge_reference_view_name, merge_reference_field_name = monitored_field_value_processing(
                                    merge_field_item_item)

                                merge_reference_data = merged_data_item.get(merge_reference_view_name, [])
                                if isinstance(merge_reference_data, dict):
                                    merge_reference_data = [merge_reference_data]

                                anchor_data_value = merge_anchor_item.get(merge_anchor_field_name)

                                for merge_reference_item in merge_reference_data:
                                    merge_reference_value = merge_reference_item.get(merge_reference_field_name)
                                    if anchor_data_value != merge_reference_value:
                                        merge_reference_data.remove(merge_reference_item)

        """
        计算合并后的数据：该结果是一个列表，列表中的每个元素都是一个字典，字典的键是合并字段的名称，值是合并字段的值。
        若数据存在一对多的情况，则取最后一个匹配值。
        """
        for calculate_merge_item in merged_data:
            calculate_dict = {}
            for calculate_merge_item_field_name, calculate_merge_item_data in calculate_merge_item.items():
                for calculate_merge_item_data_item in calculate_merge_item_data:
                    for calculate_merge_item_data_item_field_name, calculate_merge_item_data_item_field_value in calculate_merge_item_data_item.items():
                        calculate_dict[
                            f"{calculate_merge_item_field_name}_{calculate_merge_item_data_item_field_name}"] = calculate_merge_item_data_item_field_value
            result_data.append(calculate_dict)

        return result_data
    except Exception as e:
        logger.error(f"被监控数据合并计算异常，原因为：{e}")
        raise


@exception(logger)
def monitored_data_filter(monitored_data, calculate_filter_list):
    "对被检测数据进行过滤"
    try:
        if calculate_filter_list and isinstance(calculate_filter_list, list):
            for calculate_filter_item in calculate_filter_list:
                filter_type = calculate_filter_item.get("filter_type", "and")
                filter_data = calculate_filter_item.get("filter_data", [])
                if filter_type not in ["and", "or", "not"]:
                    logger.error(f"被监控数据过滤类型{filter_type}错误")
                    continue
                if filter_data and isinstance(filter_data, list):
                    # 此处获取每个过滤类型下的过滤具体条件
                    filter_data_dict = {}
                    for filter_data_item in filter_data:
                        # 获取具体过滤条件中的表名和字段名以及过滤值
                        filter_view_name, filter_field_name = monitored_field_value_processing(
                            filter_data_item)
                        filter_value = filter_data_item.get("filter_value", "")
                        # 根据过滤结果，将数据重新进行组装，组装格式为{"过滤表名"：[{"filter_field_name": "字段名","filter_value": "过滤值"}]}
                        if filter_view_name in filter_data_dict:
                            filter_view_item_data = filter_data_dict.get(filter_view_name)
                            filter_view_item_data.append({
                                "filter_field_name": filter_field_name,
                                "filter_value": filter_value
                            })
                        else:
                            filter_data_dict[filter_view_name] = [{
                                "filter_field_name": filter_field_name,
                                "filter_value": filter_value
                            }]
                    # 根据新的组装格式进行数据过滤
                    for filter_item_name, filter_item_data in filter_data_dict.items():
                        # 根据表名获取被过滤的具体子数据
                        monitored_data_item = monitored_data.get(filter_item_name)
                        monitored_item_filter_count = len(filter_item_data)
                        # 判断被过滤的数据是否存在且是否为列表
                        if monitored_data_item and isinstance(monitored_data_item, list):
                            # 获取单个数据项/数据表的过滤条件总数
                            for monitored_value_item in monitored_data_item:
                                filter_true_count = 0
                                # 针对单个数据项/数据表的过滤条件来判断
                                for filter_item_data_item in filter_item_data:
                                    data_item_filter_field_name = filter_item_data_item.get("filter_field_name")
                                    data_item_filter_value = filter_item_data_item.get("filter_value")
                                    monitored_value = monitored_value_item.get(data_item_filter_field_name)
                                    # and和or判断值相同则计算为需要过滤
                                    if filter_type == "and" or filter_type == "or":
                                        # 如果获取到的值和过滤值均为None，或均不为None且值相同，则将数据过滤掉
                                        if (not data_item_filter_value and not monitored_value) or (
                                                data_item_filter_value and monitored_value and str(
                                                monitored_value) == str(data_item_filter_value)):
                                            filter_true_count += 1
                                    # not则判断值不相同则计算为需要过滤
                                    if filter_type == "not":
                                        if data_item_filter_value and monitored_value and str(monitored_value) != str(
                                                data_item_filter_value):
                                            filter_true_count += 1
                                # and和not，需要所有条件均需要满足才需要过滤
                                if filter_type == "and" or filter_type == "not":
                                    if filter_true_count == monitored_item_filter_count:
                                        monitored_data_item.remove(monitored_value_item)
                                # or 则只要有一个条件满足则需要过滤
                                if filter_type == "or" and filter_true_count > 0:
                                    monitored_data_item.remove(monitored_value_item)
        return monitored_data
    except Exception as e:
        logger.error(f"被监控数据过滤异常，原因为：{e}")
        raise


def monitored_field_value_processing(calculate_field_item):
    "处理字段列表取值逻辑"
    try:
        # 若传值为view_name，field_name取值逻辑为原值
        if "view_name" in calculate_field_item:
            view_name = calculate_field_item.get("view_name")
            field_name = calculate_field_item.get("field_name")
        # 若传值为table_name，field_name取值逻辑为：view_name_field_name
        if "table_name" in calculate_field_item:
            view_name = calculate_field_item.get("table_name")
            field_name = calculate_field_item.get("field_name")
            if field_name:
                field_name = f"{view_name}_{field_name}"
            else:
                field_name=""
        return view_name, field_name
    except Exception as e:
        logger.error(f"被监控数据字段值处理异常，原因为：{e}")
        raise


if __name__ == "__main__":
    data = {"order_detail": [{"id": 1, "erp_code": "149856", "bill_price": 100},
                             {"id": 2, "erp_code": "825412", "bill_price": 200},
                             {"id": 3, "erp_code": "118563", "bill_price": 300}],
            "goods_info": [{"id": 1, "erp_code": "149856", "good_name": "修正口服液", "good_type": "中药"},
                           {"id": 2, "erp_code": "825412", "good_name": "999感冒灵", "good_type": "中成药"},
                           {"id": 3, "erp_code": "118563", "good_name": "阿莫西林分散片", "good_type": "西药"},
                           {"id": 3, "erp_code": "118555", "good_name": "阿奇霉素肠溶片", "good_type": "西药"}],
            "order_pick_info": [{"order_id": 1, "erp_code": "149856", "moke_no": "8522251", "moke_num": 1},
                                {"order_id": 2, "erp_code": "825412", "moke_no": "Y_256622", "moke_num": 7},
                                {"order_id": 3, "erp_code": "118563", "moke_no": "N000251", "moke_num": 9},
                                {"order_id": 4, "erp_code": "118555", "moke_no": "N000251", "moke_num": 9}]
            }
    calculate_field_list = [
        [{"view_name": "order_detail", "field_name": "id"},
         {"view_name": "goods_info", "field_name": "id"}, {"view_name": "order_pick_info", "field_name": "order_id"}],
        [{"view_name": "order_detail", "field_name": "erp_code"},
         # {"view_name": "goods_info", "field_name": "erp_code"},
         {"view_name": "order_pick_info", "field_name": "erp_code"}]]
    print(json.dumps(monitored_data_merge_calculate(data, calculate_field_list, "JOIN")))
