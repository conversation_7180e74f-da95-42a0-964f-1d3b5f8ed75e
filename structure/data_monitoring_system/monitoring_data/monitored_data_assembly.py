# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/18 9:45
@Auth ： 逗逗的小老鼠
@File ：monitored_data_assembly.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：对被检数据进行预处理和组装
"""
import copy
from lib.get_log import logger, exception
from lib.deal_json import json_loads,json_dumps
from structure.data_monitoring_system.monitoring_config.monitored_data_config import monitor_data_query_query,monitor_data_calculate_query
from structure.data_monitoring_system.monitoring_data.monitored_data_sharding_query import monitored_data_sharding
from structure.data_monitoring_system.monitoring_data.monitored_data_calculate import monitored_data_calculate


@exception(logger)
def data_assembly(**kwargs):
    "对被检测数据进行预处理和组装"
    try:
        group_name = kwargs.get("data_group_name", "")
        master_data_group_config_data=[]
        from_data_group_config_data=[]
        # 获取被检数据查询配置信息
        data_query_config=monitor_data_query_query(**kwargs)
        # 对数据查询配置信息进行预处理
        for data_query_config_item in data_query_config:
            # 获取数据组名称
            data_group_name=data_query_config_item.get('data_group_name',"")
            # 获取是否主数据标识
            is_master_data=data_query_config_item.get('is_master_data')
            # 获取查询库名称
            shcema_name = data_query_config_item.get('shcema_name', '')
            # 判断主数据配置
            if group_name==data_group_name:
                if shcema_name=='' or shcema_name is None:
                    logger.error(f"数据查询组【{data_group_name}】查询库字段【shcema_name】配置值【{shcema_name}】错误，请检查配置文件")
                    return []
                if is_master_data==1:
                    master_data_group_config_data.append(data_query_config_item)
            # 判断从数据配置
                else:
                    from_data_group_config_data.append(data_query_config_item)
        data_group_config_data={
            'master_data_config':master_data_group_config_data,
            'from_data_config':from_data_group_config_data
        }
        # 根据数查询的配置结果获取数据组数据
        group_data=group_data_query(data_group_config_data, **kwargs)
        calculate_config=[]
        data_calculate_config=monitor_data_calculate_query(**kwargs)
        # 对数据计算配置信息进行预处理
        for data_calculate_config_item in data_calculate_config:
            # 获取数据组名称
            data_group_name = data_calculate_config_item.get('data_group_name', "")
            if group_name==data_group_name:
                calculate_config.append(data_calculate_config_item)
        # 根据数据计算配置信息进行预处理
        group_data_result = group_data_calculate(group_data, calculate_config, **kwargs)
        return group_data_result
    except Exception as e:
        exception(logger)
        return e


@exception(logger)
def group_data_query(data_group_config_data, **kwargs):
    # 根据配置查询/组装源数据信息
    try:
        result_data=[]
        master_data_config=data_group_config_data.get('master_data_config',[])
        from_data_config=data_group_config_data.get('from_data_config',[])
        # 根据主数据配置处理主数据信息
        for master_data_config_item in master_data_config:
            master_data_group_name = master_data_config_item.get('data_group_name')
            # 获取数据组描述
            master_data_group_desc = master_data_config_item.get('data_group_desc')
            # 获取查询结果名称
            master_view_name = master_data_config_item.get('view_name','')
            # 获取查询库名称
            master_shcema_name = master_data_config_item.get('shcema_name', '')
            # 获取主数据展示字段
            master_master_data_field = json_loads(master_data_config_item.get('master_data_field',[]))
            # 获取需要查询的字段列表
            master_query_field_list = json_loads(master_data_config_item.get('query_field_list',[]))
            # 获取关联查询条件
            master_join_query_list = json_loads(master_data_config_item.get('join_query_list',[]))
            # 获取查询关键字列表
            master_key_field_list = json_loads(master_data_config_item.get('key_field_list', []))
            # 获取分表逻辑
            master_sharding_logic=json_loads(master_data_config_item.get('sharding_logic',[]))
            # 根据主数据查询配置查询主数据信息
            master_data = monitored_data_sharding(master_sharding_logic,master_query_field_list, master_join_query_list, master_shcema_name,
                                               master_key_field_list, **kwargs)
            # master_data=monitored_data_query(master_query_field_list, master_join_query_list, master_shcema_name,master_key_field_list, **kwargs)
            # 遍历主数据查询结果
            if not master_data:
                # 若未查询到从数据，则打印错误信息
                logger.error(f"数据组【{master_data_group_name}】查询结果【{master_view_name}】未查询到数据，请核对数据配置信息")
            for master_data_item in master_data:
                master_data_dict={}
                for master_data_field_item in master_master_data_field:
                    master_table_name=master_data_field_item.get('table_name','')
                    master_field_name=master_data_field_item.get('field_name','')
                    master_data_field_name=f"{master_table_name}_{master_field_name}"
                    if master_data_field_name in master_data_item.keys():
                        master_data_field_value=master_data_item.get(master_data_field_name)
                        master_data_dict[master_data_field_name]=master_data_field_value
                master_data_dict[master_view_name]=[master_data_item]
                result_data.append(master_data_dict)

        # 根据从数据配置处理从数据信息
        from_data_dict={}
        for from_data_config_item in from_data_config:
            from_data_group_name = from_data_config_item.get('data_group_name')
            # 获取数据组描述
            from_data_group_desc = from_data_config_item.get('data_group_desc')
            # 获取查询结果名称
            from_view_name = from_data_config_item.get('view_name', '')
            # 获取查询库名称
            from_shcema_name = from_data_config_item.get('shcema_name', '')
            # 获取需要查询的字段列表
            from_query_field_list = json_loads(from_data_config_item.get('query_field_list', []))
            # 获取关联查询条件
            from_join_query_list = json_loads(from_data_config_item.get('join_query_list', []))
            # 获取查询关键字列表
            from_key_field_list = json_loads(from_data_config_item.get('key_field_list', []))
            # 获取分表逻辑
            from_sharding_logic = json_loads(from_data_config_item.get('sharding_logic', []))
            # 根据从数据查询配置查询从数据信息
            from_data = monitored_data_sharding(from_sharding_logic,from_query_field_list, from_join_query_list, from_shcema_name,from_key_field_list, **kwargs)
            # from_data=monitored_data_query(from_query_field_list, from_join_query_list, from_shcema_name,from_key_field_list, **kwargs)
            if not from_data:
                # 若未查询到从数据，则打印错误信息
                logger.error(f"数据组【{from_data_group_name}】查询结果【{from_view_name}】未查询到数据，请核对数据配置信息")
            if result_data:
                if from_data:
                    for data_item in result_data:
                        related_items = []
                        for from_data_item in from_data:
                            # 获取两个字典 data_item 和 from_data_item 中所有共同键（字段名）的集合
                            common_keys = set(data_item.keys()).intersection(set(from_data_item.keys()))
                            if all(data_item[key] == from_data_item[key] for key in common_keys):
                                related_items.append(from_data_item)
                            # else:
                            #     logger.error(f"数据组【{from_data_group_name}】查询结果【{from_view_name}】无法关联到主数据，请核对主数据配置")
                        # 判断如果从数据未关联到主数据，则打印错误信息
                        if not related_items:
                            logger.error(f"数据组【{from_data_group_name}】查询结果【{from_view_name}】无法关联到主数据，请核对主数据配置")
                        data_item[from_view_name] = related_items
                else:
                    for data_item in result_data:
                        data_item[from_view_name] = from_data

            else:
                # 如果从数据结果不为空，则将从数据结果添加到结果列表中
                if from_data:
                    from_data_dict[from_view_name] = from_data
        # 如果从数据字典不为空，则将数据字典添加到结果列表中
        if from_data_dict:
            result_data.append(from_data_dict)
        return result_data
    except Exception as e:
        logger.error(f"根据配置查询/组装源数据信息异常，异常为{e}")
        raise

@exception(logger)
def group_data_calculate(group_data,calculate_config, **kwargs):
    "对被检测数据进行二次计算"
    try:
        if group_data and isinstance(group_data, dict):
            group_data=[group_data]
        # 输入检查
        if not group_data or not isinstance(group_data, list):
            logger.error("被检测数据为空或格式不正确")
            return []

        if not calculate_config or not isinstance(calculate_config, list):
            logger.error("数据二次计算字段列表【calculate_field_list】 为空或格式不正确")
            return group_data
        for calculate_config_item in calculate_config:
            # 获取数据组名称
            data_group_name=calculate_config_item.get('data_group_name', "")
            # 获取计算类型
            calculate_type=calculate_config_item.get('calculate_type')
            # 获取结算结果名称
            view_name=calculate_config_item.get('view_name')
            # 获取需要计算的字段列表
            calculate_field_list=json_loads(calculate_config_item.get('calculate_field_list',[]))
            # 获取过滤条件
            calculate_filter_list=json_loads(calculate_config_item.get('calculate_filter_list',[]))
            if not calculate_type or calculate_type not in ["SUM", "AVG", "MAX", "MIN", "COUNT", "JOIN"]:
                logger.error(
                    f"数据组【{data_group_name}】提供的计算方式【{calculate_type}】错误，系统仅支持【SUM求和、AVG平均值、MAX最大值、MIN最小值、COUNT计数、JOIN关联】")
                raise Exception(
                    f"数据组【{data_group_name}】提供的计算方式【{calculate_type}】错误，系统仅支持【SUM求和、AVG平均值、MAX最大值、MIN最小值、COUNT计数、JOIN关联】")
            for group_data_item in group_data:
                # 数据项进行深拷贝，防止二次计算时数据被修改
                data_item=copy.deepcopy(group_data_item)
                # 进行数据的二次计算
                calculate_data=monitored_data_calculate(data_item,calculate_field_list,calculate_type,calculate_filter_list,**kwargs)
                # 将计算结果根据配置结果名称写入数据结果中
                group_data_item[view_name]=calculate_data
        return group_data
    except Exception as e:
        logger.error(f"对被检测数据进行二次计算异常，原因为：{e}")
        raise

if __name__=="__main__":
    data_group="B2B_order_data"
    query_data={"data_group_name":"B2B_order_data","third_platform_code":"101","order_no":"2396807419607092504"}
    result=data_assembly(**query_data)
    print(json_dumps(result))

