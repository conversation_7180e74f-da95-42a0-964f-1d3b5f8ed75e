# -*- coding: utf-8 -*-
"""
@Time ： 2025/3/27 18:08
@Auth ： 逗逗的小老鼠
@File ：monitored_data_sharding_query.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import json
import copy
import re
from lib.get_log import logger, exception
from datetime import datetime
import datetime
import itertools
from structure.data_monitoring_system.monitoring_data.monitored_data_info_query import monitored_data_query


@exception(logger)
def monitored_data_sharding(sharding_logic, query_field_list, join_query_list, shcema_name, key_field_list, **kwargs):
    "对数据表进行分表处理"
    """
        sharding_logic预期格式：
        [
            {
                "sharding_table": "order_info", //分表的表名
                "sharding_suffix": "_seq",  //分表的后缀标识
                "replace_range": ["table_name","join_table_name"], //替换值的json的key
                "sharding_prefix": "",//分表的前缀标识
                "suffix_rule": [    //后缀的替换规则
                    {
                        "replace_key": "seq",   //替换的关键字
                        "sharding_rule": [
                            {
                                "rule_type": "date",    //替换的类型，若为date，则取值15天内的对应格式的值，不可重复
                                "rule_format": "YYYYMM" //类型格式
                            },
                            {
                                "rule_type": "number",//替换的类型，若为number，则按照开始值start、结束值end、步长step_len生成替换值
                                "start": 0,
                                "end": 255,
                                "step_len": 1
                            },
                            {
                                "rule_type": "fixed",//替换的类型，若为fixed，则按照value中的值进行替换
                                "value": [1,2,3,4]
                            }
                        ]
                    }
                ],
                "prefix_rule": []   //前缀的替换规则
            }
        ]
        
    """
    try:
        query_result = []
        replace_table_name_list=[]
        if not sharding_logic:
            query_result_item = monitored_data_query(query_field_list, join_query_list, shcema_name, key_field_list,
                                                     **kwargs)
            query_result.extend(query_result_item)
        else:
            for sharding_logic_item in sharding_logic:
                # 获取需要替换的表名
                sharding_table = sharding_logic_item.get("sharding_table", "")
                # 获取需要替换的表名后缀
                sharding_suffix = sharding_logic_item.get("sharding_suffix", "")
                # 获取需要替换的表名前缀
                sharding_prefix = sharding_logic_item.get("sharding_prefix", "")
                # 获取后缀的替换规则
                suffix_rule = sharding_logic_item.get("suffix_rule", [])
                # 获取前缀的替换规则
                prefix_rule = sharding_logic_item.get("prefix_rule", [])
                # 表名替换范围：
                replace_range = sharding_logic_item.get("replace_range", ["table_name","join_table_name"])
                # 分表参数替换前的新表名
                replace_table_name = f"{sharding_prefix}{sharding_table}{sharding_suffix}"
                # 后缀替换规则解析
                suffix_rule_dict = replace_rule_get(suffix_rule)
                # 前缀替换规则解析
                prefix_rule_dict = replace_rule_get(prefix_rule)
                # 将前后缀解析结果合并
                replace_rule_dict={**suffix_rule_dict,**prefix_rule_dict}
                replace_table_name_dict = {"table_name":sharding_table,"replace_table_name":replace_table_name,"replace_range": replace_range,"replace_rule":replace_rule_dict}
                replace_table_name_list.append(replace_table_name_dict)
            # 根据分表规则进行组合匹配
            sharding_rule_analysis_result=sharing_rule_analysis(replace_table_name_list)
            # 根据解析后的替换规则进行查询的配置数据替换后执行查询操作
            for sharding_analysis_item in sharding_rule_analysis_result:
                sharing_query_field_list=replace_table_name_with_rules(query_field_list,sharding_analysis_item,is_replace=False)
                sharing_join_query_list=replace_table_name_with_rules(join_query_list,sharding_analysis_item)
                sharing_key_field_list=replace_table_name_with_rules(key_field_list,sharding_analysis_item)
                query_result_item=monitored_data_query(sharing_query_field_list,sharing_join_query_list,shcema_name,sharing_key_field_list,**kwargs)
                print("查询次数+1")
                query_result.extend(query_result_item)
        return query_result
    except Exception as e:
        # 记录异常信息并重新抛出异常
        logger.error(f"Error occurred: {e}", exc_info=True)
        raise

@exception(logger)
def replace_table_name_with_rules(json_data, replacement_rules, is_replace=True):
    """
    根据规则替换JSON中的值，支持覆盖或新增键值

    :param json_data: 原始JSON数据
    :param replacement_rules: 替换规则列表
    :param mode: 替换模式 'override'(覆盖) 或 'new_key'(新增)
    :return: 替换后的JSON数据
    """

    def process_item(item):
        """
        处理单个字典项

        :param item: 待处理的字典
        :return: 处理后的字典
        """
        # 深拷贝字典以避免修改原始数据
        processed_item = copy.deepcopy(item)

        # 遍历每个替换规则
        for rule in replacement_rules:
            # 获取规则中的替换范围和目标表名
            replace_range = rule.get('replace_range', [])
            target_table_name = rule.get('table_name')
            replace_value = rule.get('replace_value')

            # 递归处理嵌套结构
            def recursive_replace(data):
                if isinstance(data, dict):
                    # 遍历字典中的每个键值对
                    for key, value in list(data.items()):
                        # 检查是否满足替换条件
                        if (key in replace_range and
                                value == target_table_name):

                            # 根据模式选择替换方式
                            if is_replace:
                                # 覆盖原值
                                data[key] = replace_value
                            else:
                                # 新增replace_table_name键
                                data['replace_table_name'] = replace_value

                        # 递归处理嵌套结构
                        if isinstance(value, (dict, list)):
                            recursive_replace(value)

                elif isinstance(data, list):
                    # 对列表中的每个元素递归处理
                    for item in data:
                        recursive_replace(item)

            # 对当前处理的项目应用替换
            recursive_replace(processed_item)

        return processed_item

    # 处理输入数据（支持列表和字典）
    if isinstance(json_data, list):
        return [process_item(item) for item in json_data]
    elif isinstance(json_data, dict):
        return process_item(json_data)
    else:
        return json_data



@exception(logger)
def sharing_rule_analysis(config):
    "根据替换规则解析出需要分表的表名"
    try:
        # 配置格式如下
        # config = [
        #     {
        #         "replace_table_name": "storecode_order_info_seq",
        #         "table_name": "order_info",
        #         "replace_range": ["join_table_name"],
        #         "replace_rule": {"storecode": ["A001", "A002", "A003"], "seq": [1, 2, 3, 4, 5]}
        #     },
        #     {
        #         "replace_table_name": "storecode_order_detail_seq",
        #         "table_name": "order_detail",
        #         "replace_range": ["table_name", "join_table_name"],
        #         "replace_rule": {"storecode": ["A001", "A002", "A003"], "seq": [1, 2, 3, 4, 5]}
        #     }
        # ]

        # 存储每个配置项的所有替换结果
        all_config_replacements = []
        for item in config:
            table_name = item.get("table_name")
            replace_table_name = item.get("replace_table_name")
            replace_range = item.get("replace_range")
            replace_rule = item.get("replace_rule")

            keys = list(replace_rule.keys())
            values_list = list(replace_rule.values())

            # 生成所有可能的组合
            all_combinations = itertools.product(*values_list)

            item_replacements = []
            for combination in all_combinations:
                temp_replace_table_name = replace_table_name
                for i, key in enumerate(keys):
                    temp_replace_table_name = temp_replace_table_name.replace(key, str(combination[i]))

                result = {
                    "table_name": table_name,
                    "replace_value": temp_replace_table_name,
                    "replace_range": replace_range
                }
                item_replacements.append(result)
            all_config_replacements.append(item_replacements)

        # 对所有配置项的替换结果进行组合
        final_results = []
        for combined in itertools.product(*all_config_replacements):
            final_results.append(list(combined))
        # print(final_results)
        return final_results
    except Exception as e:
        logger.error(f"Error occurred: {e}", exc_info=True)
        raise



@exception(logger)
def replace_rule_get(replace_rule):
    "根据替换规则解析出替换关键字和替换值"
    try:
        replace_dict={}
        for replace_rule_item in replace_rule:
            # 获取替换的关键字
            replace_list = []
            # 获取替换关键字
            replace_key = replace_rule_item.get("replace_key", "")
            # 获取替换规则
            sharding_rule = replace_rule_item.get("sharding_rule", [])
            if replace_key and sharding_rule:
                for sharding_rule_item in sharding_rule:
                    rule_type = sharding_rule_item.get("rule_type", "")
                    if rule_type == "date":
                        rule_format = sharding_rule_item.get("rule_format", "")
                        today = datetime.date.today()
                        # 获取最近7天的日期列表
                        for i in range(7):
                            date = today - datetime.timedelta(days=i)
                            replace_list.append(date.strftime(rule_format))
                    if rule_type == "number":
                        start = sharding_rule_item.get("start", 0)
                        end = sharding_rule_item.get("end", 0)
                        step = sharding_rule_item.get("step_len", 0)
                        # 根据开始值和结束值生成数字列表
                        current = start
                        while current <= end:
                            replace_list.append(current)
                            current += step
                    if rule_type == "fixed":
                        value = sharding_rule_item.get("value", [])
                        replace_list.extend(value)
            # list去重
            unique_lst = list(dict.fromkeys(replace_list))
            replace_dict[replace_key] = unique_lst
        return replace_dict
    except Exception as e:
        logger.error(f"Error occurred: {e}", exc_info=True)
        raise


if __name__ == '__main__':
    json_1=[{"join_type": "LEFT JOIN", "table_name": "offline_order", "join_relationship": [{"table_filed": "order_no", "join_table_name": "offline_order_detail", "join_table_filed": "order_no"}]}]
    rule=[{'replace_range': ['table_name', 'join_table_name'], 'replace_value': 'offline_order_2503', 'table_name': 'offline_order'}, {'replace_range': ['table_name', 'join_table_name'], 'replace_value': 'offline_order_detail_202503', 'table_name': 'offline_order_detail'}]
    reuslt=replace_table_name_with_rules(json_1,(rule))
    print(reuslt)


