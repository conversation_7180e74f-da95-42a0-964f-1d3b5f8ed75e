# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/18 14:39
@Auth ： 逗逗的小老鼠
@File ：monitored_data_transformation_preprocessing.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：转换数据预处理
"""
from lib.get_log import logger,exception
from structure.data_monitoring_system.monitoring_data.monitored_data_transformation_assembly import data_transformation_assembly
from structure.data_monitoring_system.monitoring_config.monitored_data_config import monitor_data_transformation_query
from lib.deal_json import json_loads,json_dumps

@exception(logger)
def mq_transformation_preprocessing(message,**kwargs):
    "数据转换预处理"
    try:
        data_group_name = kwargs.get("data_group_name", "")
        # 获取数据转换配置信息
        data_transformation_config = monitor_data_transformation_query()
        config_data = {}
        # 获取本次数据转换匹配的配置信息
        for config_item in data_transformation_config:
            # 配置中数据组的名称
            transformation_group_name = config_item.get("data_group_name", "")
            transformation_data_condition = json_loads(config_item.get("data_condition", []))
            # 数据来源
            transformation_data_srouce = config_item.get("data_source", "")
            if transformation_group_name == data_group_name and transformation_data_srouce == "MQ":
                if transformation_data_condition and isinstance(transformation_data_condition, list):
                    for condition_item in transformation_data_condition:
                        condition_type = condition_item.get("key_name", "")
                        condition_value = condition_item.get("key_value", "")
                        if message.get(condition_type) != condition_value:
                            break
    except Exception as e:
        raise
