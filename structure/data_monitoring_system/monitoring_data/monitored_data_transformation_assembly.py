# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/18 10:45
@Auth ： 逗逗的小老鼠
@File ：monitored_data_transformation_assembly.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：对监控json数据进行组装
"""
from lib.get_log import logger,exception
from lib.deal_json import json_loads,json_dumps
from structure.data_monitoring_system.monitoring_config.monitored_data_config import monitor_data_transformation_query,monitor_data_calculate_query
from lib.deal_jsonpath import data_by_jsonpath
from structure.data_monitoring_system.monitoring_data.monitored_data_assembly import group_data_calculate

def data_transformation_assembly(data,**kwargs):
    "数据转换处理"
    try:
        # 如果数据类型为dict，则将其转换为list
        if isinstance(data, dict):
            data = [data]
        data_group_name = kwargs.get("data_group_name","")
        data_srouce=kwargs.get("data_srouce","")
        # 获取数据转换配置信息
        data_transformation_config = monitor_data_transformation_query()
        transformation_config = {}
        # 获取本次数据转换匹配的配置信息
        for config_item in data_transformation_config:
            # 配置中数据组的名称
            transformation_group_name = config_item.get("data_group_name", "")
            # 数据来源
            transformation_data_srouce = config_item.get("data_srouce", "")
            if transformation_group_name == data_group_name and transformation_data_srouce == data_srouce:
                transformation_config=config_item
                break
        # 如果存在转换配置信息，则进行数据转换
        if transformation_config:
            data_type = transformation_config.get("data_type", "")
            transformation_result=[]
            if isinstance(data,list):
                if data_type=="json":
                    for item in data:
                        transformation_data=data_transformation_json(item,transformation_config,**kwargs)
                        transformation_result.append(transformation_data)
                else:
                    logger.error(f"暂不支持{data_type}的数据类型转换")
                    return []
            else:
                logger.error(f"暂不支持{type(data)}的数据类型转换")
                return []
        else:
            transformation_result=data
        data_calculate_config = monitor_data_calculate_query(**kwargs)
        calculate_config = []
        # 对数据计算配置信息进行预处理
        for data_calculate_config_item in data_calculate_config:
            # 获取数据组名称
            transformation_group_name = data_calculate_config_item.get('data_group_name', "")
            if transformation_group_name == data_group_name:
                calculate_config.append(data_calculate_config_item)
        # 如果存在数据计算配置信息，则进行数据计算
        if calculate_config:
            # 根据数据计算配置信息进行预处理
            group_data_result = group_data_calculate(transformation_result, calculate_config, **kwargs)
        else:
            group_data_result = transformation_result
        return group_data_result
    except Exception as e:
        raise


@exception(logger)
def data_transformation_json(data,transformation_config,**kwargs):
    " json数据转换"
    try:
        result={}
        # 获取主信息字段数据
        master_data_field=json_loads(transformation_config.get("master_data_field",""))
        # 获取非主信息字段数据
        transactional_data_field=json_loads(transformation_config.get("transactional_data_field",""))
        # 获取原始数据字段名称
        raw_data_field_name=transformation_config.get("raw_data_field_name","")
        # 获取是否保留原始数据，未配置则默认为1，保留原始数据
        is_raw_data_kept=transformation_config.get("is_raw_data_kept",1)
        # 判断是否需要保留原始数据
        if is_raw_data_kept==0:
            # 若需要保留原始数据，则需要将原始数据添加到结果中，字段名称存在则取raw_data_field_name，不存在则固定为row_data
            if raw_data_field_name:
                result[raw_data_field_name]=data
            else:
                result["row_data"]=data
        # 主数据提取
        for master_item in master_data_field:
            master_json_path=master_item.get("json_path","")
            master_view_name=master_item.get("view_name","")
            master_filed_value=data_by_jsonpath(data,master_json_path,filed_name=master_view_name)
            result.update(master_filed_value)
        # 非主数据提取
        for transactional_item in transactional_data_field:
            transactional_json_path=transactional_item.get("json_path","")
            transactional_filed_name=transactional_item.get("view_name","")
            transactional_filed_value=data_by_jsonpath(data,transactional_json_path,filed_name=transactional_filed_name)
            result.update(transactional_filed_value)
        return result
    except Exception as e:
        raise

