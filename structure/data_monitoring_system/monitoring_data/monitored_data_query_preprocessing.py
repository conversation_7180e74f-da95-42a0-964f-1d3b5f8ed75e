# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/13 10:58
@Auth ： 逗逗的小老鼠
@File ：monitored_data_query_preprocessing.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：对数据的查询条件进行处理
"""
from lib.get_log import logger, exception
from datetime import datetime, timedelta
from typing import Dict, Tuple, Union
from structure.data_monitoring_system.monitoring_config.monitored_data_config import table_default_time_filed

"""
构建时间条件查询参数。

根据提供的开始时间、结束时间和查询时间长度，构建一个SQL查询需要的时间条件。
如果没有提供开始或结束时间，将根据当前时间生成默认的时间范围。

参数:
- time_value: 时间字段名，用于SQL查询中的时间比较。
- start_time: 查询的开始时间。
- end_time: 查询的结束时间。
- query_time_long: 查询时间长度，用于生成自定义时间范围。

返回:
- 一个格式化的时间条件字符串和一个包含开始时间与结束时间的元组。
"""
def build_time_conditions(table_name: str,filed_name: str, start_time: str, end_time: str, query_time_long: int) -> Tuple[str, Tuple]:
    "构建时间条件查询参数。"
    now = datetime.now()  # 获取当前时间
    if query_time_long == 0:
        # 如果没有提供开始时间，根据当前时间生成默认的开始和结束时间
        if not start_time:
            if not end_time:
                # 如果没有提供结束时间，设置为当前日期的23:59:59
                end_time = now.replace(hour=23, minute=59, second=59, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
                # 如果没有提供开始时间，设置为当前日期的00:00:00
                start_time = now.replace(hour=0, minute=0, second=0, microsecond=0).strftime("%Y-%m-%d %H:%M:%S")
            else:
                # 如果提供了结束时间但没有提供开始时间，将开始时间设置为结束时间的前一天
                formatted_end_time = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                start_time = (formatted_end_time - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
        else:
            # 如果提供了开始时间但没有提供结束时间，将结束时间设置为开始时间的后一天
            if not end_time:
                formatted_start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                end_time = (formatted_start_time + timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
            else:
                # 如果开始时间和结束时间都提供了，将结束时间设置为当前时间
                end_time = now.strftime("%Y-%m-%d %H:%M:%S")
    else:
        # 如果提供了查询时间长度，根据当前时间生成自定义的时间范围
        five_minutes_ago = now - timedelta(minutes=5)
        time_long_ago = five_minutes_ago - timedelta(minutes=query_time_long)
        end_time = five_minutes_ago.strftime("%Y-%m-%d %H:%M:%S")
        start_time = time_long_ago.strftime("%Y-%m-%d %H:%M:%S")

    # 返回格式化的时间条件字符串和包含开始与结束时间的元组
    return f" AND {table_name}.{filed_name} >= %s AND {table_name}.{filed_name} <= %s ", (start_time, end_time)

"""
    根据给定的参数生成查询条件。

    此函数用于根据传入的关键字参数生成数据库查询条件。它主要处理两类查询条件：
    一类是基于特定关键字的查询，另一类是基于时间的查询。函数首先尝试构建基于关键字的查询条件；
    如果没有提供关键字查询条件，则构建基于时间的查询条件。

    参数:
    **kwargs: 包含查询参数的字典，参数可以是字符串或整数类型。

    返回:
    一个字典，包含两个键值对：
    - 'sql_condition': SQL查询条件字符串。
    - 'query_value': 查询值的元组。
    """
@exception(logger)
def monitored_data_query_condition(key_field_list,query_field_list,shcema_name,**kwargs: Dict[str, Union[str, int]]) -> Dict[str, Union[str, Tuple]]:
    "根据给定的参数生成查询条件"
    try:
        # 初始化SQL查询条件和查询值的元组
        sql_condition = ""
        query_value = ()
        key_field_data=monitored_data_key_field_list(shcema_name,key_field_list,query_field_list,**kwargs)
        key_word_list=key_field_data.get("key_word_list",[])
        key_time_list=key_field_data.get("key_time_list",[])


        # 定义关键字字典，用于将查询参数映射到数据库表中的字段
        # key_word_list=[{"table_name": "order_info","filed_name":"id"},{"table_name": "order_info","filed_name":"third_order_no"}]
        # 遍历关键字字典，构建基于关键字的查询条件
        for key_word_item in key_word_list:
            table_name=key_word_item.get("table_name","")
            field_name=key_word_item.get("field_name","")
            if table_name and field_name:
                query_word = kwargs.get(field_name)
                if query_word:
                    # 如果查询参数是列表类型，将其转换为逗号分隔的字符串
                    if isinstance(query_word, list):
                        placeholders = ", ".join(["%s"] * len(query_word))
                        query_value += tuple(query_word)

                    else:
                        placeholders = "%s"
                        # 将查询值添加到查询值的元组中
                        query_value += (str(query_word),)
                    # 将关键字查询条件添加到SQL条件中
                    sql_condition += f" AND {table_name}.{field_name} IN ({placeholders}) "





        # 定义时间查询字典，用于将时间查询参数映射到数据库表中的字段
        # key_time_list = [{"table_name": "order_info", "filed_name": "create"}]
        # 如果没有关键字查询条件，则构建基于时间的查询条件
        for key_time_item in key_time_list:
            table_time_name = key_time_item.get("table_name", "")
            filed_time_name = key_time_item.get("field_name", "")
            if table_time_name and filed_time_name:
                # 获取时间查询参数
                query_time_long = kwargs.get("query_time_long", 0)
                start_time = kwargs.get("start_time","")
                end_time = kwargs.get("end_time","")
                # 将查询时间长度参数转换为整数，如果转换失败，则默认为5
                try:
                    query_time_long = int(query_time_long)
                except:
                    query_time_long = 5

                # 构建时间查询条件
                try:
                    sql_time_condition, query_time_value = build_time_conditions(table_time_name, filed_time_name, start_time, end_time, query_time_long)
                    sql_condition += sql_time_condition
                    query_value += query_time_value
                except ValueError as e:
                    # 如果日期格式无效，抛出异常
                    raise ValueError(f"Invalid date format: {e}")

        # 构建结果字典，包含SQL查询条件和查询值的元组
        result = {"sql_condition": sql_condition, "query_value": query_value}
        return result
    except Exception as e:
        # 记录错误日志，并重新抛出异常
        logger.error(f"Error in monitored_data_query_condition: {e}")
        raise


"""
   根据监控字段配置生成结构化查询字段列表

   将输入的字段配置按类型分类为关键词字段和时间范围字段，用于构建监控数据查询条件

   Args:
       key_field_list (list[dict]): 关键字段配置列表，每个元素应包含：
           - table_name (str): 表名
           - field_name (str): 字段名
           - query_type (str, optional): 字段类型（'date'/'time'会被特殊处理）
       query_field_list (list[dict]): 备选字段列表，当key_field_list为空时使用

   Returns:
       dict: 分类后的字段字典，包含两个键：
           - key_word_list (list): 普通关键词字段列表
           - key_time_list (list): 时间相关字段列表

   Raises:
       ValueError: 当两个参数列表都为空时抛出异常
   """
@exception(logger)
def monitored_data_key_field_list(shcema_name,key_field_list, query_field_list,**kwargs):
    """根据给定的参数生成查询字段列表"""
    try:
        if not query_field_list:
            raise ValueError("query_field_list cannot be empty when key_field_list is empty")

        key_words = []
        key_times = []


        for item in key_field_list:

            replace_table_name = item.get("replace_table_name", "")
            if replace_table_name:
                table_name = replace_table_name
            else:
                table_name = item.get("table_name", "")
            field_name = item.get("field_name", "")
            query_type = item.get("query_type", "")

            def is_time_field(field):
                return (query_type in {'date', 'time'}) if query_type else any(t in field_name for t in ("date", "time"))

            # 使用条件表达式简化分类
            key_times.append({"table_name": table_name, "field_name": field_name}) if is_time_field(item) else key_words.append({"table_name": table_name, "field_name": field_name})

            # if query_type:
            #     if query_type in {"date", "time"}:
            #         key_times.append({"table_name": table_name, "field_name": field_name})
            #     else:
            #         key_words.append({"table_name": table_name, "field_name": field_name})
            # else:
            #     if any(t in field_name for t in ("date", "time")):
            #         key_times.append({"table_name": table_name, "field_name": field_name})
            #     else:
            #         key_words.append({"table_name": table_name, "field_name": field_name})


        # 如果create_time不存在则添加一个查询条件
        if not key_times:
            if "replace_table_name" in query_field_list[0]:
                table_name = query_field_list[0].get("replace_table_name")
            else:
                table_name = query_field_list[0].get("table_name")
            # 获取表中创建时间字段的名称
            field_data=table_default_time_filed(shcema_name,table_name,**kwargs)
            if field_data:
                default_field_name=field_data[0].get("COLUMN_NAME")
                key_times.append({"table_name": table_name, "field_name": default_field_name})
        return {"key_word_list": key_words, "key_time_list": key_times}
    except Exception as e:
        logger.error(f"Error in monitored_data_key_field_list: {e}")
        raise


if __name__ == "__main__":
    platform_codes = ["HAIDIAN", "KE_CHUAN"]

    # 生成占位符（如 PostgreSQL 用 %s，SQLite 用 ?）
    placeholders = ", ".join(["%s"] * len(platform_codes))
    print(placeholders)
    # query_time_long='aaa'
    # third_order_no=['a',"bbb",3,4]
    # id='namemm'
    # print(monitored_data_query_condition(query_time_long=query_time_long, id=id, third_order_no=third_order_no))