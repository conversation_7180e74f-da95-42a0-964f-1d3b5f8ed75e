# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/9 15:58
@Auth ： 逗逗的小老鼠
@File ：order_monitoring_task.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：执行监控任务(该方法已不再维护，建议使用monitord_task.py执行调用)
"""

from lib.get_log import exception,logger
from structure.data_monitoring_system.oms_info.order_info_query import full_order_information_assembly
from structure.data_monitoring_system.oms_verify.order_data_verify import order_data_verify_result
from structure.data_monitoring_system.verify_result.deal_verify_result import monitor_verify_info_insert,monitor_verify_info_query,monitor_verify_detail_query
from lib.deal_json import json_dumps
from datetime import datetime
from structure.handle.handle_webhook import message_wechat_push

@exception(logger)
def order_monitoring_task(**kwargs):
    # 执行任务
    try:
        environment_flag=kwargs.get("environment_flag","test")
        # 监测开始时间
        monitor_start_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        # 获取订单信息
        order_data = full_order_information_assembly(**kwargs)
        if len(order_data)>0:
            verify_result= order_data_verify_result(order_data)
            if len(verify_result)>0:
                monitor_end_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                # 写入结果数据
                verify_result_insert=monitor_verify_info_insert(verify_result,monitor_start_time=monitor_start_time,monitor_end_time=monitor_end_time,environment_flag=environment_flag)
                # 查询结果数据
                verify_info_data=monitor_verify_info_query(**kwargs)
                verify_detail_data=monitor_verify_detail_query(**kwargs)
                result = {"monitor_order_count": len(order_data),"exception_order_count": len(verify_result),"verify_result_insert":verify_result_insert,"monitor_start_time":monitor_start_time}
            else:
                result={"monitor_order_count":len(order_data),"exception_order_count":0,"monitor_start_time":monitor_start_time}
        else:
            result={"monitor_order_count":0,"exception_order_count":0,"monitor_start_time":monitor_start_time}
        message_wechat_push(result,"order_data_verify")
        return result
    except Exception as e:
        raise e





if __name__ == '__main__':


    environment_flag="prod"
    third_order_no="2430021191005521"
    # third_order_no = ["1818227715366323463",'1818199389708808711']

    query_time_long=120
    # start_time="2024-12-09 09:00:00"
    data = {"third_order_no": "2430021191005521","environment_flag": environment_flag}
    result=order_monitoring_task(**data)
    # print(result)
    print(json_dumps(result))