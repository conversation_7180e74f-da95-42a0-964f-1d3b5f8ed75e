# -*- coding: utf-8 -*-
"""
@Time ： 2025/5/9 10:29
@Auth ： 逗逗的小老鼠
@File ：result_statistics_task.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from structure.data_monitoring_system.monitoring_result.monitored_result_statistics import monitored_result_data_statistics
from structure.handle.handle_webhook import message_wechat_push

@exception(logger)
def result_statistics_push_task(**kwargs):
    "对检测结果进行统计"
    try:
        task_name = kwargs.get("data_group_name", "")
        result=monitored_result_data_statistics(**kwargs)
        message_wechat_push(result, task_name)
        return result
    except Exception as e:
        raise e


if __name__ == '__main__':
    data = {"data_group_name": "B2B_order_data", "environment_flag": "prod","query_time_long":288000}
    result=result_statistics_push_task(**data)
    print(result)