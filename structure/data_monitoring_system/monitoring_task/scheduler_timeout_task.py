# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/16 18:12
@Auth ： 逗逗的小老鼠
@File ：scheduler_timeout_task.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：处理存在超时的定时任务
"""
from lib.deal_config_json import read_json_file,write_json_file
from lib.get_log import exception,logger
from structure.handle.handle_scheduler import schedul_task_config_manager
from structure.handle.handle_webhook import message_wechat_push

"""
    自动对方法超时次数达到阈值的定时任务进行停止运行处理
"""
@exception(logger)
def shutdown_timeout_task(**kwargs):
    "关闭超时的定时任务"
    try:
        # print("开始检测定时任务超时情况")
        # 获取超时自动关闭定时任务的配置
        shutdown_timeout_file="timeout_scheduler_task_config.json"
        shutdown_timeout_data= read_json_file(shutdown_timeout_file)
        # 获取方法的超时记录
        func_timeout_file = "func_time_recode.json"
        func_timeout_data= read_json_file(func_timeout_file)
        scheduler_timeout_data={}
        # 将超时方法记录中所有涉及的定时任务记录到定时任务超时记录中
        for task_code,task_record in func_timeout_data.items():
            task_scheduler_name=task_record.get("scheduler_name","")
            taks_timeout_count=task_record.get("timeout_count",0)
            # 订单任务不为空时，进行处理，为空则忽略
            if task_scheduler_name !="" and task_scheduler_name != None:
                if task_scheduler_name in scheduler_timeout_data.keys():
                    task_scheduler_count=scheduler_timeout_data.get(task_scheduler_name,0)
                    task_scheduler_count=task_scheduler_count+taks_timeout_count
                else:
                    task_scheduler_count = taks_timeout_count
                scheduler_timeout_data[task_scheduler_name]=task_scheduler_count
        task_running_flag=0
        msg=""
        # 遍历自动关闭定时任务配置信息
        for shutdown_timeout_name,shutdown_timeout_threshold in shutdown_timeout_data.items():
            for shcheduler_name,scheduler_timeout_count in scheduler_timeout_data.items():
                if shutdown_timeout_name==shcheduler_name:
                    if scheduler_timeout_count>=shutdown_timeout_threshold:
                        task_running_flag=1
                        # 获取当前运行的定时任务
                        task_info=schedul_task_config_manager("get",shcheduler_name)
                        for task_item in task_info:
                            job_name=task_item.get("job_name","")
                            job_id=task_item.get("job_id","")
                            if shcheduler_name == job_name:
                                task_running_flag=2
                                scheduler_task_info={"scheduler_id": job_id, "is_delete": 1}
                                try:
                                    task_remove_result=schedul_task_config_manager("update",scheduler_task_info)
                                    result_msg=task_remove_result.get("msg","")
                                    if result_msg=="任务更新完成":
                                        print(task_remove_result)
                                        msg=f"定时任务【{shcheduler_name}】超时次数【{scheduler_timeout_count}】已达到阈值【{shutdown_timeout_threshold}】，已自动关闭对应的定时任务"
                                        logger.warning(msg)
                                    else:
                                        msg=f"定时任务【{shcheduler_name}】超时次数【{scheduler_timeout_count}】已达到阈值【{shutdown_timeout_threshold}】，自动关闭对应的定时任务失败，失败原因【{result_msg}】"
                                        logger.error(msg)
                                except Exception as e:
                                    msg=f"定时任务【{shcheduler_name}】超时次数【{scheduler_timeout_count}】已达到阈值【{shutdown_timeout_threshold}】，自动关闭对应的定时任务失败，失败原因【{e}】"
                                    logger.error(msg)
                                break
        if task_running_flag==1:
                msg=f"定时任务【{shcheduler_name}】超时次数【{scheduler_timeout_count}】已达到阈值【{shutdown_timeout_threshold}】，但未找到运行的对应任务，请检查定时任务配置是否正确"
                logger.warning(msg)
    except Exception as e:
        msg = f"关闭超时的定时任务处理失败，失败原因【{e}】"
        logger.error(msg)
    finally:
        if msg=="" or msg==None:
            print("未发现超时的定时任务")
            logger.info("未发现执行超过次数超过阈值的定时任务")
            return "未发现执行超过次数超过阈值的定时任务"
        else:
            message_wechat_push(msg, "scheduler_task_auto_stop")



@exception(logger)
def timeout_warning_reset(**kwargs):
    "处理超时预警"
    try:

        scheduler_code=kwargs.get("scheduler_name","")
        func_name=kwargs.get("func_name","")
        # 获取方法的超时记录
        func_timeout_file = "func_time_recode.json"
        func_timeout_data = read_json_file(func_timeout_file)
        # 初始化结果信息
        data={}
        update_msg=[]
        if scheduler_code=="" and func_name=="":
            msg = "未指定定时任务名称【scheduler_name】或方法名称【func_name】，无法进行处理"
            logger.info(msg)
        else:
            func_flag=0
            for task_code,task_record in func_timeout_data.items():
                task_scheduler_name=task_record.get("scheduler_name","")
                taks_timeout_count=task_record.get("timeout_count",0)
                # 如果当前定时任务名称不为空，则根据定时任务处理
                if scheduler_code != "" and scheduler_code != None:
                    if scheduler_code==task_scheduler_name:
                        func_flag = 1
                        reset_timeout_data=0
                        func_timeout_data[task_code]["timeout_count"]=reset_timeout_data
                        msg=(f"定时任务【{scheduler_code}】关联方法【{task_code}】超时次数已由【{taks_timeout_count}】已重置为【{reset_timeout_data}】")
                        logger.warning(msg)
                        update_msg.append(msg)
                        data = write_json_file(func_timeout_file, func_timeout_data)

                if func_name !="" and func_name != None:
                    if func_name==task_code:
                        func_flag = 2
                        reset_timeout_data=0
                        func_timeout_data[task_code]["timeout_count"]=reset_timeout_data
                        msg=(f"方法【{func_name}】超时次数已由【{taks_timeout_count}】已重置为【{reset_timeout_data}】")
                        update_msg.append(task_code)
                        data = write_json_file(func_timeout_file, func_timeout_data)
                        logger.warning(msg)
            if func_flag==0:
                msg = f"未找到任务名称为【{scheduler_code}】或关联方法名称【{func_name}】的超时记录"
                logger.warning(msg)
                update_msg.append(msg)
        result = {"msg": update_msg, "data": data}
        return result
    except Exception as e:
        update_msg=f"处理超时预警失败（重置失败次数），失败原因【{e}】"
        return update_msg
    finally:
        message_wechat_push(update_msg, "scheduler_task_auto_stop")


if __name__=="__main__":
    data=timeout_warning_reset(scheduler_name="order_data_verify")
    print(data)