# -*- coding: utf-8 -*-
"""
@Time ： 2025/1/2 13:42
@Auth ： 逗逗的小老鼠
@File ：weshop_monitoring_task.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：微商城订单数据监控任务(该方法已不再维护，建议使用monitord_task.py执行调用)
"""
from lib.get_log import exception,logger
from lib.deal_json import json_dumps
from datetime import datetime
from structure.handle.handle_webhook import message_wechat_push
from structure.data_monitoring_system.weshop_info.weshop_order_info_query import full_weshop_information_assembly
from structure.data_monitoring_system.weshop_verify.weshop_data_verify import weshop_data_verify_result
from structure.data_monitoring_system.verify_result.deal_verify_result import monitor_verify_info_insert,monitor_verify_info_query,monitor_verify_detail_query


@exception(logger)
def weshop_monitoring_task(**kwargs):
    # 执行任务
    try:
        environment_flag=kwargs.get("environment_flag","test")
        # 监测开始时间
        monitor_start_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        # 获取订单信息
        order_data = full_weshop_information_assembly(**kwargs)
        if len(order_data)>0:
            verify_result= weshop_data_verify_result(order_data)
            if len(verify_result)>0:
                monitor_end_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                # 写入结果数据
                verify_result_insert=monitor_verify_info_insert(verify_result,monitor_start_time=monitor_start_time,monitor_end_time=monitor_end_time,environment_flag=environment_flag)
                # 查询结果数据
                verify_info_data=monitor_verify_info_query(**kwargs)
                verify_detail_data=monitor_verify_detail_query(**kwargs)
                result = {"monitor_order_count": len(order_data),"exception_order_count": len(verify_result),"verify_result_insert":verify_result_insert,"monitor_start_time":monitor_start_time}
            else:
                result={"monitor_order_count":len(order_data),"exception_order_count":0,"monitor_start_time":monitor_start_time}
        else:
            result={"monitor_order_count":0,"exception_order_count":0,"monitor_start_time":monitor_start_time}
        message_wechat_push(result,"order_data_verify")
        return result
    except Exception as e:
        raise e



if __name__ == '__main__':
    environment_flag="test"
    order_no="1820118975930294793"
    data = {"order_no": order_no, "environment_flag": environment_flag}
    result=weshop_monitoring_task(**data)
    # print(result)
    print(json_dumps(result))
