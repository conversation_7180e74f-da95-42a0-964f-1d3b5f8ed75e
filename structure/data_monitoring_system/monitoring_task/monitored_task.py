# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/21 17:55
@Auth ： 逗逗的小老鼠
@File ：monitored_task.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：监控任务执行
"""

from lib.get_log import exception,logger
from structure.data_monitoring_system.monitoring_data.monitored_data_assembly import data_assembly
from structure.data_monitoring_system.monitoring_verify.monitored_data_verify import monitor_data_verify_result
from structure.data_monitoring_system.monitoring_result.monitored_result_manipulate import monitor_result_data_insert,monitor_result_info_query,monitor_result_detail_query
from lib.deal_json import json_dumps
from datetime import datetime
from structure.handle.handle_webhook import message_wechat_push

@exception(logger)
def data_monitoring_task(**kwargs):
    # 执行任务
    try:
        environment_flag=kwargs.get("environment_flag","test")
        data_group_name=kwargs.get("data_group_name","")
        task_name=kwargs.get("task_name","")
        # 监测开始时间
        monitor_start_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        # 获取订单信息
        group_data = data_assembly(**kwargs)
        if len(group_data)>0:
            print(f"开始校验数据：{datetime.now()}")
            verify_result= monitor_data_verify_result(group_data,**kwargs)
            print(f"校验数据完成：{datetime.now()}")
            monitor_end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if len(verify_result)>0:
                # 写入结果数据
                print(f"开始写入数据：{datetime.now()}")
                association_id,verify_result_insert=monitor_result_data_insert(verify_result,data_group_name,monitor_start_time=monitor_start_time,monitor_end_time=monitor_end_time,environment_flag=environment_flag)
                # 查询结果数据
                print(f"写入数据完成：{datetime.now()}")
                verify_info_data=monitor_result_info_query(**{"association_id":association_id})
                verify_detail_data=monitor_result_detail_query(**{"association_id":association_id})
                exception_rate=len(verify_info_data)/len(group_data)*100
                detail_exception_rate=len(verify_detail_data)/len(verify_info_data)*100
                if exception_rate>=50:
                    message=f"🚨警告：数据异常率已达到{exception_rate:.2f}%，请重点关注！"
                elif exception_rate>=10:
                    message=f"⚠️警告：数据异常率已达到{exception_rate:.2f}%，请及时关注！"
                else:
                    message="💡提醒：数据存在异常，请注意关注！"
                    if detail_exception_rate>=150:
                        message=f"🚨警告：单条数据异常率已达到{detail_exception_rate:.2f}%，请及时关注！"
                result = {"environment_flag":environment_flag,"task_name":task_name,"message":message,"monitor_data_count": len(group_data),"exception_data_count": len(verify_result),"exception_count":len(verify_detail_data),"verify_result_insert":verify_result_insert,"monitor_start_time":monitor_start_time,"monitor_end_time":monitor_end_time}
            else:
                result={"environment_flag":environment_flag,"task_name":task_name,"message":"🎆本次检测未发现异常数据","monitor_data_count":len(group_data),"exception_data_count":0,"exception_count":0,"monitor_start_time":monitor_start_time,"monitor_end_time":monitor_end_time}
        else:
            result={"environment_flag":environment_flag,"task_name":task_name,"message":"⚠️本次检测未查询到检测数据","monitor_data_count":0,"exception_data_count":0,"exception_count":0,"monitor_start_time":monitor_start_time,"monitor_end_time":monitor_start_time}
        message_wechat_push(result,data_group_name)
        return result
    except Exception as e:
        raise e


if __name__ == '__main__':
    environment_flag="prod"
    order_no="1824656935737871113"
    data_group_name="B2B_order_data"
    data = {"task_name":"B2B数据值检测","environment_flag": environment_flag,"data_group_name":data_group_name,"third_platform_code":"101","start_time":"2025-05-06 00:00:00","end_time":"2025-05-07 00:00:00"}
    result=data_monitoring_task(**data)
    # print(result)
    print(json_dumps(result))