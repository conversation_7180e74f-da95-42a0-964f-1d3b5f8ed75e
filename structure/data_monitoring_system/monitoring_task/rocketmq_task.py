# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/17 17:16
@Auth ： 逗逗的小老鼠
@File ：rocketmq_task.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：rocketmq消息任务
"""

from lib.get_log import logger,exception
from lib.deal_ThreadPool import combined_decorator
import time
from structure.data_monitoring_system.monitoring_data.monitored_data_transformation_preprocessing import data_transformation_assembly
from structure.data_monitoring_system.monitoring_verify.monitored_data_verify import monitor_data_verify_result
from structure.data_monitoring_system.monitoring_result.monitored_result_manipulate import monitor_result_data_insert,monitor_result_info_query,monitor_result_detail_query
import json
from datetime import datetime
from structure.handle.handle_webhook import message_wechat_push
from rocketmq.client import Message

@exception(logger)
@combined_decorator(max_workers=15,timeout_seconds=3600)
def rocketmq_message_task(message,**kwargs):
    try:
        # print("消息任务已经开始执行")
        # 监测开始时间
        monitor_start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        environment_flag = kwargs.get("environment_flag", "test")
        data_group_name = kwargs.get("data_group_name", "")
        task_name = kwargs.get("data_group_name", "")
        message_id = 0
        if kwargs.get("message_body"):
            message_body=kwargs.get("message_body")
        else:
            message_id=message.id
            try:
                # 关键步骤：用 UTF-8 解码字节数组
                body_str = message.body.decode('utf-8')
                message_body= json.loads(body_str)
            except UnicodeDecodeError:
                result = {"environment_flag": environment_flag, "task_name": task_name,
                          "message": f"⚠️ID：{message_id}，消息解码失败！原始字节: {message.body.hex()}", "monitor_data_count": 0, "exception_data_count": 0,
                          "exception_count": 0, "monitor_start_time": monitor_start_time,
                          "monitor_end_time": monitor_start_time}
                message_wechat_push(result, data_group_name)
                return result
            except json.JSONDecodeError:
                result = {"environment_flag": environment_flag, "task_name": task_name,
                          "message": f"⚠️ID：{message_id}，非法 JSON 格式！原始字节: {message.body.hex()}",
                          "monitor_data_count": 0, "exception_data_count": 0,
                          "exception_count": 0, "monitor_start_time": monitor_start_time,
                          "monitor_end_time": monitor_start_time}
                message_wechat_push(result, data_group_name)
                return result
        # 进行mq消息数据转换
        # message_body=json.loads(message.decode("utf-8"))
        group_data = data_transformation_assembly(message_body,**kwargs)
        # print(group_data)
        if len(group_data) > 0:
            # print(f"开始校验数据：{datetime.now()}")
            verify_result = monitor_data_verify_result(group_data, **kwargs)
            # print(verify_result)
            # print(f"校验数据完成：{datetime.now()}")
            monitor_end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if len(verify_result) > 0:
                # 写入结果数据
                # print(f"开始写入数据：{datetime.now()}")
                association_id, verify_result_insert = monitor_result_data_insert(verify_result, data_group_name,
                                                                                  monitor_start_time=monitor_start_time,
                                                                                  monitor_end_time=monitor_end_time,
                                                                                  environment_flag=environment_flag)
                # 查询结果数据
                # print(f"写入数据完成：{datetime.now()}")
                verify_info_data = monitor_result_info_query(**{"association_id": association_id})
                verify_detail_data = monitor_result_detail_query(**{"association_id": association_id})
                exception_rate = len(verify_info_data) / len(group_data) * 100
                detail_exception_rate = len(verify_detail_data) / len(verify_info_data) * 100
                if exception_rate >= 50:
                    message = f"🚨警告：数据异常率已达到{exception_rate:.2f}%，请重点关注！"
                elif exception_rate >= 10:
                    message = f"⚠️警告：数据异常率已达到{exception_rate:.2f}%，请及时关注！"
                else:
                    message = "💡提醒：数据存在异常，请注意关注！"
                    if detail_exception_rate >= 150:
                        message = f"🚨警告：单条数据异常率已达到{detail_exception_rate:.2f}%，请及时关注！"
                result = {"environment_flag": environment_flag, "task_name": task_name, "message": message,
                          "monitor_data_count": len(group_data), "exception_data_count": len(verify_result),
                          "exception_count": len(verify_detail_data), "verify_result_insert": verify_result_insert,
                          "monitor_start_time": monitor_start_time, "monitor_end_time": monitor_end_time}
            else:
                result = {"environment_flag": environment_flag, "task_name": task_name,
                          "message": "🎆本次检测未发现异常数据", "monitor_data_count": len(group_data),
                          "exception_data_count": 0, "exception_count": 0, "monitor_start_time": monitor_start_time,
                          "monitor_end_time": monitor_end_time}
        else:
            result = {"environment_flag": environment_flag, "task_name": task_name,
                      "message": "⚠️本次检测未查询到检测数据", "monitor_data_count": 0, "exception_data_count": 0,
                      "exception_count": 0, "monitor_start_time": monitor_start_time,
                      "monitor_end_time": monitor_start_time}
        # message_wechat_push(result, data_group_name)
        return True
    except Exception as e:
        raise


# if __name__ == '__main__':
#     message = Message("TP_ORDER_OFFLINE_ORDER-DATA")
#     message.set_keys = "1234567890"
#     msg = """
#         {
#     "baseOrderInfo": {
#         "orderNo": {
#             "orderNo": "2566773001525852505"
#         },
#         "parentOrderNo": null,
#         "thirdPlatformCodeValue": "HAIDIAN",
#         "thirdOrderNo": {
#             "thirdOrderNo": "1025050600000006"
#         },
#         "parentThirdOrderNo": null,
#         "dayNum": "",
#         "orderStateValue": "DONE",
#         "created": "2025-05-06 11:35:28",
#         "payTime": "2025-05-06 11:35:28",
#         "completeTime": "2025-05-06 11:35:28",
#         "billTime": "2025-05-06 11:35:28",
#         "dataVersion": 1,
#         "actualPayAmount": 179.000000,
#         "actualCollectAmount": 179.000000,
#         "orderCouponList": [],
#         "serialNo": null,
#         "isOnPromotion": false
#     },
#     "basePrescriptionInfo": {
#         "prescriptionTypeValue": null,
#         "prescriptionNo": null
#     },
#     "payInfoList": [
#         {
#             "payType": {
#                 "payType": "1"
#             },
#             "payName": "现金",
#             "payAmount": 29.000000
#         },
#         {
#             "payType": {
#                 "payType": "107"
#             },
#             "payName": "市医保歀",
#             "payAmount": 150.000000
#         }
#     ],
#     "orderDetailList": [
#         {
#             "baseOrderDetailInfo": {
#                 "orderNo": {
#                     "orderNo": "2566773001525852505"
#                 },
#                 "rowNo": "3",
#                 "platformSkuId": {
#                     "platformSkuId": "100631"
#                 },
#                 "erpCode": {
#                     "erpCode": "100631"
#                 },
#                 "erpName": "阿奇霉素干混悬剂_希舒美_0.1G*6袋",
#                 "commodityCount": 1.000000,
#                 "statusValue": "NORMAL",
#                 "giftTypeValue": "NOT_GIFT",
#                 "originalPrice": 20.000000,
#                 "price": 20.000000,
#                 "commodityCostPrice": 28.618045,
#                 "totalAmount": 20.000000,
#                 "totalOriginalAmount": 20.000000000000,
#                 "discountShare": 0.000000,
#                 "discountAmount": 0.000000,
#                 "billPrice": 20.000000,
#                 "billAmount": 20.000000,
#                 "isOnPromotion": false,
#                 "fiveClass": "A041304002"
#             },
#             "pickInfoList": [
#                 {
#                     "erpCode": {
#                         "erpCode": "100631"
#                     },
#                     "makeNo": {
#                         "makeNo": "FR7988"
#                     },
#                     "count": 1.000000
#                 }
#             ]
#         },
#         {
#             "baseOrderDetailInfo": {
#                 "orderNo": {
#                     "orderNo": "2566773001525852505"
#                 },
#                 "rowNo": "1",
#                 "platformSkuId": {
#                     "platformSkuId": "100724"
#                 },
#                 "erpCode": {
#                     "erpCode": "100724"
#                 },
#                 "erpName": "云南白药_(4G*1瓶+保险子1粒)*1瓶_云南白药集团",
#                 "commodityCount": 1.000000,
#                 "statusValue": "NORMAL",
#                 "giftTypeValue": "NOT_GIFT",
#                 "originalPrice": 53.000000,
#                 "price": 53.000000,
#                 "commodityCostPrice": 14.863481,
#                 "totalAmount": 53.000000,
#                 "totalOriginalAmount": 53.000000000000,
#                 "discountShare": 0.000000,
#                 "discountAmount": 0.000000,
#                 "billPrice": 53.000000,
#                 "billAmount": 53.000000,
#                 "isOnPromotion": false,
#                 "fiveClass": "A041304002"
#             },
#             "pickInfoList": [
#                 {
#                     "erpCode": {
#                         "erpCode": "100724"
#                     },
#                     "makeNo": {
#                         "makeNo": "F22221"
#                     },
#                     "count": 1.000000
#                 }
#             ]
#         },
#         {
#             "baseOrderDetailInfo": {
#                 "orderNo": {
#                     "orderNo": "2566773001525852505"
#                 },
#                 "rowNo": "2",
#                 "platformSkuId": {
#                     "platformSkuId": "100724"
#                 },
#                 "erpCode": {
#                     "erpCode": "100724"
#                 },
#                 "erpName": "云南白药_(4G*1瓶+保险子1粒)*1瓶_云南白药集团",
#                 "commodityCount": 2.000000,
#                 "statusValue": "NORMAL",
#                 "giftTypeValue": "NOT_GIFT",
#                 "originalPrice": 53.000000,
#                 "price": 53.000000,
#                 "commodityCostPrice": 14.863481,
#                 "totalAmount": 106.000000,
#                 "totalOriginalAmount": 106.000000000000,
#                 "discountShare": 0.000000,
#                 "discountAmount": 0.000000,
#                 "billPrice": 53.000000,
#                 "billAmount": 106.000000,
#                 "isOnPromotion": false,
#                 "fiveClass": "A041304002"
#             },
#             "pickInfoList": [
#                 {
#                     "erpCode": {
#                         "erpCode": "100724"
#                     },
#                     "makeNo": {
#                         "makeNo": "20240520"
#                     },
#                     "count": 2.000000
#                 }
#             ]
#         }
#     ],
#     "baseUserInfo": null,
#     "baseOrganizationInfo": {
#         "storeCode": "H812",
#         "storeName": "[H812]一心堂荣县旭水大道店",
#         "companyCode": "1006",
#         "companyName": "四川一心堂医药连锁有限公司(测试)",
#         "storeDirectJoinTypeValue": "DIRECT_SALES"
#     },
#     "baseCashierDeskInfo": {
#         "posCashierDeskNo": {
#             "posCashierDeskNo": "101"
#         },
#         "cashier": "1153740",
#         "cashierName": "杨明杰",
#         "picker": null,
#         "pickerName": null,
#         "shiftId": {
#             "shiftId": "3"
#         },
#         "shiftDate": "2025-05-06 11:35:28"
#     },
#     "medInsSettle": null,
#     "orderPromotionList": [],
#     "orderCouponList": []
# }
#
#     """.encode('utf-8')
#     message.topic = "TP_ORDER_OFFLINE_ORDER-DATA",
#     data_srouce = "RocketMQ"
#     rocketmq_message_task(msg, data_group_name="TP_ORDER_OFFLINE_ORDER-DATA_TAG_CREATED", data_srouce=data_srouce)
