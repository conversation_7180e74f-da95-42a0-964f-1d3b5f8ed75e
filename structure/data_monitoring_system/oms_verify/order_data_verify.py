# -*- coding: utf-8 -*-
"""
@Time ： 2024/10/8 11:31
@Auth ： 逗逗的小老鼠
@File ：order_data_verify.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from structure.data_monitoring_system.oms_calculate.order_pay_calculate import order_pay_info_calculate,order_detail_info_calculate
from structure.data_monitoring_system.oms_verify.order_amount_verify import order_pay_amount_verify,order_detail_amount_verify,order_detail_pay_verify

@exception(logger)
def order_data_verify_result(full_order_information_list):
    try:
        logger.info('开始执行订单数据校验')
        result=[]
        for third_order_item in full_order_information_list:
            third_order_no=third_order_item['third_order_no']
            service_mode=third_order_item['service_mode']
            order_information=third_order_item['order_information']
            # 判断赔付手工单和配送单
            if not (third_order_no.startswith("P")) and not(third_order_no.endswith("A")):
                if service_mode=='B2C':
                    order_info_key='oms_order_info'
                else:
                    order_info_key='order_info'
                for order_info_item in order_information:
                    exception_list=[]
                    # 订单基本信息
                    order_info=order_info_item.get(order_info_key,{})
                    # 订单详情
                    order_detail=order_info_item.get('order_detail',[])
                    # 订单的支付信息
                    order_pay = order_info_item.get('order_pay',[])
                    # 订单的地址信息
                    order_address_data=order_info_item.get('order_address_data',[])
                    # 获取订单的系统单号
                    order_no=order_info.get('order_no','0')
                    # 获取订单OMS单号，仅B2C存在
                    oms_order_no=order_info.get('oms_order_no','0')
                    # 获取订单的第三方平台码
                    third_platform_code=order_info.get('third_platform_code','27')
                    # 是否邮费单
                    is_post_fee_order=order_info.get('is_post_fee_order',0)
                    if is_post_fee_order==1:
                        break
                    # 对订单的支付信息进行汇总计算处理
                    order_pay_calc=order_pay_info_calculate(order_pay)
                    # 对订单的商品详情信息进行汇总计算处理
                    order_detail_calc=order_detail_info_calculate(order_detail)
                    # 对订单的支付信息进行校验
                    order_pay_amount_verify_result=order_pay_amount_verify(order_pay_calc,third_platform_code=third_platform_code,service_mode=service_mode)
                    if order_pay_amount_verify_result:
                        exception_list.extend(order_pay_amount_verify_result)
                    # 对订单的商品详情信息进行校验
                    order_detail_amount_verify_result=order_detail_amount_verify(order_detail,third_platform_code=third_platform_code,service_mode=service_mode)
                    if order_detail_amount_verify_result:
                        exception_list.extend(order_detail_amount_verify_result)
                    # 对订单的商品财务详情信息进行校验
                    order_detail_pay_verify_result=order_detail_pay_verify(order_pay_calc,order_detail_calc,third_platform_code=third_platform_code,service_mode=service_mode)
                    if order_detail_pay_verify_result:
                        exception_list.extend(order_detail_pay_verify_result)
                    # 如果存在异常，则组装数据
                    if len(exception_list)>0:
                        oms_exception_dict={"order_no":order_no,"oms_order_no":oms_order_no,"third_order_no":third_order_no,"service_mode":service_mode,"third_platform_code":third_platform_code,"exception_list":exception_list}
                        result.append(oms_exception_dict)
        return result
    except Exception as e:
        raise e


if __name__=='__main__':
    sta1="1"
    sta2="1"
    print(sta1==sta2)