# -*- coding: utf-8 -*-
"""
@Time ： 2024/10/8 11:30
@Auth ： 逗逗的小老鼠
@File ：order_amount_verify.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from datetime import datetime
from structure.data_monitoring_system.monitoring_verify.monitoring_amount_verify import order_amount_verify



@exception(logger)
def order_pay_amount_verify(order_pay_calc,**kwargs):
    "订单财务金额校验"
    try:
        verification_code_value="order_pay_verify"
        pay_amount_verify=order_amount_verify(order_pay_calc,verification_code_value,**kwargs)
        return pay_amount_verify
    except Exception as e:
        raise e


@exception(logger)
def order_detail_amount_verify(order_detail,**kwargs):
    "订单明细金额校验"
    try:
        detail_amount_verify_result=[]
        verification_code_value = "order_detail_verify"
        # 循环调用
        for order_item in order_detail:
            detail_amount_verify=order_amount_verify(order_item,verification_code_value,**kwargs)
            detail_amount_verify_result.extend(detail_amount_verify)
        return detail_amount_verify_result
    except Exception as e:
        raise e

@exception(logger)
def order_detail_pay_verify(order_pay_calc,detail_calc_reslt,**kwargs):
    "订单明细财务信息校验"
    try:
        verification_code_value = "order_detail_pay_verify"
        merged_calc={}
        for key,value in detail_calc_reslt.items():
            merged_calc[f"detail_{key}"]=value
        for key,value in order_pay_calc.items():
            merged_calc[f"pay_{key}"]=value
        third_platform_code=kwargs.get('third_platform_code','27')
        service_mode=kwargs.get('service_mode','O2O')
        detail_pay_verify=order_amount_verify(merged_calc,verification_code_value,**kwargs)
        return detail_pay_verify
    except Exception as e:
        raise e



if __name__ == '__main__':
    calc_pay={'pay_id': 3032977, 'order_no': 1813034781630218501, 'pay_status': '1', 'pay_channel': 0, 'buyer_actual_amount': 24.0, 'merchant_actual_amount': 24.0, 'total_amount': 25.0, 'total_discount': 1.0, 'merchant_total_discount_sum': 0.0, 'merchant_total_discount_sum_not_delivery_fee': 1.0, 'merchant_discount_sum': 1.0, 'discount_fee_dtl': 0.0, 'platform_discount': 0.0, 'post_fee_discount': 0.0, 'merchant_delivery_fee': 0.0, 'merchant_delivery_fee_discount': 0.0, 'merchant_pack_fee': 0.0, 'platform_delivery_fee': 0.0, 'platform_delivery_fee_discount': 0.0, 'platform_pack_fee': 0.0, 'buyer_cod_service_fee': 0.0, 'seller_cod_service_fee': 0.0, 'brokerage_amount': 0.0, 'buyer_cod_amount': 0.0, 'platform_fee_collection': 0.0, 'manual_fix_amount': 0.0, 'detail_discount_collect': 0.0, 'different_amount': 0.0, 'create_time': '2024-10-16T10:11:59', 'modify_time': '2024-10-16T10:11:59', 'pay_code': '809080', 'delivery_fee': 0.0, 'pack_fee': 0.0, 'oms_order_no': 1813034795434721797, 'remain_brokerage_amount': 0.0, 'health_num': 0, 'health_value': 0, 'warehouse_agency_fee': 0.0, 'apportion_type': 0, 'medicare_amount': 0.0, 'medicare_order_id': 0, 'pay_sale_info': 0, 'total_discount_sum_not_delivery_fee': 0, 'accounts_receivable': 0, 'platform_discount_sum_not_delivery_fee': 0}
    # print(json_dumps(calc_pay))
    create_time="2024-12-16 17:35:00"
    pay_time="2024-12-16 17:35:52"
    time_formula="(create_time-pay_time).total_seconds()"
    order_info={'oms_order_no':1813034795434721797,"create_time":datetime.strptime(create_time,"%Y-%m-%d %H:%M:%S"),"pay_time":datetime.strptime(pay_time,"%Y-%m-%d %H:%M:%S")}
    # diff_time=safe_eval(time_formula,order_info)
    # print(diff_time)



    # order_pay_amount_verify=order_pay_amount_verify(calc_pay)
    # print(json_dumps(order_pay_amount_verify))




