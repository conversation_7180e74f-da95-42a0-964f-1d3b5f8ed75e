```mermaid
graph TD
    classDef process fill:#333333,stroke:#73A6FF,stroke-width:2px,color:#FFFFFF,font-family:Arial;
    A([检测任务启动]):::process --> B([数据处理]):::process
    subgraph 数据的查询处理及计算处理
    B --> C([根据data_group_name获取数据查询的配置信息]):::process
    C --> D([根据配置信息查询数据]):::process
    D --> E([将查询结果按照主副数据进行组装]):::process
    E --> F([获取数据计算配置信息]):::process
    F --> G{判断是否需要进行数据过滤}:::process
    G --> |是| H([执行数据过滤]):::process
    H --> I{判断数据计算方式}:::process
    G --> |否| I:::process
    I --> |聚合| J([执行数据聚合计算]):::process
    I --> |合并| K([执行数据合并]):::process
    J --> L([对源数据和计算后数据进行整合组装]):::process
    K --> L:::process
    end
    subgraph 数据检测
    L --> M([获取数据检测规则]):::process
    M --> N([根据规则进行数据检测]):::process
    end
    subgraph 结果数据处理及存储
    N --> MI{是否存在异常结果}:::process
    MI --> |是| O([将检测结果进行数据处理]):::process
    O --> P([将处理结果进行数据存储]):::process
    end
    P --> R([发送企微通知]):::process
    MI --> |否| R:::process
    R --> Q([检测任务结束]):::process
```