# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/20 15:28
@Auth ： 逗逗的小老鼠
@File ：monitored_data_rule_validation.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：根据规则进行数据校验
"""

from lib.get_log import exception,logger
from decimal import Decimal, getcontext, ROUND_HALF_UP
import decimal
import ast
import operator
import re

# 设置Decimal精度为28位，足够处理大多数金融计算
getcontext().prec = 28
getcontext().rounding = ROUND_HALF_UP


@exception(logger)
def data_rule_validation(validation_data,validation_rule,**kwargs):
    "根据校验公式进行数据校验"
    try:
        rule_verify_result=[]
        rule_id = validation_rule.get('id')
        # 验证代码
        verification_code = validation_rule.get('verification_code')
        # 验证目的
        verification_purpose = validation_rule.get('verification_purpose')
        # 目标字段
        target_field = validation_rule.get('target_field')
        # 目标公式
        target_formula = validation_rule.get('target_formula')
        # 判断关系
        judge_relationship = validation_rule.get('judge_relationship')
        # 计算字段
        calculated_field = validation_rule.get('calculated_field')
        # 计算公式
        calculation_formula = validation_rule.get('calculation_formula')
        # 异常代码
        exception_code = validation_rule.get('exception_code')
        # 异常简述
        exception_brief = validation_rule.get('exception_brief')
        # 异常说明
        exception_description = validation_rule.get('exception_description')
        # 异常等级
        exception_level = validation_rule.get('exception_level')
        # 适用场景
        applicable_scenarios = validation_rule.get('applicable_scenarios')
        # 使用模式
        service_model = validation_rule.get('service_model')
        # 适用平台
        third_platform_code = validation_rule.get('third_platform_code')
        # 允许误差范围
        allowable_error_range = validation_rule.get('allowable_error_range')
        for validation_data_item in validation_data:
            # print("进入目标规则:"+str(rule_id))
            special_identifier=""
            """
                target_formula：要计算的字符串表达式。
                {"__builtins__": None}：一个字典，用于限制eval()可以访问的内置函数和对象。在这里，我们将__builtins__设置为None，以禁用所有内置函数和对象的访问。这是一种提高安全性的做法，但请注意，即使这样做了，eval()仍然可以执行一些潜在的危险操作（比如通过字符串拼接构造新的函数名并调用它们）。
                order_pay_calc：一个字典，包含了要在表达式中使用的变量及其值。
            """
            # 目标计算结果值
            # target_result=eval(target_formula,{"__builtins__":None},order_amount_calc)
            target_result=safe_eval(target_formula,validation_data_item)
            # 实际计算结束值
            # calculated_result=eval(calculation_formula,{"__builtins__":None},order_amount_calc)
            calculated_result=safe_eval(calculation_formula,validation_data_item)
            # 对比关系字典
            judge_relationship_dict={"target_result":target_result,"calculated_result":calculated_result}
            # 比对关系公式
            judge_relationship_formula=f"{target_result} {judge_relationship} {calculated_result}"
            # 比对结果
            verify_result=safe_eval(judge_relationship_formula,judge_relationship_dict)

            if not verify_result:
                if can_convert_to_decimal(allowable_error_range):
                    try:
                        # 直接将值转换为Decimal类型，避免格式化字符串引入的舍入误差
                        # 如果已经是Decimal类型则不需要转换
                        target_decimal = target_result if isinstance(target_result, decimal.Decimal) else decimal.Decimal(str(target_result))
                        calculated_decimal = calculated_result if isinstance(calculated_result, decimal.Decimal) else decimal.Decimal(str(calculated_result))

                        # 计算绝对误差
                        error_value = abs(target_decimal - calculated_decimal)

                        # 将允许误差范围转换为Decimal进行比较
                        allowable_error_decimal = decimal.Decimal(str(allowable_error_range))

                        # 判断误差是否在允许范围内
                        if allowable_error_decimal >= error_value:
                            special_identifier = "精度问题"
                            exception_level = "waring"

                        # 记录详细的精度比较信息，便于调试
                        logger.debug(f"精度比较: 目标值={target_decimal}, 计算值={calculated_decimal}, 误差={error_value}, 允许误差={allowable_error_decimal}")
                    except Exception as e:
                        logger.error(f"精度判断失败，判断值为{target_result}，{calculated_result}，异常信息为：{e}")
                else:
                    allowed_error_flag=False
                    if can_convert_to_list(allowable_error_range):
                        if target_result in list(allowable_error_range):
                            allowed_error_flag = True
                    else:
                        if str(target_result) == str(allowable_error_range):
                            allowed_error_flag = True
                            special_identifier = "误差值范围内"
                            exception_level = "waring"
                    if allowed_error_flag == True:
                        special_identifier = "误差值范围内"
                        exception_level = "waring"
                # 将错误描述中的变量替换为实际值
                exception_expression = exception_description
                for key,value in validation_data_item.items():
                    exception_description=exception_description.replace(f"【{key}】",f"【{value}】")
                err_result={
                    'verification_code':verification_code,
                    'verification_purpose':verification_purpose,
                    'exception_expression':exception_expression,
                    'exception_code':exception_code,
                    'exception_brief':exception_brief,
                    'special_identifier':special_identifier,
                    'exception_description':exception_description,
                    'exception_level':exception_level}
                rule_verify_result.append(err_result)

        return rule_verify_result
    except Exception as e:
        raise e



# 定义安全的数学运算环境，使用Decimal确保精度
_SAFE_MATH_ENV = {
    "__builtins__": None,  # 禁用内置函数，提高安全性
    "Decimal": Decimal,   # 允许使用Decimal进行精确计算
    "abs": abs,          # 允许使用abs函数
    "round": round,      # 允许使用round函数
    "+": operator.add,
    "-": operator.sub,
    "*": operator.mul,
    "/": operator.truediv,
    "//": operator.floordiv,
    "%": operator.mod,
    "**": operator.pow,
    "<": operator.lt,
    ">": operator.gt,
    "<=": operator.le,
    ">=": operator.ge,
    "==": operator.eq,
    "!=": operator.ne,
}

@exception(logger)
def safe_eval(calculation_formula, context):
    """表达式执行器 - 使用Decimal进行高精度计算

    Args:
        calculation_formula: 要计算的表达式字符串
        context: 包含表达式中变量的字典

    Returns:
        计算结果，如果计算失败则返回None
    """
    try:
        # 检查表达式是否为空或None
        if not calculation_formula:
            return None

        # 解析表达式语法树，检查变量
        tree = ast.parse(calculation_formula, mode='eval')
        for node in ast.walk(tree):
            if isinstance(node, ast.Name):
                name = node.id
                if name in context and context[name] is None:
                    return None

        # 创建一个新的上下文，将数值转换为Decimal以确保精度
        decimal_context = {}
        for key, value in context.items():
            if value is None:
                decimal_context[key] = None
            elif isinstance(value, (int, float, str)) and not isinstance(value, bool):
                try:
                    # 尝试将数值转换为Decimal
                    decimal_context[key] = Decimal(str(value))
                except (decimal.InvalidOperation, ValueError):
                    # 如果无法转换为Decimal，保留原始值
                    decimal_context[key] = value
            else:
                decimal_context[key] = value

        # 替换比较运算符，确保在表达式中正确处理
        formula = calculation_formula
        # 确保比较运算符周围有空格，以便正确解析
        formula = re.sub(r'([<>=!]+)', r' \1 ', formula)

        # 执行表达式计算
        result = eval(formula, _SAFE_MATH_ENV, decimal_context)

        # 如果结果是Decimal类型，转换为float以便于后续处理
        if isinstance(result, Decimal):
            # 保留足够的精度，避免舍入误差
            return float(result)
        return result

    except Exception as e:
        # 如果执行失败，记录错误并返回None
        logger.error(f"Error evaluating formula: {e}, calculation_formula: {calculation_formula}, context: {context}")
        return None

@exception(logger)
def can_convert_to_decimal(value):
    """判断是否可以转换为Decimal类型

    Args:
        value: 要检查的值

    Returns:
        bool: 如果可以转换为Decimal返回True，否则返回False
    """
    if value is None:
        return False

    try:
        # 先转换为字符串，再转换为Decimal，提高兼容性
        decimal.Decimal(str(value))
        return True
    except (decimal.InvalidOperation, ValueError, TypeError):
        return False
    except Exception as e:
        logger.debug(f"Unexpected error in can_convert_to_decimal: {e}")
        return False

@exception(logger)
def can_convert_to_list(value):
    """判断是否可以转换为列表类型

    Args:
        value: 要检查的值

    Returns:
        bool: 如果可以转换为列表返回True，否则返回False
    """
    if value is None:
        return False

    try:
        list(value)
        return True
    except TypeError:
        return False
    except Exception as e:
        logger.debug(f"Unexpected error in can_convert_to_list: {e}")
        return False


if __name__ == "__main__":
    # 测试精度问题优化
    context = {
        "orderNo": {
            "orderNo": "2985346444288092505"
        },
        "rowNo": "1",
        "platformSkuId": {
            "platformSkuId": "134023"
        },
        "erpCode": {
            "erpCode": "134023"
        },
        "erpName": "复方金银花颗粒_诺金_10G*24袋",
        "commodityCount": 1.000000,
        "statusValue": "NORMAL",
        "giftTypeValue": "NOT_GIFT",
        "originalPrice": 39.800000,
        "price": 31.128800,
        "commodityCostPrice": 10.673242,
        "totalAmount": 31.128800,
        "totalOriginalAmount": 39.************,
        "discountShare": 8.671200,
        "discountAmount": 8.671200,
        "billPrice": 31.128800,
        "billAmount": 31.128800,
        "isOnPromotion": "",
        "fiveClass": "A041303002"
    }

    # 测试计算公式
    calculation_formula = "commodityCount * (originalPrice - price)"
    result = safe_eval(calculation_formula, context)
    print(f"\n优化后的计算结果: {result}")

    # 测试精度问题
    precision_test = {
        "a": 0.1,
        "b": 0.2,
        "c": 0.3
    }

    # 测试浮点数精度问题 (0.1 + 0.2 != 0.3 in floating point)
    formula1 = "a + b"
    result1 = safe_eval(formula1, precision_test)
    print(f"\n测试浮点数精度: {formula1} = {result1}")
    print(f"与预期值比较: {result1} {'==' if result1 == 0.3 else '!='} 0.3")

    # 测试复杂公式
    formula2 = "(a + b) * 100 - c * 10"
    result2 = safe_eval(formula2, precision_test)
    print(f"\n测试复杂公式: {formula2} = {result2}")

    # 测试比较运算
    formula3 = "a + b == c"
    result3 = safe_eval(formula3, precision_test)
    print(f"\n测试比较运算: {formula3} = {result3}")