# -*- coding: utf-8 -*-
"""
@Time ： 2025/1/1 15:22
@Auth ： 逗逗的小老鼠
@File ：monitoring_amount_verify.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
from structure.data_monitoring_system.monitoring_config.monitored_verify_config import monitor_verify_config_query
from decimal import Decimal

"""
    该方法基于从数据库中查询的计算公式，进行金额等相关数据的一致性进行校对
    该方法主要基于eval进行实现，可以实现进行数学公式的运算
"""
@exception(logger)
def order_amount_verify(order_amount_calc,verification_code_value,**kwargs):
    "订单金额根据公式进行验证"
    try:
        amount_verify_result=[]
        scenarios_value=kwargs.get('applicable_scenarios',"default")
        service_model_verification=kwargs.get('service_mode','all')
        third_platform_code_verfication=kwargs.get('third_platform_code','all')
        verify_rule=monitor_verify_config_query(**kwargs)
        for rule_item in verify_rule:
            rule_id=rule_item['id']
            # 验证代码
            verification_code=rule_item['verification_code']
            # 验证目的
            verification_purpose=rule_item['verification_purpose']
            # 目标字段
            target_field=rule_item['target_field']
            # 目标公式
            target_formula=rule_item['target_formula']
            # 判断关系
            judge_relationship=rule_item['judge_relationship']
            # 计算字段
            calculated_field=rule_item['calculated_field']
            # 计算公式
            calculation_formula=rule_item['calculation_formula']
            # 异常代码
            exception_code=rule_item['exception_code']
            # 异常简述
            exception_brief=rule_item['exception_brief']
            # 异常说明
            exception_description=rule_item['exception_description']
            # 异常等级
            exception_level=rule_item['exception_level']
            # 适用场景
            applicable_scenarios=rule_item['applicable_scenarios']
            # 使用模式
            service_model=rule_item['service_model']
            # 适用平台
            third_platform_code=rule_item['third_platform_code']
            # 允许误差范围
            allowable_error_range=rule_item['allowable_error_range']
            if service_model=='all' or service_model_verification in service_model:
                if third_platform_code=='all' or str(third_platform_code_verfication) in str(third_platform_code):
                    if verification_code==verification_code_value and applicable_scenarios==scenarios_value:
                        # print("进入目标规则:"+str(rule_id))
                        special_identifier=""
                        """
                            target_formula：要计算的字符串表达式。
                            {"__builtins__": None}：一个字典，用于限制eval()可以访问的内置函数和对象。在这里，我们将__builtins__设置为None，以禁用所有内置函数和对象的访问。这是一种提高安全性的做法，但请注意，即使这样做了，eval()仍然可以执行一些潜在的危险操作（比如通过字符串拼接构造新的函数名并调用它们）。
                            order_pay_calc：一个字典，包含了要在表达式中使用的变量及其值。
                        """
                        # 目标计算结果值
                        # target_result=eval(target_formula,{"__builtins__":None},order_amount_calc)
                        target_result=safe_eval(target_formula,order_amount_calc)
                        # 实际计算结束值
                        # calculated_result=eval(calculation_formula,{"__builtins__":None},order_amount_calc)
                        calculated_result=safe_eval(calculation_formula,order_amount_calc)
                        # 对比关系字典
                        judge_relationship_dict={"target_result":target_result,"calculated_result":calculated_result}
                        # 比对关系公式
                        judge_relationship_formula=f"{target_result} {judge_relationship} {calculated_result}"
                        # 比对结果
                        # verify_result=eval(judge_relationship_formula,{"__builtins__":None},judge_relationship_dict)
                        verify_result=safe_eval(judge_relationship_formula,judge_relationship_dict)

                        if not verify_result:
                            try:
                                target_result = Decimal(target_result)
                                calculated_result = Decimal(calculated_result)
                                error_value = abs(target_result - calculated_result)
                                if allowable_error_range >= error_value:
                                    special_identifier = "精度问题"
                                    exception_level = "waring"
                            except:
                                logger.error(f"精度判断失败，判断值为{target_result}，{calculated_result}")
                            # 将错误描述中的变量替换为实际值
                            exception_expression = exception_description
                            for key,value in order_amount_calc.items():
                                exception_description=exception_description.replace(f"【{key}】",f"【{value}】")
                            err_result={
                                'verification_code':verification_code,
                                'verification_purpose':verification_purpose,
                                'exception_expression':exception_expression,
                                'exception_code':exception_code,
                                'exception_brief':exception_brief,
                                'special_identifier':special_identifier,
                                'exception_description':exception_description,
                                'exception_level':exception_level}
                            amount_verify_result.append(err_result)
                    else:
                        pass
                        # 调试使用
                        # print(f"规则id：{rule_id}规则不匹配,验证规则为{verification_code},场景为{applicable_scenarios}，实际值为：{verification_code_value}，{scenarios_value}")
                else:
                    pass
                    # 调试使用
                    # print(f"规则id：{rule_id}平台不匹配,验证规则为{verification_code},适用平台为{third_platform_code},实际值为：{third_platform_code_verfication}")
            else:
                pass
                # 调试使用
                # print(f"规则id：{rule_id}模式不匹配,验证规则为{verification_code},适用模式为{service_model},实际值为：{service_model_verification}")
        return amount_verify_result
    except Exception as e:
        raise e



def safe_eval(calculation_formula, context):
    try:
        # 尝试执行表达式
        result = eval(calculation_formula, {"__builtins__": None}, context)
        return result
    except Exception as e:
        # 如果执行失败，返回0
        print(f"Error evaluating formula: {e}")
        return 0