# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/16 16:48
@Auth ： 逗逗的小老鼠
@File ：test.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
# 业务处理函数（以下函数由配置中的TOPICS映射）
def handle_order_msg(msg):
    try:
        logger.info(f"[订单处理] 收到消息: {msg.body.decode()}")
        # 实际业务逻辑（如写入数据库）
        # return ConsumeStatus.CONSUME_SUCCESS
    except Exception as e:
        logger.error(f"订单处理失败: {str(e)}")
        # return ConsumeStatus.RECONSUME_LATER

def handle_log_msg(msg):
    try:

        logger.info(f"[日志处理] 收到消息: {msg.body.decode()}")
        # 实际业务逻辑（如写入ES）
        # return ConsumeStatus.CONSUME_SUCCESS
    except Exception as e:
        logger.error(f"日志处理失败: {str(e)}")
        # return ConsumeStatus.RECONSUME_LATER