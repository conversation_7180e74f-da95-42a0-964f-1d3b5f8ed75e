# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/20 14:53
@Auth ： 逗逗的小老鼠
@File ：monitored_data_verify.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：执行数据的校验操作
"""
from lib.get_log import exception,logger
from structure.data_monitoring_system.monitoring_config.monitored_verify_config import monitor_verify_config_query
from structure.data_monitoring_system.monitoring_verify.monitored_data_rule_validation import data_rule_validation
from lib.deal_json import json_loads,json_dumps
from lib.deal_ThreadPool import combined_decorator

@combined_decorator(timeout_seconds=120, max_workers=5)
@exception(logger)
def monitor_data_verify_result(group_data,**kwargs):
    "对数据进行校验"
    try:
        data_group_name = kwargs.get("data_group_name", "")
        result_data=[]
        logger.info('开始执行数据校验操作')
        # 获取校验规则数据
        verify_rule_data = monitor_verify_config_query(**kwargs)
        """ 
            定义规则的限制条件，数据格式为rule_limitations=[{"view_name":"order_info_id","limit_value":"5"}]
                限制条件判断为，全部满足，该条规则方可使用
            # 定义规则验证的视图/数据项名称，数据格式为verification_view_name="order_info"
        """
        for group_data_item in group_data:
            exception_list = []
            for rule_item in verify_rule_data:
                # 规则对应的数据组名称
                rule_group_name=rule_item.get("data_group_name")
                # 规则限制条件
                rule_limitations=json_loads(rule_item.get("rule_limitations",[]))
                # 规则对应的字段项
                verification_view_name=rule_item.get("verification_view_name")
                # 判断数据组名称是否一致
                if rule_group_name == data_group_name:
                    # 根据规则的字段项获取数据
                    verification_data=group_data_item.get(verification_view_name,[])
                    if not verification_data:
                        logger.error(f"【{data_group_name}】数据组中{verification_view_name}字段对应的数据为空，请检查核实")
                        continue

                    if rule_limitations and isinstance(rule_limitations,list):
                        # 规则限制总个数
                        rule_limitations_count = len(rule_limitations)
                        rule_match_count=0
                        # 获取规则限制条件
                        for rule_limitations_item in rule_limitations:
                            # 获取规则限制条件字段
                            rule_limitations_name=rule_limitations_item.get("view_name")
                            # 获取规则限制条件值
                            rule_limitations_value=rule_limitations_item.get("limit_value")
                            # 获取被检测数据项中限制字段的值
                            group_data_limit_value=group_data_item.get(rule_limitations_name)
                            # 如果限制条件值为list，则按照包含的方式进行校验
                            if isinstance(rule_limitations_value,list):
                                if group_data_limit_value in rule_limitations_value:
                                    rule_match_count+=1
                            # 如果限制条件值非list，则按照完全匹配的方式进行校验
                            else:
                                if group_data_limit_value and rule_limitations_value and str(group_data_limit_value)==str(rule_limitations_value):
                                    rule_match_count += 1
                        # 如果限制个数与匹配个数一致，则表示该规则适用于该数据
                        if rule_limitations_count==rule_match_count:
                            # 执行具体数据的规则验证
                            data_data_rule_validation=data_rule_validation(verification_data,rule_item)
                            exception_list.extend(data_data_rule_validation)
                    else:
                        # 执行具体数据的规则验证
                        data_data_rule_validation = data_rule_validation(verification_data, rule_item)
                        exception_list.extend(data_data_rule_validation)
            # 若存在错误数据
            if exception_list:
                exception_dict={}
                # 获取具体每条数据的数据字段和对应值，该值可能为字符、字典，也可能为列表
                for data_item_name, data_item_value in group_data_item.items():
                    # 将所有主数据中非list和字典的键值添加到字典中
                    if data_item_value and isinstance(data_item_value,list):
                        exception_dict[data_item_name] = data_item_value[0]
                    else:
                        exception_dict[data_item_name]=data_item_value
                exception_dict["exception_list"]=exception_list
                result_data.append(exception_dict)
        return result_data
    except Exception as e:
        logger.error(f'对数据进行规则校验发生异常，异常信息为：{e}')
        raise



if __name__ == "__main__":
    data_group_name="TP_ORDER_OFFLINE_ORDER-DATA_TAG_CREATED"
    data =[ {
    "baseOrderInfo": {
        "orderNo": {
            "orderNo": "2985346444288092505"
        },
        "parentOrderNo": "",
        "thirdPlatformCodeValue": "HAIDIAN",
        "thirdOrderNo": {
            "thirdOrderNo": "1425051400042524"
        },
        "parentThirdOrderNo": "",
        "dayNum": "",
        "orderStateValue": "DONE",
        "created": "2025-05-14 20:21:53",
        "payTime": "2025-05-14 20:21:53",
        "completeTime": "2025-05-14 20:21:53",
        "billTime": "2025-05-14 20:21:53",
        "dataVersion": 1,
        "actualPayAmount": 184.5,
        "actualCollectAmount": 184.5,
        "orderCouponList": [],
        "serialNo": "",
        "isOnPromotion": ""
    },
    "basePrescriptionInfo": {
        "prescriptionTypeValue": "",
        "prescriptionNo": ""
    },
    "payInfoList": [
        {
            "payType": {
                "payType": "102"
            },
            "payName": "收钱吧微信",
            "payAmount": 184.5
        }
    ],
    "orderDetailList": [
        {
            "baseOrderDetailInfo": {
                "orderNo": {
                    "orderNo": "2985346444288092505"
                },
                "rowNo": "1",
                "platformSkuId": {
                    "platformSkuId": "134023"
                },
                "erpCode": {
                    "erpCode": "134023"
                },
                "erpName": "复方金银花颗粒_诺金_10G*24袋",
                "commodityCount": 1,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 39.8,
                "price": 31.1288,
                "commodityCostPrice": 10.673242,
                "totalAmount": 31.1288,
                "totalOriginalAmount": 39.8,
                "discountShare": 8.6712,
                "discountAmount": 8.6712,
                "billPrice": 31.1288,
                "billAmount": 31.1288,
                "isOnPromotion": "",
                "fiveClass": "A041303002"
            },
            "pickInfoList": [
                {
                    "erpCode": {
                        "erpCode": "134023"
                    },
                    "makeNo": {
                        "makeNo": "427A022"
                    },
                    "count": 1
                }
            ]
        },
        {
            "baseOrderDetailInfo": {
                "orderNo": {
                    "orderNo": "2985346444288092505"
                },
                "rowNo": "2",
                "platformSkuId": {
                    "platformSkuId": "187999"
                },
                "erpCode": {
                    "erpCode": "187999"
                },
                "erpName": "金振口服液_康缘_10ML*10支",
                "commodityCount": 1,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 66,
                "price": 66,
                "commodityCostPrice": 23.362494,
                "totalAmount": 66,
                "totalOriginalAmount": 66,
                "discountShare": 0,
                "discountAmount": 0,
                "billPrice": 66,
                "billAmount": 66,
                "isOnPromotion": "",
                "fiveClass": "A041205002"
            },
            "pickInfoList": [
                {
                    "erpCode": {
                        "erpCode": "187999"
                    },
                    "makeNo": {
                        "makeNo": "240173"
                    },
                    "count": 1
                }
            ]
        },
        {
            "baseOrderDetailInfo": {
                "orderNo": {
                    "orderNo": "2985346444288092505"
                },
                "rowNo": "3",
                "platformSkuId": {
                    "platformSkuId": "156659"
                },
                "erpCode": {
                    "erpCode": "156659"
                },
                "erpName": "抗病毒口服液_999_10ML*12支",
                "commodityCount": 1,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 29,
                "price": 29,
                "commodityCostPrice": 9.557498,
                "totalAmount": 29,
                "totalOriginalAmount": 29,
                "discountShare": 0,
                "discountAmount": 0,
                "billPrice": 29,
                "billAmount": 29,
                "isOnPromotion": "",
                "fiveClass": "A041303002"
            },
            "pickInfoList": [
                {
                    "erpCode": {
                        "erpCode": "156659"
                    },
                    "makeNo": {
                        "makeNo": "202501001"
                    },
                    "count": 1
                }
            ]
        },
        {
            "baseOrderDetailInfo": {
                "orderNo": {
                    "orderNo": "2985346444288092505"
                },
                "rowNo": "4",
                "platformSkuId": {
                    "platformSkuId": "100826"
                },
                "erpCode": {
                    "erpCode": "100826"
                },
                "erpName": "毛萼香茶菜清热利咽片_12片*3板_红云制药(昆明）",
                "commodityCount": 1,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 39.6,
                "price": 39.6,
                "commodityCostPrice": 14.017351,
                "totalAmount": 39.6,
                "totalOriginalAmount": 39.6,
                "discountShare": 0,
                "discountAmount": 0,
                "billPrice": 39.6,
                "billAmount": 39.6,
                "isOnPromotion": "",
                "fiveClass": "A040915001"
            },
            "pickInfoList": [
                {
                    "erpCode": {
                        "erpCode": "100826"
                    },
                    "makeNo": {
                        "makeNo": "241002"
                    },
                    "count": 1
                }
            ]
        },
        {
            "baseOrderDetailInfo": {
                "orderNo": {
                    "orderNo": "2985346444288092505"
                },
                "rowNo": "5",
                "platformSkuId": {
                    "platformSkuId": "129951"
                },
                "erpCode": {
                    "erpCode": "129951"
                },
                "erpName": "蒲地蓝消炎片(薄膜衣片)_星辰牌_0.6G*12片*3板*1袋",
                "commodityCount": 1,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 24,
                "price": 18.7711,
                "commodityCostPrice": 3.986881,
                "totalAmount": 18.7711,
                "totalOriginalAmount": 24,
                "discountShare": 5.2289,
                "discountAmount": 5.2289,
                "billPrice": 18.7711,
                "billAmount": 18.7711,
                "isOnPromotion": "",
                "fiveClass": "A041306001"
            },
            "pickInfoList": [
                {
                    "erpCode": {
                        "erpCode": "129951"
                    },
                    "makeNo": {
                        "makeNo": "20241208"
                    },
                    "count": 1
                }
            ]
        },
        {
            "baseOrderDetailInfo": {
                "orderNo": {
                    "orderNo": "2985346444288092505"
                },
                "rowNo": "6",
                "platformSkuId": {
                    "platformSkuId": "802148"
                },
                "erpCode": {
                    "erpCode": "802148"
                },
                "erpName": "生物降解塑料购物袋(绿色小号)_380*(220+100)MM*0.025MM",
                "commodityCount": 1,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 0.3,
                "price": 0,
                "commodityCostPrice": 0.097335,
                "totalAmount": 0,
                "totalOriginalAmount": 0.3,
                "discountShare": 0.3,
                "discountAmount": 0.3,
                "billPrice": 0,
                "billAmount": 0,
                "isOnPromotion": "",
                "fiveClass": "G010507002"
            },
            "pickInfoList": [
                {
                    "erpCode": {
                        "erpCode": "802148"
                    },
                    "makeNo": {
                        "makeNo": "20250208"
                    },
                    "count": 1
                }
            ]
        }
    ],
    "baseUserInfo": "",
    "baseOrganizationInfo": {
        "storeCode": "M169",
        "storeName": "[M169]一心堂演丰镇振兴路分店",
        "companyCode": "1009",
        "companyName": "海南鸿翔一心堂医药连锁有限公司",
        "storeDirectJoinTypeValue": "DIRECT_SALES"
    },
    "baseCashierDeskInfo": {
        "posCashierDeskNo": {
            "posCashierDeskNo": "101"
        },
        "cashier": "1067025",
        "cashierName": "何丹",
        "picker": "",
        "pickerName": "",
        "shiftId": {
            "shiftId": "1"
        },
        "shiftDate": "2025-05-14 20:21:53"
    },
    "medInsSettle": ""
}]
    result=monitor_data_verify_result(data,data_group_name=data_group_name)
