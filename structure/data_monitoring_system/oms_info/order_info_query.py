# -*- coding: utf-8 -*-
"""
@Time ： 2024/9/26 11:25
@Auth ： 逗逗的小老鼠
@File ：order_info_query.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import decimal
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect
from structure.data_monitoring_system.oms_info.order_info_query_preprocessing import order_query_condition
import json
from datetime import datetime
from lib.decorator_func_timeout import timeout_decorator

@exception(logger)
def full_order_information_assembly(**kwargs):
    "组装订单的完整信息，包括订单基础信息、订单商品信息、订单金额信息、订单配送信息、订单支付信息"
    try:
        query_start_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"开始查询订单数据，时间：{query_start_time}")
        order_info=order_info_data_query(**kwargs)
        query_main_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"订单主数据查询完成，时间：{query_main_time}")
        # print(order_info)
        order_detail=order_detail_data_query(**kwargs)
        query_detail_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"订单商品数据查询完成，时间：{query_detail_time}")
        # print(order_detail)
        order_pay=order_pay_data_query(**kwargs)
        query_pay_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"订单财务数据查询完成，时间：{query_pay_time}")
        # print(order_pay)
        order_address_data=order_address_data_query(**kwargs)
        query_address_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"订单地址数据查询完成，时间：{query_address_time}")
        # print(order_address_data)
        order_address_record=order_address_record_query(**kwargs)
        query_address_record_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"订单配送信息查询完成，时间：{query_address_record_time}")
        # print(order_address_record)
        order_oms_data=order_oms_data_query(**kwargs)
        query_oms_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"B2C订单数据查询完成，时间：{query_oms_time}")
        # print(order_oms_data)
        third_order=order_third_no_data_query(**kwargs)
        query_third_no_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"三方订单号数据查询完成，时间：{query_third_no_time}")
        # print(third_order)
        full_order_information_list=[]
        # 根据三方订单号组装订单列表信息
        for third_order_item in third_order:
            service_mode = third_order_item['service_mode']
            third_order_no = third_order_item['third_order_no']
            full_order_information = {}
            full_order_information['third_order_no']=third_order_no
            full_order_information['service_mode']=service_mode
            order_information=[]
            # B2C订单信息组装
            if service_mode =='B2C':
                for oms_item in order_oms_data:
                    if third_order_no == oms_item['third_order_no']:
                        order_no=oms_item['order_no']
                        oms_order_no=oms_item['oms_order_no']
                        oms_order_information = {}
                        oms_order_information['oms_order_info']=oms_item
                        # 订单商品信息
                        detail_information=[]
                        for detail_item in order_detail:
                            if detail_item['oms_order_no']==oms_order_no:
                                detail_information.append(detail_item)
                        oms_order_information['order_detail']=detail_information
                        # 订单支付信息
                        pay_information=[]
                        for pay_item in order_pay:
                            if pay_item['oms_order_no']==oms_order_no:
                                pay_information.append(pay_item)
                        oms_order_information['order_pay']=pay_information
                        # 订单收货地址信息
                        address_information = []
                        for address_item in order_address_data:
                            if address_item['oms_order_no'] == oms_order_no:
                                address_information.append(address_item)
                        oms_order_information['order_address_data'] = address_item
                        order_information.append(oms_order_information)
            # O2O订单信息组装
            else:
                for info_item in order_info:
                    if third_order_no == info_item['third_order_no']:
                        order_no = info_item['order_no']
                        o2o_order_information = {}
                        o2o_order_information['order_info']=info_item
                        # 订单商品信息
                        detail_information=[]
                        for detail_item in order_detail:
                            if detail_item['order_no']==order_no:
                                detail_information.append(detail_item)
                        o2o_order_information['order_detail']=detail_information
                        # 订单支付信息
                        pay_information=[]
                        for pay_item in order_pay:
                            if pay_item['order_no']==order_no:
                                pay_information.append(pay_item)
                        o2o_order_information['order_pay']=pay_information
                        # 订单收货地址信息
                        address_information=[]
                        for address_item in order_address_data:
                            if address_item['order_no']==order_no:
                                address_information.append(address_item)
                        o2o_order_information['order_address_data']=address_item
                        # 订单配送信息
                        address_record_information = []
                        for address_record_item in order_address_record:
                            if address_record_item['order_no'] == order_no:
                                address_record_information.append(address_record_item)
                        o2o_order_information['order_address_record'] = address_record_information
                        order_information.append(o2o_order_information)
            full_order_information["order_information"]=order_information
            full_order_information_list.append(full_order_information)
        # print(full_order_information_list)
        return full_order_information_list
    except Exception as e:
        raise e

@timeout_decorator(10,scheduler_name="order_data_verify")
@exception(logger)
def order_third_no_data_query(**kwargs):
    "查询订单的三方订单来源信息，order_info"
    try:
        query_condition = order_query_condition(**kwargs)
        sql_condition=query_condition['sql_condition']
        # 查询参数
        query_value=query_condition['query_value']
        # 查询订单来源信息
        third_data_sql=f"""
            SELECT DISTINCT
                info.third_order_no AS 'third_order_no',
                info.service_mode AS 'service_mode'
            FROM
                order_info info
            WHERE
                1 = 1 
                {sql_condition}
        """
        query_result = db_mysql_connect("dscloud", third_data_sql, sql_val=query_value, **kwargs)
        third_no_data = query_result['data']
        return third_no_data
    except Exception as e:
        raise e



@timeout_decorator(10,scheduler_name="order_data_verify")
@exception(logger)
def order_info_data_query(**kwargs):
    "查询订单的主数据，order_info表"
    try:
        query_condition = order_query_condition(**kwargs)
        sql_condition=query_condition['sql_condition']
        # 查询参数
        query_value=query_condition['query_value']
        # 查询订单基础信息
        info_data_sql=f"""
            SELECT
                info.id AS 'id',
                info.order_no AS 'order_no',
                info.order_state AS 'order_state',
                info.third_platform_code AS 'third_platform_code',
                info.third_order_id AS 'third_order_id',
                info.third_order_no AS 'third_order_no',
                info.third_order_state AS 'third_order_state',
                info.off_state AS 'off_state',
                info.mer_code AS 'mer_code',
                info.client_code AS 'client_code',
                info.online_store_code AS 'online_store_code',
                info.online_store_name AS 'online_store_name',
                info.organization_code AS 'organization_code',
                info.organization_name AS 'organization_name',
                info.delivery_time_type AS 'delivery_time_type',
                info.delivery_time_desc AS 'delivery_time_desc',
                info.seller_remark AS 'seller_remark',
                info.buyer_message AS 'buyer_message',
                info.lock_flag AS 'lock_flag',
                info.lock_msg AS 'lock_msg',
                info.locker_id AS 'locker_id',
                info.remind_flag AS 'remind_flag',
                info.buyer_name AS 'buyer_name',
                info.receiver_lat AS 'receiver_lat',
                info.receiver_lng AS 'receiver_lng',
                info.acceptor_id AS 'acceptor_id',
                info.acceptor_name AS 'acceptor_name',
                info.accept_time AS 'accept_time',
                info.picker_id AS 'picker_id',
                info.picker_name AS 'picker_name',
                info.pick_operator_id AS 'pick_operator_id',
                info.pick_operator_name AS 'pick_operator_name',
                info.pick_time AS 'pick_time',
                info.canceller_id AS 'canceller_id',
                info.canceller_name AS 'canceller_name',
                info.cancel_reason AS 'cancel_reason',
                info.cancel_time AS 'cancel_time',
                info.ex_operator_id AS 'ex_operator_id',
                info.ex_operator_name AS 'ex_operator_name',
                info.ex_operator_time AS 'ex_operator_time',
                info.complete_time AS 'complete_time',
                info.created AS 'created',
                info.day_num AS 'day_num',
                info.modify_time AS 'modify_time',
                info.erp_sale_no AS 'erp_sale_no',
                info.create_time AS 'create_time',
                info.prescription_flag AS 'prescription_flag',
                info.self_verify_code AS 'self_verify_code',
                info.erp_state AS 'erp_state',
                info.bill_time AS 'bill_time',
                info.member_no AS 'member_no',
                info.is_prescription AS 'is_prescription',
                info.prescription_status AS 'prescription_status',
                info.client_conf_id AS 'client_conf_id',
                info.appointment AS 'appointment',
                info.integral_flag AS 'integral_flag',
                info.new_customer_flag AS 'new_customer_flag',
                info.source_online_store_code AS 'source_online_store_code',
                info.source_online_store_name AS 'source_online_store_name',
                info.source_organization_code AS 'source_organization_code',
                info.source_organization_name AS 'source_organization_name',
                info.remark AS 'remark',
                info.service_mode AS 'service_mode',
                info.pay_time AS 'pay_time',
                info.order_type AS 'order_type',
                info.order_is_new AS 'order_is_new',
                info.data_version AS 'data_version',
                info.complex_modify_flag AS 'complex_modify_flag',
                info.medical_insurance AS 'medical_insurance',
                info.source_channel_type AS 'source_channel_type',
				info.migration_order_no AS 'migration_order_no',
                info.extend_info AS 'extend_info',
                info.freight_order_no AS 'freight_order_no',
                info.deleted  AS 'deleted'
            FROM
                order_info info
            WHERE
                1 = 1 
                {sql_condition}
        """
        # 调用数据库连接函数执行查询
        query_result = db_mysql_connect("dscloud", info_data_sql, sql_val=query_value, **kwargs)
        info_data = query_result['data']
        return info_data
    except Exception as e:
        raise e
@timeout_decorator(10,scheduler_name="order_data_verify")
@exception(logger)
def order_detail_data_query(**kwargs):
    "查询订单的详情数据，order_detail表"
    try:
        query_condition = order_query_condition(**kwargs)
        sql_condition=query_condition['sql_condition']
        # 查询参数
        query_value=query_condition['query_value']
        detail_data_sql=f"""
            SELECT
                detail.id AS 'detail_id',
                detail.order_no AS 'order_no',
                detail.platform_sku_id AS 'platform_sku_id',
                detail.erp_code AS 'erp_code',
                detail.bar_code AS 'bar_code',
                detail.commodity_name AS 'commodity_name',
                detail.main_pic AS 'main_pic',
                detail.commodity_spec AS 'commodity_spec',
                detail.commodity_count AS 'commodity_count',
                detail.original_price AS 'original_price',
                detail.price AS 'price',
                detail.total_amount AS 'total_amount',
                detail.discount_amount AS 'discount_amount',
                detail.actual_amount AS 'actual_amount',
                detail.discount_share AS 'discount_share',
                detail.actual_net_amount AS 'actual_net_amount',
                detail.different_share AS 'different_share',
                detail.`status` AS 'status',
                detail.manufacture AS 'manufacture',
                detail.swap_id AS 'swap_id',
                detail.create_time AS 'create_time',
                detail.modify_time AS 'modify_time',
                detail.adjust_amount AS 'adjust_amount',
                detail.third_detail_id AS 'third_detail_id',
                detail.bill_price AS 'bill_price',
                detail.is_gift AS 'is_gift',
                detail.goods_type AS 'goods_type',
                detail.refund_count AS 'refund_count',
                detail.origin_type AS 'origin_type',
                detail.st_code AS 'st_code',
                detail.original_erp_code AS 'original_erp_code',
                detail.original_erp_code_num AS 'original_erp_code_num',
                detail.oms_order_no AS 'oms_order_no',
                detail.is_joint AS 'is_joint',
                detail.erp_gift AS 'erp_gift',
                detail.old_erp_code AS 'old_erp_code',
                detail.relation_code AS 'relation_code',
                detail.is_reduce_stock AS 'is_reduce_stock',
                detail.chailing AS 'chailing',
                detail.platform_discount_fee AS 'platform_discount_fee',
                detail.merchant_discount_fee AS 'merchant_discount_fee',
                detail.brokerage_amount AS 'brokerage_amount',
                detail.vip_different_amt AS 'vip_different_amt',
                detail.drug_type AS 'drug_type',
                detail.chai_ling_original_num AS 'chai_ling_original_num',
                detail.chai_ling_original_erp_code AS 'chai_ling_original_erp_code',
                detail.settle_price AS 'settle_price',
                detail.modify_price_diff AS 'modify_price_diff',
                detail.health_value AS 'health_value',
                detail.extend AS 'extend',
                detail.original_oms_order_no AS 'original_oms_order_no',
                detail.third_order_no AS 'third_order_no',
                detail.payment AS 'payment',
                detail.near_effective_status AS 'near_effective_status',
                detail.average_price AS 'average_price',
                detail.chai_ling_num AS 'chai_ling_num',
                detail.detail_discount AS 'detail_discount',
                detail.detail_settlement_status AS 'detail_settlement_status',
                detail.is_medicare_item AS 'is_medicare_item' 
            FROM
                order_info info
                INNER JOIN order_detail detail ON info.order_no = detail.order_no 
            WHERE
                1 = 1 
                {sql_condition}
        """
        query_result = db_mysql_connect("dscloud", detail_data_sql, sql_val=query_value, **kwargs)
        detail_data = query_result['data']
        return detail_data
    except Exception as e:
        raise e
@timeout_decorator(10,scheduler_name="order_data_verify")
@exception(logger)
def order_pay_data_query(**kwargs):
    "查询订单的支付数据，order_pay_info表"
    try:
        query_condition = order_query_condition(**kwargs)
        sql_condition=query_condition['sql_condition']
        # 查询参数
        query_value=query_condition['query_value']
        pay_data_sql=f"""
            SELECT
                pay.id AS 'pay_id',
                pay.order_no AS 'order_no',
                pay.pay_status AS 'pay_status',
                pay.pay_channel AS 'pay_channel',
                pay.buyer_actual_amount AS 'buyer_actual_amount',
                pay.merchant_actual_amount AS 'merchant_actual_amount',
                pay.total_amount AS 'total_amount',
                pay.total_discount AS 'total_discount',
                pay.merchant_total_discount_sum AS 'merchant_total_discount_sum',
                pay.merchant_total_discount_sum_not_delivery_fee AS 'merchant_total_discount_sum_not_delivery_fee',
                pay.merchant_discount_sum AS 'merchant_discount_sum',
                pay.discount_fee_dtl AS 'discount_fee_dtl',
                pay.platform_discount AS 'platform_discount',
                pay.post_fee_discount AS 'post_fee_discount',
                pay.merchant_delivery_fee AS 'merchant_delivery_fee',
                pay.merchant_delivery_fee_discount AS 'merchant_delivery_fee_discount',
                pay.merchant_pack_fee AS 'merchant_pack_fee',
                pay.platform_delivery_fee AS 'platform_delivery_fee',
                pay.platform_delivery_fee_discount AS 'platform_delivery_fee_discount',
                pay.platform_pack_fee AS 'platform_pack_fee',
                pay.buyer_cod_service_fee AS 'buyer_cod_service_fee',
                pay.seller_cod_service_fee AS 'seller_cod_service_fee',
                pay.brokerage_amount AS 'brokerage_amount',
                pay.buyer_cod_amount AS 'buyer_cod_amount',
                pay.platform_fee_collection AS 'platform_fee_collection',
                pay.manual_fix_amount AS 'manual_fix_amount',
                pay.detail_discount_collect AS 'detail_discount_collect',
                pay.different_amount AS 'different_amount',
                pay.create_time AS 'create_time',
                pay.modify_time AS 'modify_time',
                pay.pay_code AS 'pay_code',
                pay.delivery_fee AS 'delivery_fee',
                pay.pack_fee AS 'pack_fee',
                pay.oms_order_no AS 'oms_order_no',
                pay.remain_brokerage_amount AS 'remain_brokerage_amount',
                pay.health_num AS 'health_num',
                pay.health_value AS 'health_value',
                pay.warehouse_agency_fee AS 'warehouse_agency_fee',
                pay.apportion_type AS 'apportion_type',
                pay.medicare_amount AS 'medicare_amount',
                pay.medicare_order_id AS 'medicare_order_id',
                pay.pay_sale_info AS 'pay_sale_info',
                pay.total_discount_sum_not_delivery_fee AS 'total_discount_sum_not_delivery_fee',
                pay.accounts_receivable AS 'accounts_receivable',
                pay.platform_discount_sum_not_delivery_fee AS 'platform_discount_sum_not_delivery_fee'
            FROM
                order_info info
                INNER JOIN order_pay_info pay ON info.order_no = pay.order_no 
            WHERE
                1 = 1 
                {sql_condition}
        """
        query_result = db_mysql_connect("dscloud", pay_data_sql, sql_val=query_value, **kwargs)
        pay_data = query_result['data']
        return pay_data
    except Exception as e:
        raise e
@timeout_decorator(10,scheduler_name="order_data_verify")
@exception(logger)
def order_address_data_query(**kwargs):
    "查询订单的收货地址信息，order_address表"
    try:
        query_condition = order_query_condition(**kwargs)
        sql_condition=query_condition['sql_condition']
        # 查询参数
        query_value=query_condition['query_value']
        address_data_sql=f"""
            SELECT
                address.id AS 'address_id',
                address.order_no AS 'order_no',
                address.receiver_name AS 'receiver_name',
                address.receiver_telephone AS 'receiver_telephone',
                address.receiver_mobile AS 'receiver_mobile',
                address.province AS 'province',
                address.city AS 'city',
                address.district AS 'district',
                address.town AS 'town',
                address.address AS 'address',
                address.zip_code AS 'zip_code',
                address.full_address AS 'full_address',
                address.create_time AS 'create_time',
                address.modify_time AS 'modify_time',
                address.original_full_address AS 'original_full_address',
                address.oms_order_no AS 'oms_order_no',
                address.receiver_mobile_desen AS 'receiver_mobile_desen',
                address.oaid AS 'oaid',
                address.privacy_phone AS 'privacy_phone',
                address.receiver_name_privacy AS 'receiver_name_privacy',
                address.receiver_address_privacy AS 'receiver_address_privacy',
                address.receiver_phone_privacy AS 'receiver_phone_privacy',
                address.privacy_detail AS 'privacy_detail'
            FROM
                order_info info
                INNER JOIN order_delivery_address address ON info.order_no = address.order_no 
            WHERE
                1 = 1 
                {sql_condition}
	    """
        query_result = db_mysql_connect("dscloud", address_data_sql, sql_val=query_value, **kwargs)
        address_data = query_result['data']
        return address_data
    except Exception as e:
        raise e
@timeout_decorator(10,scheduler_name="order_data_verify")
@exception(logger)
def order_address_record_query(**kwargs):
    "查询订单的收货地址记录，order_address_record表"
    try:
        query_condition = order_query_condition(**kwargs)
        sql_condition=query_condition['sql_condition']
        # 查询参数
        query_value=query_condition['query_value']
        address_record_data_sql=f"""
                SELECT
                    record.id AS 'record_id',
                    record.order_no AS 'order_no',
                    record.state AS 'state',
                    record.rider_order_no AS 'rider_order_no',
                    record.delivery_type AS 'delivery_type',
                    record.delivery_plat_name AS 'delivery_plat_name',
                    record.delivery_client_code AS 'delivery_client_code',
                    record.delivery_store_code AS 'delivery_store_code',
                    record.delivery_tip AS 'delivery_tip',
                    record.rider_name AS 'rider_name',
                    record.rider_phone AS 'rider_phone',
                    record.rider_address AS 'rider_address',
                    record.latitude AS 'latitude',
                    record.longitude AS 'longitude',
                    record.call_time AS 'call_time',
                    record.accept_time AS 'accept_time',
                    record.pick_time AS 'pick_time',
                    record.cancel_from AS 'cancel_from',
                    record.cancel_reason AS 'cancel_reason',
                    record.cancel_detail AS 'cancel_detail',
                    record.exception_reason AS 'exception_reason',
                    record.actual_delivery_fee AS 'actual_delivery_fee',
                    record.delivery_fee_total AS 'delivery_fee_total',
                    record.cancel_flag AS 'cancel_flag',
                    record.create_time AS 'create_time',
                    record.modify_time AS 'modify_time',
                    record.logistics_company AS 'logistics_company',
                    record.logistics_no AS 'logistics_no',
                    record.logistics_name AS 'logistics_name',
                    record.extra_info AS 'extra_info',
                    record.delay_state AS 'delay_state',
                    record.pre_call_flag AS 'pre_call_flag',
                    record.rider_staff_code AS 'rider_staff_code',
                    record.upload_location_flag AS 'upload_location_flag' 
                FROM
                    order_info info
                    INNER JOIN order_delivery_record record ON info.order_no = record.order_no 
                WHERE
                    1 = 1 
                    {sql_condition}
        """
        query_result = db_mysql_connect("dscloud", address_record_data_sql, sql_val=query_value, **kwargs)
        address_record_data = query_result['data']
        return address_record_data
    except Exception as e:
        raise e

@timeout_decorator(10,scheduler_name="order_data_verify")
@exception(logger)
def order_oms_data_query(**kwargs):
    "查询B2C订单信息，order_oms表"
    try:
        query_condition = order_query_condition(**kwargs)
        sql_condition=query_condition['sql_condition']
        # 查询参数
        query_value=query_condition['query_value']
        oms_data_sql=f"""
            SELECT
                oms.id AS 'oms_id',
                oms.order_no AS 'order_no',
                oms.oms_order_no AS 'oms_order_no',
                oms.oms_ship_no AS 'oms_ship_no',
                oms.order_status AS 'order_status',
                oms.warehouse_id AS 'warehouse_id',
                oms.warehouse_name AS 'warehouse_name',
                oms.express_name AS 'express_name',
                oms.express_id AS 'express_id',
                oms.express_number AS 'express_number',
                oms.remark AS 'remark',
                oms.create_time AS 'create_time',
                oms.creator AS 'creator',
                oms.modify_time AS 'modify_time',
                oms.order_type AS 'order_type',
                oms.split_status AS 'split_status',
                oms.ship_time AS 'ship_time',
                oms.audit_time AS 'audit_time',
                oms.is_refund AS 'is_refund',
                oms.ship_operator_id AS 'ship_operator_id',
                oms.ship_operator_name AS 'ship_operator_name',
                oms.audit_operator_id AS 'audit_operator_id',
                oms.audit_operator_name AS 'audit_operator_name',
                oms.complete_time AS 'complete_time',
                oms.cancel_time AS 'cancel_time',
                oms.bill_time AS 'bill_time',
                oms.ex_status AS 'ex_status',
                oms.erp_status AS 'erp_status',
                oms.erp_deliver_no AS 'erp_deliver_no',
                oms.erp_sale_no AS 'erp_sale_no',
                oms.intercept_status AS 'intercept_status',
                oms.warehouse_code AS 'warehouse_code',
                oms.ex_reason AS 'ex_reason',
                oms.is_post_fee_order AS 'is_post_fee_order',
                oms.join_wms AS 'join_wms',
                oms.ship_status AS 'ship_status',
                oms.third_platform_code AS 'third_platform_code',
                oms.third_order_no AS 'third_order_no',
                oms.mer_code AS 'mer_code',
                oms.client_code AS 'client_code',
                oms.online_store_code AS 'online_store_code',
                oms.online_store_name AS 'online_store_name',
                oms.organization_code AS 'organization_code',
                oms.is_prescription AS 'is_prescription',
                oms.pay_time AS 'pay_time',
                oms.created AS 'created',
                oms.buyer_name AS 'buyer_name',
                oms.client_conf_id AS 'client_conf_id',
                oms.need_invoice AS 'need_invoice',
                oms.buyer_message AS 'buyer_message',
                oms.seller_remark AS 'seller_remark',
                oms.sheet_status AS 'sheet_status',
                oms.warehouse_type AS 'warehouse_type',
                oms.supplier_code AS 'supplier_code',
                oms.o2o_shop_id AS 'o2o_shop_id',
                oms.o2o_client_code AS 'o2o_client_code',
                oms.sendorder_print_num AS 'sendorder_print_num',
                oms.goods_qty AS 'goods_qty',
                oms.goods_category_qty AS 'goods_category_qty',
                oms.version AS 'version',
                oms.buyer_id AS 'buyer_id',
                oms.tag AS 'tag',
                oms.erp_code_list AS 'erp_code_list',
                oms.seq AS 'seq',
                oms.erp_error_code AS 'erp_error_code',
                oms.virtual_merge_no AS 'virtual_merge_no',
                oms.shipped_status AS 'shipped_status',
                oms.spread_store_code AS 'spread_store_code',
                oms.order_owner_type AS 'order_owner_type',
                oms.mer_code AS 'mer_code',
                oms.stock_state AS 'stock_state',
                oms.platform AS 'platform',
                oms.sub_biz_type AS 'sub_biz_type',
                oms.vip_level AS 'vip_level',
                oms.erp_audit_status AS 'erp_audit_status',
                oms.logistics_back_status AS 'logistics_back_status',
                oms.settlement_status AS 'settlement_status',
                oms.extend_info AS 'extend_info',
                oms.deleted AS 'deleted',
                oms.is_procurement_erp AS 'is_procurement_erp',
                oms.procurement_no AS 'procurement_no'
            FROM
                order_info info
                INNER JOIN oms_order_info oms ON info.order_no = oms.order_no 
            WHERE
                1 = 1 
                {sql_condition}
        """
        query_result = db_mysql_connect("dscloud", oms_data_sql, sql_val=query_value, **kwargs)
        oms_data = query_result['data']
        return oms_data
    except Exception as e:
        raise e

def default_serializer(obj):
    if isinstance(obj, datetime):
        return obj.isoformat()
    if isinstance(obj, decimal.Decimal):
        return float(obj)
    raise TypeError(f"Type {type(obj)} not serializable")

if __name__=="__main__":
    third_order_no=['3801266613090057683']
    start_time="2024-12-06 00:00:00"
    data=full_order_information_assembly(start_time=start_time)
    print(json.dumps(data,default=default_serializer))
