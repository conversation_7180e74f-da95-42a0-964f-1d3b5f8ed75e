```mermaid
graph TD
    bgsched>定时任务启动]--> |定时任务启动| schedul_task_config_add{{定时任务添加}};
    bgsched>定时任务启动]--> |监听器启动| my_listener{{定时任务监听器}};    
    schedul_task_config_add{{定时任务添加}}-->|操作表| scheduler_task_config[/定时任务配置表/];
    schedul_task_config_add{{定时任务添加}}-->|添加定时任务| my_listener{{定时任务监听器}};
    my_listener{{定时任务监听器}}-->|添加定时任务| store_execution_record{{定时任务执行记录方法}};
    store_execution_record{{定时任务执行记录方法}}-->|操作表| scheduler_task_execution_logs[/定时任务执行记录表/];
    scheduler_task_config[/定时任务配置表/] -->|添加数据| data_monitoring_task((检测任务执行方法));
    data_monitoring_task((检测任务执行方法)) -->|获取被检测的数据| data_assembly((检测数据组装方法));
    data_monitoring_task((检测任务执行方法)) -->|对数据进行校验| order_data_verify_result((数据结果预处理方法));
    data_monitoring_task((检测任务执行方法)) -->|对检测结果进行记录| monitor_result_data_insert((监测结果记录方法));
    data_monitoring_task((检测任务执行方法)) -->|查询检测记录信息| monitor_result_info_query((获取检测结果记录信息方法));
    data_monitoring_task((检测任务执行方法)) -->|查询检测详情信息| monitor_result_detail_query((获取检测结果详情信息方法));
    data_monitoring_task((检测任务执行方法)) -->|发送检测结果信息到企微| message_wechat_push((发送企业微信消息方法));
    data_assembly((检测数据组装方法))-->|查询源数据查询的配置信息| monitor_data_query_query((查询源数据查询配置信息方法));
    data_assembly((检测数据组装方法))-->|根据配置信息进行数据查询| group_data_query((源数据预处理方法));
    data_assembly((检测数据组装方法))-->|查询源数据计算的配置信息| monitor_data_calculate_query((查询数据计算配置信息方法));
    data_assembly((检测数据组装方法))-->|根据配置信息进行数据计算| group_data_calculate((源数据预计算方法));
    group_data_query((源数据预处理方法))-->|根据配置信息进行数据查询| monitored_data_query((查询源数据方法));
    group_data_calculate((源数据预计算方法))-->|根据配置信息进行数据计算| monitored_data_calculate{源数据分类计算判断方法};
    monitored_data_calculate{源数据分类计算判断方法}-->|对源数据进行过滤| monitored_data_filter((源数据计算前过滤方法));
    monitored_data_calculate{源数据分类计算判断方法}-->|数据分类计算| monitored_data_aggregation_calculate((源数据聚合计算方法));
    monitored_data_calculate{源数据分类计算判断方法}-->|数据分类计算| monitored_data_merge_calculate((源数据合并计算方法));
    monitored_data_query((查询源数据方法))-->|源数据查询条件获取| monitored_data_query_condition((查询条件预处理方法));
    monitored_data_query_condition((查询条件预处理方法))-->|获取查询字段| monitored_data_key_field_list((获取条件查询字段方法));
    monitored_data_query_condition((查询条件预处理方法))-->|获取时间查询条件| build_time_conditions((时间查询条件处理方法));
    order_data_verify_result((数据结果预处理方法)) -->|获取检测规则配置信息| monitor_verify_config_query((检测规则配置信息查询方法));
    order_data_verify_result((数据结果预处理方法)) -->|对数据进行校验| data_rule_validation((根据规则进行数据检测方法));
    monitor_result_data_insert((监测结果记录方法)) -->|获取结果数据字段信息| result_data_config((结果数据字段映射关系方法));
    monitor_result_data_insert((监测结果记录方法)) -->|对数据进行写入预处理| monitor_result_data_detection((对结果数据进行预处理方法));
    result_data_config((结果数据字段映射关系方法)) -->|获取结果数据字段信息| monitor_result_config_query((结果数据映射关系查询方法));
    monitor_result_data_detection((对结果数据进行预处理方法)) -->|处理数据字段列表| result_data_column_detection((数据字段列表预处理方法));
    result_data_column_detection((数据字段列表预处理方法)) -->|获取数据结果表字段| monitor_result_data_column((获取数据字段方法));
    monitor_result_data_detection((对结果数据进行预处理方法)) -->|处理结果数据字段| result_data_field_detection((处理结果数据的字段方法));
    monitor_result_info_query((获取检测结果记录信息方法)) -->|操作表| business_monitor_result_detail[/检测结果详情表/];
    monitor_result_detail_query((获取检测结果详情信息方法)) -->|操作表| business_monitor_result_info[/检测结果记录表/];
    monitor_data_query_query((查询源数据查询配置信息方法)) -->|操作表| business_monitor_data_query[/检测数据查询配置表/];
    monitor_data_calculate_query((查询数据计算配置信息方法)) -->|操作表| business_monitor_data_calculate[/检测数据计算配置表/];
    monitor_verify_config_query((检测规则配置信息查询方法)) -->|操作表| business_monitor_verify_config[/检测公式配置表/];
    monitor_result_config_query((结果数据映射关系查询方法)) -->|操作表| business_monitor_result_config[/检测结果配置表/]
    data_monitoring_task((检测任务执行方法)) -->|获取企微配置信息| message_config_data((获取企微配置信息方法));
    message_config_data((获取企微配置信息方法)) -->|操作表| business_webhook_msg_config[/企微消息配置表/];
    data_monitoring_task((检测任务执行方法)) -->|获取企微配置信息| message_content_info((消息发送地址及通知人列表处理方法));
    data_monitoring_task((检测任务执行方法)) -->|获取企微配置信息| message_config_data((获取企微配置信息方法));
    data_monitoring_task((检测任务执行方法)) -->|发送记录写入| message_push_record_write((发送记录写入方法));
    message_push_record_write((发送记录写入方法)) -->|操作表| business_webhook_msg_record[/企微消息记录表/];
```