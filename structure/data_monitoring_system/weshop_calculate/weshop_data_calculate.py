# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/18 14:39
@Auth ： 逗逗的小老鼠
@File ：weshop_data_calculate.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：对订单中的详情数据进行二次计算
"""
from structure.data_monitoring_system.oms_calculate.order_pay_calculate import order_amount_calculate
from lib.get_log import logger, exception
from lib.deal_json import json_loads
from decimal import Decimal


@exception(logger)
def weshop_detail_calculate(detail_detail_list):
    "微商城订单详情数据二次计算"
    try:
        logger.info("开始对微商城订单详情数据进行二次计算")
        weshop_detail_data = []
        for detail_item in detail_detail_list:
            # 是否组合商品
            is_combined_commodity = detail_item.get("is_combined_commodity")
            # 是否为组合商品主品
            is_main_commodity = detail_item.get("is_main_commodity")
            # 是否已删除商品，1为已删除，1为未删除
            isvalid = detail_item.get("isvalid")
            # 组合商品且为主品的，则不参与二次计算
            if is_combined_commodity == 1 and is_main_commodity == 1 and isvalid == 1:
                break
            weshop_detail_data.append(detail_item)
        detail_calc_key_list = ["total_amount", "coupon_amount", "total_actual_amount", "activity_discount_amont",
                                "cus_distribute_amount", "deposit_pay_amount", "final_payment_amount", "vip_discount",
                                "vip_discount_plus", "cash_coupon_amount"]
        detail_calc = order_amount_calculate(weshop_detail_data, detail_calc_key_list)
        return detail_calc
    except Exception as e:
        logger.error(f"微商城订单详情数据二次计算异常，异常原因：{e}")
        raise e


@exception(logger)
def weshop_info_calculate(weshop_order_info):
    "微商城订单基础数据二次计算"
    try:
        logger.info("开始对微商城订单基础数据进行二次计算")
        weshop_info_data = []
        for info_item in weshop_order_info:
            # 支付券支付信息
            pay_sale_info = info_item.get("pay_sale_info", [])
            pay_sale_amount = 0
            if pay_sale_info is not None and pay_sale_info != "":
                pay_sale_info = json_loads(pay_sale_info)
                for pay_sale_item in pay_sale_info:
                    # 支付券支付金额
                    saleAmount = pay_sale_item.get("saleAmount")
                    pay_sale_amount += saleAmount
            info_item["pay_sale_amount"] = Decimal(pay_sale_amount)
            weshop_info_data.append(info_item)
        info_calc_key_list = ["total_goods_number", "total_order_amount", "integral_deduction", "coupon_deduction",
                              "activity_discount_amont", "other_discount_amont", "original_freight_amount",
                              "actual_freight_amount", "freight_deduction_of_hb", "total_actual_order_amount",
                              "total_actual_hb", "actually_paid", "amount_tobepaid", "refund_amount",
                              "pay_balance_amount", "refund_balance_amount", "pay_medical_amount",
                              "total_deposit_pay_amount", "total_final_payment_amount","pay_sale_amount"]
        info_calc = order_amount_calculate(weshop_info_data, info_calc_key_list)
        return info_calc
    except Exception as e:
        logger.error(f"微商城订单基础数据二次计算异常，异常原因：{e}")
        raise e
