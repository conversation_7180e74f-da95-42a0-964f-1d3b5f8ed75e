# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/18 15:35
@Auth ： 逗逗的小老鼠
@File ：monitored_data_config.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：获取检测数据查询相关配置信息
"""
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect


@exception(logger)
def monitor_data_query_query(**kwargs):
    "查询源数据查询配置信息"
    try:
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        query_sql="""
            SELECT * FROM business_monitor_data_query WHERE is_delete=0
        """
        query_result = db_mysql_connect("insight", query_sql,environment_flag="common", **kwargs)
        query_data = query_result['data']
        # print(query_result)
        return query_data

    except Exception as e:
        logger.error(f"查询数据验证的表达式发生异常，异常为：{e}")
        raise

@exception(logger)
def monitor_data_calculate_query(**kwargs):
    "查询源数据查询配置信息"
    try:
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        query_sql="""
            SELECT * FROM business_monitor_data_calculate WHERE is_delete=0
        """
        query_result = db_mysql_connect("insight", query_sql,environment_flag="common", **kwargs)
        query_data = query_result['data']
        # print(query_result)
        return query_data

    except Exception as e:
        logger.error(f"查询数据验证的表达式发生异常，异常为：{e}")
        raise


@exception(logger)
def monitor_data_transformation_query(**kwargs):
    "查询源数据查询配置信息"
    try:
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        query_sql="""
            SELECT * FROM business_monitor_data_transformation WHERE is_delete=0
        """
        query_result = db_mysql_connect("insight", query_sql,environment_flag="common", **kwargs)
        query_data = query_result['data']
        # print(query_result)
        return query_data

    except Exception as e:
        logger.error(f"查询数据验证的表达式发生异常，异常为：{e}")
        raise

@exception(logger)
def table_default_time_filed(shcema_name,table_name,**kwargs):
    "查询源数据查询配置信息"
    try:
        query_sql=f"""
            SELECT 
                   DISTINCT  COLUMN_NAME
                FROM 
                    information_schema.COLUMNS
                WHERE 
                     TABLE_NAME = '{table_name}' 
                    AND COLUMN_DEFAULT = 'CURRENT_TIMESTAMP';
        """
        query_result = db_mysql_connect(shcema_name, query_sql,**kwargs)
        query_data = query_result['data']
        # print(query_result)
        return query_data

    except Exception as e:
        logger.error(f"查询数据验证的表达式发生异常，异常为：{e}")
        raise
