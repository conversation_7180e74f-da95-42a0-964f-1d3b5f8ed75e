# -*- coding: utf-8 -*-
"""
@Time ： 2025/2/21 13:39
@Auth ： 逗逗的小老鼠
@File ：monitored_result_config.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：检测结果配置信息
"""
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect

@exception(logger)
def monitor_result_config_query(**kwargs):
    "查询检测结果配置信息"
    try:
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        query_sql="""
            SELECT * FROM business_monitor_result_config WHERE is_delete=0
        """
        query_result = db_mysql_connect("insight", query_sql,environment_flag="common", **kwargs)
        query_data = query_result['data']
        # print(query_result)
        return query_data

    except Exception as e:
        logger.error(f"查询检测结果配置信息发生异常，异常为：{e}")
        raise


@exception(logger)
def monitor_result_data_column(table_name,**kwargs):
    "查询监测结果信息表字段"
    try:
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        query_sql=f"""
            SELECT * FROM {table_name} WHERE 2=1
        """
        query_result = db_mysql_connect("insight", query_sql,environment_flag="common", **kwargs)
        query_data = query_result['column']
        # print(query_result)
        return query_data

    except Exception as e:
        logger.error(f"查询监测结果信息表字段异常，异常为：{e}")
        raise



if __name__ == '__main__':
    print(monitor_result_data_column('business_monitor_result_info'))