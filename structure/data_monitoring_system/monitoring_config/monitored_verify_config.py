# -*- coding: utf-8 -*-
"""
@Time ： 2024/10/8 17:35
@Auth ： 逗逗的小老鼠
@File ：monitored_verify_config.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：获取检测数据验证配置信息
"""
from lib.get_log import logger, exception
from lib.deal_db_mysql import db_mysql_connect

@exception(logger)
def monitor_verify_config_query(**kwargs):
    "查询数据验证的表达式"
    try:
        if "environment_flag" in kwargs:
            del kwargs['environment_flag']
        query_sql="""
            SELECT * FROM business_monitor_verify_config WHERE is_delete=0
        """
        query_result = db_mysql_connect("insight", query_sql,environment_flag="common", **kwargs)
        query_data = query_result['data']
        # print(query_result)
        return query_data

    except Exception as e:
        logger.error(f"查询数据验证的表达式发生异常，异常为：{e}")
        raise



if __name__ == '__main__':
    monitor_verify_config_query()