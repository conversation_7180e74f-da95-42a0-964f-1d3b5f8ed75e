/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "gmock/gmock.h"
#include "gtest/gtest.h"

#include "SocketUtil.h"

using ::testing::InitGoogleMock;
using ::testing::InitGoogleTest;
using testing::Return;

TEST(socketUtil, init) {
  sockaddr addr = rocketmq::IPPort2socketAddress(inet_addr("127.0.0.1"), 10091);

  EXPECT_EQ(rocketmq::socketAddress2IPPort(addr), "*********:10091");

  int host;
  int port;

  rocketmq::socketAddress2IPPort(addr, host, port);
  EXPECT_EQ(host, inet_addr("127.0.0.1"));
  EXPECT_EQ(port, 10091);

  EXPECT_EQ(rocketmq::socketAddress2String(addr), "*********");
}

int main(int argc, char* argv[]) {
  InitGoogleMock(&argc, argv);
  testing::GTEST_FLAG(throw_on_failure) = true;
  testing::GTEST_FLAG(filter) = "socketUtil.init";
  int itestts = RUN_ALL_TESTS();
  return itestts;
}
