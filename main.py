# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/3 22:03
@Auth ： 逗逗的小老鼠
@File ：apps.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

from structure.handle.handle_rocketmq_consumer import RocketMQConsumer
from lib.deal_jsonpath import data_by_jsonpath
import json
from structure.data_monitoring_system.monitoring_data.monitored_data_transformation_assembly import data_transformation_assembly
from structure.data_monitoring_system.monitoring_task.rocketmq_task import rocketmq_message_task

if __name__ == "__main__":
    jsonpath = "$.orderDetailList[*].baseOrderDetailInfo"
    data = """
    {
    "baseOrderInfo":
    {
        "orderNo":
        {
            "orderNo": "1186969224289370018"
        },
        "parentOrderNo": null,
        "thirdPlatformCodeValue": "KE_CHUAN",
        "thirdOrderNo":
        {
            "thirdOrderNo": "TO1054344083"
        },
        "parentThirdOrderNo": null,
        "dayNum": "",
        "orderStateValue": "DONE",
        "created": "2025-04-17 11:26:16",
        "payTime": "2025-04-17 11:26:16",
        "completeTime": "2025-04-17 11:26:16",
        "billTime": "2025-04-17 11:26:16",
        "dataVersion": 1,
        "actualPayAmount": 101.1,
        "actualCollectAmount": 101.1,
        "orderCouponList":
        [
            {
                "couponCode": "${couponCode}"
            }
        ],
        "serialNo": "${serialNo}",
        "isOnPromotion": false,
        "orderTagList": null
    },
    "basePrescriptionInfo":
    {
        "prescriptionTypeValue": null,
        "prescriptionNo": null
    },
    "payInfoList":
    [
        {
            "payType":
            {
                "payType": "CH"
            },
            "payName": "现金",
            "payAmount": 101.1
        }
    ],
    "orderDetailList":
    [
        {
            "baseOrderDetailInfo":
            {
                "orderNo":
                {
                    "orderNo": "1186969224289370018"
                },
                "rowNo": "1",
                "platformSkuId":
                {
                    "platformSkuId": null
                },
                "erpCode":
                {
                    "erpCode": "100001"
                },
                "erpName": "妇可舒医用磁热贴(A型)_4贴",
                "commodityCount": 1.0,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 78.0,
                "price": 62.38,
                "commodityCostPrice": 22.287333,
                "totalAmount": 78.0,
                "totalOriginalAmount": 78.0,
                "discountShare": 15.62,
                "discountAmount": 15.62,
                "billPrice": 62.38,
                "billAmount": 62.38,
                "isOnPromotion": false,
                "fiveClass": "A041304002"
            },
            "pickInfoList":
            []
        },
        {
            "baseOrderDetailInfo":
            {
                "orderNo":
                {
                    "orderNo": "1186969224289370018"
                },
                "rowNo": "1",
                "platformSkuId":
                {
                    "platformSkuId": null
                },
                "erpCode":
                {
                    "erpCode": "181351"
                },
                "erpName": "(兴)小儿肺热咳喘颗粒 葫芦娃4g*13袋4G*13袋",
                "commodityCount": 1.0,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 78.0,
                "price": 62.38,
                "commodityCostPrice": 12.127287,
                "totalAmount": 78.0,
                "totalOriginalAmount": 78.0,
                "discountShare": 15.62,
                "discountAmount": 15.62,
                "billPrice": 62.38,
                "billAmount": 62.38,
                "isOnPromotion": false,
                "fiveClass": "A041304002"
            },
            "pickInfoList":
            []
        },
        {
            "baseOrderDetailInfo":
            {
                "orderNo":
                {
                    "orderNo": "1186969224289370018"
                },
                "rowNo": "2",
                "platformSkuId":
                {
                    "platformSkuId": null
                },
                "erpCode":
                {
                    "erpCode": "149856"
                },
                "erpName": "陈香露白露片_红云制药_0.5g*100片",
                "commodityCount": 1.0,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 78.0,
                "price": 62.38,
                "commodityCostPrice": 4.424999,
                "totalAmount": 78.0,
                "totalOriginalAmount": 78.0,
                "discountShare": 15.62,
                "discountAmount": 15.62,
                "billPrice": 62.38,
                "billAmount": 62.38,
                "isOnPromotion": false,
                "fiveClass": "A041304002"
            },
            "pickInfoList":
            []
        },
        {
            "baseOrderDetailInfo":
            {
                "orderNo":
                {
                    "orderNo": "1186969224289370018"
                },
                "rowNo": "2",
                "platformSkuId":
                {
                    "platformSkuId": null
                },
                "erpCode":
                {
                    "erpCode": "131075"
                },
                "erpName": "第六感超薄超润滑天然橡胶胶乳避孕套(光面)_52mm*10只+2只",
                "commodityCount": 1.0,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 78.0,
                "price": 62.38,
                "commodityCostPrice": 24.425865,
                "totalAmount": 78.0,
                "totalOriginalAmount": 78.0,
                "discountShare": 15.62,
                "discountAmount": 15.62,
                "billPrice": 62.38,
                "billAmount": 62.38,
                "isOnPromotion": false,
                "fiveClass": "A041304002"
            },
            "pickInfoList":
            []
        },
        {
            "baseOrderDetailInfo":
            {
                "orderNo":
                {
                    "orderNo": "1186969224289370018"
                },
                "rowNo": "3",
                "platformSkuId":
                {
                    "platformSkuId": null
                },
                "erpCode":
                {
                    "erpCode": "105167"
                },
                "erpName": "盐酸吡格列酮片_15mg*7片*3板_重庆科瑞",
                "commodityCount": 5.0,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 78.0,
                "price": 62.38,
                "commodityCostPrice": null,
                "totalAmount": 78.0,
                "totalOriginalAmount": 390.0,
                "discountShare": 78.1,
                "discountAmount": 15.62,
                "billPrice": 62.38,
                "billAmount": 62.38,
                "isOnPromotion": false,
                "fiveClass": "A010105006"
            },
            "pickInfoList":
            []
        },
        {
            "baseOrderDetailInfo":
            {
                "orderNo":
                {
                    "orderNo": "1186969224289370018"
                },
                "rowNo": "3",
                "platformSkuId":
                {
                    "platformSkuId": null
                },
                "erpCode":
                {
                    "erpCode": "102400"
                },
                "erpName": "健心胶囊 仁和 0.25G*12粒*10板",
                "commodityCount": 5.0,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 78.0,
                "price": 62.38,
                "commodityCostPrice": null,
                "totalAmount": 78.0,
                "totalOriginalAmount": 390.0,
                "discountShare": 78.1,
                "discountAmount": 15.62,
                "billPrice": 62.38,
                "billAmount": 62.38,
                "isOnPromotion": false,
                "fiveClass": "A041304002"
            },
            "pickInfoList":
            []
        },
        {
            "baseOrderDetailInfo":
            {
                "orderNo":
                {
                    "orderNo": "1186969224289370018"
                },
                "rowNo": "3",
                "platformSkuId":
                {
                    "platformSkuId": null
                },
                "erpCode":
                {
                    "erpCode": "830257"
                },
                "erpName": "藿香正气口服液(ZZ)_太极_10ml*2支",
                "commodityCount": 1.0,
                "statusValue": "NORMAL",
                "giftTypeValue": "NOT_GIFT",
                "originalPrice": 78.0,
                "price": 62.38,
                "commodityCostPrice": null,
                "totalAmount": 78.0,
                "totalOriginalAmount": 78.0,
                "discountShare": 15.62,
                "discountAmount": 15.62,
                "billPrice": 62.38,
                "billAmount": 62.38,
                "isOnPromotion": false,
                "fiveClass": "A041304002"
            },
            "pickInfoList":
            []
        }
    ],
    "baseUserInfo":
    {
        "userId":
        {
            "userId": "1829538544635428873"
        },
        "userName": "导入档案1",
        "userCardNo":
        {
            "userCardNo": "200000610507"
        },
        "userMobile":
        {
            "userMobile": "19871987198"
        }
    },
    "baseOrganizationInfo":
    {
        "storeCode": "A011",
        "storeName": "一心堂昆明金色俊园连锁二店",
        "companyCode": null,
        "companyName": null,
        "storeDirectJoinTypeValue": "DIRECT_SALES"
    },
    "baseCashierDeskInfo":
    {
        "posCashierDeskNo":
        {
            "posCashierDeskNo": "01"
        },
        "cashier": "1162318",
        "cashierName": "张鹏",
        "picker": null,
        "pickerName": null,
        "shiftId":
        {
            "shiftId": null
        },
        "shiftDate": null
    },
    "medInsSettle": null
}
    """
    # result = data_transformation_assembly(json.loads(data),
    #                       data_group_name="TP_ORDER_OFFLINE_ORDER-DATA_TAG_CREATED")
    rocketmq_message_task("",message_body=json.loads(data),data_group_name="TP_ORDER_OFFLINE_ORDER-DATA_TAG_CREATED")
    # print(result)
    # # 使用示例
    # consumer = RocketMQConsumer(
    #     name_servers="10.4.3.242:9876;10.4.3.243:9876",
    #     consumer_group="test_group",
    #     topics=["TP_ORDER_OFFLINE_SYNC-HD_2"],
    #     retry_policy={
    #         "max_attempts": 10,  # 最大重试次数
    #         "base_delay": 1  # 基础延迟时间(秒)
    #     }
    # )
    #
    # try:
    #     consumer.start()
    # except KeyboardInterrupt:
    #     consumer.stop()
