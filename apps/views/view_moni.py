# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/24 9:51
@Auth ： 逗逗的小老鼠
@File ：view_moni.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from flask import Blueprint,request
from structure.monitor.moni_kc_heartbeat import order_kc_heartbeat
from structure.monitor.moni_order_erpcode import order_H1_erpcode

moni=Blueprint('moni',__name__)

# 比对科传心跳异常
@moni.route('moni_kc_hrartbeat_exc',methods=["POST"])
def moni_kc_hrartbeat_exc():
    try:
        request_data=request.get_json()
        data=order_kc_heartbeat()
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 比对异常
@moni.route('moni_H1_erpcode_exc',methods=["POST"])
def moni_H1_erpcode_exc():
    try:
        request_data=request.get_json()
        data=order_H1_erpcode(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res
