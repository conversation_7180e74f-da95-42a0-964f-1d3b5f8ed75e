# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/11 17:07
@Auth ： 逗逗的小老鼠
@File ：view_moni_order.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from flask import Blueprint,request
from lib.deal_json import json_loads,json_dumps
from structure.data_monitoring_system.verify_result.deal_verify_result import monitor_verify_info_query,monitor_verify_detail_query,monitor_verify_repair
from structure.data_monitoring_system.monitoring_task.order_monitoring_task import order_monitoring_task
from structure.data_monitoring_system.monitoring_task.scheduler_timeout_task import shutdown_timeout_task,timeout_warning_reset
import json


moni_order_system=Blueprint('moni_order_system',__name__)


@moni_order_system.route('verify_info',methods=["POST"])
def verify_info():
    "查询异常整体数据"
    try:
        request_body = request.get_json()
        # 将获取的json数据转字典
        request_data=json_loads(request_body)
        data=monitor_verify_info_query(**request_data)
        # 构建成功响应，包含状态码200和差异数据
        res = {"code": 200, "data": (data)}
        return res
    # 捕获任何异常，并返回错误信息
    except Exception as e:
        # 构建失败响应，包含状态码500和错误消息
        res = {"code": 500, "msg": e}
        return res



@moni_order_system.route('verify_detail',methods=["POST"])
def verify_detail():
    "查询异常详情"
    try:
        request_body = request.get_json()
        # 将获取的json数据转字典
        request_data=json_loads(request_body)
        data=monitor_verify_detail_query(**request_data)
        # 构建成功响应，包含状态码200和差异数据
        res = {"code": 200, "data": (data)}
        return res
    # 捕获任何异常，并返回错误信息
    except Exception as e:
        # 构建失败响应，包含状态码500和错误消息
        res = {"code": 500, "msg": e}
        return res

@moni_order_system.route('verify_repair',methods=["POST"])
def verify_repair():
    "处理异常数据"
    try:
        request_body = request.get_json()
        # 将获取的json数据转字典
        request_data=json_loads(request_body)
        data=monitor_verify_repair(**request_data)
        # 构建成功响应，包含状态码200和差异数据
        res = {"code": 200, "data": (data)}
        return res
    # 捕获任何异常，并返回错误信息
    except Exception as e:
        # 构建失败响应，包含状态码500和错误消息
        res = {"code": 500, "msg": e}
        return res


@moni_order_system.route('order_monitoring',methods=["POST"])
def order_monitoring():
    "执行订单监控任务"
    try:
        request_body = request.get_json()
        # 将获取的json数据转字典
        request_data=json_loads(request_body)
        data=order_monitoring_task(**request_data)
        # 构建成功响应，包含状态码200和差异数据
        res = {"code": 200, "data": (data)}
        return res
    # 捕获任何异常，并返回错误信息
    except Exception as e:
        # 构建失败响应，包含状态码500和错误消息
        res = {"code": 500, "msg": e}
        return res


@moni_order_system.route('shutdown_functimeout_task',methods=["POST"])
def shutdown_functimeout_task():
    "关闭方法执行超时的定时任务"
    try:
        request_body = request.get_json()
        # 将获取的json数据转字典
        request_data=json_loads(request_body)
        data=shutdown_timeout_task(**request_data)
        # 构建成功响应，包含状态码200和差异数据
        res = {"code": 200, "data": (data)}
        return res
    # 捕获任何异常，并返回错误信息
    except Exception as e:
        # 构建失败响应，包含状态码500和错误消息
        res = {"code": 500, "msg": e}
        return res


@moni_order_system.route('reset_functimeout_task',methods=["POST"])
def reset_functimeout_task():
    "执行订单监控任务"
    try:
        request_body = request.get_json()
        # 将获取的json数据转字典
        request_data=json_loads(request_body)
        data=timeout_warning_reset(**request_data)
        # 构建成功响应，包含状态码200和差异数据
        res = {"code": 200, "data": (data)}
        return res
    # 捕获任何异常，并返回错误信息
    except Exception as e:
        # 构建失败响应，包含状态码500和错误消息
        res = {"code": 500, "msg": e}
        return res