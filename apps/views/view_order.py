# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/6 18:18
@Auth ： 逗逗的小老鼠
@File ：view_order.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

#
from flask import Blueprint,request
from structure.monitor.moni_order_count import order_sale_amount_o2o_yx,order_sale_count_o2o_yx,order_return_amount_o2o_yx
from structure.monitor.moni_order_erp import order_sale_iserp_o2o_xy,order_return_iserp_o2o_xy
from structure.monitor.compare_order_info import order_return_o2o_info,order_sale_o2o_info
from structure.monitor.moni_order_stock import order_sale_o2o_stock_info,order_sale_nobill_o2o_stock_info
from structure.monitor.moni_order_bill_amount import order_sale_o2o_bill_amount
from structure.monitor.moni_order_bill_config import order_sale_o2o_bill_config
from structure.monitor.moni_order_amount_discount import order_sale_o2o_amount_discount
from structure.monitor.moni_order_efficiency import order_sale_o2o_nomal_accept,order_sale_o2o_prescription_accept,order_sale_o2o_pick
from structure.monitor.moni_order_erp_status import order_sale_o2o_erp_status_xy

compare=Blueprint('compare',__name__)

# 比对订单数量
@compare.route('sale_count',methods=["POST"])
def com_sale_count():
    try:
        request_data=request.get_json()
        data=order_sale_count_o2o_yx(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 比对订单总额
@compare.route('sale_amount',methods=["POST"])
def com_sale_amount():
    try:
        request_data=request.get_json()
        data=order_sale_amount_o2o_yx(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 比对退款单信息
@compare.route('return_order',methods=["POST"])
def com_return_order():
    try:
        request_data=request.get_json()
        data=order_return_amount_o2o_yx(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 比对订单下账状态
@compare.route('sale_erp',methods=["POST"])
def com_sale_erp():
    try:
        request_data=request.get_json()
        data=order_sale_iserp_o2o_xy(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 比对订单下账状态
@compare.route('return_erp',methods=["POST"])
def com_return_erp():
    try:
        request_data=request.get_json()
        data=order_return_iserp_o2o_xy(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 比对退款单信息
@compare.route('return_info',methods=["POST"])
def com_return_info():
    try:
        request_data=request.get_json()
        data=order_return_o2o_info(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 比对销售单信息
@compare.route('sale_info',methods=["POST","GET"])
def com_sale_info():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_info(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res


# 销售单O2O占用库存释放
@compare.route('sale_stock',methods=["POST","GET"])
def com_sale_stock():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_stock_info(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 销售单O2O占用库存
@compare.route('sale_nobill_stock',methods=["POST","GET"])
def com_nobill_sale_stock():
    try:
        request_data=request.get_json()
        data=order_sale_nobill_o2o_stock_info(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res


# 销售单O2O下账金额比对
@compare.route('sale_billl_amount',methods=["POST","GET"])
def com_sale_billl_amount():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_bill_amount(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 销售单O2O下账配置检查
@compare.route('sale_billl_config',methods=["POST","GET"])
def com_sale_billl_config():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_bill_config(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res


# 销售单O2O金额低于三折
@compare.route('sale_amount_discount',methods=["POST","GET"])
def com_sale_amount_discount():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_amount_discount(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res


# 非处方单接单效率监控
@compare.route('sale_o2o_nomal_accept',methods=["POST","GET"])
def com_order_sale_o2o_nomal_accept():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_nomal_accept(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 处方单接单效率监控
@compare.route('sale_o2o_prescription_accept',methods=["POST","GET"])
def com_order_sale_o2o_prescription_accept():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_prescription_accept(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res


# 订单单拣货效率监控
@compare.route('sale_o2o_pick',methods=["POST","GET"])
def com_order_sale_o2o_pick():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_pick(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 订单下账状态监控
@compare.route('sale_o2o_erp_status',methods=["POST","GET"])
def com_order_sale_o2o_erp_status():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_erp_status_xy(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res