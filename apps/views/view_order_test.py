# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/18 15:02
@Auth ： 逗逗的小老鼠
@File ：view_order_test.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from flask import Blueprint,request
from structure.order_test.xy_order_compare import xy_order_info_compare
from structure.order_test.xy_order_stock_compare import order_stock_check
from structure.order_test.xy_order_pick_compare import order_pick
from structure.iteration_test.order_route.order_route_test import order_route_store_test,compare_order_route


order_test=Blueprint('order_test',__name__)

@order_test.route('sale_test',methods=["POST"])
def sale_test():
    try:
        request_data = request.get_json()
        third_order_no=request_data['third_order_no']
        data=xy_order_info_compare(third_order_no)
        res = deal_order_test_result(data)
        return res
    except Exception as e:
        res = {"code": 500, "msg": "接口获取出错，请联系管理员","exce":e}
        return res


@order_test.route('stock_test',methods=["POST"])
def stock_test():
    try:
        request_data = request.get_json()
        third_order_no=request_data['third_order_no']
        data=order_stock_check(third_order_no)
        res = deal_order_test_result(data)
        return res
    except Exception as e:
        res = {"code": 500, "msg": "接口获取出错，请联系管理员","exce":e}
        return res


@order_test.route('order_pick',methods=["POST"])
def pick_test():
    try:
        request_data = request.get_json()
        third_order_no=request_data['third_order_no']
        res=order_pick(third_order_no)
        return res
    except Exception as e:
        res = {"code": 500, "msg": "接口获取出错，请联系管理员","exce":e}
        return res



@order_test.route('order_route_test',methods=["POST"])
def order_route_test():
    try:
        request_data = request.get_json()
        thirdOrderNo=request_data['thirdOrderNo']
        strategy_id=request_data['strategy_id']
        rule_key_list=request_data['rule_key_list']
        res=order_route_store_test(thirdOrderNo,strategy_id,rule_key_list)
        return res
    except Exception as e:
        res = {"code": 500, "msg": "接口获取出错，请联系管理员","exce":e}
        return res


@order_test.route('com_order_route_test',methods=["POST"])
def com_order_route_test():
    try:
        request_data = request.get_json()
        thirdOrderNo=request_data['thirdOrderNo']
        strategy_id=request_data['strategy_id']
        rule_key_list=request_data['rule_key_list']
        res=compare_order_route(thirdOrderNo,strategy_id,rule_key_list)
        return res
    except Exception as e:
        res = {"code": 500, "msg": "接口获取出错，请联系管理员","exce":e}
        return res


"""
    处理测试结果接口返回数据
"""
def deal_order_test_result(data):
    try:
        sum_pass=0
        sum_fail=0
        code=200
        for data_item in data:
            sum_pass=sum_pass+data_item['compare_pass']
            sum_fail=sum_fail+data_item['compare_fail']
        if sum_fail>0:
            code=100
        result={"code":code,"sum_pass":sum_pass,"sum_fail":sum_fail,"data":data}
        return result
    except Exception as e:
        raise e

