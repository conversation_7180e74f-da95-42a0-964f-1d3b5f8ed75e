# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/7 14:26
@Auth ： 逗逗的小老鼠
@File ：view_task.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from flask import Blueprint,request
from lib.deal_scheduler import sch_manage_job
from structure.handle.handle_scheduler import schedul_task_config_manager


task=Blueprint('task',__name__)

@task.route('task_manage',methods=["POST"])
def task_manage():
    try:
        request_data = request.get_json()
        manage_type=request_data['manage_type']
        job_id=request_data['job_id']
        data=sch_manage_job(manage_type,job_id)
        res = {"code": 200, "data": data}
        return res
    except Exception as e:
        res = {"code": 500, "msg": "接口获取出错，请联系管理员"}
        return res

# 配置任务管理
@task.route('task_config_manage',methods=["POST"])
def task_config_manager():
    try:
        request_data = request.get_json()
        manage_type=request_data['manage_type']
        task_info=request_data['task_info']
        data=schedul_task_config_manager(manage_type,task_info)
        res = {"code": 200, "data": data}
        return res
    except Exception as e:
        res = {"code": 500, "msg": "接口获取出错，请联系管理员"}
        return res