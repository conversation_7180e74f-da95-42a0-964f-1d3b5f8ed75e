# -*- coding: utf-8 -*-
"""
@Time ： 2024/8/6 17:34
@Auth ： 逗逗的小老鼠
@File ：view_tools.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from flask import Blueprint,request
from structure.tools.diff_json import diff_json_value
from structure.tools.order_info_write import test_order_info_write
import json


tools=Blueprint('tools',__name__)

# 定义一个名为diff_json的函数，用于处理JSON差异请求
@tools.route('diff_json',methods=["POST"])
def diff_json():
    # 尝试从请求中获取JSON数据
    try:
        request_data = request.get_json()
        # 调用diff_json_value函数计算请求数据的差异，并将结果赋值给data
        data=diff_json_value(request_data)
        # 构建成功响应，包含状态码200和差异数据
        res = {"code": 200, "data": data}
        return res
    # 捕获任何异常，并返回错误信息
    except Exception as e:
        # 构建失败响应，包含状态码500和错误消息
        res = {"code": 500, "msg": e}
        return res

@tools.route('test_order_mq',methods=["POST"])
def test_order_mq():
    # 尝试从请求中获取JSON数据
    try:
        # request_data = request.get_json()
        # 调用diff_json_value函数计算请求数据的差异，并将结果赋值给data
        data=test_order_info_write()
        # 构建成功响应，包含状态码200和差异数据
        res = {"code": 200, "data": data}
        return res
    # 捕获任何异常，并返回错误信息
    except Exception as e:
        # 构建失败响应，包含状态码500和错误消息
        res = {"code": 500, "msg": e}
        return res
