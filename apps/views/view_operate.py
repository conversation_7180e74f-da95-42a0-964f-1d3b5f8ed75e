# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/25 9:44
@Auth ： 逗逗的小老鼠
@File ：view_operate.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""


from flask import Blueprint,request
from structure.operate.operate_mt_shop import mt_shop_renewal
from structure.operate.operate_commodity_syn import operate_commodity_stock_syn
from structure.jenkins.JenkinsAPI import post_json
from structure.operate.operate_b2c_commodity_release import release_b2c_commodity_stock

operate=Blueprint('operate',__name__)

# 美团测试门店续期
@operate.route('mt_test_shop_renewal',methods=["POST"])
def mt_test_shop_renewal():
    try:
        request_data=request.get_json()
        data=mt_shop_renewal()
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 测试环境门店库存及价格同步修改
@operate.route('commodity_stock_syn_config',methods=["POST"])
def commodity_stock_syn_config():
    try:
        request_data=request.get_json()
        data=operate_commodity_stock_syn(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res


# 测试计划调用
@operate.route('jenkins_run_MStest',methods=["POST"])
def jenkins_run_MStest():
    try:
        request_data=request.get_json()
        res=post_json(request_data)
        # res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res


# B2C库存占用调用
@operate.route('release_b2c_stock',methods=["POST"])
def release_b2c_stock():
    try:
        request_data=request.get_json()
        res=release_b2c_commodity_stock(**request_data)
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res