# -*- coding: utf-8 -*-
"""
@Time ： 2024/1/4 17:14
@Auth ： 逗逗的小老鼠
@File ：view_migration.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from flask import Blueprint,request
from structure.migration.order_sale_o2o_migration import order_sale_o2o_base_info,order_sale_o2o_sum_amount,order_sale_o2o_amount,order_sale_o2o_sum_detail
from structure.migration.order_refund_o2o_migration import order_o2o_refund_info,order_o2o_refund_detail_sum
from structure.migration.erp_no import test_erp_np


migration=Blueprint('migration',__name__)

# 检查订单基本信息
@migration.route('sale_base_info',methods=["POST"])
def migration_sale_base_info():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_base_info(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 检查订单金额汇总
@migration.route('sale_sum_amount',methods=["POST"])
def migration_sale_sum_amount():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_sum_amount(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res


# 检查订单详情
@migration.route('sale_sum_detail',methods=["POST"])
def migration_sale_sum_detail():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_sum_detail(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 检查订单金额
@migration.route('sale_amount',methods=["POST"])
def migration_sale_amount():
    try:
        request_data=request.get_json()
        data=order_sale_o2o_amount(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res

# 核对逆向单信息
@migration.route('refund_info',methods=["POST"])
def migration_refund_info():
    try:
        request_data=request.get_json()
        data=order_o2o_refund_info(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res



# 核对逆向单信息
@migration.route('refund_detail',methods=["POST"])
def migration_refund_detail():
    try:
        request_data=request.get_json()
        data=order_o2o_refund_detail_sum(request_data)
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res


# 临时使用
@migration.route('test_erp_np',methods=["POST"])
def test_erp_bill_np():
    try:
        request_data=request.get_json()
        data=test_erp_np()
        res={"code":200,"data":data}
        return res
    except Exception as e:
        res={"code":500,"msg":"接口获取出错，请联系管理员","info":e}
        return res