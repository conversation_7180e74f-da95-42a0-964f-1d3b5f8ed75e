# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/3 21:49
@Auth ： 逗逗的小老鼠
@File ：__init__.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from flask import Flask, Blueprint
from .config import config
from lib.get_log import logger, exception
import os
import importlib

@exception(logger)
def create_app(config_name=None):
    try:
        app = Flask(__name__)

        # 根据环境变量加载配置（默认开发环境）
        if not config_name:
            config_name = os.getenv("FLASK_ENV", "development")
        app.config.from_object(config[config_name])

        auto_register_blueprints(app)

        return app
    except Exception as e:
        raise


def auto_register_blueprints(app):
    # 获取当前脚本的绝对路径（假设 app.py 在 myproject 根目录）
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 构建 views 目录的绝对路径：myproject/apps/views
    views_dir = os.path.join(current_dir,"views")

    # print(f"[调试] 当前目录: {current_dir}")
    # print(f"[调试] Views目录绝对路径: {views_dir}")
    # print(f"[调试] 目录是否存在: {os.path.exists(views_dir)}")

    if not os.path.exists(views_dir):
        raise FileNotFoundError(f"Views目录不存在: {views_dir}")

    # 遍历 views 目录
    for filename in os.listdir(views_dir):
        if filename.endswith(".py") and filename != "__init__.py":
            # 模块名（如 user）
            module_name = filename[:-3]
            # 动态导入模块的路径（注意 apps.views）
            module_path = f"apps.views.{module_name}"

            try:
                module = importlib.import_module(module_path)
                for var_name in dir(module):
                    obj = getattr(module, var_name)
                    if isinstance(obj, Blueprint):
                        # 从配置中获取蓝图前缀（优先使用蓝图专用配置）
                        bp_prefix = app.config.get("BLUEPRINT_PREFIXES", {}).get(module_name)
                        if not bp_prefix:
                            # 使用全局前缀 + 模块名
                            url_prefix = f"{app.config['API_PREFIX']}/{var_name}"
                        # 生成动态前缀（如 /api/tools）
                        # url_prefix = f"/api/{var_name}"
                        # 注册蓝图
                        app.register_blueprint(obj, url_prefix=url_prefix)
                        print(f"[Success] 注册蓝图: 变量名={var_name}, 前缀={url_prefix}")
            except Exception as e:
                print(f"[错误] 导入模块失败: {module_path}, 错误: {e}")


