# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/3 21:50
@Auth ： 逗逗的小老鼠
@File ：config.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
# config.py
import os

class Config:
    # 通用配置
    API_PREFIX = "/api"  # 全局API前缀
    PORT = 8000  # 默认端口
    DEBUG = False
    HOST = "0.0.0.0"
    BLUEPRINT_PREFIXES = {
        "tools": "/api/tools",  # 可覆盖特定蓝图的前缀
    }
    SECRET_KEY = os.getenv("SECRET_KEY", "default-secret-key")


class DevelopmentConfig(Config):
    DEBUG = True
    FLASK_DEBUG = 1
    API_PREFIX = "/dev/api"  # 开发环境API前缀

class ProductionConfig(Config):
    PORT = 9099  # 生产环境端口
    HOST = "0.0.0.0"
    DEBUG = False
    FLASK_DEBUG = 0
    API_PREFIX = "/prod/api"  # 生产环境API前缀

config = {
    "test": DevelopmentConfig,
    "prod": ProductionConfig,
    "default": DevelopmentConfig
}