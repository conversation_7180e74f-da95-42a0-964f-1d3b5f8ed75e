---
name: ISSUE_TEMPLATE
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

The issue tracker is **ONLY** used for the CPP/C client (feature request of RocketMQ need to follow [RIP process](https://github.com/apache/rocketmq/wiki/RocketMQ-Improvement-Proposal)). Keep in mind, please check whether there is an existing same report before your raise a new one.

Alternately (especially if your communication is not a bug report), you can send mail to our [mailing lists](http://rocketmq.apache.org/about/contact/). We welcome any friendly suggestions, bug fixes, collaboration, and other improvements.

Please ensure that your bug report is clear and that it is complete. Otherwise, we may be unable to understand it or to reproduce it, either of which would prevent us from fixing the bug. We strongly recommend the report(bug report or feature request) could include some hints as to the following:

**BUG REPORT**

1. Please describe the issue you observed:

- What did you do (The steps to reproduce)?

- What did you expect to see?

- What did you see instead?

2. Please tell us about your environment:

 - What is your OS?

 - What is your client version?

 - What is your RocketMQ version?

3. Other information (e.g. detailed explanation, logs, related issues, suggestions on how to fix, etc):

**FEATURE REQUEST**

1. Please describe the feature you are requesting.

2. Provide any additional detail on your proposed use case for this feature.

2. Indicate the importance of this issue to you (blocker, must-have, should-have, nice-to-have). Are you currently using any workarounds to address this issue?

4. If there are some sub-tasks using -[] for each subtask and create a corresponding issue to map to the sub task:

- [sub-task1-issue-number](example_sub_issue1_link_here): sub-task1 description here, 
- [sub-task2-issue-number](example_sub_issue2_link_here): sub-task2 description here,
- ...
