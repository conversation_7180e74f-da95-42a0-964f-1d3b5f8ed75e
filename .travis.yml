language: cpp
sudo: required
dist: trusty
compiler: gcc

addons:
  apt:
    packages: lcov

install:
  #- sudo apt-get update
  #- sudo apt-get install -y git gcc-4.8 g++-4.8 autoconf cmake libtool wget unzip libbz2-dev zlib1g-dev
  - echo 'MAVEN_OPTS="$MAVEN_OPTS -Xmx1024m -XX:MaxPermSize=512m -XX:+BytecodeVerificationLocal"' >> ~/.mavenrc
  - cat ~/.mavenrc
  - if [ "$TRAVIS_OS_NAME" == "osx" ]; then export JAVA_HOME=$(/usr/libexec/java_home); fi
  - if [ "$TRAVIS_OS_NAME" == "linux" ]; then jdk_switcher use "$CUSTOM_JDK"; fi

before_script:
  - wget https://archive.apache.org/dist/rocketmq/4.3.2/rocketmq-all-4.3.2-bin-release.zip
  - unzip rocketmq-all-4.3.2-bin-release.zip
  - cd rocketmq-all-4.3.2-bin-release
  - perl -i -pe's/-Xms8g -Xmx8g -Xmn4g/-Xms2g -Xmx2g -Xmn1g/g' bin/runbroker.sh
  - nohup sh bin/mqnamesrv &
  - nohup sh bin/mqbroker -n localhost:9876 &
  - sleep 10
  - ./bin/mqadmin updateTopic -b '127.0.0.1:10911' –n '127.0.0.1:9876' -t test
  - ./bin/mqadmin updateSubGroup -b '127.0.0.1:10911' –n '127.0.0.1:9876' -g testGroup
  - cd ..

script:
  - ./build.sh test codecov noVerbose

after_success:
  # Create lcov report
  # capture coverage info
  - lcov --directory . --capture --output-file coverage.info
  # filter out system and extra files.
  # To also not include test code in coverage add them with full path to the patterns: '*/tests/*'
  - lcov --remove coverage.info '/usr/*' '/home/<USER>/build/*/rocketmq-client-cpp/bin/*' '/home/<USER>/build/*/rocketmq-client-cpp/libs/*' --output-file coverage.info
  # output coverage data for debugging (optional)
  - lcov --list coverage.info
  # Uploading to CodeCov
  # '-f' specifies file(s) to use and disables manual coverage gathering and file search which has already been done above
  - bash <(curl -s https://codecov.io/bash) -f coverage.info || echo "Codecov did not collect coverage reports"

matrix:
  include:
    - os: linux
      env: CUSTOM_JDK="oraclejdk8"

    # This is the job to check code format.
    - os: linux
      dist: trusty
      env: LINT=1 PYTHON=2.7
      before_install:
        #- sudo apt-get update -qq
        #- sudo apt-get install -qq clang-format-3.8
      install: []
      script:
        - sudo sh .travis/check-git-clang-format.sh
