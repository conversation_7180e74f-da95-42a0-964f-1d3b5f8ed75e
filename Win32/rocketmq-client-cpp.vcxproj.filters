<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
    <Filter Include="common">
      <UniqueIdentifier>{41638677-648d-470b-a0dd-27ffbb643445}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\libs\signature\src\base64.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\libs\signature\src\hmac.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\libs\signature\src\param_list.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\libs\signature\src\sha1.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\libs\signature\src\sha256.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\libs\signature\src\sha512.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\libs\signature\src\spas_client.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\Arg_helper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\AsyncCallbackWrap.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\big_endian.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\ClientRPCHook.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\dataBlock.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\InputStream.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\MemoryInputStream.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\MemoryOutputStream.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\MessageSysFlag.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\MQClient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\MQVersion.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\OutputStream.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\PermName.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\PullSysFlag.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\sync_http_client.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\TopAddressing.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\TopicConfig.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\url.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\UtilAll.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\Validators.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\common\VirtualEnvUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\consumer\ConsumeMessageConcurrentlyService.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\consumer\ConsumeMessageOrderlyService.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\consumer\DefaultMQPullConsumer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\consumer\DefaultMQPushConsumer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\consumer\OffsetStore.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\consumer\PullAPIWrapper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\consumer\PullRequest.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\consumer\PullResult.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\consumer\Rebalance.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\consumer\SubscriptionData.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\dllmain.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\extern\CMessage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\extern\CMessageExt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\extern\CProducer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\extern\CPullConsumer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\extern\CPushConsumer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\extern\CSendResult.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\log\Logging.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\message\MQDecoder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\message\MQMessage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\message\MQMessageExt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\message\MQMessageQueue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\MQClientAPIImpl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\MQClientFactory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\MQClientManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\producer\DefaultMQProducer.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\producer\SendResult.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\protocol\CommandHeader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\protocol\ConsumerRunningInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\protocol\LockBatchBody.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\protocol\MessageQueue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\protocol\RemotingCommand.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\thread\task_queue.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\transport\ClientRemotingProcessor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\transport\ResponseFuture.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\transport\SocketUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\transport\TcpRemotingClient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\transport\TcpTransport.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\src\producer\StringIdMaker.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\src\common\AsyncArg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\AsyncCallbackWrap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\big_endian.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\ByteOrder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\ClientRPCHook.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\CommunicationMode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\dataBlock.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\FilterAPI.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\InputStream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\MemoryInputStream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\MemoryOutputStream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\MessageSysFlag.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\MQVersion.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\NamesrvConfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\OutputStream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\PermName.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\PullSysFlag.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\ServiceState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\SubscriptionGroupConfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\sync_http_client.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\TopAddressing.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\TopicConfig.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\TopicFilterType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\url.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\UtilAll.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\Validators.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\common\VirtualEnvUtil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\consumer\AllocateMQStrategy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\consumer\ConsumeMsgService.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\consumer\FindBrokerResult.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\consumer\OffsetStore.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\consumer\PullAPIWrapper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\consumer\PullRequest.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\consumer\PullResultExt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\consumer\Rebalance.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\consumer\SubscriptionData.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\log\Logging.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\message\MQDecoder.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\message\MQMessageId.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\MQClientAPIImpl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\MQClientFactory.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\MQClientManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\producer\TopicPublishInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\CommandHeader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\ConsumerRunningInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\HeartbeatData.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\KVTable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\LockBatchBody.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\MessageQueue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\MQProtos.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\ProcessQueueInfo.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\RemotingCommand.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\RemotingSerializable.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\TopicList.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\protocol\TopicRouteData.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptorLFQ.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\batch_descriptor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\claim_strategy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\event_processor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\event_publisher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\exceptions.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\exception_handler.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\interface.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\ring_buffer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\sequence.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\sequencer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\sequence_barrier.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\disruptor\wait_strategy.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\thread\task_queue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\transport\ClientRemotingProcessor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\transport\ResponseFuture.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\transport\SocketUtil.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\transport\TcpRemotingClient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\src\transport\TcpTransport.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>