/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifndef __SENDRESULT_H__
#define __SENDRESULT_H__

#include "MQMessageQueue.h"
#include "RocketMQClient.h"

namespace rocketmq {

enum SendStatus { SEND_OK, SEND_FLUSH_DISK_TIMEOUT, SEND_FLUSH_SLAVE_TIMEOUT, SEND_SLAVE_NOT_AVAILABLE };

class ROCKETMQCLIENT_API SendResult {
 public:
  SendResult();
  SendResult(const SendStatus& sendStatus,
             const std::string& msgId,
             const std::string& offsetMsgId,
             const MQMessageQueue& messageQueue,
             int64 queueOffset);
  SendResult(const SendStatus& sendStatus,
             const std::string& msgId,
             const std::string& offsetMsgId,
             const MQMessageQueue& messageQueue,
             int64 queueOffset,
             const std::string& regionId,
             const bool traceOn);

  virtual ~SendResult();
  SendResult(const SendResult& other);
  SendResult& operator=(const SendResult& other);

  void setTransactionId(const std::string& id) { m_transactionId = id; }

  std::string getTransactionId() { return m_transactionId; }

  const std::string& getMsgId() const;
  const std::string& getOffsetMsgId() const;

  const std::string& getRegionId() const;
  void setRegionId(const std::string& regionId);
  SendStatus getSendStatus() const;
  MQMessageQueue getMessageQueue() const;
  int64 getQueueOffset() const;
  bool getTraceOn() const;

  std::string toString() const;

 private:
  SendStatus m_sendStatus;
  std::string m_msgId;
  std::string m_offsetMsgId;
  MQMessageQueue m_messageQueue;
  int64 m_queueOffset;
  std::string m_transactionId;
  std::string m_regionId;
  bool m_traceOn;
};

}  // namespace rocketmq
#endif
