/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef _ARG_HELPER_H_
#define _ARG_HELPER_H_

#include <string>
#include <vector>
#include "RocketMQClient.h"

namespace rocketmq {
class ROCKETMQCLIENT_API Arg_helper {
 public:
  Arg_helper(int argc, char* argv[]);
  Arg_helper(std::string arg_str_);
  std::string get_option(int idx_) const;
  bool is_enable_option(std::string opt_) const;
  std::string get_option_value(std::string opt_) const;

 private:
  std::vector<std::string> m_args;
};

}  // namespace rocketmq

#endif  //<!_ARG_HELPER_H_;
