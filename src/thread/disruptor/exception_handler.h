// Copyright (c) 2011, <PERSON>
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//     * Redistributions of source code must retain the above copyright
//       notice, this list of conditions and the following disclaimer.
//     * Redistributions in binary form must reproduce the above copyright
//       notice, this list of conditions and the following disclaimer in the
//       documentation and/or other materials provided with the distribution.
//     * Neither the name of the disruptor-- nor the
//       names of its contributors may be used to endorse or promote products
//       derived from this software without specific prior written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
// ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
// WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL FRANÇOIS SAINT-JACQUES BE LIABLE FOR ANY
// DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
// (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
// LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
// ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
// SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.

#ifndef DISRUPTOR_EXCEPTION_HANDLER_H_  // NOLINT
#define DISRUPTOR_EXCEPTION_HANDLER_H_  // NOLINT

#include <exception>

#include "interface.h"

namespace rocketmq {

template<typename T>
class IgnoreExceptionHandler: public ExceptionHandlerInterface<T> {
 public:
    virtual void Handle(const std::exception& exception,
                         const int64_t& sequence,
                         T* event) {
        // do nothing with the exception.
        ;
    }
};

template<typename T>
class FatalExceptionHandler: public ExceptionHandlerInterface<T> {
 public:
    virtual void Handle(const std::exception& exception,
                         const int64_t& sequence,
                         T* event) {
        // rethrow the exception
        throw exception;
    }
};

};  // namespace rocketmq

#endif // DISRUPTOR_EXCEPTION_HANDLER_H_  NOLINT
