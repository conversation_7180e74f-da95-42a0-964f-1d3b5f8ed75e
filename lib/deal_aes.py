# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/19 17:10
@Auth ： 逗逗的小老鼠
@File ：deal_aes.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from Crypto.Util.Padding import pad, unpad
from cryptography.hazmat.primitives import padding
from base64 import b64encode,b64decode
from os import urandom


# 密钥生成和加密工具类
class AESUtils:
    # 原始密钥
    KEY_STR = "hydee-order"
    # 密钥算法
    KEY_ALGORITHM = "AES"
    # 加密-解密算法 / 工作模式 / 填充方式
    CIPHER_ALGORITHM = "AES-ECB"
    # 密钥
    key = None

    @classmethod
    def generate_key(cls):
        # 由于Python的cryptography库不直接支持使用特定的种子生成密钥，
        # 这里我们简单生成一个随机的AES密钥。
        # 如果确实需要基于特定种子生成密钥，需要实现自己的密钥派生函数。
        from cryptography.hazmat.primitives.asymmetric import padding
        from cryptography.hazmat.primitives import hashes
        from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

        # 使用PBKDF2HMAC从KEY_STR派生出密钥
        salt = urandom(16)
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
            backend=default_backend()
        )
        derived_key = kdf.derive(cls.KEY_STR.encode())
        return derived_key[:16]  # 取前16字节作为AES密钥

    @classmethod
    def encrypt(cls, plaintext):
        if cls.key is None:
            cls.key = cls.generate_key()

        # 对明文进行填充
        padder = padding.PKCS7(128).padder()
        padded_data = padder.update(plaintext.encode()) + padder.finalize()

        # 创建Cipher对象
        cipher = Cipher(algorithms.AES(cls.key), modes.ECB(), backend=default_backend())
        encryptor = cipher.encryptor()

        # 加密
        ciphertext = encryptor.update(padded_data) + encryptor.finalize()

        # Base64编码
        encoded_ciphertext = b64encode(ciphertext).decode('utf-8')

        return encoded_ciphertext

    # def decrypt(cls, encrypted_str):
    #
    #     try:
    #         # 获取AES密钥
    #         if cls.key is None:
    #             cls.key = cls.generate_key()
    #         # 对Base64编码的字符串进行解码
    #         encrypted_bytes = b64decode(encrypted_str)
    #
    #         # 创建Cipher对象
    #         cipher = AES.new(self.key, AES.MODE_ECB)
    #
    #         # 进行解密
    #         decrypted_bytes = cipher.decrypt(encrypted_bytes)
    #
    #         # 去除填充
    #         decrypted_text = unpad(decrypted_bytes, algorithms.AES.block_size).decode('utf-8')
    #
    #         return decrypted_text
    #     except Exception as e:
    #         # 抛出解密异常，让调用者知道解密失败
    #         raise ValueError("Decryption failed: {}".format(e))

        # 示例用法
aes_utils = AESUtils()
plaintext = "255556332"
encrypted_text = aes_utils.encrypt(plaintext)
print(encrypted_text)
# decode_text=aes_utils.decrypt(encrypted_text)
# print(decode_text)