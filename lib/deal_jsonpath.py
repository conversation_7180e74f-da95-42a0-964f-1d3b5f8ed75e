# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/18 11:37
@Auth ： 逗逗的小老鼠
@File ：deal_jsonpath.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger, exception
import jsonpath

@exception(logger)
def data_by_jsonpath(json_data,json_path,**kwargs):
    # 通过jsonpath获取json中对应的值
    try:
        filed_name=kwargs.get("filed_name","")
        result=""
        try:
            result = jsonpath.jsonpath(json_data, json_path)
        except:
            result = json_data.get(filed_name)
        finally:
            # 如果未获取到值，则返回空字符串
            if not result:
                result=""
            if filed_name:
                return {filed_name: result}
            else:
                return result

    except Exception as e:
        raise


if __name__ == '__main__':
    json_data = {
        "name": "John",
        "age": 30,
        "address": {
            "city": "New York",
            "country": "USA"
        },
        "hobbies": ["reading", "traveling"],
        "decription": [{"type":"os","name":"windows"},{"type":"os","name":"linux"}]
    }
    json_path = ""
    result = data_by_jsonpath(json_data,json_path,filed_name="name")
    print(result)