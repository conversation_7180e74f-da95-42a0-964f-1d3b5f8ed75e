# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/10 15:34
@Auth ： 逗逗的小老鼠
@File ：deal_sort.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception



"""
    根据list中的字典值进行排序
"""
def sort_list_dict(sort_list,sort_key):
    try:
        sort_list.sort(key=lambda x:x[sort_key])
        print(sort_list)
        return sort_list
    except Exception as e:
        raise e


if __name__=="__main__":
    value=[{"code":"A002","stock":25},{"code":"A003","stock":21},{"code":"A005","stock":28}]
    test=sort_list_dict(value,"stock")