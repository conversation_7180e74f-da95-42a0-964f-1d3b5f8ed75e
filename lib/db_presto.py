# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/23 16:50
@Auth ： 逗逗的小老鼠
@File ：db_presto.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：实现presto数据库连接
"""
from prestodb.dbapi import connect


def db_presto(sql):
    "实现presto数据库连接"
    try:
        # 创建连接
        conn = connect(
            host='presto-server-host',  # Presto服务器地址
            port=8080,  # 默认端口通常为8080
            user='your_username',  # 用户名
            catalog='hive',  # 默认目录（如hive）
            schema='default',  # 默认schema
        )
        # 执行查询
        cursor = conn.cursor()
        cursor.execute('SELECT * FROM your_table LIMIT 10')

        # 获取结果
        rows = cursor.fetchall()
        for row in rows:
            print(row)

        # 关闭连接
        cursor.close()
        conn.close()

        pass
    except Exception as e:
        raise