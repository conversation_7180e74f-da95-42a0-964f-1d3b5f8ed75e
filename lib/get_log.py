# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/6 15:27
@Auth ： 逗逗的小老鼠
@File ：get_log.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

import logging.config
import logging
import os
import traceback
from functools import wraps
from datetime import datetime
from textwrap import shorten
import sys,locale
import configparser
import tempfile
"""
    生成系统日志方法
    :param : 日志内容
    :return: 日志输出
"""


def get_log():
    curpath = os.path.abspath(os.path.dirname(__file__))
    project_root = os.path.dirname(curpath)
    logpath = os.path.abspath(project_root + os.path.sep + "cfg/log.ini")

    # 读取配置文件并修改相对路径为绝对路径
    config = configparser.ConfigParser()
    config.read(logpath, encoding="UTF-8")

    # 检查并修改fileHandler中的日志文件路径
    if config.has_section('handler_fileHandler') and config.has_option('handler_fileHandler', 'args'):
        args_str = config.get('handler_fileHandler', 'args')

        # 解析args字符串，提取日志文件路径
        if args_str.startswith('(') and ')' in args_str:
            args_parts = args_str.strip('()').split(',', 1)
            log_file_path = args_parts[0].strip('\'"')
            other_args = args_parts[1:]

            # # 检查路径是否是相对路径或需要替换的绝对路径
            # if not os.path.isabs(log_file_path) :
            # 使用项目根目录创建绝对路径
            new_log_path=os.path.abspath(project_root + os.path.sep + log_file_path)
            # 确保日志目录存在
            os.makedirs(os.path.dirname(new_log_path), exist_ok=True)

            # 构建新的args字符串
            new_args = f"('{new_log_path}'"
            if other_args:
                new_args += "," + ",".join(other_args)
            new_args += ")"

            # 更新配置
            config.set('handler_fileHandler', 'args', new_args)

    # 使用修改后的配置初始化日志系统
    # 创建临时配置文件
    import tempfile
    with tempfile.NamedTemporaryFile(delete=False, mode='w', encoding='utf-8') as temp_file:
        config.write(temp_file)
        temp_file_path = temp_file.name

    try:
        # 使用临时配置文件初始化日志
        logging.config.fileConfig(
            temp_file_path,
            defaults={'encoding': 'utf-8'},
            disable_existing_loggers=False
        )
    finally:
        # 删除临时文件
        try:
            os.unlink(temp_file_path)
        except:
            pass

    return logging.getLogger()

logger=get_log()

def exception(logger):
    """
    增强版日志装饰器
    :param logger: 日志记录器对象
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 基础信息
            func_name = func.__name__
            func_doc = shorten(func.__doc__ or "无方法描述", width=30, placeholder="...")

            # 参数处理
            params = []
            if args:
                params.append(f"位置参数：{args}")
            if kwargs:
                params.append(f"关键字参数：{kwargs}")
            param_str = "\n".join(params) or "无入参"

            # 执行计时
            start_time = datetime.now()
            result = None
            exc_info = None
            elapsed = 0

            try:
                result = func(*args,**kwargs)
                elapsed = (datetime.now() - start_time).total_seconds()

                # 成功日志模板
                log_msg = f"""
✅方法执行成功，执行详情如下：
    执行时间：{start_time.strftime('%Y-%m-%d %H:%M:%S')}
    方法名称：{func_name}
    方法描述：{func_doc}
    方法入参：
    {param_str}
    执行耗时：{elapsed:.3f}秒
    执行结果：成功
                """.strip().encode('utf-8').decode('utf-8')

                logger.info(log_msg)
                return result
            except Exception as e:
                elapsed = (datetime.now() - start_time).total_seconds()
                exc_info = traceback.format_exc()

                # 异常日志模板
                log_msg = f"""
❌方法执行异常，执行详情如下：
    执行时间：{start_time.strftime('%Y-%m-%d %H:%M:%S')}
    方法名称：{func_name}
    方法描述：{func_doc}
    方法入参：
    {param_str}
    执行耗时：{elapsed:.3f}秒
    执行结果：异常
    异常信息：
        {str(e)}
    追踪信息：
        {exc_info}
                """.strip().encode('utf-8').decode('utf-8')
                logger.error(log_msg)
                raise  # 保持原有异常抛出
            finally:
                # 可选：记录执行耗时监控
                logger.debug(f"Method {func_name} executed in {elapsed:.3f}s")

        return wrapper

    return decorator


@exception(logger)
def test(data):
    try:
        if data=="ok":
            print(data)
            logger.info("数据正常")
        else:
            logger.error("数据错误")
            raise Exception("数据错误")
    except Exception as e:
        raise e


def check_encoding():
    """ 在程序启动时调用 """
    print(f"文件系统编码: {sys.getfilesystemencoding()}")
    print(f"默认编码: {locale.getpreferredencoding()}")
    print(f"Python默认编码: {sys.getdefaultencoding()}")



if __name__=='__main__':
    # check_encoding()
    test("o11k")