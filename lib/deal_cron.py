# -*- coding: utf-8 -*-
"""
@Time ： 2024/5/27 15:03
@Auth ： 逗逗的小老鼠
@File ：deal_cron.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from crontab import CronTab
import datetime

"""
    解析cron表达式
"""
def parse_cron_expression(expression):
    parts = expression.split()
    if len(parts) not in [5, 6, 7]:
        raise ValueError("Invalid Cron Expression")
    seconds = parts[0]
    minutes = parts[1]
    hours = parts[2]
    day_of_month = parts[3]
    month = parts[4]
    day_of_week = parts[5]
    return {
        'seconds': seconds,
        'minutes': minutes,
        'hours': hours,
        'day_of_month': day_of_month,
        'month': month,
        'day_of_week': day_of_week,
    }



if __name__=="__main__":
    # 使用例子
    cron_exp = "15 10 * * * ?"
    parsed = parse_cron_expression(cron_exp)
    print(parsed)