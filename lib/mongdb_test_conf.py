# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/26 15:47
@Auth ： 逗逗的小老鼠
@File ：mongdb_test_conf.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception, logger
from lib.deal_ini import readini
from pymongo import MongoClient
import re

"""
    MongoDB链接
"""


@exception(logger)
def mongdb_test_ds_aurora(collection_name, find_precise_dict):
    try:
        host = readini("mongdb_test.ini", "test_ds_aurora", "ip")
        port = readini("mongdb_test.ini", "test_ds_aurora", "port")
        user = readini("mongdb_test.ini", "test_ds_aurora", "user")
        password = readini("mongdb_test.ini", "test_ds_aurora", "password")
        authSource = readini("mongdb_test.ini", "test_ds_aurora", "authSource")
        # 创建MongoClient
        client = MongoClient(f"mongodb://{user}:{password}@{host}:{port}/{authSource}")
        # 选择数据库
        db = client[authSource]
        # 选择集合
        collection = db[collection_name]
        find_dict = {}
        # 获取查询结果
        if isinstance(find_precise_dict, dict):
            results = collection.find(find_precise_dict)
        else:
            results = collection.find()
        result = []
        for item in results:
            result.append(item)
        client.close()
        return result
    except Exception as e:
        raise e


if __name__ == "__main__":
    collect_name = "c_oms_order_mt"
    find_precise_dict = {"olorderno": {"$in": ["3801048863144842960", "3801048882888637755"]},
                         "mqmsg": {"$regex": r'"status":"create"'}}
    find_fuzzy_dict = {'olorderno': {'$in': ['3801048863144842960', '3801048882888637755']},
                       'mqmsg': {'$regex': '"status":"create"'}}

    resylt = mongdb_test_ds_aurora(collect_name, find_fuzzy_dict)
    print(resylt)
