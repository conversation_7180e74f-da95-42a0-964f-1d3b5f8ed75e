# -*- coding: utf-8 -*-
"""
@Time ： 2024/8/19 20:06
@Auth ： 逗逗的小老鼠
@File ：deal_db_mysql.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.deal_config_json import read_json_file
import pymysql
from lib.get_log import logger,exception
from lib.deal_func_performance import func_performance


@func_performance
@exception(logger)
def db_mysql_connect(database_name,sql,**kwargs):
    try:
        json_path = "db_conf.json"
        db_config = read_json_file(json_path)
        # 获取环境变量，若无环境变量，默认为test
        environment_flag=kwargs.get("environment_flag","test")
        #
        sql_val = kwargs.get("sql_val", ())
        # 获取执行SQL，若sql值为None或者为空，默认为查询1
        if sql is None or sql == "":
            sql = "SELECT * FROM (SELECT 1) AS temp;"
        # 获取对应环境下的所有数据库信息
        database_dict=db_config.get(environment_flag)
        # 判断数据库是否在对应环境中存在
        if database_name in database_dict.keys():
            # 根据数据库名称获取对应的数据库连接信息
            database_connect=database_dict.get(database_name)
            host = database_connect.get("ip")
            user = database_connect.get("user")
            password = database_connect.get("password")
            port = database_connect.get("port")
            database = database_connect.get("dbname")
            result=db_mysql_execute(host,user,password,port,database,sql,sql_val)
            # print(f"执行sql：{sql},参数：{sql_val}")
            return result
        else:
            raise Exception(f"{database_name}数据库不存在，请检查配置文件")

    except Exception as e:
        raise e



"""
    连接MySQL数据库并执行SQL语句
    
    参数:
    host (str): 数据库主机地址
    user (str): 数据库用户名
    password (str): 数据库密码
    port (int): 数据库端口号
    database (str): 数据库名称
    sql (str): 待执行的SQL语句
    
    返回:
    dict: 包含查询结果和列名的字典，包括 'column' 和 'data' 两个键
"""
@func_performance
@exception(logger)
def db_mysql_execute(host,user,password,port,database,sql,val,**kwargs):
    "连接MySQL数据库并执行SQL语句"
    try:
        if not isinstance(val, (list, tuple)):
            raise TypeError("val_list must be a list or tuple")

        # 连接和游标改用 with 上下文管理器（自动关闭资源）
        with pymysql.connect(
                host=host, user=user, port=port,
                password=password, database=database,
                charset='utf8mb4',collation="utf8mb4_unicode_ci"
        ) as api_db:
            with api_db.cursor(pymysql.cursors.DictCursor) as cursor:  # 直接返回字典格式
                try:
                    if isinstance(val, list):
                        cursor.executemany(sql, val)
                    else:
                        cursor.execute(sql, val)  # 参数化查询
                    # 判断是否为查询语句（更精准的条件）
                    if cursor.description:
                        dbresult = cursor.fetchall()
                        columns = [col[0] for col in cursor.description]
                    else:
                        dbresult = cursor.rowcount
                        columns = None
                    api_db.commit()
                    return {"column": columns, "data": dbresult}
                except Exception as e:
                    api_db.rollback()  # 明确回滚事务
                    logger.error(f"SQL执行失败: {sql} | 参数: {val}")  # 记录错误日志
                    raise
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")  # 记录连接错误日志
        raise



if __name__ == '__main__':
    database_name="dscloud"
    sql="SELECT * FROM order_info WHERE organization_code=%s AND third_platform_code=%s;"
    sql = "SELECT * FROM order_info WHERE organization_code='A002' AND third_platform_code=27;"
    # sql_val=('A002','27')
    sql_val = ()
    environment_flag="test"
    sql_result=db_mysql_connect(database_name,sql,environment_flag=environment_flag,sql_val=sql_val)


