# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/18 10:23
@Auth ： 逗逗的小老鼠
@File ：deal_excel.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
import os
import pandas as pd
import numpy as np


"""
    读取Excel数据
"""
def read_excel(excel_name,sheet_name):
    try:
        curpath = os.path.abspath(os.path.dirname(__file__))
        excelpath = os.path.abspath(os.path.dirname(curpath) + os.path.sep + f"filed/{excel_name}")
        df=pd.read_excel(excelpath,sheet_name=sheet_name)
        data=df.fillna('')
        records_dict = data.to_dict('records')
        return records_dict
    except Exception as e:
        raise e

"""
    向excel中写入数据
"""
def write_excel(excel_name,sheet_name,column_list,data):
    try:
        curpath = os.path.abspath(os.path.dirname(__file__))
        excelpath = os.path.abspath(os.path.dirname(curpath) + os.path.sep + f"filed/{excel_name}")
        len_data=len(data)+1
        index=[i for i in range(1,len_data)]
        df=pd.DataFrame(data=data,index=index,columns=column_list)
        df.to_excel(excelpath,sheet_name=sheet_name)
    except Exception as e:
        raise e



if __name__=="__main__":
    excel_name="order_offline_hydee_sale.xlsx"
    sheet_name="sale_order"
    colus_name=["语文","数学","英语","政治"]
    data=[[10,20,30,40],[11,21,31,41],[12,22,32,42]]
    # write_excel(excel_name,sheet_name,colus_name,data)
    exl=read_excel(excel_name,sheet_name)
    print(exl)
