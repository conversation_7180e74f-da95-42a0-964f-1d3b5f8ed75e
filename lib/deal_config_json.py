# -*- coding: utf-8 -*-
"""
@Time ： 2024/8/19 19:17
@Auth ： 逗逗的小老鼠
@File ：deal_config_json.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import os
import json
import time
import threading
from contextlib import contextmanager
from lib.get_log import logger, exception

# 创建一个字典来存储每个文件的锁
_file_locks = {}
_file_locks_lock = threading.RLock()

@contextmanager
def file_lock(file_path):
    """
    线程安全的文件锁上下文管理器。

    此上下文管理器确保同一时间只有一个线程可以访问指定的文件。
    如果文件已被锁定，其他线程将等待直到锁被释放。

    参数:
    file_path (str): 要锁定的文件路径。

    用法:
    with file_lock(file_path):
        # 对文件的操作
    """
    global _file_locks

    # 获取文件的绝对路径作为锁的键
    abs_path = os.path.abspath(file_path)

    # 获取或创建文件锁
    with _file_locks_lock:
        if abs_path not in _file_locks:
            _file_locks[abs_path] = threading.RLock()
        lock = _file_locks[abs_path]

    # 尝试获取锁，最多等待10秒
    acquired = False
    try:
        # 尝试获取锁，最多尝试10次，每次等待1秒
        for _ in range(10):
            acquired = lock.acquire(blocking=False)
            if acquired:
                break
            time.sleep(1)

        if not acquired:
            logger.error(f"Could not acquire lock for file {abs_path} after 10 seconds")
            # 强制获取锁
            lock.acquire(blocking=True)

        yield
    finally:
        # 确保锁被释放
        if acquired or lock._is_owned():
            lock.release()

"""
    读取JSON文件内容。

    此函数尝试打开指定路径的JSON文件，并读取其内容。
    使用线程安全的文件锁机制确保多线程环境下的安全读取。
    如果文件成功读取，将返回读取的内容。
    如果读取过程中发生异常，将返回空字典。

    参数:
    json_path (str): JSON文件的路径。

    返回:
    dict: JSON文件的内容，如果发生错误则返回空字典。
"""
@exception(logger)
def read_json_file(json_path):
    "获取json配置文件数据，线程安全"
    try:
        conf_path = jsonpath(json_path)

        # 使用文件锁确保线程安全
        with file_lock(conf_path):
            # 打开JSON文件，使用utf-8编码读取文件内容
            with open(conf_path, 'r', encoding='utf-8') as f:
                # 读取文件内容
                file_content = f.read().strip()

                # 检查文件是否为空
                if not file_content:
                    logger.error(f"JSON file is empty: {conf_path}")
                    return {}

                try:
                    # 尝试解析JSON
                    json_data = json.loads(file_content)
                    return json_data
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON format in file {conf_path}: {str(e)}")
                    # 返回空字典而不是抛出异常，以便程序可以继续运行
                    return {}
    except FileNotFoundError as e:
        logger.error(f"File not found: {json_path}")
        # 如果文件不存在，返回空字典
        return {}
    except IOError as e:
        logger.error(f"IO error occurred while reading file: {json_path}")
        # 如果发生IO错误，返回空字典
        return {}
    except Exception as e:
        logger.error(f"An error occurred while reading the JSON file {json_path}: {str(e)}")
        # 如果发生其他异常，返回空字典
        return {}


"""
    将数据写入JSON文件。

    此函数尝试将提供的数据写入指定路径的JSON文件。
    使用线程安全的文件锁机制确保多线程环境下的安全写入。
    如果写入成功，将返回写入后读取的文件内容。
    如果写入过程中发生异常，将抛出异常。

    参数:
    json_path (str): JSON文件的路径。
    data (dict): 要写入文件的数据。

    返回:
    dict: 写入后读取的JSON文件内容。

    异常:
    FileNotFoundError: 如果文件不存在。
    IOError: 如果发生IO错误。
    Exception: 如果发生其他异常。
"""
@exception(logger)
def write_json_file(json_path, data):
    "写入json配置文件数据，线程安全"
    try:
        conf_path = jsonpath(json_path)

        # 使用文件锁确保线程安全
        with file_lock(conf_path):
            # 确保目录存在
            os.makedirs(os.path.dirname(conf_path), exist_ok=True)

            # 使用临时文件进行写入，然后重命名，以确保原子性操作
            temp_path = f"{conf_path}.tmp"
            with open(temp_path, 'w', encoding='utf-8') as file:
                json.dump(data, file, ensure_ascii=False, indent=4)

            # 在Windows上，可能需要先删除目标文件
            if os.path.exists(conf_path):
                try:
                    os.remove(conf_path)
                except Exception as e:
                    logger.error(f"Error removing existing file {conf_path}: {str(e)}")

            # 重命名临时文件为目标文件
            os.rename(temp_path, conf_path)

            # 读取写入后的文件内容并返回
            # 注意：这里不需要再使用file_lock，因为我们已经在with file_lock块内
            with open(conf_path, 'r', encoding='utf-8') as file:
                file_content = file.read().strip()
                if not file_content:
                    return {}
                return json.loads(file_content)
    except FileNotFoundError as e:
        logger.error(f"File not found: {conf_path}")
        raise FileNotFoundError(f"File not found: {conf_path}") from e
    except IOError as e:
        logger.error(f"IO error occurred while writing file: {conf_path}")
        raise IOError(f"IO error occurred while writing file: {conf_path}") from e
    except Exception as e:
        logger.error(f"An error occurred while writing the JSON file: {conf_path}: {str(e)}")
        raise Exception(f"An error occurred while writing the JSON file: {conf_path}") from e


"""
    获取json配置文件的路径
    :param json_name:配置文件名称
    :return conf_path：配置文件路径
"""
@exception(logger)
def jsonpath(json_name):
    "获取ini配置文件的路径"
    try:
        # 获取当前文件路径
        current_directory = os.path.dirname(os.path.abspath(__file__))
        # 配置yaml文件路径
        conf_path = os.path.abspath(os.path.dirname(current_directory) + os.path.sep + "cfg/" + json_name)
        return conf_path
    except Exception as e:
        raise e

if __name__ == '__main__':
    json_path="db_conf.json"
    result=read_json_file(json_path)
    print(result)