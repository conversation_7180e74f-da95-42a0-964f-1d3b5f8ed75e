# -*- coding: utf-8 -*-
"""
@Time ： 2024/11/18 17:37
@Auth ： 逗逗的小老鼠
@File ：deal_method.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import exception,logger
import importlib
"""
    实现动态引入包
    :param module_path：模块名称
    :param method_name：方法名称
    :param body：传参
"""
@exception(logger)
def dynamically_import_and_call_method(module_path, method_name, body,**kwargs):
    "实现动态引入包"
    try:
        # 导入模块
        module = importlib.import_module(module_path)
        # 获取模块中的方法
        method = getattr(module, method_name, None)
        if method is not None and callable(method):
            # 调用方法
            return method(**kwargs)
        else:
            raise AttributeError(f"模块 '{module_path}' 不存在方法 '{method_name}' 或未调用成功")
    except ImportError as e:
        raise ImportError(f"导入模块失败 '{module_path}': {e}")

