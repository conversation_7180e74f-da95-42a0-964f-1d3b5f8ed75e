# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/6 15:38
@Auth ： 逗逗的小老鼠
@File ：deal_ini.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
import configparser
import os

"""
    获取ini配置文件中的参数值
    :param ini_name:配置文件名称
    :param selection:selection的名称
    :param option:option的名称
    :return value：获取到的对应配置参数的值
"""
@exception(logger)
def readini(ininame,selection,option):
    "获取ini配置文件中的参数值"
    try:
        conf_path=inipath(ininame)
        cfg=configparser.ConfigParser()
        cfg.read(conf_path,encoding="UTF-8")
        if cfg.has_section(selection):
            if cfg.has_option(selection,option):
                value=cfg.get(selection,option)
            else:
                if option=="" or option==None:
                    options_list=cfg.options(selection)
                    value={}
                    for opt_dict in options_list:
                        opt_value=cfg.get(selection,opt_dict)
                        value[opt_dict]=opt_value
                else:
                    value=""
        else:
            value = ""
        return value
    except Exception as e:
        raise e

"""
    更新ini配置文件中的参数值
    :param ini_name:配置文件名称
    :param selection:selection的名称
    :param option:option的名称
    :return value：获取到的对应配置参数的值
"""
@exception(logger)
def updateini(ininame,selection,option,value):
    "修改ini配置文件中的参数值，不存在则创建"
    try:
        conf_path = inipath(ininame)
        config=configparser.ConfigParser()
        config.read(conf_path, encoding="utf-8")
        config.set(selection, option, value)
        config.write(open(conf_path, "w"))
        return True
    except Exception as e:
        raise e

"""
    获取ini配置文件的路径
    :param ini_name:配置文件名称
    :return conf_path：配置文件路径
"""
@exception(logger)
def inipath(ini_name):
    "获取ini配置文件的路径"
    try:
        # 获取当前文件路径
        current_directory = os.path.dirname(os.path.abspath(__file__))
        # 配置yaml文件路径
        conf_path = os.path.abspath(os.path.dirname(current_directory) + os.path.sep + "cfg/" + ini_name)
        return conf_path
    except Exception as e:
        raise e


if __name__=='__main__':
    value=readini("db.ini","zentao","")
    print(value)
    # path=inipath("zhangsan")