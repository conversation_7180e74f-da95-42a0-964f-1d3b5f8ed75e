# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/4 10:18
@Auth ： 逗逗的小老鼠
@File ：db_conf_test.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.deal_ini import readini
from lib.get_log import logger,exception
from lib.db_conf import db_mysql


"此方法已不再维护，请使用deal_db_mysql.py方法"

"""
    test环境dsclound库连接及SQL执行
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def db_yx_test_dsclound(sql):
    "test环境dsclound库连接及SQL执行"
    try:
        zentao_ip=readini("db_test.ini","yx_test_dscloud","ip")
        zentao_user=readini("db_test.ini","yx_test_dscloud","user")
        zentao_port=int(readini("db_test.ini","yx_test_dscloud","port"))
        zentao_password = readini("db_test.ini","yx_test_dscloud", "password")
        zentao_dbname = readini("db_test.ini","yx_test_dscloud", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e


"""
    test环境commodity_base库连接及SQL执行
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def yx_test_commodity_base(sql):
    "test环境commodity_base库连接及SQL执行"
    try:
        zentao_ip=readini("db_test.ini","yx_test_commodity_base","ip")
        zentao_user=readini("db_test.ini","yx_test_commodity_base","user")
        zentao_port=int(readini("db_test.ini","yx_test_commodity_base","port"))
        zentao_password = readini("db_test.ini","yx_test_commodity_base", "password")
        zentao_dbname = readini("db_test.ini","yx_test_commodity_base", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e


"""
    test环境commodity库连接及SQL执行
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def yx_test_commodity(sql):
    "test环境commodity库连接及SQL执行"
    try:
        zentao_ip=readini("db_test.ini","yx_test_commodity","ip")
        zentao_user=readini("db_test.ini","yx_test_commodity","user")
        zentao_port=int(readini("db_test.ini","yx_test_commodity","port"))
        zentao_password = readini("db_test.ini","yx_test_commodity", "password")
        zentao_dbname = readini("db_test.ini","yx_test_commodity", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e


"""
    test环境base_info库连接及SQL执行
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def yx_test_base_info(sql):
    "test环境base_info库连接及SQL执行"
    try:
        zentao_ip=readini("db_test.ini","yx_test_base_info","ip")
        zentao_user=readini("db_test.ini","yx_test_base_info","user")
        zentao_port=int(readini("db_test.ini","yx_test_base_info","port"))
        zentao_password = readini("db_test.ini","yx_test_base_info", "password")
        zentao_dbname = readini("db_test.ini","yx_test_base_info", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e



"""
    test环境dsclound_offline库连接及SQL执行
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def db_yx_test_dsclound_offline(sql):
    "test环境dsclound_offline库连接及SQL执行"
    try:
        zentao_ip=readini("db_test.ini","yx_test_dscloud_offline","ip")
        zentao_user=readini("db_test.ini","yx_test_dscloud_offline","user")
        zentao_port=int(readini("db_test.ini","yx_test_dscloud_offline","port"))
        zentao_password = readini("db_test.ini","yx_test_dscloud_offline", "password")
        zentao_dbname = readini("db_test.ini","yx_test_dscloud_offline", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e


"""
    test环境dsclound_offline_archive库连接及SQL执行
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def db_yx_test_dsclound_offline_archive(sql):
    "test环境dsclound_offline库连接及SQL执行"
    try:
        zentao_ip=readini("db_test.ini","yx_test_dscloud_archive","ip")
        zentao_user=readini("db_test.ini","yx_test_dscloud_archive","user")
        zentao_port=int(readini("db_test.ini","yx_test_dscloud_archive","port"))
        zentao_password = readini("db_test.ini","yx_test_dscloud_archive", "password")
        zentao_dbname = readini("db_test.ini","yx_test_dscloud_archive", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e