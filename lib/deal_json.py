# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/2 10:00
@Auth ： 逗逗的小老鼠
@File ：deal_json.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

import json
from datetime import datetime
from decimal import Decimal, InvalidOperation
import base64
import math
from lib.get_log import logger, exception

@exception(logger)
def json_loads(text):
    "将文本解析为JSON"
    try:
        if isinstance(text, str):
            # 尝试将文本解析为JSON
            result = json.loads(text)
            return result
        else:
            return text
    except json.JSONDecodeError as e:
        # 如果解析失败，返回原值
        return text

@exception(logger)
def json_dumps(data):
    "将数据转换为JSON字符串"
    try:
        json_str = json.dumps(data, cls=CustomJSONEncoder,ensure_ascii=False, indent=4)
        return json_str
    except TypeError as e:
        # 如果数据无法转换为 JSON，返回原值
        return data

class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            # 将 datetime 转换为字符串
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        elif isinstance(obj, set):
            # 将 set 转换为 list
            return list(obj)
        elif isinstance(obj, bytes):
            # 将 bytes 转换为 Base64 编码的字符串
            try:
                return base64.b64encode(obj).decode('utf-8')
            except TypeError:
                # 如果 bytes 对象中包含无法编码为 Base64 的字符（实际上不太可能），则抛出异常
                raise TypeError("Object of type bytes is not JSON serializable")
        elif isinstance(obj, Decimal):
            # 将 Decimal 转换为字符串或浮点数（根据精度需求）
            try:
                return float(obj)  # 可能会丢失精度
            except (OverflowError, InvalidOperation):
                return str(obj)
        elif hasattr(obj, 'to_dict'):
            # 假设自定义对象有一个 to_dict 方法来转换为字典
            return obj.to_dict()
        elif isinstance(obj, float):
            if math.isinf(obj):
                return "Infinity" if obj > 0 else "-Infinity"
            elif math.isnan(obj):
                return "NaN"
        elif isinstance(obj, type):
            # 将类型对象转换为字符串（它们的名称）
            return obj.__name__
        # 如果对象类型仍然无法处理，则抛出 TypeError 异常
        raise TypeError(f"数据类型 {obj.__class__.__name__} 不支持JSON 处理，请检查数据类型或实现 to_dict 方法")

if __name__ == '__main__':
    # 使用示例
    data = {
        "timestamp": datetime.now(),
        "unique_items": {1, 2, 3},
        "binary_data": b'\x80\x03]q\x00(X\x03\x00\x00\x00oneq\x01',
        "decimal_value": Decimal('10.5')
    }

    # 将字典序列化为 JSON 字符串
    json_str = json_dumps(data)
    print(json_str)