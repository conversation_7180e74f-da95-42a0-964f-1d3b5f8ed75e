# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/3 10:34
@Auth ： 逗逗的小老鼠
@File ：deal_ThreadPool.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

from lib.deal_config_json import read_json_file, write_json_file
from structure.handle.handle_webhook import message_wechat_push
from lib.get_log import exception, logger
import functools
import threading
import ctypes
from datetime import datetime



@exception(logger)
class InterruptableThread(threading.Thread):
    """支持精确时间统计的线程类"""

    def __init__(self, target, args, kwargs):
        super().__init__(target=target, args=args, kwargs=kwargs)
        self._result = None
        self._exception = None
        self.start_time = None
        self.end_time = None
        self._ready = threading.Event()  # 新增同步事件

    def run(self):
        """重写run方法确保时间记录原子性"""
        self.start_time = datetime.now()
        self._ready.set()  # 标记时间已记录
        try:
            self._result = self._target(*self._args, **self._kwargs)
        except Exception as e:
            self._exception = e
        finally:
            self.end_time = datetime.now()

    def safe_get_time(self, attr_name):
        """线程安全获取时间属性"""
        self._ready.wait(5)  # 最多等待5秒确保时间记录
        return getattr(self, attr_name, datetime.now())

    def terminate(self):
        """安全终止线程"""
        if self.is_alive():
            thread_id = self.ident
            res = ctypes.pythonapi.PyThreadState_SetAsyncExc(
                ctypes.c_long(thread_id),
                ctypes.py_object(SystemExit)
            )
            if res not in (0, 1):
                ctypes.pythonapi.PyThreadState_SetAsyncExc(thread_id, None)

"""
    多线程执行装饰器
    timeout_seconds：超时时间，单位为秒
    max_workers：最大线程数
    scheduler_name：调度器名称
    
    需要注意：
        1、该装饰器运行时，会阻塞主线程，直到所有线程执行完成或超时。
        2、该装饰器运行时，会根据max_workers参数来限制同时运行的线程数，以避免资源消耗过大。
        3、该装饰器运行时，会根据timeout_seconds参数来设置线程的超时时间，以避免线程长时间运行而导致程序阻塞。
        4、该装饰器运行时，会根据scheduler_name参数来记录调度器名称，以方便后续分析。
        5、该装饰器运行时，会根据fun_name参数来记录函数名称，以方便后续分析。
        6、该装饰器运行时，会根据fun_doc参数来记录函数文档，以方便后续分析。
        7、该装饰器运行时，会根据start_time参数来记录线程开始时间，以方便后续分析。
        8、该装饰器运行时，会根据end_time参数来记录线程结束时间，以方便后续分析。
        9、该装饰器运行时，会根据duration参数来记录线程运行时间，以方便后续分析。
        10、该装饰器运行时，会根据is_timeout参数来记录线程是否超时，以方便后续分析。
        11、该装饰器运行时，会根据Threshold参数来记录线程超时时间，以方便后续分析。
"""
@exception(logger)
def combined_decorator(timeout_seconds=120, max_workers=1, **kwargs):
    scheduler_name = kwargs.get('scheduler_name', "")

    def decorator(func):
        semaphore = threading.Semaphore(max_workers)

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            semaphore.acquire()
            thread = None
            try:
                # 创建并启动线程
                thread = InterruptableThread(
                    target=func,
                    args=args,
                    kwargs=kwargs
                )
                thread.start()

                # 确保时间记录已完成
                thread.safe_get_time('start_time')

                # 等待线程完成或超时
                thread.join(timeout_seconds)

                # 获取安全时间
                start_time = thread.safe_get_time('start_time')
                end_time = thread.safe_get_time('end_time') if not thread.is_alive() else datetime.now()

                # 构建执行信息
                exec_info = {
                    "fun_name": func.__name__,
                    "fun_doc": func.__doc__ or "",
                    "scheduler_name": scheduler_name,
                    "start_time": start_time,
                    "end_time": end_time,
                    "duration": round((end_time - start_time).total_seconds(), 2),
                    "is_timeout": thread.is_alive(),
                    "Threshold": timeout_seconds
                }

                # 处理超时
                if exec_info["is_timeout"]:
                    thread.terminate()
                    update_execution_records(exec_info)
                    handle_timeout(exec_info)
                    raise TimeoutError(f"【{exec_info['fun_doc']}】执行超时（耗时{exec_info['duration']}秒）")

                # 更新正常记录
                update_execution_records(exec_info)

                # 处理异常
                if thread._exception:
                    raise thread._exception

                return thread._result

            finally:
                semaphore.release()
                if thread and thread.is_alive():
                    thread.terminate()

        return wrapper

    return decorator


# update_execution_records 和 handle_timeout 保持原样

def update_execution_records(exec_info):
    """更新执行记录到JSON文件"""
    try:
        json_path = "func_time_recode.json"

        # 读取现有数据
        config_data = read_json_file(json_path)
        func_data = config_data.get(exec_info["fun_name"], {})

        # 更新统计信息
        total_executions = func_data.get("total_executions", 0) + 1
        total_duration = func_data.get("total_duration", 0.0) + exec_info["duration"]

        # 构建新记录
        new_record = {
            "last_execution": exec_info["end_time"].strftime("%Y-%m-%d %H:%M:%S"),
            "last_duration": exec_info["duration"],
            "total_executions": total_executions,
            "average_duration": round(total_duration / total_executions, 2),
            "scheduler_name": exec_info["scheduler_name"],
            "function_doc": exec_info["fun_doc"]
        }

        # 更新超时记录
        if exec_info["is_timeout"]:
            new_record.update({
                "timeout_count": func_data.get("timeout_count", 0) + 1,
                "last_timeout": exec_info["end_time"].strftime("%Y-%m-%d %H:%M:%S")
            })

        # 合并数据
        config_data[exec_info["fun_name"]] = {**func_data, **new_record}

        # 写入文件
        write_json_file(json_path, config_data)
    except Exception as e:
        logger.error(f"更新执行记录时出错: {e}")
        raise


def handle_timeout(exec_info):
    """方法执行超时后的处理逻辑"""
    try:
        message = {"fun_name": exec_info["fun_name"], "fun_doc": exec_info["fun_doc"],"waring_image":"🚨🚨🚨🚨🚨",
                   "fun_duration": exec_info["duration"], "fun_start": exec_info["start_time"],
                   "is_timeout": exec_info["is_timeout"], "fun_end": exec_info["end_time"],"waring_Threshold": exec_info["Threshold"]}
        logger.error(message)
        message_wechat_push(message, "function_excute_timeout")
    except Exception as e:
        logger.error(f"处理超时通知时出错: {e}")
        raise


# @combined_decorator(timeout_seconds=5, max_workers=3, scheduler_name="function_excute_timeout")
# def data_processing():
#     "测试方法"
#     import time
#     time.sleep(10)  # 模拟耗时操作
#     return {"status": "success"}
#
#
# try:
#     result = data_processing()  # 5秒后将抛出TimeoutError
#     print(result)
# except TimeoutError as e:
#     print("任务超时:", e)
# except Exception as e:
#     print("执行出错:", e)
