# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/6 14:44
@Auth ： 逗逗的小老鼠
@File ：deal_xml.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
import xml.etree.ElementTree as ET

# 从xml文件中获取元素文本，未获取到则返回默认值
def get_element_text(element, tag,**kwargs):
    """
    从xml文件中获取元素文本，未获取到则返回默认值
    :param element: xml文件内容
    :param tag: 元素标签
    :param default: 默认值
    :return: 元素文本
    """
    try:
        default=kwargs.get('default','')
        root = ET.fromstring(element)
        found_element = root.find(tag)
        return found_element.text if found_element is not None else default
    except Exception as e:
        raise e
