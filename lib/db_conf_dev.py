# -*- coding: utf-8 -*-
"""
@Time ： 2024/4/26 10:49
@Auth ： 逗逗的小老鼠
@File ：db_conf_dev.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.deal_ini import readini
from lib.get_log import logger,exception
from lib.db_conf import db_mysql

"此方法已不再维护，请使用deal_db_mysql.py方法"

"""
    dev环境dsclound库连接及SQL执行
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def yx_dev_dsclound(sql):
    "test环境dsclound库连接及SQL执行"
    try:
        zentao_ip=readini("db_dev.ini","yx_dev_dscloud","ip")
        zentao_user=readini("db_dev.ini","yx_dev_dscloud","user")
        zentao_port=int(readini("db_dev.ini","yx_dev_dscloud","port"))
        zentao_password = readini("db_dev.ini","yx_dev_dscloud", "password")
        zentao_dbname = readini("db_dev.ini","yx_dev_dscloud", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e
