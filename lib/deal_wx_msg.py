# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/6 19:07
@Auth ： 逗逗的小老鼠
@File ：deal_wx_msg.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：微信webhook消息推送，新消息建议调用“handle_webhook”中方法
"""
from lib.get_log import logger,exception
from lib.db_conf import db_insight
import requests
import json
import datetime

"""
    发送消息
    :param query_dict:查询条件
    :return defect_info：查询结果
"""
@exception(logger)
def message_wechat_push(message_info,msg_task_code):
    "推送企业微信机器人消息"
    try:
        msg_conf_result=message_config_info(msg_task_code)
        msg_robot_url=msg_conf_result['msg_url']
        notification_person=msg_conf_result['noti_person']
        noto_msg=""
        for item in list(notification_person):
            noto_msg=noto_msg+f"<@{item}>"
        headers={"Content-Type":"application/json"}
        data={"msgtype":"markdown","markdown":{"content":noto_msg+message_info}}
        print(data)
        response=requests.post(msg_robot_url,data=json.dumps(data),headers=headers)
        result=response.json()
        print(result)
        if 'errmsg' in result and 'errcode' in result:
            if result['errcode']==0 and result['errmsg']=='ok':
                send_result="发送成功"
                errmsg=""
            else:
                send_result = "发送失败"
                errmsg=result['errmsg']
        now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        send_date = datetime.datetime.strptime(now, '%Y-%m-%d %H:%M:%S')
        push_result={"send_result":send_result,"send_date":send_date,"errmsg":errmsg}
        return push_result
    except Exception as e:
        raise e


"""
    消息发送地址及通知人列表处理
    :param msg_task_code:消息任务编码
    :return defect_info：查询结果
"""
@exception(logger)
def message_config_info(msg_task_code):
    "消息发送地址及通知人列表处理"
    try:
        msg_url=""
        noti_person=[]
        msg_conf_sql=f"""SELECT * FROM wx_wehook_msg_config WHERE msg_task_code='{msg_task_code}' AND is_delete=0"""
        msg_conf_result=db_insight(msg_conf_sql)
        msg_conf_data=msg_conf_result['data']
        for msg_conf_item in msg_conf_data:
            msg_url=msg_conf_item['msg_url']
            notification_person=msg_conf_item['notification_person']
            if notification_person != "" and notification_person != None:
                noti_person=notification_person.split(",")
        result={"msg_url":msg_url,"noti_person":noti_person}
        return result
    except Exception as e:
        raise e


if __name__=="__main__":
    msg_task="order_sale_message_push"
    msg_result=message_config_info(msg_task)
    print(msg_result)
