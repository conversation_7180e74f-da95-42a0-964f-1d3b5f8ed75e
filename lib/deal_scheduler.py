# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/7 9:29
@Auth ： 逗逗的小老鼠
@File ：deal_scheduler.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：定时任务
"""
import datetime
from lib.deal_sql import dict_to_sql_insert,dict_to_convert_sql_insert
import sys
import pytz
from lib.get_log import exception,logger
from apscheduler.schedulers.background import BackgroundScheduler
from lib.db_conf import db_insight
from apscheduler.events import EVENT_JOB_EXECUTED,EVENT_JOB_ERROR
from lib.deal_db_mysql import db_mysql_connect
bgsched = BackgroundScheduler(timezone="Asia/Shanghai")

"""
    写入任务执行记录
"""
@exception(logger)
def store_execution_record(job_id, status, message):
    try:
        now= datetime.datetime.now()
        execution_time=now.strftime("%Y-%m-%d %H:%M:%S")
        execution_dict={"job_id":job_id,"execution_time":execution_time,"status":status,"message":message}
        tablename="scheduler_task_execution_logs"
        execution_list=[]
        execution_list.append(execution_dict)
        record_sql, record_val = dict_to_convert_sql_insert(tablename, execution_list)
        info_result = db_mysql_connect('insight', record_sql, sql_val=record_val, environment_flag="common")
    except Exception as e:
        raise e

"""
    任务监听器
"""
@exception(logger)
def my_listener(event):
    try:
        if event.exception:
            status = 'ERROR'
            message = str(event.exception)
        else:
            status = 'SUCCESS'
            message = event.retval if hasattr(event, 'retval') else '任务执行成功'
        # 存储任务执行记录
        store_execution_record(event.job_id, status, message)
    except Exception as e:
        raise e


# 添加定时任务
@exception(logger)
def sch_add_job(fun,id,name,trigger,*args,**kwargs):
    try:
        print(datetime.timezone)
        myjob = bgsched.add_job(func=fun, id=id, name=name, trigger=trigger,timezone="Asia/Shanghai",*args,**kwargs)
        msg="ture"
        return msg
    except Exception as e:
        raise e


"""
    查看定时任务
"""
@exception(logger)
def sch_get_job():
    "查看定时任务"
    try:
        job_data = bgsched.get_jobs()
        result=job_to_json(job_data)
        return result
    except Exception as e:
        raise e

"""
    移除定时任务
"""
@exception(logger)
def sch_remove_job(job_id):
    "移除定时任务"
    try:
        result = bgsched.remove_job(str(job_id))
        return result
    except Exception as e:
        raise e

"""
    暂停定时任务
"""
@exception(logger)
def sch_pause_job(job_id):
    "暂停定时任务"
    try:
        result = bgsched.pause_job(str(job_id))
        return result
    except Exception as e:
        raise e

"""
    恢复定时任务
"""
@exception(logger)
def sch_resume_job(job_id):
    "恢复定时任务"
    try:
        result = bgsched.resume_job(str(job_id))
        return result
    except Exception as e:
        raise e


# 操作定时任务
def sch_manage_job(manage_type,job_id):
    try:
        # 查看任务
        if manage_type == "get":
            manage_result=bgsched.get_jobs()
        # 暂停任务
        if manage_type=="pause":
            manage_result=bgsched.pause_job(job_id=job_id)
        # 恢复任务
        if manage_type=="resume":
            manage_result=bgsched.resume_job(job_id=job_id)
        # 移除任务
        if manage_type == "remove":
            manage_result=bgsched.remove_job(job_id=job_id)
        result = job_to_json(manage_result)
        return result
    except Exception as e:
        raise e

# 将job数据转化为json
def job_to_json(job):
    try:
        result = []
        if isinstance(job,list):
            for job_item in job:
                job_id=job_item.id
                job_name=job_item.name
                job_next_run_time=job_item.next_run_time
                job_func=job_item.func_ref
                next_run_time=str(job_next_run_time)
                job_data={"job_id":job_id,"job_name":job_name,"next_run_time":next_run_time,"job_func":job_func}
                result.append(job_data)
        else:
            job_id = job.id
            job_name = job.name
            job_next_run_time = job.next_run_time
            job_func = job.func_ref
            job_data = {"job_id": job_id, "job_name": job_name, "next_run_time": job_next_run_time,"job_func":job_func}
            result.append(job_data)
        return result
    except Exception as e:
        raise e

def my_job(value):
    print(f"定时任务执行中...{value}")
    # 在这里编写你的任务逻辑
    # ...
    return "任务执行成功"

# 添加监听
bgsched.add_listener(my_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)

if __name__=="__main__":

    job = bgsched.add_job(my_job, 'interval', minutes=5, id='my_job_id')
    bgsched.start()



