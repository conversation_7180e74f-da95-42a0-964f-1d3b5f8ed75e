# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/13 10:49
@Auth ： 逗逗的小老鼠
@File ：deal_dict.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception


"""
    删除json中所有类型为dict/list的字段
    :param data:原始值
    :return value：处理后的值
"""
@exception(logger)
def deal_dict_del_dict(data):
    "删除json中所有类型为dict/list的字段"
    try:
        data_new=data.copy()
        for item in data:
            value=data[item]
            if isinstance(value,list) or isinstance(value,dict):
                del(data_new[item])
        return data_new

    except Exception as e:
        raise e



if __name__=="__main__":
    data={
"id": 809780,
"orderNo": "1793285196496751618",
"orderState": 20,
"erpState": 20,
"thirdPlatformCode": "27",
"thirdOrderNo": "3800981264243726948",
"thirdOrderId": "3800981264243726948",
"thirdOrderState": "27_2",
"offState": 1,
"merCode": "500001",
"clientCode": "48775e71e85c48a8b971ac8b3cff1dcc",
"onlineStoreCode": "123614_2694633",}
    deal_dict_del_dict(data)