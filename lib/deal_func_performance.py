# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/11 18:01
@Auth ： 逗逗的小老鼠
@File ：deal_func_performance.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：该方法主要使用装饰器，用于监控方法的执行性能情况
"""
import time
from decorator import decorator
from lib.get_log import exception,logger
from lib.deal_config_json import read_json_file


"""
    注意：
        1、该装饰器已不再维护，新代码可使用from lib.decorator_func_timeout import timeout_decorator 替代，使用时请注意参数顺序
"""
@decorator
@exception(logger)
def func_performance(func, *args, **kwargs):
    "该装饰器主要用于监控方法的执行性能，超过设定的时间，会记录日志"
    json_path = "func_time.json"
    config_data = read_json_file(json_path)
    func_time_waring=config_data.get("func_time_waring",1)
    func_time_error=config_data.get("func_time_error",3)

    start_time = time.time()
    result = func(*args, **kwargs)
    end_time = time.time()
    elapsed_time = end_time - start_time
    if elapsed_time>=func_time_error:
        logger.error(f"{func.__name__} 执行花费时间为 {elapsed_time:.4f} 秒，入参为【 {args} {kwargs}】")
    else:
        if elapsed_time>=func_time_waring:
            logger.warning(f"{func.__name__} 执行花费时间为 {elapsed_time:.4f} 秒，入参为【 {args} {kwargs}】")
        else:
            logger.info(f"{func.__name__} 执行花费时间为 {elapsed_time:.4f} 秒，入参为【 {args} {kwargs}】")
    # print(f"{func.__name__} executed in {elapsed_time:.4f} seconds")
    return result


@func_performance
def your_method():
    time.sleep(5)
    # 你的方法代码
    pass


# 调用你的方法
# your_method()