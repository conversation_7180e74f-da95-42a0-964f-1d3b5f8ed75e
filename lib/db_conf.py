# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/6 15:31
@Auth ： 逗逗的小老鼠
@File ：db_conf.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.deal_ini import readini
import pymysql
from lib.get_log import logger,exception



"此方法已不再维护，请使用deal_db_mysql.py方法"

"""
    监测数据库
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def db_insight(sql):
    "测试部监测数据库"
    try:
        zentao_ip=readini("db.ini","insight","ip")
        zentao_user=readini("db.ini","insight","user")
        zentao_port=int(readini("db.ini","insight","port"))
        zentao_password = readini("db.ini", "insight", "password")
        zentao_dbname = readini("db.ini", "insight", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e


"""
    dev订单库执行SQL
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def db_yx_dev_dsclound(sql):
    "dev订单中台库"
    try:
        zentao_ip=readini("db.ini","yx_dev_dscloud","ip")
        zentao_user=readini("db.ini","yx_dev_dscloud","user")
        zentao_port=int(readini("db.ini","yx_dev_dscloud","port"))
        zentao_password = readini("db.ini", "yx_dev_dscloud", "password")
        zentao_dbname = readini("db.ini", "yx_dev_dscloud", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e



"""
    心云基础中台baseinfo库执行SQL
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def db_yx_base_info(sql):
    "心云中台baseinfo库"
    try:
        zentao_ip=readini("db.ini","yx_base_info","ip")
        zentao_user=readini("db.ini","yx_base_info","user")
        zentao_port=int(readini("db.ini","yx_base_info","port"))
        zentao_password = readini("db.ini", "yx_base_info", "password")
        zentao_dbname = readini("db.ini", "yx_base_info", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e


"""
    心云商品中台base库执行SQL
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def db_yx_commodity_base(sql):
    "心云中台商品库"
    try:
        zentao_ip=readini("db.ini","yx_commodity_base","ip")
        zentao_user=readini("db.ini","yx_commodity_base","user")
        zentao_port=int(readini("db.ini","yx_commodity_base","port"))
        zentao_password = readini("db.ini", "yx_commodity_base", "password")
        zentao_dbname = readini("db.ini", "yx_commodity_base", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e

"""
    心云中台订单库执行SQL
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def db_yx_dscloud(sql):
    "心云中台订单库"
    try:
        zentao_ip=readini("db.ini","yx_dscloud","ip")
        zentao_user=readini("db.ini","yx_dscloud","user")
        zentao_port=int(readini("db.ini","yx_dscloud","port"))
        zentao_password = readini("db.ini", "yx_dscloud", "password")
        zentao_dbname = readini("db.ini", "yx_dscloud", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e

"""
    心云前台订单库执行SQL
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def db_yx_middle_order(sql):
    "心云前台订单库"
    try:
        zentao_ip=readini("db.ini","yx_middle_order","ip")
        zentao_user=readini("db.ini","yx_middle_order","user")
        zentao_port=int(readini("db.ini","yx_middle_order","port"))
        zentao_password = readini("db.ini", "yx_middle_order", "password")
        zentao_dbname = readini("db.ini", "yx_middle_order", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e


"""
    雨诺OMS库执行SQL
    :param sql:SQL语句
    :return result：查询结果
"""
@exception(logger)
def db_yn_oms(sql):
    "雨诺OMS库执行SQL"
    try:
        zentao_ip=readini("db.ini","yn_oms","ip")
        zentao_user=readini("db.ini","yn_oms","user")
        zentao_port=int(readini("db.ini","yn_oms","port"))
        zentao_password = readini("db.ini", "yn_oms", "password")
        zentao_dbname = readini("db.ini", "yn_oms", "dbname")
        result=db_mysql(zentao_ip,zentao_user,zentao_password,zentao_port,zentao_dbname,sql)
        return result
    except Exception as e:
        raise e


"""
    连接MySQL数据库
    :param host:配置文件名称
    :param user:selection的名称
    :param password:option的名称
    :param databse:数据库名称
    :param sql:SQL语句
    :return dbresult：查询结果
"""
@exception(logger)
def db_mysql(host,user,password,port,database,sql):
    "连接MySQL数据库"
    api_db = pymysql.connect(host=host,user= user,port=port, password=password,database= database, charset='utf8')
    cursor = api_db.cursor()
    try:
        cur = cursor.execute(sql)
        if cursor.description == None or cursor.description == '':
            dbresult = cur
            columns=0
        else:
            columns = [col[0] for col in cursor.description]
            dbresult = [dict(zip(columns, row)) for row in cursor.fetchall()]
        result={"column":columns,"data":dbresult}
        api_db.commit()
        cursor.close()
        api_db.close()
        return result
    except Exception as e:
        raise e

"""
    连接MySQL数据库
    :param host:配置文件名称
    :param user:selection的名称
    :param password:option的名称
    :param databse:数据库名称
    :param sql:SQL语句
    :return dbresult：字段列表
"""
@exception(logger)
def col_mysql(host,user,password,port,database,sql):
    "连接MySQL数据库"
    api_db = pymysql.connect(host=host,user= user,port=port, password=password,database= database, charset='utf8')
    cursor = api_db.cursor()
    try:
        cur = cursor.execute(sql)
        if cursor.description == None or cursor.description == '':
            columns = cur
        else:
            columns = [col[0] for col in cursor.description]
        api_db.commit()
        cursor.close()
        api_db.close()
        return columns
    except Exception as e:
        raise e


if __name__=="__main__":
    sql="SELECT * FROM zt_api"
    # result=db_zentao(sql)
    # print(result)