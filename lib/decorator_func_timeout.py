# -*- coding: utf-8 -*-
"""
@Time ： 2024/12/12 11:23
@Auth ： 逗逗的小老鼠
@File ：decorator_func_timeout.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：根据方法执行时间，判断是否超时，超时后，记录超时次数和最新运行时间，记录到配置文件中，推送微信消息
"""
from lib.get_log import exception,logger
import threading
import functools
from lib.deal_config_json import read_json_file,write_json_file
from structure.handle.handle_webhook import message_wechat_push
from datetime import datetime


"""
    注意：
        1、该装饰器已不再维护，新代码可使用from lib.decorator_func_timeout import timeout_decorator 替代，使用时请注意参数顺序
"""
@exception(logger)
def timeout_decorator(timeout_seconds,**kwargs):
    scheduler_name = kwargs.get("scheduler_name", "")
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            fun_name = func.__name__  # 获取方法名称
            fun_doc = func.__doc__  # 获取方法注释
            class InterruptableThread(threading.Thread):
                def __init__(self):
                    super().__init__()
                    self._result = None
                    self._exception = None
                def run(self):
                    try:
                        self._result = func(*args, **kwargs)
                    except Exception as e:
                        self._exception = e
                def join(self, timeout=None):
                    super().join(timeout)
                    if self.is_alive():
                        new_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        # 方法超时后，记录超时次数和最新运行时间，记录到配置文件中
                        json_path = "func_time_recode.json"
                        config_data = read_json_file(json_path)
                        if fun_name in config_data:
                            func_time_data=config_data.get(fun_name)
                            timeout_count=int(func_time_data.get("timeout_count"))+1
                            func_time_data={"timeout_count":timeout_count,"scheduler_name":scheduler_name,"update_time":new_time,"fun_doc":fun_doc}
                        else:
                            func_time_data = {"timeout_count": 1,"fun_doc":fun_doc,"scheduler_name":scheduler_name, "update_time": new_time}
                        config_data[fun_name] = func_time_data
                        write_json_file(json_path, config_data)
                        msg=f"【{func.__doc__}】方法【 {func.__name__} 】执行时间超过限制时间【 {timeout_seconds}】 seconds，涉及定时任务：【{scheduler_name}】，请尽快核查"
                        logger.error(msg)
                        # 推送微信消息
                        message_wechat_push(msg, "function_excute_timeout")
                        raise TimeoutError(msg)
                    if self._exception:
                        raise self._exception
                    return self._result

            thread = InterruptableThread()
            thread.start()
            return thread.join(timeout_seconds)
        return wrapper
    return decorator

# 使用装饰器
@timeout_decorator(5,scheduler_name="order_data_verify") # 设置超时时间为5秒
def long_running_task():
    "测试方法"
    try:
        import time
        time.sleep(6)  # 模拟长时间运行的任务
        print(11111111)
        return "Task completed"

    except TimeoutError as err_out:
        print("timeout")
        print(err_out)
    except Exception as e:
        print(e)


if __name__=="__main__":
    result = long_running_task()
    print(result)