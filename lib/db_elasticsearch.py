# -*- coding: utf-8 -*-
"""
@Time ： 2025/4/21 11:47
@Auth ： 逗逗的小老鼠
@File ：db_elasticsearch.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：es操作处理
"""
from lib.deal_config_json import read_json_file
from elasticsearch import Elasticsearch
from lib.get_log import logger,exception


def get_es_info(es_index,query_body,**kwargs):
    """获取es信息"""
    try:
        es_connt=connect_elasticsearch(**kwargs)
        result=es_connt.search(index=es_index,body=query_body)
        return result
    except Exception as e:
        raise


@exception(logger)
def connect_elasticsearch(**kwargs):
    """连接到Elasticsearch服务器"""
    try:
        json_path = "es_conf.json"
        db_config = read_json_file(json_path)
        # 获取环境变量，若无环境变量，默认为test
        environment_flag = kwargs.get("environment_flag", "test")
        # 该值默认可以不传，不传默认连接dscloud_offline数据库
        connect_name = kwargs.get("database_name", "dscloud_offline")
        # 获取对应环境下的所有数据库信息
        es_dict = db_config.get(environment_flag)
        # 判断数据库是否在对应环境中存在
        if connect_name in es_dict.keys():
            # 根据数据库名称获取对应的数据库连接信息
            connect_info = es_dict.get(connect_name)
            host = connect_info.get("host")
            user = connect_info.get("user")
            password = connect_info.get("password")
            # 创建客户端实例
            es = Elasticsearch(
                host,
                http_auth=(user, password),
                timeout=30
            )
            print(f"服务端版本: {es.info()['version']['number']}")

            # 验证连接
            try:
                es.ping()
            except Exception as e:
                raise ConnectionError("无法连接到Elasticsearch,异常信息：", e)

            print(f"成功连接！集群名称：{es.info()['cluster_name']}")
            return es
        else:
            logger.error(f"数据库{connect_name}不存在")
    except Exception as e:
        raise


if __name__ == "__main__":
    query_body = {
        "query": {
            "bool": {
                "filter": [  # 使用 filter 上下文提升性能
                    {"term": {"storeCode": "H812"}},
                    {"range": {
                        "created": {
                            "gte": "2024-04-20 00:00:00",
                            "lte": "2025-04-20 00:00:00",
                            "format": "yyyy-MM-dd HH:mm:ss"  # 自定义格式需字段 mapping 支持
                        }
                    }}
                ]
            }
        },
        "track_total_hits": True  # 禁用精确统计提升速度
    }
    es = get_es_info("test_es_offline_order_manage",query_body)
    print(es.info())