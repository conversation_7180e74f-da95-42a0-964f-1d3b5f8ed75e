# -*- coding: utf-8 -*-
"""
@Time ： 2023/12/26 17:12
@Auth ： 逗逗的小老鼠
@File ：deal_text.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""

from lib.get_log import logger,exception
import os,re
"""
    向txt文件中写入数据
    :param txt_name:文件名称
    :param txt:文本内容
    :return result：True
"""
def text_write(txt_name,txt):
    ""
    try:
        curpath = os.path.abspath(os.path.dirname(__file__))
        textpath = os.path.abspath(os.path.dirname(curpath) + os.path.sep + "text/"+txt_name)
        # with语句来自动管理文件的打开和关闭，即使发生异常也能正确关闭文件
        with open(textpath, "a+",encoding="UTF-8") as file:
            file.write(str(txt)+"\n")
        return True
    except Exception as e:
        raise e

"""
    向txt文件中写入数据
    :param txt_name:文件名称
    :param key_words:需要查询的内容
    :return result：True
"""
def text_find(txt_name,key_words):
    ""
    try:
        curpath = os.path.abspath(os.path.dirname(__file__))
        textpath = os.path.abspath(os.path.dirname(curpath) + os.path.sep + "text/"+txt_name)
        count=0
        with open(textpath,'r',encoding="UTF-8", errors="ignore") as file:
            for line in file:
                if re.search(key_words,line):
                    count=count+1
                    # print(line)
        return count
    except Exception as e:
        raise e




if __name__=="__main__":
    text_name="text.txt"
    # text="vvvv,sjsjsjsj,ceshi,csjng"
    # text_write(text_name,text)
    key_words="sjsjsjsj"
    count=text_find(text_name,key_words)
    print(count)
