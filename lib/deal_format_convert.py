# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/14 16:16
@Auth ： 逗逗的小老鼠
@File ：deal_format_convert.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from datetime import datetime
import json
from decimal import Decimal


"""
    字段格式转换
    :param filed_value:待转换的值
    :param format_value:转换的格式
    :return filed_value：转换后的值
"""
@exception(logger)
def filed_format_convert(filed_value,format_value):
    "字段格式转换"
    try:
        if format_value=='int':
            filed_value=int(filed_value)
        elif format_value=='float':
            filed_value=float(Decimal(str(filed_value)))
        elif format_value=='datetime':
            file_type=type(filed_value)
            if isinstance(filed_value,int):
                filed_value_stamp = datetime.fromtimestamp(filed_value)
                filed_value = filed_value_stamp.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(filed_value,datetime):
                filed_value = filed_value.strftime('%Y-%m-%d %H:%M:%S')
            else:
                if is_date(filed_value):
                    filed_value = datetime.strptime(filed_value, "%Y-%m-%d %H:%M:%S")
                    filed_value=filed_value.strftime("%Y-%m-%d %H:%M:%S")
                else:
                    return None
        elif format_value=='list':
            filed_value=list(filed_value)
        elif format_value=='dict' or format_value=='json':
            filed_value=json.dumps(filed_value)
        else:
            filed_value=str(filed_value)
        return filed_value
    except Exception as e:
        raise e
"""
    将参数进行数学运算
    :param val1:值1
    :param val2:值2
    :return r_value：结果值
"""
@exception(logger)
def value_mathematics(val1,val2,math_type):
    try:
        if math_type=="+":
            r_value=Decimal(str(val1))+Decimal(str(val2))
        elif math_type=="-":
            r_value = Decimal(str(val1)) - Decimal(str(val2))
        elif math_type=="*":
            r_value = Decimal(str(val1)) * Decimal(str(val2))
        elif math_type=="/":
            r_value = Decimal(str(val1)) / Decimal(str(val2))
        else:
            r_value=str(val1)+str(val2)
        r_value=round(filed_format_convert(r_value,'float'),2)
        return r_value
    except Exception as e:
        raise e


"""
    将数据中空值转换为None
    :param filed_value:待转换的值
    :param format_value:转换的格式
    :return filed_value：转换后的值
"""
@exception(logger)
def null_to_None(filed_value):
    try:
        if filed_value =="" or filed_value is None:
            filed_value=None
        else:
            filed_value=filed_value
        return filed_value
    except Exception as e:
        raise e




"""
    判断是否为日期格式
    :param text
    :return value：
"""
@exception(logger)
def is_date(text):
    try:
        datetime.strptime(text, "%Y-%m-%d %H:%M:%S")
        return True
    except ValueError:
        return False

"""
    将list列表转换为SQL中in条件值
"""
@exception(logger)
def list_to_sql_filter(value_list):
    try:
        joined_string = "', '".join(value_list)
        filter_value = "'" + joined_string.replace(" ", "") + "'"
        return filter_value
    except Exception as e:
        raise e


if __name__=="__main__":
    value="2024-03-18 10:54:36"
    date_value=datetime.now()
    # is_date(date_value)
    filed_format_convert(date_value,'datetime')