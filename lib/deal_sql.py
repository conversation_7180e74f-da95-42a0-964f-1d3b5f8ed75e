# -*- coding: utf-8 -*-
"""
@Time ： 2024/3/11 18:20
@Auth ： 逗逗的小老鼠
@File ：deal_sql.py
@IDE ：PyCharm
@Motto：Rules are made to be broken
@Remark：What do you want？
"""
from lib.get_log import logger,exception
from pymysql.converters import escape_string
import configparser
import os

"""
    将list转换成为sql语句
    :param table_name:表名
    :param insert_list:列表
    :return result：SQL语句集合
    
    注意：
        1、因该方法存在部分效率和安全问题，不建议使用该方法，建议使用dict_to_convert_sql_insert方法
"""
@exception(logger)
def dict_to_sql_insert(table_name,insert_list):
    "将list转换成为sql语句"
    try:
        result=[]
        for item in insert_list:
            columns = ', '.join(item.keys())
            values = ', '.join([f"'{escape_string(str(v))}'" for v in item.values()])
            sql = f"INSERT INTO {table_name} ({columns}) VALUES ({values});"
            # print(sql)

            result.append(sql)
        return result
    except Exception as e:
        raise e


"""
    将dict数据转换成为sql语句
    :param table_name:表名
    :param insert_list:列表
    :return result：SQL语句集合
"""
@exception(logger)
def dict_to_sql_update(table_name,update_dict,filter_dict):
    "将list转换成为sql语句"
    try:
        update_item=[]
        filter_item=[]
        for update_key in update_dict:
            update_value=escape_string(str(update_dict[update_key]))
            update_param=f"{update_key} = '{update_value}'"
            update_item.append(update_param)
        update_sql=', '.join(update_item)
        for filter_key in filter_dict:
            filter_value=escape_string(str(filter_dict[filter_key]))
            filter_param=f"{filter_key}='{filter_value}'"
            filter_item.append(filter_param)
        where_sql=' AND '.join(filter_item)
        sql=f""" UPDATE {table_name} SET {update_sql} WHERE 1=1 AND {where_sql} """
        # print(sql)
        return sql
    except Exception as e:
        raise e


import json

"""
    将字典列表转换为参数化 INSERT 语句，自动处理字典类型的值（转为JSON字符串）

    Args:
        table_name (str): 目标表名
        insert_list (list[dict]): 插入的数据列表，每个元素为字段键值对

    Returns:
        tuple: (sql_template, val_list) 参数化SQL和对应的值列表

    Raises:
        ValueError: 数据字段不一致或空数据
    """
def dict_to_convert_sql_insert(table_name, insert_list):
    "将字典列表转换为参数化 INSERT 语句，自动处理字典类型的值（转为JSON字符串）"
    if not insert_list:
        return "", []

    # 1. 校验所有字典的键是否一致
    first_keys = list(insert_list[0].keys())
    for idx, item in enumerate(insert_list[1:], start=1):
        if list(item.keys()) != first_keys:
            raise ValueError(f"第 {idx + 1} 条数据字段与第1条不一致，无法批量生成SQL")

    # 2. 转义表名和列名（防御SQL注入）
    escaped_table = f"`{table_name}`"
    columns = [f"`{k}`" for k in first_keys]

    # 3. 生成参数化SQL模板
    columns_str = ", ".join(columns)
    placeholders = ", ".join(["%s"] * len(columns))
    sql_template = f"INSERT INTO {escaped_table} ({columns_str}) VALUES ({placeholders})"

    # 4. 提取值列表，自动转换字典为JSON字符串
    val_list = [
        tuple(
            json.dumps(value, ensure_ascii=False)  # 保留中文/特殊字符
            if isinstance(value, dict)
            else value
            for value in (item[key] for key in first_keys)
        )
        for item in insert_list
    ]

    return sql_template, val_list



if __name__=="__main__":
    data = [
        {"name": "Alice", "age": 25, "email": "<EMAIL>"},
        {"name": "Bob", "age": 30, "email": None},  # 支持None值
        {"name": "Charlie", "age": 35, "email": "100%完成#测试"}  # 含特殊符号
    ]

    # 生成参数化SQL和值
    sql_template, val_list = dict_to_convert_sql_insert("users", data)

    print(sql_template)
    # 输出: INSERT INTO `users` (`name`,`age`,`email`) VALUES (%s,%s,%s)

    print(val_list)
    # 输出: [
    #   ('Alice', 25, '<EMAIL>'),
    #   ('Bob', 30, None),
    #   ('Charlie', 35, '100%完成#测试')
    # ]

    # 执行批量插入（高性能）
    # cursor.executemany(sql_template, val_list)